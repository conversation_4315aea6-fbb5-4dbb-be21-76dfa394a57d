import { computed, ref, set, type Ref } from 'vue'
import { cPostArticles, fetchWarehouses } from '@/shared/api/erp_server'
import { cPostSales } from '@/shared/api/erp_server/sales/cPostSales'
import { i18n } from '@/services/config/i18n'
import { useToastStore } from '@/services/plugin/toast/stores'
import useDateFormatter from '@/shared/composable/useDateFormatter'
import cPostBrands from '@/shared/api/erp_server/brand/cPostBrands'
import cPostSubcategories from '@/shared/api/erp_server/subcategory/cPostSubcategories'
import { cPostCountries } from '@/shared/api/erp_server/country/cPostCountries'
import type { SystemEvent } from '@/shared/api/erp_server/system/cPostSystemEvents'
import { cPostSuppliers } from '@/shared/api/erp_server/supplier/cPostSuppliers'
import { useArticleColor } from '@/apps/erp/composable/useArticleColor'
import { useSalesChannel } from '@/apps/erp/composable/useSalesChannel'

const { colors, fetchColorsOnce } = useArticleColor()
const { fetchSalesChannelsOnce, sales_channels } = useSalesChannel()

const color_names = computed(() =>
    colors.value.reduce(
        (acc, c) => {
            acc[c.color_id] = c.name
            return acc
        },
        {} as { [key: string]: string },
    ),
)

const supplier_names = ref<{ [key: string]: string }>({})
const article_skus = ref<{ [key: string]: string }>({})
const brand_names = ref<{ [key: string]: string }>({})
const subcategory_names = ref<{ [key: string]: string }>({})
const warehouses = ref([])
const is_fetching_warehouses = ref(false)
const country_names = ref<{ [key: string]: string }>({})
const sales_period_names = ref<{ [key: string]: string }>({})

const fetchHistoryContext = (value: SystemEvent[]) => {
    const new_supplier_ids = value
        .filter((e) => e.type === 'article.update.suppliers')
        .reduce((acc: number[], e) => {
            const new_ids = getUnknownSupplierIds(e.payload.data.supplier_id)
            acc.push(...new_ids)
            return acc
        }, [])
    if (new_supplier_ids.length > 0) {
        fetchSupplierNames(new_supplier_ids)
    }

    const new_brand_ids = value
        .filter((e) => e.type === 'article.update.general_information')
        .reduce((acc: number[], e) => {
            const new_ids = getUnknownBrandIds(e.payload.data.brand_id)
            acc.push(...new_ids)
            return acc
        }, [])
    if (new_brand_ids.length > 0) {
        fetchBrandNames(new_brand_ids)
    }

    const new_subcategory_ids = value
        .filter((e) => e.type === 'article.update.general_information')
        .reduce((acc: number[], e) => {
            const new_ids = getUnknownSubcategoryIds(e.payload.data.subcategory_id)
            acc.push(...new_ids)
            return acc
        }, [])
    if (new_subcategory_ids.length > 0) {
        fetchSubcategoryNames(new_subcategory_ids)
    }

    const article_ids_from_packaged_update = value
        .filter((e) => e.type === 'article.update.packaged_article')
        .reduce((acc: number[], e) => {
            if (!(e.payload._rel.packaged_article in article_skus.value)) {
                acc.push(e.payload._rel.packaged_article)
            }
            return acc
        }, [])

    const article_ids_from_package_update = value
        .filter((e) => e.type === 'article.update.package')
        .reduce((acc: number[], e) => {
            const new_ids = getUnknowPackagedArticleSkus(e.payload.data.packaged_article)
            acc.push(...new_ids)
            return acc
        }, [])
    const article_ids = [...article_ids_from_packaged_update, ...article_ids_from_package_update]
    if (article_ids.length > 0) {
        fetchArticleSkus(article_ids)
    }

    const new_country_ids = value
        .filter((e) => e.type === 'article.update.logistic_information')
        .reduce((acc: number[], e) => {
            const new_ids = getUnknownCountryIds(e.payload.data.source_country_id)
            acc.push(...new_ids)
            return acc
        }, [])
    if (new_country_ids.length > 0) {
        fetchCountryNames(new_country_ids)
    }

    const new_sales_period_ids_from_create = value
        .filter((e) => e.type === 'article.create.article_sale_period')
        .reduce((acc: number[], e) => {
            if (!(e.payload.data.sales_period_id in sales_period_names.value)) {
                acc.push(e.payload.data.sales_period_id)
            }
            return acc
        }, [])

    const new_sales_period_ids_from_update = value
        .filter((e) => e.type === 'article.update.article_sale_period')
        .reduce((acc: number[], e) => {
            if (!(e.payload._rel.sales_period_id in sales_period_names.value)) {
                acc.push(e.payload._rel.sales_period_id)
            }
            const new_ids = getUnknownSalesPeriodId(e.payload.data.sales_period_id)
            acc.push(...new_ids)
            return acc
        }, [])
    const new_sales_period_ids = [...new_sales_period_ids_from_create, ...new_sales_period_ids_from_update]
    if (new_sales_period_ids.length > 0) {
        fetchSales(new_sales_period_ids)
    }
}

interface Change<T> {
    old: T
    new: T
}

interface ChangeList<T> {
    added: T[]
    deleted: T[]
}

const getUnknownSupplierIds = (changes: Change<number>) => {
    if (changes === undefined) {
        return []
    }
    return [changes.old, changes.new].filter((supplier_id) => !(supplier_id in supplier_names.value))
}

const getUnknownBrandIds = (changes: Change<number>) => {
    if (changes === undefined) {
        return []
    }
    return [changes.old, changes.new].filter((brand_id) => !(brand_id in brand_names.value))
}

const getUnknownSubcategoryIds = (changes: Change<number>) => {
    if (changes === undefined) {
        return []
    }
    return [changes.old, changes.new].filter((subcategory_id) => !(subcategory_id in subcategory_names.value))
}

const getUnknowPackagedArticleSkus = (changes: ChangeList<{ article_id: number; packaged_article_id: number }>) => {
    if (changes === undefined) {
        return []
    }
    return [...changes.added, ...changes.deleted]
        .map((p) => p.article_id)
        .filter((article_id) => !(article_id in article_skus.value))
}

const getUnknownCountryIds = (changes: Change<number>) => {
    if (changes === undefined) {
        return []
    }
    return [changes.old, changes.new].filter((country_id) => !(country_id in country_names.value))
}

const getUnknownSalesPeriodId = (changes: Change<number>) => {
    if (changes === undefined) {
        return []
    }
    return [changes.old, changes.new].filter((sales_period_id) => !(sales_period_id in sales_period_names.value))
}

const fields = {
    supplier_id: 'supplier_id',
    name: 'name',
    status: 'status',
    origin_country_id: 'origin_country_id',
}

async function fetchSupplierNames(supplier_ids: number[]) {
    try {
        const response = await cPostSuppliers({
            where: {
                supplier_id: {
                    _in: supplier_ids,
                },
            },
            fields: Object.values(fields),
            limit: 100,
        })
        response.data.suppliers.forEach((s) => {
            set(supplier_names.value, s.supplier_id, s.name)
        })
    } catch (e) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    }
}

async function fetchBrandNames(brand_ids: number[]) {
    try {
        const response = await cPostBrands({
            where: {
                brand_id: {
                    _in: brand_ids,
                },
            },
            limit: 100,
        })
        response.data.brands.forEach((s) => {
            set(brand_names.value, s.brand_id, s.name)
        })
    } catch (e) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    }
}

async function fetchArticleSkus(article_ids: number[]) {
    try {
        const response = await cPostArticles({
            where: { product_id: { _in: article_ids } },
        })
        response.data.articles.forEach((a: any) => {
            set(article_skus.value, a.product_id, a.sku)
        })
    } catch (e) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    }
}

async function fetchSubcategoryNames(subcategory_ids: number[]) {
    try {
        const response = await cPostSubcategories({
            where: {
                subcategory_id: {
                    _in: subcategory_ids,
                },
            },
            limit: 100,
        })
        response.data.subcategories.forEach((s) => {
            set(subcategory_names.value, s.subcategory_id, s.name)
        })
    } catch (e) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    }
}

async function fetchCountryNames(country_ids: number[]) {
    try {
        const response = await cPostCountries({
            where: {
                country_id: {
                    _in: country_ids,
                },
            },
            limit: 100,
        })

        response.data.countries.forEach((c) => {
            set(country_names.value, c.country_id, c.name)
        })
    } catch (e) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    }
}

async function fetchSales(sales_period_ids: number[]) {
    try {
        const response = await cPostSales({
            where: {
                sale_id: {
                    _in: sales_period_ids,
                },
            },
            limit: 100,
        })

        response.data.sales.forEach((sp) => {
            set(sales_period_names.value, sp.sale_id, sp.start_at + ' au ' + sp.end_at)
        })
    } catch (e) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    }
}

const applyMapping = (
    key_from: string,
    key_to: string,
    reference: Ref<{ [key: number]: string }>,
    content: { [key: string]: any },
) => {
    if (typeof content[key_from] === 'object') {
        applyChangeMapping(key_from, key_to, reference, content)
    }
    if (typeof content[key_from] === 'number') {
        applyCreateMapping(key_from, key_to, reference, content)
    }
}

const applyChangeMapping = (
    key_from: string,
    key_to: string,
    reference: Ref<{ [key: number]: string }>,
    content: { [key: string]: any },
) => {
    const changes = content[key_from] as Change<number>
    const new_changes: Partial<Change<string>> = {}
    ;(Object.entries(changes) as Array<[key: keyof typeof changes, needle: number]>).forEach(([key, needle]) => {
        if (reference.value[needle] !== undefined) {
            new_changes[key] = reference.value[needle]
            delete changes[key]
        }
    })
    if (Object.keys(new_changes).length > 0) {
        content[key_to] = new_changes
    }
    if (Object.keys(changes).length === 0) {
        delete content[key_from]
    }
}

const applyCreateMapping = (
    key_from: string,
    key_to: string,
    reference: Ref<{ [key: number]: string }>,
    content: { [key: string]: any },
) => {
    const id = content[key_from] as number

    if (reference.value[id] !== undefined) {
        content[key_to] = reference.value[id]
        delete content[key_from]
    }
}

interface HistoryMapper {
    [key: string]: {
        format: (content: { [key: string]: any }, payload?: any) => void
    }
}
const history_mappers = computed<HistoryMapper>(() => ({
    supplier_id: {
        format: (content) => {
            applyMapping('supplier_id', 'supplier', supplier_names, content)
        },
    },
    brand_id: {
        format: (content) => {
            applyMapping('brand_id', 'brand', brand_names, content)
        },
    },
    subcategory_id: {
        format: (content) => {
            applyMapping('subcategory_id', 'subcategory', subcategory_names, content)
        },
    },
    color_id: {
        format: (content) => {
            fetchColorsOnce()
            applyMapping('color_id', 'color', color_names, content)
        },
    },
    safety_stock_threshold: {
        format: (content, payload) => {
            if (warehouses.value.length === 0 && !is_fetching_warehouses.value) {
                fetchWharehousesList()
            }
            const warehouse_name = warehouse_list.value?.find((w) => w.id === (payload._rel.warehouse as number))?.name
            if (warehouse_name) {
                content.warehouse = {
                    old: '',
                    new: warehouse_name,
                }
            }
        },
    },
    eans: {
        format: (content) => {
            // ean system events payload are stored as object instead of as array. this should be changed server-side
            const change_list = content.eans as ChangeList<string>
            ;(Object.values(change_list) as Array<{ [index: string]: string }>).forEach((action) => {
                Object.entries(action).forEach(([index, ean]) => {
                    action[index] = ean.padStart(13, '0')
                })
            })
        },
    },
    embargo_date: {
        format: (content) => {
            const changes = content.embargo_date
            Object.keys(changes).forEach((key) => {
                if (changes[key] === null) {
                    return
                }
                changes[key] = formatDate(new Date(changes[key]))
            })
        },
    },
    date_range: {
        format: (content) => {
            const changes = content.date_range

            Object.keys(changes).forEach((key) => {
                if (changes[key] === null) {
                    return
                }
                const from = formatDate(changes[key].from)
                const to = formatDate(changes[key].to)

                changes[key] = `du ${from} au ${to}`
            })
        },
    },
    packaged_article: {
        format: (content) => {
            type Entry = { article_id: number; quantity: number } | string
            const changes = content.packaged_article as ChangeList<Entry>
            ;(Object.values(changes) as Entry[][]).forEach((change) => {
                change.forEach((changed, index) => {
                    if (typeof changed === 'object') {
                        const sku = article_skus.value[changed.article_id]
                        if (sku) {
                            change[index] = `${sku} x ${changed.quantity}`
                        }
                    }
                })
            })
        },
    },
    packaged_article_quantity: {
        format: (content, payload) => {
            const sku = article_skus.value[payload._rel.packaged_article]

            content.packaged_article_sku = {
                old: '',
                new: sku ? `${sku}` : payload._rel.packaged_article,
            }
        },
    },
    source_country_id: {
        format: (content) => {
            applyMapping('source_country_id', 'source_country', country_names, content)
        },
    },
    weight_tmp: {
        format: (content) => {
            const changes = content.weight_tmp
            Object.keys(changes).forEach((key) => {
                changes[key] = formatOnOff(changes[key])
            })
        },
    },
    is_packaged: {
        format: (content) => {
            const changes = content.is_packaged
            Object.keys(changes).forEach((key) => {
                changes[key] = formatOnOff(changes[key])
            })
        },
    },
    is_active: {
        format: (content) => {
            const changes = content.is_active
            Object.keys(changes).forEach((key) => {
                changes[key] = formatOnOff(changes[key])
            })
        },
    },
    is_active_havre: {
        format: (content) => {
            const changes = content.is_active_havre
            Object.keys(changes).forEach((key) => {
                changes[key] = formatOnOff(changes[key])
            })
        },
    },
    sales_period_id: {
        format: (content) => {
            applyMapping('sales_period_id', 'sales_period', sales_period_names, content)
        },
    },
    starts_at: {
        format: (content) => {
            applyFormatter(content, 'starts_at', formatDateTime)
        },
    },
    ends_at: {
        format: (content) => {
            applyFormatter(content, 'ends_at', formatDateTime)
        },
    },
    sales_channel_ids: {
        format: (content) => {
            fetchSalesChannelsOnce()
            applyFormatter(content, 'sales_channel_ids', (ids: number[]) => {
                return ids.map((id) => sales_channels.value.find((sc) => sc.sales_channel_id === id)?.label ?? id)
            })
        },
    },
}))

type HistoryMapperKey = keyof typeof history_mappers.value

function applyFormatter<T>(target: { [key: string]: any }, key: string, formatter: (content: T) => any) {
    if (isChange(target[key])) {
        Object.entries(target[key]).forEach(([k, v]) => {
            target[key][k] = formatter(v)
        })
        return
    }
    target[key] = formatter(target[key])
}

function isChange<T>(content: T | Change<T>): content is Change<T> {
    return content !== null && typeof content === 'object' && ('old' in content || 'new' in content)
}

const formatDate = (date: Date | string) =>
    isValidDate(date)
        ? useDateFormatter(date, {
              french_format: 'DD/MM/YYYY',
          }).formatted_date.value.french
        : date

const formatDateTime = (date: Date | string) =>
    isValidDate(date)
        ? useDateFormatter(date, {
              french_format: 'DD/MM/YYYY à HH:mm',
          }).formatted_date.value.french
        : date

const isValidDate = (date: Date | string): boolean => !isNaN(new Date(date).getTime())

const formatOnOff = (value: any) => {
    if (typeof value === 'boolean' || typeof value === 'number') {
        return value ? 'Oui' : 'Non'
    }

    if (typeof value === 'string') {
        return { Y: 'Oui', N: 'Non' }[value] ?? value
    }

    return value
}

async function fetchWharehousesList() {
    try {
        is_fetching_warehouses.value = true

        warehouses.value = await fetchWarehouses()
    } catch (e) {
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    } finally {
        is_fetching_warehouses.value = false
    }
}

const warehouse_list = computed<Array<{ id: number; name: string }>>(() => {
    const warehouseData = warehouses.value.data?.warehouses
    return warehouseData?.map((w) => ({
        id: w.warehouse_id,
        name: w.name,
    }))
})

export {
    // methods
    fetchHistoryContext,

    // computed
    history_mappers,
    type HistoryMapperKey,
}
