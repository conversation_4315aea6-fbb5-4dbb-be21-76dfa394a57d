<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/shared/components'
import { AVAILABILITY } from '@/shared/referential/product/availability'
// @ts-ignore-next-line
import { faGlobe } from '@son-video/font-awesome-pro-duotone/index.es'

interface Props {
    delay: number
}

const props = defineProps<Props>()

const AVAILABILITY_COLOR_MATCHING = {
    gray: 'text-slate-500',
    red: 'text-red-500',
    green: 'text-lime-600',
    yellow: 'text-yellow-500',
    orange: 'text-orange-500',
}

const availability_details = computed(
    () =>
        AVAILABILITY.find((a) => a.value === props.delay) ?? {
            label: 'Indisponible',
            value: 0,
            indicator: 'red',
        },
)

const availability_icon_color = computed(
    () => AVAILABILITY_COLOR_MATCHING[availability_details.value.indicator as keyof typeof AVAILABILITY_COLOR_MATCHING],
)
</script>

<template>
    <badge
        class="relative -ml-px focus:z-10"
        rounded="right"
        :icon="faGlobe"
        :icon-class="[availability_icon_color]"
        data-context="availability"
    >
        {{ availability_details.label }}
    </badge>
</template>
