// editable fields needs to be displayed in form and in timeline

// labels that appear in several categories
export const SHARED_LABELS = {
    type: 'Type',
    comment: 'Commentaire',
    selling_price: 'Prix de vente',
    starts_at: 'Date de début',
    ends_at: 'Date de fin',
}

export const GENERAL_INFORMATION_LABELS = {
    brand: 'Marque',
    model: 'Modèle',
    subcategory: 'Sous-catégorie',
    color: 'Couleur',
    short_description: 'Description courte',
    basket_description: 'Description panier',
    marketplace_description: 'Description marketplace',
    packages: 'Vendu par lot',
    embargo_date: "Date d'embargo",
    comparator: 'Comparateur',
    manufacturer_warranty_years: 'Garantie constructeur',
}

export const STATUS_LABELS = {
    status: 'Statut',
}

export const BUYER_INFORMATION_LABELS = {
    packaged_article_sku: 'Composant',
    packaged_article: 'Composant',
    packaged_article_quantity: 'Quantité du composant',
}

export const SUPPLIER_LABELS = {
    supplier: 'Fournisseur',
    supplier_id: 'ID Fournisseur',
    supplier_reference: 'Référence',
    mininum_order_quantity: 'Quantité minimale de commande',
}

export const SAFETY_STOCK_THRESHOLD_LABELS = {
    safety_stock_threshold: 'Stock de sécurité',
    warehouse: 'Entrepôt',
}

export const PRICES_LABELS = {
    tariff_tax_excluded: 'PA Tarif',
    purchase_tax_excluded: 'PA Net',
    pvgc: 'PVGC',
    initial_selling_price: 'Lancé à',
    ecotax: 'Écotaxe',
    sorecop: 'Sorecop',
    reseller_price: 'Revendeur',
}

const WEIGHTED_COST_ADJUSTMENT_LABELS = {
    chrono: 'Chrono',
    quantity: 'Quantité',
    date_range: 'Dates (fourchette)',
}

export const LOGISTIC_INFORMATION_LABELS = {
    package_unit: 'Colisage',
    is_packaged: 'Conditionnement par lot',
    number_of_packages: 'Nombre de colis',
    weight: 'Poids',
    weight_tmp: 'Poids Temporaire',
    source_country: "Pays d'origine",
    customs_code: 'Code douanier',
    ecotax_code: 'Code écotaxe',
}

export const LOGISTIC_HAVRE_LABELS = {
    sku_havre: 'SKU Havre',
    is_active_havre: 'Stock actif',
    package_unit_havre: 'Colisage',
}

export const COMMENT_LABELS = {
    message: 'Message',
    is_active: 'Actif',
}

export const DESTOCK_CONDITION_LABELS = {
    internal_comment: 'Commentaire interne',
    public_description: 'Commentaire publique',
}

export const SALE_LABELS = {
    sales_period: 'soldes',
    sales_period_id: 'ID soldes',
    order_quantity: 'quantité en commande',
    stock_30d_quantity: 'quantité en stock',
}

export const PLANNED_PRICE_LABELS = {
    start_at: "Date d'entrée en vigueur",
}

export const PROMO_BUDGET_LABELS = {
    start_at: 'Date de début',
    end_at: 'Date de fin',
    amount: 'Montant',
    executed_at: "Date d'activation",
}

export const PLANNED_PRICES_LABELS = {
    sales_channel_ids: 'Canaux de vente',
    exit_selling_price: 'Prix de sortie',
}

export const DELIVERY_TIME_LABELS = {
    delay: 'Délai de livraison',
}

export default {
    ...SHARED_LABELS,
    ...STATUS_LABELS,
    ...SUPPLIER_LABELS,
    ...SAFETY_STOCK_THRESHOLD_LABELS,
    ...PRICES_LABELS,
    ...BUYER_INFORMATION_LABELS,
    ...GENERAL_INFORMATION_LABELS,
    ...WEIGHTED_COST_ADJUSTMENT_LABELS,
    ...LOGISTIC_INFORMATION_LABELS,
    ...LOGISTIC_HAVRE_LABELS,
    ...COMMENT_LABELS,
    ...DESTOCK_CONDITION_LABELS,
    ...SALE_LABELS,
    ...PLANNED_PRICE_LABELS,
    ...PROMO_BUDGET_LABELS,
    ...PLANNED_PRICES_LABELS,
    ...DELIVERY_TIME_LABELS,
}
