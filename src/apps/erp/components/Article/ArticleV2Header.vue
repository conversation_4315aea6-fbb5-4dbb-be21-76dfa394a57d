<script setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useArticlePage } from '@/apps/erp/composable/useArticlePage'
import { ARTICLE_TYPES } from '@/apps/erp/composable/useCmsArticleDataProvider'
import { formatCurrency, formatNumber } from '@/shared/string_utils'
import copy from 'copy-to-clipboard'
import ErpButton from '@/shared/components/buttons/ErpButton.vue'
import DropdownMenu from '@/shared/components/ui/DropdownMenu.vue'
import DropdownMenuItem from '@/shared/components/ui/DropdownMenuItem.vue'
import ErpTooltip from '@/shared/components/ui/ErpTooltip.vue'
import { AVAILABILITY } from '@/shared/referential/product/availability'
import {
    PRODUCT_STOCK_READ,
    TRANSFER_CREATE,
    ARTICLE_STATUS_WRITE,
    ARTICLE_DESTOCK_WRITE,
} from '@/apps/erp/permissions'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { faCheck } from '@son-video/font-awesome-pro-regular/index.es'
import { faPlus, faCircle, faGlobe, faHandHoldingUsd } from '@son-video/font-awesome-pro-solid/index.es'
import {
    faTag,
    faBalanceScaleRight,
    faBringFront,
    faCalendarDay,
    faCoin,
    faShoppingCart,
    faShieldCheck,
    faBoxOpen,
} from '@son-video/font-awesome-pro-light/index.es'
import { faSparkles, faExternalLink } from '@son-video/font-awesome-pro-duotone/index.es'
import CreateTransferForm from '@/apps/erp/components/Transfer/createTransferForm.vue'
import ErpMultiselect from '@/shared/components/form/ErpMultiselect.vue'
import { i18n } from '@/services/config/i18n'
import { useToastStore } from '@/services/plugin/toast/stores'
import ErpLink from '@/shared/components/link/ErpLink.vue'
import ErpAccountPermissionInContext from '@/shared/components/misc/ErpAccountPermissionInContext.vue'
import DateFormatter from '@/shared/components/ui/DateFormatter.vue'
import { Badge } from '@/shared/components'
import ErpSkeleton from '@/shared/components/ui/ErpSkeleton.vue'
import { ArticleStatuses } from '@/shared/api/erp_server/article/types'
import { useAuthenticationStore } from '@/stores/authentication'
import SlideOutContainer from '@/shared/components/ui/SlideOutContainer.vue'
import { useCmsArticleStore } from '@/apps/erp/components/Article/stores/cms_article'
import { storeToRefs } from 'pinia'

const { article, updateArticle, validation_messages, is_updating_article, is_loading_initial } = useArticlePage()
const store = useCmsArticleStore()
const { article_type, cms_article, has_deduced_type } = storeToRefs(store)
const cms_url = computed(() => `${import.meta.env.VITE_APP_BOCMS_BASE_URL}/article/edit/${article.value.sku}`)
const commercial_opx_url = computed(() => `/articles/${article.value.article_id}/commercial-operation`)

const is_opened = ref(false)

const AVAILABILITY_COLOR_MATCHING = {
    gray: 'text-slate-500',
    red: 'text-red-500',
    green: 'text-lime-600',
    yellow: 'text-yellow-500',
    orange: 'text-orange-500',
}

const availability_icon_color = computed(
    () => AVAILABILITY_COLOR_MATCHING[AVAILABILITY.find((a) => a.value === article.value.delay)?.indicator ?? 'red'],
)

const availability_text = computed(
    () => AVAILABILITY.find((a) => a.value === article.value.delay)?.label ?? 'Indisponible',
)

const article_statuses = computed(() =>
    [
        { textClasses: 'text-slate-700', iconClass: 'text-slate-400', value: ArticleStatuses.A_VOIR },
        { textClasses: 'text-blue-800', iconClass: 'text-blue-500', value: ArticleStatuses.TODO },
        { textClasses: 'text-orange-800', iconClass: 'text-orange-500', value: ArticleStatuses.NON },
        { textClasses: 'text-lime-900', iconClass: 'text-lime-600', value: ArticleStatuses.OUI },
        { textClasses: 'text-amber-800', iconClass: 'text-amber-500', value: ArticleStatuses.LAST },
        { textClasses: 'text-purple-800', iconClass: 'text-purple-500', value: ArticleStatuses.TMP },
        { textClasses: 'text-red-900', iconClass: 'text-red-600', value: ArticleStatuses.YAPU },
    ].map((i) => {
        i.icon = faCircle
        i.label = i.value.toUpperCase()

        return i
    }),
)
const article_current_status = computed(() =>
    article_statuses.value.find((s) => s.value.toLowerCase() === article.value.status),
)

const cms_tooltip = computed(() => {
    if (cms_article.value && cms_article.value.unbasketable_reason !== null) {
        return 'Non vendable sur son-video.com'
    }
    if (!has_deduced_type.value) {
        return 'Article non présent dans le CMS'
    }
    return null
})

const more_actions_is_open = ref(false)
const copyToClipboard = (value) => {
    copy(value) // always return false in Cypress
    useToastStore().add({
        content: `"${value}" copié dans le presse-papier`,
        duration: 1500,
        type: 'default',
    })
}

const onSelectStatus = (option) => {
    updateArticle('status', { status: option.value })
}

const has_transfer_create_permission = computed(() =>
    useAuthenticationStore().hasPermission([TRANSFER_CREATE, PRODUCT_STOCK_READ]),
)

const has_status_permission = computed(() => {
    if (article.value.is_destock) {
        return useAuthenticationStore().hasAtLeastOnePermission([ARTICLE_STATUS_WRITE, ARTICLE_DESTOCK_WRITE])
    }
    return useAuthenticationStore().hasPermission(ARTICLE_STATUS_WRITE)
})

const show_more_dropdown_actions = computed(() => {
    return [
        {
            text: 'CAP Caisse',
            icon: faExternalLink,
            iconClass: 'text-ezl',
            href: `${import.meta.env.VITE_EASYLOUNGE_BACKOFFICE_URL}/produits/bysku/${article.value.sku}`,
            show: !article.value.is_package && !article.value.is_destock,
        },
        {
            text: 'Créer Transfert',
            icon: faPlus,
            iconClass: 'text-blue-600',
            disabled: false === has_transfer_create_permission.value,
            tooltip:
                false === has_transfer_create_permission.value
                    ? `Vous n'avez pas la permission de créer un nouveau transfert`
                    : undefined,
            slide_in: true,
            dataContext: 'create-transfer',
            show: !article.value.is_package,
        },
        {
            text: 'Commande en attente',
            icon: faExternalLink,
            route: {
                path: '/legacy/commande/recherche',
                query: { filter: 'sku_attente', value: article.value.sku },
            },
            target: '_blank',
            dataContext: 'backorder',
            show: !article.value.is_package && !article.value.is_destock,
        },
        {
            text: 'Ancienne fiche article',
            icon: faSparkles,
            iconClass: 'text-purple-600',
            route: {
                path: '/legacy/produit/ficheArticle',
                query: { reference: article.value.sku, legacy: true },
            },
            target: '_blank',
            dataContext: 'old-item-sheet',
            show: true,
        },
    ]
})

watch(validation_messages, (value) => {
    if (undefined !== value?.status) {
        const message = validation_messages.value.status
        if (message !== undefined) {
            useToastStore().add({ content: message, duration: 4000 })
            return
        }
        useToastStore().add({ content: i18n.t('error_message:request_error') })
    }
})
</script>

<template>
    <header class="flex flex-col align-items-start bg-white border-b" data-context="article-v2-header">
        <erp-skeleton v-if="is_loading_initial" class="w-full p-3" is-loading />
        <template v-else>
            <div class="pb-3 mb-1.5 sm:pb-0 flex flex-grow flex-wrap items-center">
                <div class="p-3 flex gap-3 items-center flex-grow" data-context="page-title">
                    <h1 class="font-semibold text-xl leading-none m-0" data-context="page-title">
                        {{ article.brand.name }} {{ article.name }}
                    </h1>
                </div>
                <div class="px-3 flex flex-wrap items-center gap-3">
                    <erp-tooltip
                        :message="`en cours | ${article.prices.promo_budget.amount} EUR pris en charge`"
                        v-if="article.prices.promo_budget"
                    >
                        <RouterLink :to="commercial_opx_url">
                            <badge
                                class="relative -ml-px focus:z-10"
                                rounded="right"
                                :icon="faHandHoldingUsd"
                                color="gray"
                                data-context="promo-code-status"
                            >
                                {{ 'Budget Promo' }}
                            </badge>
                        </RouterLink>
                    </erp-tooltip>
                    <badge
                        class="relative -ml-px focus:z-10"
                        rounded="right"
                        :icon="faGlobe"
                        :icon-class="[availability_icon_color]"
                        data-context="availability"
                    >
                        {{ availability_text }}
                    </badge>

                    <div class="isolate inline-flex rounded-md shadow-sm">
                        <erp-tooltip :message="cms_tooltip">
                            <erp-button
                                tertiary
                                class="relative focus:z-10"
                                rounded="left"
                                :icon="faExternalLink"
                                :icon-class="['text-blue-600']"
                                :to="cms_url"
                                target="_blank"
                                :disabled="!has_deduced_type"
                                data-context="cms-link"
                            >
                                CMS
                                <span v-if="article_type === ARTICLE_TYPES.DRAFT" class="text-amber-500">DRAFT</span>
                                <span
                                    v-if="cms_article && cms_article.unbasketable_reason !== null"
                                    class="text-red-500"
                                >
                                    <font-awesome-icon
                                        fixed-width
                                        class="text-orange-600"
                                        :icon="faShoppingCart"
                                        data-context="icon"
                                    />
                                </span>
                            </erp-button>
                        </erp-tooltip>
                        <erp-tooltip :disabled="has_deduced_type" message="Article non publié sur son-video.com">
                            <erp-button
                                tertiary
                                class="relative -ml-px focus:z-10"
                                rounded="right"
                                :icon="faExternalLink"
                                :icon-class="['text-blue-600']"
                                :to="article.article_url ?? undefined"
                                target="_blank"
                                :disabled="article_type !== ARTICLE_TYPES.PUBLISHED"
                                data-context="site-link"
                                >son-video.com</erp-button
                            >
                        </erp-tooltip>
                    </div>

                    <erp-tooltip class="w-24" :denied="!has_status_permission">
                        <erp-multiselect
                            :options="article_statuses"
                            :value="[article_current_status]"
                            :is-loading="is_updating_article"
                            :disabled="!has_status_permission || is_updating_article"
                            :max-height="400"
                            hide-internal-search
                            close-on-select
                            class="inline-flex items-center text-sm font-semibold gap-x-1.5 pr-5"
                            data-context="article-status"
                            @select="onSelectStatus"
                        >
                            <template #single="{ value: status }">
                                <font-awesome-icon
                                    fixed-width
                                    class="-mx-0.5 h-3 w-3"
                                    :class="status.iconClass"
                                    :icon="status.icon"
                                />
                                <span :class="status.textClasses">{{ status.label }}</span>
                            </template>

                            <template #suggestion="{ option }">
                                <div class="inline-flex items-center gap-x-1.5">
                                    <font-awesome-icon
                                        fixed-width
                                        class="-mx-0.5 h-3 w-3"
                                        :class="option._original.iconClass"
                                        :icon="option._original.icon"
                                    />
                                    <span class="whitespace-nowrap" :class="option._original.textClasses">{{
                                        option.label
                                    }}</span>
                                    <font-awesome-icon
                                        v-if="option._is_selected"
                                        fixed-width
                                        class="ml-auto text-xs text-blue-600"
                                        :icon="faCheck"
                                        data-context="checked-icon"
                                    />
                                </div>
                            </template>
                        </erp-multiselect>
                    </erp-tooltip>

                    <erp-account-permission-in-context
                        :for="['PRODUCT_STOCK_READ', 'TRANSFER_CREATE', 'ARTICLE_STATUS_WRITE', 'ARTICLE_QR_CODE_VIEW']"
                        tooltip="Gérer les permissions générales des articles"
                        data-context="permissions-in-header"
                    />

                    <dropdown-menu v-model="more_actions_is_open" tertiary data-context="show-more-trigger">
                        <template v-for="item of show_more_dropdown_actions">
                            <dropdown-menu-item
                                v-if="item.show"
                                v-bind="item"
                                :key="item.text"
                                :data-context="item.dataContext"
                                @click="is_opened = true === item.slide_in"
                            >
                                {{ item.text }}
                            </dropdown-menu-item>
                        </template>
                    </dropdown-menu>
                </div>
            </div>

            <div
                class="gap-y-1.5 gap-x-3 sm:gap-x-4 text-sm text-slate-500 flex flex-wrap flex-grow px-3 pb-3"
                data-context="sub-title"
            >
                <erp-tooltip message="Copier dans le presse papier">
                    <span
                        class="flex items-center gap-0.5 hover:text-svd-800 cursor-pointer"
                        data-context="item"
                        @click="copyToClipboard(article.sku)"
                    >
                        #{{ article.sku }}
                    </span>
                </erp-tooltip>

                <erp-tooltip message="Copier dans le presse papier">
                    <span
                        class="flex items-center gap-0.5 hover:text-svd-800 cursor-pointer"
                        data-context="item"
                        @click="copyToClipboard(article.article_id)"
                    >
                        #{{ article.article_id }}
                    </span>
                </erp-tooltip>

                <span
                    v-if="article.is_package"
                    class="flex items-center gap-1 text-purple-600"
                    data-context="icon-package"
                >
                    <font-awesome-icon :icon="faBringFront" fixed-width />
                    <span class="font-medium bg-purple-50 px-1">Composé</span>
                </span>

                <span
                    v-if="article.is_destock"
                    class="flex items-center gap-1 text-purple-600"
                    data-context="icon-destock"
                >
                    <font-awesome-icon :icon="faBoxOpen" fixed-width />
                    <span class="font-medium bg-purple-50 px-1">Destock</span>
                </span>

                <span class="flex items-center gap-1" data-context="item">
                    <font-awesome-icon :icon="faCalendarDay" fixed-width />
                    Crée le <date-formatter :date="article.created_at" format="DD/MM/YYYY" />
                </span>

                <span v-if="article?.last_log_date" class="flex items-center gap-1" data-context="item">
                    <font-awesome-icon :icon="faCalendarDay" fixed-width />
                    Modifié le <date-formatter :date="article.last_log_date" format="DD/MM/YYYY" />
                </span>

                <span class="flex items-center gap-1" data-context="item">
                    <erp-link :to="`/category/subcategory/${article.subcategory.subcategory_id}/edit`" target="_blank">
                        <font-awesome-icon :icon="faTag" fixed-width />
                        {{ article.subcategory.name }}
                    </erp-link>
                </span>

                <span class="flex items-center gap-1" data-context="item">
                    <erp-link :to="`/legacy/marque/editionMarque?id=${article.brand.brand_id}`" target="_blank">
                        <font-awesome-icon :icon="faTag" fixed-width />
                        {{ article.brand.name }}
                    </erp-link>
                </span>

                <span class="flex items-center gap-1" data-context="item">
                    <font-awesome-icon :icon="faBalanceScaleRight" fixed-width />
                    <span>{{ formatNumber(article.logistic.weight) }} kg</span>
                </span>

                <span class="flex items-center gap-1" data-context="item">
                    <font-awesome-icon :icon="faShoppingCart" fixed-width />
                    <span class="bg-yellow-100 px-1">{{ formatCurrency(article.prices.selling_price) }}</span>
                </span>

                <erp-tooltip :message="formatCurrency(article.prices.margin_tax_excluded) + ' HT'">
                    <span class="flex items-center gap-1" data-context="item">
                        <font-awesome-icon :icon="faCoin" fixed-width />
                        <span class="bg-yellow-100 px-1">{{
                            formatNumber(article.prices.margin_rate * 100) + ' %'
                        }}</span>
                    </span>
                </erp-tooltip>

                <erp-tooltip message="GLD TTC">
                    <span
                        v-if="article.has_warranty_extension_5_years"
                        class="flex items-center gap-1"
                        data-context="item"
                    >
                        <font-awesome-icon :icon="faShieldCheck" fixed-width />
                        <span class="bg-yellow-100 px-1">{{
                            formatCurrency(article.warranty_extension_5_years_price)
                        }}</span>
                    </span>
                </erp-tooltip>

                <slide-out-container :is-open="is_opened" @close="is_opened = false">
                    <create-transfer-form :product_id="article.article_id.toString()" @created="is_opened = false" />
                </slide-out-container>
            </div>
        </template>
    </header>
</template>
