import ErpTimelineCommentItem from '@/shared/components/ui/ErpTimelineCommentItem.vue'
import Vue from 'vue'
import { VTooltip } from 'floating-vue'

Vue.directive('tooltip', VTooltip)

describe(
    'ErpTimelineEventItem',
    {
        viewportWidth: 350,
        viewportHeight: 350,
    },
    function () {
        const BASE_PROPS = {
            author: 'toto',
            when: '2020-01-01',
            content: 'test',
        }

        const check = () => {
            return cy.get('[data-context=erp-timeline-comment-item]').as('component')
        }

        context('Props', () => {
            describe('author', function () {
                it('should show the author', function () {
                    cy.mount(ErpTimelineCommentItem, {
                        propsData: { ...BASE_PROPS },
                    })

                    check().find('[data-context=author]').should('contain', 'toto')
                })
            })
            describe('when', function () {
                it('should show the date when the event occurred', function () {
                    const now = new Date()
                    cy.clock(now)

                    const props = { ...BASE_PROPS }
                    props.when = new Date(now)
                    props.when.setDate(props.when.getDate() - 14) // - 14 days

                    cy.mount(ErpTimelineCommentItem, {
                        propsData: props,
                    })

                    check().find('[data-context=when]').should('contain', 'il y a 14 jours')
                })
            })
            describe('noRelativeTime', function () {
                it('should show the full date when the event occurred', function () {
                    const props = { ...BASE_PROPS }
                    props.when = new Date(2023, 4, 25, 10, 44, 21, 0)
                    props.noRelativeTime = true

                    cy.mount(ErpTimelineCommentItem, {
                        propsData: props,
                    })

                    check().find('[data-context=when]').should('contain', 'le 25/05/2023 à 10:44')
                })
            })
            describe('content', function () {
                it('should show the content', function () {
                    cy.mount(ErpTimelineCommentItem, {
                        propsData: { ...BASE_PROPS },
                    })

                    check().find('[data-context=content]').should('contain', 'test')
                })
            })
            describe('color', function () {
                it('should display the custom color variants', function () {
                    ;[
                        { color: 'base', klass: 'bg-white' },
                        { color: 'red', klass: 'bg-red-50' },
                        { color: 'amber', klass: 'bg-amber-50' },
                        { color: 'blue', klass: 'bg-blue-50' },
                    ].forEach((opt) => {
                        const props = { ...BASE_PROPS }
                        props.color = opt.color

                        cy.mount(ErpTimelineCommentItem, {
                            propsData: props,
                        })

                        check().find('[data-context=wrapper]').should('have.class', opt.klass)
                    })
                })
            })
        })
    },
)
