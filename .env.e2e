VITE_APP_ENV=test

VITE_APP_HAL_PORTAL_BASE_URL=/_hal

VITE_APP_ERP_BASE_URL=http://erp-client.lxc
VITE_APP_ERP_OAUTH_CLIENT_ID=oauth_client_id
VITE_APP_ERP_OAUTH_CLIENT_SECRET=oauth_client_secret

VITE_APP_HAL_API=http://_erp_proxy/api/hal
VITE_APP_LEGACY_BASE_URL=http://legacy.lxc
VITE_APP_ERP_API=http://_erp_proxy/api/erp
VITE_APP_PAYMENT_API=http://_erp_proxy/api/erp-payment

VITE_APP_BOCMS_BASE_URL=http://bo-cms.url

VITE_APP_GRAPHQL_ENDPOINT=https://erp.graphql/v1/graphql

# The same value should also be synchronized in the cypress.env.json file in the variable "cdn_static_images"
VITE_APP_CDN_STATIC_IMAGES=https://image.son-video.com

VITE_EASYLOUNGE_BACKOFFICE_URL=https://stagingcapcaisse.easylounge.com

# Empty DSN, does not send errors
VITE_APP_SENTRY_DSN=''
