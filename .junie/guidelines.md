# ERP Client Development Guidelines

This document provides guidelines and information for developers working on the ERP Client project.

## Build and Configuration

### Prerequisites
- Node.js (version specified in `.nvmrc`)
- npm (comes with Node.js)

### Environment Setup
The project uses different environment configurations for different deployment targets:
- Development: `.env.development`
- E2E Testing: `.env.e2e`
- Preview: `.env.preview`
- Staging: `.env.staging`
- Validation: `.env.validation`
- Production: `.env.production.dist`

### Build Commands
The project uses Vite as the build tool. The following build commands are available:

```bash
# Development server
npm run serve

# Build for different environments
npm run build:preview
npm run build:staging
npm run build:validation
npm run build:production
npm run build:e2e
```

### Dependencies
The project has several dependencies that need to be started before running the application:
- Traefik
- HAL
- erp-server
- erp-elk
- legacy
- phcdb
- sendy

You can start these dependencies using the Makefile commands:
```bash
make required-dependencies
make external-dependencies
```

## Testing

The project uses three types of tests:
1. Unit tests (Vitest)
2. Component tests (Cypress)
3. E2E tests (Cypress)

### Unit Tests

Unit tests are located in the `tests/unit/specs` directory. They use Vitest as the test runner and are organized by module.

To run unit tests:
```bash
# Run all unit tests
npm run test:unit

# Run a specific test file
npm run test:unit tests/unit/specs/path/to/file.spec.js
```

Example of a unit test:
```javascript
import { someFunction } from '@/path/to/module'
import { describe, it, expect } from 'vitest'

describe('Module name', function () {
    describe('someFunction()', () => {
        it('should do something specific', function () {
            expect(someFunction(input)).toEqual(expectedOutput)
        })
    })
})
```

### Component Tests

Component tests are located in the `src` directory with the `.cy.js` extension. They use Cypress for testing Vue components in isolation.

To run component tests:
```bash
# Open Cypress component test runner
npm run component:open

# Run component tests in headless mode
npm run component
```

### E2E Tests

E2E tests are located in the `tests/e2e/specs` directory with the `.e2e.js` extension. They use Cypress to test the application in a browser environment.

To run E2E tests:
```bash
# Build and serve the application, then open Cypress E2E test runner
npm run e2e:open

# Build and serve the application, then run E2E tests in headless mode
npm run e2e

# Run E2E tests in CI mode (with test splitting)
npm run e2e:ci
```

## Code Style and Development Practices

### Code Style

The project uses ESLint and Prettier for code style enforcement. The configuration is defined in `.eslintrc.json` and the Prettier configuration in `package.json`.

Key style guidelines:
- Print width: 120 characters
- Tab width: 4 spaces
- No semicolons
- Single quotes
- Always use parentheses for arrow function parameters

### TypeScript

The project uses TypeScript for type checking. TypeScript configuration is defined in `tsconfig.json`.

Important TypeScript practices:
- Use consistent type imports (`@typescript-eslint/consistent-type-imports` rule is enabled)
- Define interfaces for complex data structures

### Project Structure

The project is organized into several key directories:

- `src/apps`: Contains the main application modules
- `src/assets`: Contains static assets like images
- `src/layout`: Contains layout components
- `src/services`: Contains service modules
- `src/shared`: Contains shared components, utilities, and APIs
- `src/stores`: Contains Pinia stores
- `src/translations`: Contains translation files
- `src/@types`: Contains TypeScript type definitions

### Vue Components

The project uses Vue 2.7 with the Composition API. Components are organized by module in the `src/apps` directory, with shared components in the `src/shared/components` directory.

### State Management

The project uses Pinia for state management. Store definitions are created using `defineStore` from Pinia, and components access store state using `storeToRefs` or `mapState` from Pinia.

### Pre-commit Hooks

The project uses pre-git for pre-commit hooks. The `stop-only` hook prevents committing code with `.only` in tests.

## Additional Resources

- [Vue.js Documentation](https://v2.vuejs.org/)
- [Vite Documentation](https://vitejs.dev/)
- [Cypress Documentation](https://docs.cypress.io/)
- [Vitest Documentation](https://vitest.dev/)
- [Pinia Documentation](https://pinia.vuejs.org/)
