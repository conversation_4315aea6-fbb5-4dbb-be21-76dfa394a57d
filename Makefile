INCLUDE_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))/.phcstack

include $(INCLUDE_DIR)/base.mk
include $(INCLUDE_DIR)/with-deploy.mk

required-dependencies:: ## Démarre les dépendances requises (mais ne les installent pas). Ex: BDD, Auth...
	@bash $(BASE_DIR)/.phcstack/check_dependency.sh Traefik traefik traefik
	@bash $(BASE_DIR)/.phcstack/check_dependency.sh HAL hal-php hal

external-dependencies:: ## Démarre les dépendances externes (mais ne les installent pas). Ex: BDD, Auth...
	@printf " $(KO_COLOR)❯$(NO_COLOR) Starting all external dependencies (You may have to install them yourself manually)\n"
	@phcstack erp-server start
	@phcstack erp-elk start
	@phcstack legacy start
	@phcstack phcdb start
	@phcstack sendy start

.PHONY: e2e-split
e2e-split: ##@ Run the all e2e test split as in the CI (~20mn)
	@$(DOCKER_COMPOSE_BASE) -f $(BASE_DIR)/stack/compose-e2e-split.yml up && $(DOCKER_COMPOSE_BASE) -f $(BASE_DIR)/stack/compose-e2e-split.yml rm -fsv

