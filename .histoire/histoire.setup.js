// This file is executed automatically when rendering stories
// See histoire.config.js
// Further doc https://histoire.dev/examples/tailwind.html#setup-file

// global app scss + custom to override Histoire
import '@/assets/css/main.scss'
import './histoire.scss'

import { defineSetupVue2 } from '@histoire/plugin-vue2'
import Vue from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { VTooltip } from 'floating-vue'

// Font-awesome : only load needed fonts
import * as light_icons from '@/services/config/font_awesome_light'
import * as solid_icons from '@/services/config/font_awesome_solid'
import * as regular_icons from '@/services/config/font_awesome_regular'
import * as duotone_icons from '@/services/config/font_awesome_duotone'
import { library } from '@fortawesome/fontawesome-svg-core'
// mocked router
import VueRouter from 'vue-router'

library.add(light_icons)
library.add(solid_icons)
library.add(regular_icons)
library.add(duotone_icons)

Vue.use(VueRouter)
const router = new VueRouter({
    routes: [{ path: '/', name: 'home', component: { render: () => null } }],
})

export const setupVue2 = defineSetupVue2(({ story, variant }) => {
    // Global directives
    Vue.directive('tooltip', VTooltip)

    // Global components
    Vue.component('font-awesome-icon', FontAwesomeIcon)

    // App options
    return {
        router, // Vue Router
        // provide: {
        //     key: 'meow',
        // },
    }
})
