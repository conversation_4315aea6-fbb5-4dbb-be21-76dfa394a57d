// Some hacky thing to override styles, loaded by histoire.setup

// Docs tab
.histoire-story-docs {
    h1 {
        font-size: 1.75em;
    }

    table {
        thead th {
            &:first-child {
                padding-left: 14px;
            }
            &:last-child {
                padding-right: 14px;
            }
        }
        tbody tr {
            &:nth-child(odd) {
                background: #283631;
            }
            &:nth-child(even) {
                background: #294438;
            }

            td:first-child {
                padding-left: 14px;
            }
            td:last-child {
                padding-right: 14px;
            }
        }
    }
}

// Controls tab
.histoire-story-controls {
    // default color in controls
    .histoire-wrapper {
        color: rgb(52, 211, 153);

        input {
            color: #1d6c06;
        }
    }

    .htw-hidden {
        display: none;
    }
}

// Popover in events
.v-popper__wrapper pre {
    color: white;
}
