import { mount } from 'cypress/vue2'
import { pinia } from '@/stores'

Cypress.Commands.add('mount', (component, options = {}) => {
    // Setup options object
    options.extensions = options.extensions || {}
    options.extensions.plugins = options.extensions.plugins || []
    options.pinia = pinia

    return mount(component, options).then(({ wrapper }) => {
        return cy.wrap(wrapper).as('vue')
    })
})

Cypress.Commands.add('authenticate', (tokens) => {
    // transform string to object
    if (typeof tokens === 'string') {
        tokens = { access: tokens }
    }

    // set defaults
    const { access, refresh } = Object.assign(
        {
            access: 'ACCESS_TOKEN',
            refresh: 'REFRESH_TOKEN',
        },
        tokens,
    )

    cy.window().its('localStorage').invoke('setItem', 'session-access-token', access)
    cy.window().its('localStorage').invoke('setItem', 'session-refresh-token', refresh)

    cy.intercept('GET', '**/ping', { statusCode: 204 })
    cy.intercept('GET', '**/tasks', { body: { statusCode: 200, success: true, quantity: '0' } })

    // Turn off uncaught exception handling unhandled promise rejections (those used to fail silently in older versions)
    // https://docs.cypress.io/api/events/catalog-of-events.html#To-catch-a-single-uncaught-exception
    cy.on('uncaught:exception', (err, runnable, promise) => {
        // Error thrown by apps while redirecting to the error page
        if (err.message.includes('Cannot read properties of undefined')) {
            // returning false here prevents Cypress from
            // failing the test
            return false
        }
        if (err.message.includes("Cannot read property 'message' of undefined")) {
            // returning false here prevents Cypress from
            // failing the test
            return false
        }
        // when the exception originated from an unhandled promise
        // rejection, the promise is provided as a third argument
        // you can turn off failing the test in this case
        if (promise) {
            // returning false here prevents Cypress from
            // failing the test
            return false
        }
    })
})

/**
 * Check that the chained element displays a tooltip on mouseover
 *
 * set message to null to check that no tooltip is displayed at all
 *
 * @param {null|string} message
 *
 * @example cy.get('.target').tooltip('tooltip message')
 * @example cy.get('.target').tooltip(null)
 */
Cypress.Commands.add('tooltip', { prevSubject: 'true' }, (subject, message, use_clock = false) => {
    cy.wrap(subject)
        .trigger('mouseenter')
        .then(($element) => {
            if (use_clock) {
                cy.tick(500)
            }

            if (null === message) {
                cy.document().find('.v-popper__inner:visible').should('not.exist')
                return
            }
            cy.document().find('.v-popper__inner').should('exist')
            cy.document().find('.v-popper__inner').should('be.visible').contains(message)

            cy.wrap($element).trigger('mouseleave')
            if (use_clock) {
                cy.tick(500)
            }

            cy.document().find('.v-popper__inner').should('not.be.visible')
        })

    // allow chaining https://docs.cypress.io/guides/references/error-messages#Cypress-detected-that-you-invoked-one-or-more-cy-commands-in-a-custom-command-but-returned-a-different-value
    return cy.wrap(subject)
})
