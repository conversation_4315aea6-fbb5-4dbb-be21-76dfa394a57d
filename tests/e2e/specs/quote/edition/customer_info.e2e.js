import { visitPageWithFixture } from './helpers'
import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Quote - Customer info', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })
    describe('Billing address', () => {
        describe('Without permission', () => {
            it('Display create a billing address button without permission', () => {
                visitPageWithFixture('erp/quotes/quotation_active.json')

                cy.get('[data-context=customer-info]')
                    .find('[data-context=open-address-editor]')
                    .should('contain', 'Sélectionner ou créer une adresse')
                    .should('be.disabled')
            })

            it('Display an edit a billing address button without permission', () => {
                visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

                cy.get('[data-context=customer-info]').find('[data-context=open-address-editor]').should('be.disabled')
            })
        })

        describe('With permission', () => {
            beforeEach(() => {
                cy.mockErpUser([QUOTE_WRITE])
            })

            it('Display locked quote', () => {
                visitPageWithFixture('erp/quotes/quotation_locked.json')

                cy.get('[data-context=customer-info]').find('[data-context=open-address-editor]').should('be.disabled')
            })

            it('can create a billing address', () => {
                visitPageWithFixture('erp/quotes/cpost_quotes.json')

                cy.get('[data-context=customer-info]').find('[data-context=placeholder-icon]').should('be.visible')

                cy.get('[data-context=customer-info]').find('[data-context=details]').should('not.exist')

                cy.get('[data-context=customer-info]')
                    .find('[data-context=open-address-editor]')
                    .should('contain', 'Sélectionner ou créer une adresse')
                    .click()

                cy.get('[data-context=slide-out-container]')
                    .should('be.visible')
                    .should('contain', 'Adresse de facturation')
            })

            it('can display and edit a billing address', () => {
                visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

                cy.get('[data-context=customer-info]').find('[data-context=placeholder-icon]').should('not.exist')

                cy.get('[data-context=customer-info]')
                    .find('[data-context=details]')
                    .should('be.visible')
                    .should('contain', 'Mme arthur LOLO')
                    .should('contain', '38 rue de la ville en bois')
                    .should('contain', '44000 Naoned')
                    .should('contain', 'GUYANE FRANÇAISE')
                    .should('contain', '0678787878')

                cy.get('[data-context=customer-info]').find('[data-context=open-address-editor]').click()

                cy.get('[data-context=slide-out-container]')
                    .should('be.visible')
                    .should('contain', 'Adresse de facturation')
            })

            describe('Prefill form with last quote billing address', () => {
                it('should prefill the billing address form when empty', () => {
                    cy.intercept('POST', '**/api/erp/v1/quotes', async (req) => {
                        if ('1' === req.body.where._and[0]?.quote_id?._eq) {
                            req.reply({ fixture: 'erp/quotes/cpost_quotes.json' })
                            req.alias = 'cpost_quotes'
                        } else {
                            req.reply({ fixture: 'erp/quotes/cpost_quotes_prefill_shipping_address.json' })
                            req.alias = 'cpost_last_billing_address'
                        }
                    })
                    cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                        body: {
                            data: {
                                system_events: [],
                            },
                        },
                    }).as('events')

                    cy.visit('/quote/1')
                    cy.wait(['@cpost_quotes', '@events'])
                    cy.toggleMenu()

                    // previous quote must be called
                    cy.wait('@cpost_last_billing_address').then((xhr) => {
                        expect(xhr.request.body).to.deep.eq({
                            where: {
                                _and: [
                                    { customer_id: { _eq: 1085749 } },
                                    { billing_address: { _null: false } },
                                    { quote_id: { _neq: 1 } },
                                ],
                            },
                            order_by: 'created_at desc',
                            limit: 1,
                        })
                    })

                    cy.get('[data-context=customer-info] [data-context=open-address-editor]').click()

                    cy.get('[data-context=slide-out-container]').as('form').should('be.visible')

                    cy.get('@form').find('[data-context="civility"]').should('contain', 'M.')

                    cy.get('@form').find('[data-context="company"] input').should('have.value', 'PHC Holding')

                    cy.get('@form').find('[data-context="firstname"] input').should('have.value', 'Gérard')

                    cy.get('@form').find('[data-context="lastname"] input').should('have.value', 'MANVUSSA')

                    cy.get('@form')
                        .find('[data-context="address"] input')
                        .should('have.value', '38 rue de la ville en bois')

                    cy.get('@form').find('[data-context="postal-code"] input').should('have.value', '44100')

                    cy.get('@form').find('[data-context="city"] input').should('have.value', 'NANTES')

                    cy.get('@form')
                        .find('[data-context=country] [data-context=country-autocomplete]')
                        .eq(0)
                        .should('contain', 'FRANCE')

                    cy.get('@form').find('[data-context=cellphone] input').should('have.value', '0600000000')

                    cy.get('@form').find('[data-context=phone] input').should('have.value', '0102030405')
                })
            })
        })
    })
})
