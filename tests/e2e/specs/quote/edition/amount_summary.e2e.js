import { visitPageWithFixture } from './helpers'

describe('Quote - address form', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    it('Display a quote total amounts summary', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        // check initial values without shipping address
        cy.get('[data-context=totals]')
            .as('amounts_summary')
            .find('[data-context=discount-label]')
            .should('contain', 'Remise cumulée')
        cy.get('@amounts_summary').find('[data-context=discount-tax-excluded]').should('contain', '31,27 € HT')
        cy.get('@amounts_summary').find('[data-context=discount-tax-included]').should('contain', '37,52 € TTC')
        cy.get('@amounts_summary').find('[data-context=vat-label]').should('contain', 'TVA 20%')
        cy.get('@amounts_summary').find('[data-context=vat]').should('contain', '79,40 €')
        cy.get('@amounts_summary').find('[data-context=quote-amount-label]').should('contain', 'Total devis')
        cy.get('@amounts_summary').find('[data-context=quote-amount-tax-excluded]').should('contain', '396,98 € HT')
        cy.get('@amounts_summary').find('[data-context=quote-amount-tax-included]').should('contain', '476,38 € TTC')
    })
})
