import { visitPageWithFixture } from './helpers'
import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Quote - Send email', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Without permission', () => {
        it('Cannot send an email without permission', () => {
            visitPageWithFixture('erp/quotes/draft_fully_complete.json')

            cy.get('[data-context=send-email-btn]').should('contain', 'Envoyer').should('be.disabled')
        })
    })

    describe('With all permissions', () => {
        beforeEach(() => {
            cy.mockErpUser([QUOTE_WRITE])
        })

        describe('draft', () => {
            it('Cannot send an email for offer and quotation if no products selected', () => {
                visitPageWithFixture('erp/quotes/draft_with_lines_empty.json')

                // check values
                cy.get('[data-context=table-row-product]').should('not.exist')

                // button should be active but some error is displayed instead of confirmation
                cy.get('[data-context=send-email-btn]').should('contain', 'Envoyer').should('not.be.disabled').click()
                cy.get('[data-context=slide-out-container]').should('be.visible').as('content')
                cy.get('@content').find('[data-context=confirm-message]').should('not.exist')
                cy.get('@content').find('[data-context=confirm-submit-btn]').should('not.exist')
                cy.get('@content').find('[data-context=remaining-step]').should('be.visible').should('have.length', 1)

                cy.get('@content')
                    .find('[data-context=confirm-block-cancel-btn]')
                    .should('be.visible')
                    .should('contain', 'Annuler')
                    .click()

                cy.get('[data-context=slide-out-container]').should('not.exist')
            })

            it('Can send offer but not quotation', () => {
                visitPageWithFixture('erp/quotes/draft_without_billing_address.json')

                cy.get('[data-context=send-email-btn]').should('contain', 'Envoyer').should('not.be.disabled').click()

                cy.get('[data-context=slide-out-container]').should('be.visible').as('content')

                // header content
                cy.get('@content').find('[data-context=page-header]').should('contain', 'Envoyer devis/offre client')

                // offer
                cy.get('@content')
                    .find('[data-context=offer_label]')
                    .should('contain', 'Offre')
                    .should('be.visible')
                    .as('offer')

                cy.get('@content').find('[data-context=missing-right-alert]').should('not.exist')

                cy.get('@content').find('[data-context=offer_description]').should('be.visible').as('offer_description')

                cy.get('@offer_description').find('[data-context=remaining-step]').should('not.exist')

                // quotation
                cy.get('@content')
                    .find('[data-context=quotation_label]')
                    .should('contain', 'Devis')
                    .should('be.visible')
                    .as('quotation')

                cy.get('@content')
                    .find('[data-context=quotation_description]')
                    .should('be.visible')
                    .as('quotation_description')

                cy.get('@quotation_description')
                    .find('[data-context=remaining-step]')
                    .should('be.visible')
                    .should('have.length', 1)

                cy.get('@quotation').click()

                cy.get('@content')
                    .find('[data-context=confirm-message]')
                    .should('contain', `Destinataire de l'email <EMAIL>`)

                cy.get('@content')
                    .find('[data-context=confirm-submit-btn]')
                    .should('contain', 'Envoyer le devis')
                    .should('be.disabled')
                    .should('be.visible')

                cy.get('@content')
                    .find('[data-context=confirm-cancel-btn]')
                    .should('be.visible')
                    .should('be.not.disabled')
                    .should('contain', 'Annuler')

                cy.get('@offer').click()

                cy.get('@content')
                    .find('[data-context=confirm-message]')
                    .should('contain', `Destinataire de l'email <EMAIL>`)

                cy.get('@content')
                    .find('[data-context=confirm-submit-btn]')
                    .should('contain', `Envoyer l'offre`)
                    .should('be.not.disabled')
                    .should('be.visible')

                cy.get('@content')
                    .find('[data-context=confirm-cancel-btn]')
                    .should('be.visible')
                    .should('be.not.disabled')
                    .should('contain', 'Annuler')
                    .click()

                cy.get('[data-context=slide-out-container]').should('not.exist')
            })

            it('Can send offer and quotation. Send quotation', () => {
                visitPageWithFixture('erp/quotes/draft_fully_complete.json')

                cy.get('[data-context=send-email-btn]').should('not.be.disabled').click()

                cy.get('[data-context=slide-out-container]').should('be.visible').as('content')

                // offer
                cy.get('@content').find('[data-context=offer_label]').should('be.visible')

                cy.get('@content')
                    .find('[data-context=offer_description] [data-context=remaining-step]')
                    .should('not.exist')

                // quotation
                cy.get('@content').find('[data-context=quotation_label]').should('be.visible').click()

                cy.get('@content')
                    .find('[data-context=quotation_description] [data-context=remaining-step]')
                    .should('not.exist')

                cy.get('@content')
                    .find('[data-context=confirm-message]')
                    .should('contain', `Destinataire de l'email <EMAIL>`)

                cy.get('@content')
                    .find('[data-context=confirm-cancel-btn]')
                    .should('be.visible')
                    .should('be.not.disabled')
                    .should('contain', 'Annuler')

                cy.intercept('POST', '**/api/erp/v1/quote/1/send-email', {
                    statusCode: 200,
                    body: {},
                }).as('send_success')

                cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: { updated: 1 },
                    },
                }).as('update_quote')

                cy.get('@content')
                    .find('[data-context=confirm-submit-btn]')
                    .should('contain', 'Envoyer le devis')
                    .should('be.not.disabled')
                    .should('be.visible')
                    .click()

                cy.wait(['@update_quote', '@send_success', '@cpost_quotes'])

                cy.get('[data-context=slide-out-container]').should('not.exist')

                cy.toast(`Email envoyé au client`, 'success')
            })
        })

        describe('offer / quotation', () => {
            it('can send back an email on a locked quotation', () => {
                visitPageWithFixture('erp/quotes/quotation_with_shipment_locked.json')

                cy.get('[data-context=send-email-btn]').should('contain', 'Envoyer').should('not.be.disabled').click()

                // confirm
                cy.get('[data-context=quote-send-email-confirm]')
                    .should('exist')
                    .as('content')
                    .find('[data-context=page-header]')
                    .should('contain', 'Envoyer devis/offre client')

                cy.get('@content')
                    .find('[data-context=confirm-message]')
                    .should('contain', `Destinataire de l'email <EMAIL>`)

                // submit error
                cy.intercept('POST', '**/api/erp/v1/quote/1/send-email', {
                    statusCode: 500,
                    body: {},
                }).as('send_error')

                cy.get('@content')
                    .find('[data-context=confirm-submit-btn]')
                    .should('contain', 'Renvoyer le devis')
                    .click()

                cy.wait('@send_error')

                cy.toast(`Une erreur est survenue`, 'danger')

                // submit success
                cy.intercept('POST', '**/api/erp/v1/quote/1/send-email', {
                    statusCode: 200,
                    body: {},
                }).as('send_success')

                cy.get('@content').find('[data-context=confirm-submit-btn]').click()

                cy.wait(['@send_success', '@cpost_quotes'])

                // closes panel and displays success message
                cy.get('[data-context=quote-send-email-confirm]').should('not.exist')

                cy.toast(`Email envoyé au client`, 'success')
            })

            it('updates the quote message if typed and not saved before sending email', () => {
                cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: { updated: 1 },
                    },
                }).as('update_quote')

                visitPageWithFixture('erp/quotes/quotation_complete_unlocked.json')

                // new comment message
                cy.get('[data-context=quote-comment]').clear().type('hello')

                // check button
                cy.get('[data-context=send-email-btn]').should('contain', 'Envoyer').click()

                // update quote
                cy.wait('@update_quote').then((xhr) => {
                    expect(xhr.request.body.data.message).to.eq('hello')
                })

                // confirm panel opens
                cy.get('[data-context=quote-send-email-confirm]').should('exist')
            })

            it('Cannot send back an email if quote is expired', () => {
                visitPageWithFixture('erp/quotes/quotation_expired.json')

                cy.get('[data-context=send-email-btn]').should('contain', 'Envoyer').should('not.be.disabled').click()
                cy.get('[data-context=slide-out-container]').should('be.visible').as('content')
                cy.get('@content').find('[data-context=confirm-message]').should('not.exist')
                cy.get('@content').find('[data-context=confirm-submit-btn]').should('not.exist')
                cy.get('@content').find('[data-context=remaining-step]').should('be.visible').should('have.length', 1)
            })

            it('block quotation and show message for offer when cotation shipment', () => {
                visitPageWithFixture('erp/quotes/draft_with_shipment_cotation.json')

                cy.get('[data-context=send-email-btn]').should('contain', 'Envoyer').should('not.be.disabled').click()

                // confirm
                cy.get('[data-context=quote-send-email-confirm]')
                    .should('exist')
                    .as('content')
                    .find('[data-context=page-header]')
                    .should('contain', 'Envoyer devis/offre client')

                cy.get('@content')
                    .find('[data-context=confirm-message]')
                    .should('contain', `Destinataire de l'email <EMAIL>`)

                cy.get('@content').find('[data-context=confirm-submit-btn]').as('submit-btn')

                cy.get('@submit-btn').should('not.be.disabled')

                cy.get('@content').find('fieldset label').as('labels')
                cy.get('@labels').should('have.length', 2)

                // Offer label
                cy.get('@labels').first().as('offer-label')
                cy.get('@offer-label').should(
                    'contain',
                    "Le client ne pourra pas finaliser l'achat avec cette adresse.",
                )

                // Quotation label
                cy.get('@labels').eq(1).as('quotation-label')
                cy.get('@quotation-label').should('contain', 'Le mode de transport est cotation')
                cy.get('@quotation-label').click()
                cy.get('@submit-btn').should('be.disabled')

                cy.get('@offer-label').click()
                cy.get('@submit-btn').should('not.be.disabled')

                // submit success
                cy.intercept('POST', '**/api/erp/v1/quote/1/send-email', {
                    statusCode: 200,
                    body: {},
                }).as('send_success')

                cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: { updated: 1 },
                    },
                }).as('update_quote')

                cy.get('@submit-btn').click()

                cy.wait(['@update_quote', '@send_success', '@cpost_quotes'])

                // closes panel and displays success message
                cy.get('[data-context=quote-send-email-confirm]').should('not.exist')

                cy.toast(`Email envoyé au client`, 'success')
            })
        })
    })
})
