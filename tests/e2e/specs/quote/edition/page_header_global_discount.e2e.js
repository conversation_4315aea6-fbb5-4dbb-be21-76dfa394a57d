import { visitPageWithFixture } from './helpers'
import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions'

describe('Quote - Page header', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe(`Quote actions`, () => {
        describe('Without permission', () => {
            it('Does not show the actions if the user has no write permission', () => {
                visitPageWithFixture('erp/quotes/quotation_ordered.json')

                cy.get('[data-context=quote-actions]').should('not.exist')
            })
        })

        describe('With permission', () => {
            beforeEach(() => {
                cy.mockErpUser([QUOTE_WRITE])
            })

            describe('Apply discount globally', () => {
                it('should show the global discount calculator slide over', () => {
                    visitPageWithFixture('erp/quotes/quotation_inactive.json')

                    cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                        statusCode: 200,
                        body: {
                            data: {
                                updated: 0,
                                quote_lines: [],
                            },
                        },
                    }).as('put_discount_globally')

                    cy.get('[data-context=global-discount-calculator]').should('not.exist')

                    cy.get('[data-context=quote-actions]').should('be.visible').click()

                    cy.get('[data-context=open-global-discount-calculator-btn]')
                        .should('be.visible')
                        .should('contain', 'Appliquer une remise globale')
                        .click()

                    cy.wait('@put_discount_globally')

                    // open a slide over
                    cy.get('[data-context=global-discount-calculator]').should('be.visible')

                    // show list of products of the quote

                    // Check table headers
                    const table_headers = [
                        'Article',
                        'Qté',
                        'Prix Un. TTC',
                        'Remise Un. TTC',
                        'Remise Totale',
                        'Prix TTC',
                    ]

                    cy.get('[data-context=global-discount-table] th[scope=col]').should(
                        'have.length',
                        table_headers.length,
                    )

                    table_headers.forEach((text, idx) => {
                        cy.get('[data-context=global-discount-table] th[scope=col]').eq(idx).should('contain', text)
                    })

                    cy.get('[data-context=global-discount-table] [data-context=table-no-result]').should(
                        'contain',
                        'Aucun résultat',
                    )

                    cy.get('[data-context=global-total-with-discount]').should('contain', '0 €')

                    // show a form where the user can specify an amount to apply globally
                    cy.get('[data-context=global-discount-amount]').should('be.visible').should('have.value', 0)

                    cy.get('[data-context=global-discount-percent]').should('be.visible').should('have.value', 0)

                    // show a disabled button to validate the discount
                    cy.get('[data-context=global-discount-apply-btn]').should('be.visible').should('be.disabled')

                    cy.get('[data-context=global-discount-cancel-btn]')
                        .should('be.visible')
                        .should('not.be.disabled')
                        .click()

                    cy.get('[data-context=global-discount-calculator]').should('not.exist')
                })

                // show error messages
                // enable button to validate the discount if the discount is valid
                // reload page once the discount has been applied

                describe('Error messages', () => {
                    beforeEach(() => {
                        cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                            fixture: 'erp/quotes/put_quote_allocate_discount_globally.json',
                        }).as('put_discount_globally')

                        visitPageWithFixture('erp/quotes/quotation_complete_for_discount.json')

                        cy.get('[data-context=global-discount-calculator]').should('not.exist')

                        cy.get('[data-context=quote-actions]').should('be.visible').click()

                        cy.get('[data-context=open-global-discount-calculator-btn]')
                            .should('be.visible')
                            .should('contain', 'Appliquer une remise globale')
                            .click()

                        // open a slide over
                        cy.get('[data-context=global-discount-calculator]').should('be.visible')
                    })

                    it('should show input validation error messages', () => {
                        cy.get('[data-context=alert-danger]').should('not.exist')

                        cy.get('[data-context=global-discount-amount]')
                            .should('have.value', 0)
                            .clear()
                            .type('abc')
                            .should('have.class', 'ring-red-500')

                        cy.get('[data-context=alert-danger]')
                            .should('be.visible')
                            .should('contain', 'Le montant et/ou le pourcentage doivent être des valeurs numériques')

                        cy.get('[data-context=global-discount-amount]')
                            .clear()
                            .type('0')
                            .should('not.have.class', 'ring-red-500')

                        cy.get('[data-context=alert-danger]').should('not.exist')

                        cy.get('[data-context=global-discount-percent]')
                            .should('have.value', 0)
                            .clear()
                            .type('abc')
                            .should('have.class', 'ring-red-500')

                        cy.get('[data-context=alert-danger]')
                            .should('be.visible')
                            .should('contain', 'Le montant et/ou le pourcentage doivent être des valeurs numériques')

                        cy.get('[data-context=global-discount-percent]')
                            .clear()
                            .type('0')
                            .should('not.have.class', 'ring-red-500')

                        cy.get('[data-context=global-discount-apply-btn]').should('be.visible').should('be.disabled')
                    })

                    it('should show an error messages when discount amount is negative', () => {
                        cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                            statusCode: 400,
                            body: {
                                status: 'error',
                                message: 'Unit discount amount must be negative, \u00221\u0022 given',
                                code: 1013,
                                data: [],
                            },
                        }).as('put_discount_globally')

                        cy.get('[data-context=global-discount-amount]').should('have.value', 0).type('-150')

                        cy.wait('@put_discount_globally')

                        cy.get('[data-context=alert-danger]').should(
                            'contain',
                            'Le montant de la remise doit être positif',
                        )

                        cy.get('[data-context=global-discount-apply-btn]').should('be.visible').should('be.disabled')
                    })

                    it('should show an error messages when the selling price of a product is negative', () => {
                        cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                            statusCode: 400,
                            body: {
                                status: 'error',
                                message: 'Selling price must be positive, \u0022-2323.3583333333\u0022 given',
                                code: 1014,
                                data: { sku: 'SVSSB3000BC', selling_price_with_discount: -2323.358333333333 },
                            },
                        }).as('put_discount_globally')

                        cy.get('[data-context=global-discount-amount]').should('have.value', 0).type('-150')

                        cy.wait('@put_discount_globally')

                        cy.get('[data-context=alert-danger]').should(
                            'contain',
                            'SVSSB3000BC: Le prix de vente doit être positif (-2323.358333333333)',
                        )

                        cy.get('[data-context=global-discount-apply-btn]').should('be.visible').should('be.disabled')
                    })

                    it('should show an error messages when the margin rate is too weak', () => {
                        // No need to check for both 0.05 and 0 since the server provides the data and we're
                        // basically testing the mocked response
                        cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                            statusCode: 400,
                            body: {
                                status: 'error',
                                message: 'Margin rate (-0.26115339083376) can\u0027t be less than \u00220\u0022',
                                code: 1015,
                                data: {
                                    sku: 'SVSSB3000BC',
                                    margin_rate_with_discount: -0.2611533908337561,
                                    min_margin_rate: 0,
                                },
                            },
                        }).as('put_discount_globally')

                        cy.get('[data-context=global-discount-amount]').should('have.value', 0).type('150')

                        cy.wait('@put_discount_globally')

                        cy.get('[data-context=alert-danger]').should(
                            'contain',
                            'SVSSB3000BC: Remise impossible, le taux de marque est trop faible (-0.2611533908337561 < 0)',
                        )
                    })

                    it('should show an error messages when the quote is locked', () => {
                        cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                            statusCode: 400,
                            body: { status: 'error', message: 'Quote is locked', code: 1016, data: [] },
                        }).as('put_discount_globally')

                        visitPageWithFixture('erp/quotes/quotation_locked.json')

                        cy.get('[data-context=global-discount-calculator]').should('not.exist')

                        cy.get('[data-context=quote-actions]').should('be.visible').click()

                        cy.get('[data-context=open-global-discount-calculator-btn]')
                            .should('be.visible')
                            .should('contain', 'Appliquer une remise globale')
                            .click()

                        cy.get('[data-context=alert-danger]').should('contain', 'Le devis/offre est verrouillé')

                        cy.get('[data-context=global-discount-apply-btn]').should('be.visible').should('be.disabled')
                    })
                })

                describe('Product without margin', () => {
                    // we only test the UI behaviour as the discount allocation is handled & tested server-side
                    // and it does not make much sense to test an entirely mocked response
                    it('should not show discount computed column if the product has a margin <= 0', () => {
                        cy.fixture('erp/quotes/put_quote_allocate_discount_globally.json').then((payload) => {
                            payload.data.quote_lines[0].margin = 0

                            cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                                statusCode: 200,
                                body: payload,
                            }).as('put_discount_globally')
                        })

                        visitPageWithFixture('erp/quotes/quotation_complete_for_discount.json')

                        cy.get('[data-context=global-discount-calculator]').should('not.exist')

                        cy.get('[data-context=quote-actions]').should('be.visible').click()

                        cy.get('[data-context=open-global-discount-calculator-btn]')
                            .should('be.visible')
                            .should('contain', 'Appliquer une remise globale')
                            .click()

                        // open a slide over
                        cy.get('[data-context=global-discount-calculator]').should('be.visible')

                        cy.get(
                            '[data-context=global-discount-calculator] [data-context=erp-table] [data-context=cell-unit-discount-tax-included]',
                        )
                            .eq(0)
                            .find('[data-context=not-allowed]')
                            .should('be.visible')

                        cy.get(
                            '[data-context=global-discount-calculator] [data-context=erp-table] [data-context=cell-discount-total]',
                        )
                            .eq(0)
                            .find('[data-context=not-allowed]')
                            .should('be.visible')
                    })
                })

                describe('Apply discount successfully', () => {
                    it('should dispatch discount on all products', () => {
                        cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                            fixture: 'erp/quotes/put_quote_allocate_discount_globally.json',
                        }).as('put_discount_globally')

                        visitPageWithFixture('erp/quotes/quotation_complete_for_discount.json')

                        cy.get('[data-context=global-discount-calculator]').should('not.exist')

                        cy.get('[data-context=quote-actions]').should('be.visible').click()

                        cy.get('[data-context=open-global-discount-calculator-btn]')
                            .should('be.visible')
                            .should('contain', 'Appliquer une remise globale')
                            .click()

                        cy.wait('@put_discount_globally').then((xhr) => {
                            expect(xhr.request.body).to.deep.eq({
                                amount: 0,
                                mode: 'DRY_RUN',
                            })
                        })

                        // open a slide over
                        cy.get('[data-context=global-discount-calculator]').should('be.visible')

                        // button disabled until the discount is valid
                        cy.get('[data-context=global-discount-apply-btn]').should('be.visible').should('be.disabled')

                        // verify info prior to any updates
                        cy.get('[data-context=global-total-with-discount]').should('contain', '2 974,00 €')
                        cy.get('[data-context=global-total-without-discount]').should('contain', '2 974,00 €')

                        cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                            fixture: 'erp/quotes/put_quote_allocate_discount_globally_with_discount.json',
                        }).as('put_discount_globally')

                        // apply a valid discount value
                        cy.get('[data-context=global-discount-amount]').should('have.value', 0).clear().type('400')

                        cy.wait('@put_discount_globally').then((xhr) => {
                            expect(xhr.request.body).to.deep.eq({
                                amount: -400,
                                mode: 'DRY_RUN',
                            })
                        })

                        // verify info on the recap table
                        cy.get('[data-context=global-discount-percent]').should('have.value', '13.4')

                        cy.get(
                            '[data-context=global-discount-calculator] [data-context=erp-table] [data-context=cell-unit-discount-tax-included]',
                        )
                            .eq(0)
                            .should('contain', '142,11 €')

                        cy.get(
                            '[data-context=global-discount-calculator] [data-context=erp-table] [data-context=cell-discount-total]',
                        )
                            .eq(0)
                            .should('contain', '142,11 €')

                        cy.get(
                            '[data-context=global-discount-calculator] [data-context=erp-table] [data-context=cell-selling-price-tax-included]',
                        )
                            .eq(0)
                            .should('contain', '1 456,89 €')

                        cy.get(
                            '[data-context=global-discount-calculator] [data-context=erp-table] [data-context=cell-unit-discount-tax-included]',
                        )
                            .eq(1)
                            .should('contain', '51,58 €')

                        cy.get(
                            '[data-context=global-discount-calculator] [data-context=erp-table] [data-context=cell-discount-total]',
                        )
                            .eq(1)
                            .should('contain', '257,90 €')

                        cy.get(
                            '[data-context=global-discount-calculator] [data-context=erp-table] [data-context=cell-selling-price-tax-included]',
                        )
                            .eq(1)
                            .should('contain', '1 117,10 €')

                        // verify totals columns
                        cy.get('[data-context=global-total-with-discount]').should('contain', '2 573,99 €')
                        cy.get('[data-context=global-total-without-discount]').should('contain', '2 974,00 €')

                        // apply the discount
                        cy.fixture('erp/quotes/put_quote_allocate_discount_globally_with_discount.json').then(
                            (payload) => {
                                payload.data.updated = 2

                                cy.intercept('PUT', '**/api/erp/v1/quote/1/allocate-discount-globally', {
                                    statusCode: 200,
                                    body: payload,
                                }).as('put_discount_globally')
                            },
                        )

                        // button is enabled, proceed...
                        cy.get('[data-context=global-discount-apply-btn]')
                            .should('be.visible')
                            .should('be.enabled')
                            .click()

                        cy.wait('@put_discount_globally').then((xhr) => {
                            expect(xhr.request.body).to.deep.eq({
                                amount: -400,
                                mode: 'NORMAL',
                            })
                        })

                        // Close the side panel and reload the quote page content
                        cy.get('[data-context=global-discount-calculator]').should('not.be.visible')
                        cy.wait('@cpost_quotes')
                    })
                })
            })
        })
    })
})
