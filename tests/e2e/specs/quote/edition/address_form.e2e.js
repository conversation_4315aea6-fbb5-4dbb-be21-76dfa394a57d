import { visitPageWithFixture } from './helpers'
import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions'

describe('Quote - address form', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([QUOTE_WRITE])
    })

    it('Form is correctly displayed when no data is supplied and check validation messages', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')
        cy.intercept('POST', '**/api/erp/v1/countries', {
            fixture: 'erp/country/france.json',
        })

        cy.get('[data-context=customer-info]')
            .find('[data-context=open-address-editor]')
            .should('contain', 'Sélectionner ou créer une adresse')
            .click()

        cy.get('[data-context=slide-out-container] [data-context=address-form]').should('be.visible')

        // form
        cy.get('[data-context=form-quote-address]').should('be.visible').as('form')

        // click for display validator
        cy.get('@form').find('[data-context=submit]').should('contain', 'Sauvegarder').click()

        // civility
        cy.get('@form').find('[data-context=civility] label').should('contain', 'Civilité')
        cy.get('@form').find('[data-context=civility] [data-context=message]').should('not.exist')
        cy.get('@form').find('[data-context=civility] select').should('have.value', 'M.')
        // company
        cy.get('@form').find('[data-context=company] label').should('contain', 'Société')
        cy.get('@form').find('[data-context=company] [data-context=message]').should('not.exist')
        cy.get('@form')
            .find('[data-context=company] input')
            .should('have.value', '')
            .clear()
            .type(
                'Chaîne de caractères trop longggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggue',
                { delay: 0 },
            )
        cy.get('@form')
            .find('[data-context=company] [data-context=message]')
            .should('contain', 'La société ne doit pas excéder 64 caractères.')
        // firstname
        cy.get('@form').find('[data-context=firstname] label').should('contain', 'Prénom')
        cy.get('@form')
            .find('[data-context=firstname] [data-context=message]')
            .should('contain', 'Ce champ est obligatoire')
        cy.get('@form')
            .find('[data-context=firstname] input')
            .should('have.value', '')
            .clear()
            .type(
                'Chaîne de caractères trop longggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggue',
                { delay: 0 },
            )
        cy.get('@form')
            .find('[data-context=firstname] [data-context=message]')
            .should('contain', 'Le prénom ne doit pas excéder 64 caractères.')
        // lastname
        cy.get('@form').find('[data-context=lastname] label').should('contain', 'Nom')
        cy.get('@form')
            .find('[data-context=lastname] [data-context=message]')
            .should('contain', 'Ce champ est obligatoire')
        cy.get('@form')
            .find('[data-context=lastname] input')
            .should('have.value', '')
            .clear()
            .type(
                'Chaîne de caractères trop longggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggue',
                { delay: 0 },
            )
        cy.get('@form')
            .find('[data-context=lastname] [data-context=message]')
            .should('contain', 'Le nom ne doit pas excéder 64 caractères.')
        // address
        cy.get('@form').find('[data-context=address] label').should('contain', 'Adresse')
        cy.get('@form')
            .find('[data-context=address] [data-context=message]')
            .should('contain', 'Ce champ est obligatoire')
        cy.get('@form')
            .find('[data-context=address] input')
            .should('have.value', '')
            .clear()
            .type(
                'Chaîne de caractères trop longggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggue',
                { delay: 0 },
            )
        cy.get('@form')
            .find('[data-context=address] [data-context=message]')
            .should('contain', "L'adresse ne doit pas excéder 128 caractères.")
        // postal-code
        cy.get('@form').find('[data-context=postal-code] label').should('contain', 'Code postal')
        cy.get('@form')
            .find('[data-context=postal-code] [data-context=message]')
            .should('contain', 'Ce champ est obligatoire')
        cy.get('@form')
            .find('[data-context=postal-code] input')
            .should('have.value', '')
            .clear()
            .type(
                'Chaîne de caractères trop longggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggue',
                { delay: 0 },
            )
        cy.get('@form')
            .find('[data-context=postal-code] [data-context=message]')
            .should('contain', 'Le code postal ne doit pas excéder 12 caractères.')
        // city
        cy.get('@form').find('[data-context=city] label').should('contain', 'Ville')
        cy.get('@form').find('[data-context=city] [data-context=message]').should('contain', 'Ce champ est obligatoire')
        cy.get('@form')
            .find('[data-context=city] input')
            .should('have.value', '')
            .clear()
            .type(
                'Chaîne de caractères trop longggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggue',
                { delay: 0 },
            )
        cy.get('@form')
            .find('[data-context=city] [data-context=message]')
            .should('contain', 'La ville ne doit pas excéder 64 caractères.')
        // country
        cy.get('@form').find('[data-context=country] label').should('contain', 'Pays')
        cy.get('@form')
            .find('[data-context=country] [data-context=country-autocomplete]')
            .eq(0)
            .should('contain', 'FRANCE')
        cy.get('@form').find('[data-context=country] [data-context=message]').should('not.exist')
        // cellphone
        cy.get('@form').find('[data-context=cellphone] label').should('contain', 'N° de mobile')
        cy.get('@form')
            .find('[data-context=cellphone] [data-context=message]')
            .should('contain', 'Ce champ est obligatoire')
        cy.get('@form').find('[data-context=cellphone] input').should('have.value', '')
        // phone
        cy.get('@form').find('[data-context=phone] label').should('contain', 'N° de téléphone')
        cy.get('@form').find('[data-context=phone] input').should('have.value', '')
        cy.get('@form').find('[data-context=phone] [data-context=message]').should('not.exist')
    })

    it('Form is correctly displayed with pre-defined data', () => {
        visitPageWithFixture('erp/quotes/quotation_active_with_address.json')
        cy.intercept('POST', '**/api/erp/v1/countries', {
            fixture: 'erp/country/guyane_francaise.json',
        })

        cy.get('[data-context=customer-info]').eq(0).find('[data-context=open-address-editor]').click()

        cy.get('[data-context=slide-out-container] [data-context=address-form]').should('be.visible')

        // form
        cy.get('[data-context=form-quote-address]').should('be.visible').as('form')

        // civility
        cy.get('@form').find('[data-context=civility] select').should('have.value', 'Mme')
        // company
        cy.get('@form').find('[data-context=company] input').should('have.value', '')
        // firstname
        cy.get('@form').find('[data-context=firstname] input').should('have.value', 'arthur')
        // lastname
        cy.get('@form').find('[data-context=lastname] input').should('have.value', 'LOLO')
        // address
        cy.get('@form').find('[data-context=address] input').should('have.value', '38 rue de la ville en bois')
        // postal-code
        cy.get('@form').find('[data-context=postal-code] input').should('have.value', '44000')
        // city
        cy.get('@form').find('[data-context=city] input').should('have.value', 'Naoned')
        // country
        cy.get('@form')
            .find('[data-context=country] [data-context=country-autocomplete]')
            .eq(0)
            .should('contain', 'GUYANE FRANÇAISE')
        // cellphone
        cy.get('@form').find('[data-context=cellphone] input').should('have.value', '0678787878')
        // phone
        cy.get('@form').find('[data-context=phone] input').should('have.value', '')
    })

    it('Edit address', () => {
        visitPageWithFixture('erp/quotes/quotation_active_with_address.json')
        cy.intercept('POST', '**/api/erp/v1/countries', {
            fixture: 'erp/country/guyane_francaise.json',
        })

        cy.intercept('PUT', '**/api/erp/v1/quote/1', {
            statusCode: 200,
            body: {
                status: 'success',
                data: { updated: 1 },
            },
        }).as('edit_success')

        cy.get('[data-context=customer-info]').eq(0).find('[data-context=open-address-editor]').click()

        cy.get('[data-context=slide-out-container] [data-context=address-form]').should('be.visible')

        // form
        cy.get('[data-context=form-quote-address]').should('be.visible').as('form')

        // cellphone
        cy.get('@form').find('[data-context=cellphone] input').should('have.value', '0678787878').clear().type('118218')

        cy.get('@form').find('[data-context=submit]').should('contain', 'Sauvegarder').click()

        cy.wait('@edit_success').then((xhr) => {
            expect(xhr.request.body.data.billing_address).to.deep.eq({
                civility: 'Mme',
                company_name: '',
                firstname: 'arthur',
                lastname: 'LOLO',
                address: '38 rue de la ville en bois',
                postal_code: '44000',
                city: 'Naoned',
                country: {
                    name: 'GUYANE FRAN\u00c7AISE',
                    country_id: 84,
                    country_code: 'GF',
                },
                cellphone: '118218',
                phone: '',
            })
        })

        cy.wait('@cpost_quotes')

        cy.get('[data-context=address-form]').should('not.exist')
    })

    it('Can cancel', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=customer-info]').eq(0).find('[data-context=open-address-editor]').click()

        cy.get('[data-context=slide-out-container] [data-context=address-form]').should('be.visible')

        cy.get('[data-context=form-quote-address] [data-context=close]')
            .should('be.visible')
            .should('contain', 'Annuler')
            .click()

        cy.get('[data-context=address-form]').should('not.exist')
    })
    it('Address suggestion', () => {
        visitPageWithFixture('erp/quotes/quotation_active_with_address.json')
        cy.intercept('POST', '**/api/erp/v1/countries', {
            fixture: 'erp/country/france.json',
        })

        cy.intercept('GET', '**/api-adresse.data.gouv.fr/search/**', {
            fixture: 'erp/housenumber/search.json',
        })

        cy.get('[data-context=customer-info]').eq(0).find('[data-context=open-address-editor]').click()
        cy.get('[data-context=address]').find('[data-context=erp-input-suggestion]').as('addressSuggestion')
        cy.get('@addressSuggestion').find('input').should('exist').focus()
        cy.get('@addressSuggestion').find('[data-context=erp-input-suggestion-options]').should('not.exist')

        cy.get('[data-context=country]').find('[data-context=country-autocomplete]').as('country')
        cy.get('@country').click().find('.multiselect__content-wrapper').click()
        cy.get('@country').type('France').find('.multiselect__content-wrapper').contains('FRANCE').click()

        cy.get('@addressSuggestion').find('input').should('exist').clear().type('5 rue')
        cy.get('@addressSuggestion').find('[data-context=erp-input-suggestion-options]').should('exist')
        cy.get('@addressSuggestion').find('ul li').eq(4).click()

        cy.get('[data-context=address]').find('input').should('have.value', '5 Rue plus loins')
        cy.get('[data-context=postal-code]').find('input').should('have.value', 22220)
        cy.get('[data-context=city]').find('input').should('have.value', 'Minihy-Tréguier')
    })
})
