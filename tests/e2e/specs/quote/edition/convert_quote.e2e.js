import { visitPageWithFixture } from './helpers'
import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Quote - Convert quote to customer order (BO)', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Without permission', () => {
        it('is disabled if has no proper right', () => {
            visitPageWithFixture('erp/quotes/draft_fully_complete.json')

            cy.get('[data-context=convert-quote-btn]')
                .should('be.visible')
                .should('contain', '<PERSON><PERSON><PERSON> commande backoffice')
                .should('be.disabled')
        })
    })

    describe('With permission', () => {
        beforeEach(() => {
            cy.mockErpUser([QUOTE_WRITE])
        })

        it('Display why it cannot be converted', () => {
            visitPageWithFixture('erp/quotes/draft_with_lines_empty.json')

            cy.get('[data-context=convert-quote-btn]').should('be.visible').click()

            cy.get('[data-context=slide-out-container]').should('be.visible').as('content')

            cy.get('@content').find('[data-context=remaining-step]').should('have.length', 1)

            cy.get('@content')
                .find('[data-context=confirm-block-cancel-btn]')
                .should('be.visible')
                .should('contain', 'Annuler')
                .click()

            cy.get('[data-context=slide-out-container]').should('not.exist')
        })

        it('Convert it successfully', () => {
            const visitPageForSuccessfulConversionToCustomerOrder = (file) => {
                cy.intercept('POST', '**/api/erp/v1/quotes', { fixture: file }).as('cpost_quotes')

                cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                    body: {
                        data: {
                            system_events: [],
                        },
                    },
                }).as('event')

                cy.visit('/quote/1', {
                    onBeforeLoad(win) {
                        cy.stub(win, 'open').as('new_order')
                    },
                })

                cy.wait('@cpost_quotes')
                cy.wait('@event')
            }

            visitPageForSuccessfulConversionToCustomerOrder('erp/quotes/draft_fully_complete.json')

            cy.intercept('POST', '**/api/erp/v1/quote/1/customer-order', {
                statusCode: 200,
                body: { status: 'success', data: { customer_order_id: 3244162 } },
            }).as('api_convert_quote')

            cy.get('[data-context=convert-quote-btn]').should('be.visible').click()

            cy.wait('@api_convert_quote')

            cy.get('@new_order').should(
                'be.calledWith',
                '/legacy/v1/commandes/edition_commande.php?id_commande=3244162',
                '_blank',
            )

            // reload quote
            cy.wait('@cpost_quotes')
        })
    })
})
