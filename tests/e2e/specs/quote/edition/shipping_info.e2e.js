import { visitPageWithFixture } from './helpers'
import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Quote - Shipping info', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Without permission', () => {
        it('Display without permission', () => {
            visitPageWithFixture('erp/quotes/quotation_active2.json')

            cy.get('[data-context=quote-shipping-info]')
                .find('[data-context=open-address-editor]')
                .should('contain', '<PERSON><PERSON><PERSON><PERSON><PERSON> ou créer une adresse de livraison')
                .should('be.disabled')
        })
    })

    describe('With permission', () => {
        beforeEach(() => {
            cy.mockErpUser([QUOTE_WRITE])
        })

        it('cannot create a shipping address on locked quote', () => {
            visitPageWithFixture('erp/quotes/quotation_locked.json')

            cy.get('[data-context=quote-shipping-info]')
                .find('[data-context=open-address-editor]')
                .should('contain', '<PERSON><PERSON><PERSON><PERSON><PERSON> ou créer une adresse de livraison')
                .should('be.disabled')
        })

        it('can create a shipping address', () => {
            visitPageWithFixture('erp/quotes/quotation_active2.json')

            cy.get('[data-context=quote-shipping-info] [data-context=shipping-address]').should('not.exist')

            cy.get('[data-context=quote-shipping-info]')
                .find('[data-context=open-address-editor]')
                .should('contain', 'Sélectionner ou créer une adresse de livraison')
                .click()

            cy.get('[data-context=slide-out-container]').should('be.visible').should('contain', 'Adresse de livraison')
        })

        it('display and edit a shipping address', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

            cy.get('[data-context=quote-shipping-info] [data-context=shipping-address]')
                .should('be.visible')
                .should('contain', 'Mme arthur LOLO')
                .should('contain', '38 rue de la ville en bois')
                .should('contain', '44000 Naoned')
                .should('contain', 'GUYANE FRANÇAISE')
                .should('contain', '0678787878')
            cy.get('[data-context=quote-shipping-info] [data-context=shipment-method]')
                .find('[data-context=erp-button]')
                .should('be.visible')

            cy.get('[data-context=quote-shipping-info]').find('[data-context=open-address-editor]').click()

            cy.get('[data-context=slide-out-container]').should('be.visible').should('contain', 'Adresse de livraison')
        })

        it('display retail store address and shipment method', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_retail_store_address.json')

            cy.get('[data-context=quote-shipping-info] [data-context=shipping-address]')
                .should('be.visible')
                .should('contain', 'Gérard Menvussa')
                .should('contain', '3 place de la Bourse')
                .should('contain', '44000 Nantes')
                .should('contain', 'FRANCE')

            cy.get('[data-context=quote-shipping-info] [data-context=shipment-method]')
                .as('shipping_method')
                .should('be.visible')
                .should('contain', 'Emport Nantes')
                .should('contain', 'Offert')
                .should('contain', 'Retrait magasin')

            cy.get('@shipping_method').find('[data-context=delete-shipment-method-btn]').should('be.visible')

            cy.get('[data-context=quote-shipping-info]').find('[data-context=open-address-editor]').should('not.exist')
        })

        it('save the retail store in the local storage', () => {
            visitPageWithFixture('erp/quotes/cpost_quotes_prefill_shipping_address.json')

            cy.intercept('POST', '**/api/erp/v2/wms/warehouses', {
                fixture: 'erp/wms/warehouses/v2/warehouses.json',
            }).as('fetch-warehouses')
            cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                statusCode: 200,
                body: { status: 'success' },
            }).as('put-quote')

            cy.get('[data-context=quote-shipping-info] [data-context=open-address-editor]').click()
            cy.get('[data-context=retail-store-form-btn]').click()
            cy.get('[data-context=warehouse-autocomplete]').click()
            cy.wait('@fetch-warehouses')

            // select Antibes
            cy.get('#null-2').click()

            cy.get('[data-context=submit]').click()
            cy.wait('@put-quote')

            // reload
            visitPageWithFixture('erp/quotes/cpost_quotes_prefill_shipping_address.json')
            cy.get('[data-context=quote-shipping-info] [data-context=open-address-editor]').click()
            cy.get('[data-context=retail-store-form-btn]').click()
            cy.wait('@fetch-warehouses')

            cy.get('[data-context=warehouse-autocomplete]').find('.multiselect__single').should('have.text', 'Antibes')
        })

        it('can delete the shipment method with the shipping address', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_retail_store_address.json')

            cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: { updated: 1 },
                },
            }).as('api_put')
            cy.get('[data-context=quote-shipping-info] [data-context=delete-shipment-method-btn]').click()

            cy.wait('@api_put').then((xhr) => {
                expect(xhr.request.body.data).to.deep.eq({
                    shipping_address: null,
                    shipment_method: null,
                })
            })
            cy.wait('@cpost_quotes')
        })

        it('cannot delete the shipment method on locked quote', () => {
            visitPageWithFixture('erp/quotes/quotation_with_shipment_locked.json')

            cy.get('[data-context=quote-shipping-info] [data-context=delete-shipment-method-btn]').should('be.disabled')
        })

        describe('Prefill form with billing address', () => {
            it('should prefill the shipping address form when empty', () => {
                visitPageWithFixture('erp/quotes/cpost_quotes_prefill_shipping_address.json')

                cy.get('[data-context=quote-shipping-info] [data-context=open-address-editor]').click()

                cy.get('[data-context=slide-out-container]').as('form').should('be.visible')

                cy.get('@form').find('[data-context="civility"]').should('contain', 'Mme')

                cy.get('@form').find('[data-context="company"] input').should('have.value', 'PHC Holding')

                cy.get('@form').find('[data-context="firstname"] input').should('have.value', 'Gérard')

                cy.get('@form').find('[data-context="lastname"] input').should('have.value', 'MANVUSSA')

                cy.get('@form')
                    .find('[data-context="address"] input')
                    .should('have.value', '38 rue de la ville en bois')

                cy.get('@form').find('[data-context="postal-code"] input').should('have.value', '44100')

                cy.get('@form').find('[data-context="city"] input').should('have.value', 'NANTES')

                cy.get('@form')
                    .find('[data-context=country] [data-context=country-autocomplete]')
                    .eq(0)
                    .should('contain', 'FRANCE')

                cy.get('@form').find('[data-context=cellphone] input').should('have.value', '0600000000')

                cy.get('@form').find('[data-context=phone] input').should('have.value', '0102030405')
            })
        })
    })
})
