import { visitPageWithFixture } from './helpers'
import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions'

describe('Quote - Page header', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe(`Quote information`, () => {
        it('Check not sent quotation', () => {
            visitPageWithFixture('erp/quotes/quotation_inactive.json')

            cy.get('[data-context=title]').should('contain', 'Devis 1')
            cy.get('[data-context=obfuscated-margin-code]').should('contain', 'A0.DR145.FR0Z')

            cy.get('[data-context=info-badge]').as('info_badge')
            cy.get('@info_badge').find('[data-context=created-at]').should('contain', '04/02/2022')
            cy.get('@info_badge').find('[data-context=status]').should('contain', 'Inactif')

            // check prospect badge
            cy.get('[data-context=customer-badge]')
                .as('customer_badge')
                .find('[data-context=scope]')
                .should('contain', 'CLIENT')
            cy.get('@customer_badge')
                .find('[data-context=erp-link]')
                .should('contain', '970481')
                .should('have.attr', 'href', 'http://erp-client.lxc/customer/970481')
                .should('have.attr', 'target', '_blank')

            // expire at date
            cy.get('[data-context=expired-at-badge]').should('not.exist')
        })

        it('Check sent quotation', () => {
            visitPageWithFixture('erp/quotes/quotation_locked.json')

            cy.get('[data-context=title]').should('contain', 'Devis 1')
            cy.get('[data-context=obfuscated-margin-code]').should('contain', 'A91.DR145.FR20Z')

            cy.get('[data-context=info-badge]').find('[data-context=status]').should('contain.text', 'Envoyé')

            cy.get('[data-context=locked]').should('be.visible')

            cy.get('[data-context=not-locked]').should('not.exist')

            // expire at date
            cy.get('[data-context=expired-at-badge]').should('be.visible').should('contain', '01/04/2022')
        })

        it('Check ordered quotation', () => {
            visitPageWithFixture('erp/quotes/quotation_ordered.json')

            cy.get('[data-context=info-badge]').find('[data-context=status]').should('contain.text', 'Commandé')

            // check customer orders
            cy.get('[data-context=customer-orders-badge]')
                .as('customer_orders_badge')
                .find('[data-context=scope]')
                .should('contain', 'cmd')
            cy.get('@customer_orders_badge')
                .find('[data-context=erp-link]')
                .should('have.length', 2)
                .as('customer_orders')
                .eq(0)
                .should('contain', '123456')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=123456',
                )
                .should('have.attr', 'target', '_blank')
            cy.get('@customer_orders').eq(1).should('contain', '234567')
        })

        it('Check quote draft label', () => {
            visitPageWithFixture('erp/quotes/draft_fully_complete.json')

            cy.get('[data-context=title]').should('contain', 'Brouillon 1')
            cy.title().should('eq', 'Brouillon 1 - Son-Vidéo.com ERP')
        })

        it('Check quote offer label', () => {
            visitPageWithFixture('erp/quotes/offer.json')

            cy.get('[data-context=title]').should('contain', 'Offre 1')
            cy.title().should('eq', 'Offre 1 - Son-Vidéo.com ERP')
        })

        it('Check old offer label', () => {
            visitPageWithFixture('erp/quotes/old_offer_fully_complete.json')

            cy.get('[data-context=title]').should('contain', 'Ancien devis 1')
            cy.title().should('eq', 'Ancien devis 1 - Son-Vidéo.com ERP')
        })
    })

    describe(`Quote actions`, () => {
        describe('Without permission', () => {
            it('Does not show the actions if the user has no write permission', () => {
                visitPageWithFixture('erp/quotes/quotation_ordered.json')

                cy.get('[data-context=title]').should('contain', 'Devis 1')
                cy.get('[data-context=quote-actions]').should('not.exist')
            })
        })

        describe('With permission', () => {
            const cPostAfterClone = () => {
                cy.fixture('erp/quotes/quotation_ordered.json').then((payload) => {
                    payload.data.quotes[0].quote_id = 123456

                    cy.intercept('POST', '**/api/erp/v1/quotes', {
                        statusCode: 200,
                        body: payload,
                    }).as('cpost_quotes')
                })
            }

            beforeEach(() => {
                cy.mockErpUser([QUOTE_WRITE])
            })

            describe('Clone Action', () => {
                it('Check button and API requests', () => {
                    const visitPageForCloningQuote = (file) => {
                        cy.intercept('POST', '**/api/erp/v1/quotes', { fixture: file }).as('cpost_quotes')

                        cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                            body: {
                                data: {
                                    system_events: [],
                                },
                            },
                        }).as('event')

                        cy.visit('/quote/1', {
                            onBeforeLoad(win) {
                                cy.stub(win, 'open').as('cloned_quote')
                            },
                        })

                        cy.wait('@cpost_quotes')
                        cy.wait('@event')
                    }

                    visitPageForCloningQuote('erp/quotes/quotation_ordered.json')

                    cy.get('[data-context=title]').should('contain', 'Devis 1')
                    cy.get('[data-context=quote-actions]').should('be.visible').click()

                    cy.intercept('POST', '**/api/erp/v1/quote/1/clone', {
                        statusCode: 200,
                        body: { status: 'success', data: { quote_id: 123456 } },
                    }).as('api_clone_quote')

                    cPostAfterClone()

                    cy.get('[data-context=clone-quote-btn]').should('be.visible').click()

                    cy.wait('@api_clone_quote')

                    cy.get('@cloned_quote').should('be.calledWith', '/quote/123456', '_blank')

                    // reload quote
                    cy.wait('@cpost_quotes')
                })

                it('Check cant clone for old offer', () => {
                    visitPageWithFixture('erp/quotes/old_offer_fully_complete.json')
                    cy.get('[data-context=quote-actions]').should('be.visible').click()

                    cy.get('[data-context=clone-quote-btn]')
                        .should('be.visible')
                        .should('have.class', 'cursor-not-allowed')
                })
            })

            describe('Generate PDF Action', () => {
                it('Check button for a quotation quote type whose status is not lock', () => {
                    visitPageWithFixture('erp/quotes/quotation_inactive.json')

                    cy.get('[data-context=quote-actions]').should('be.visible').click()

                    cy.get('[data-context=generate-pdf-btn]')
                        .should('be.visible')
                        .should(($a) => {
                            const url = new URL($a.attr('href'))
                            expect(url.searchParams.get('access_token')).to.eq('ACCESS_TOKEN')
                            expect(url.searchParams.get('options[quote_id]')).to.eq('1')
                            expect(url.searchParams.get('options[regenerate]')).to.eq('true')
                            expect(url.searchParams.get('filename')).to.eq('SONVIDEO_DEVIS_1')
                        })
                })

                it('Check button for a quotation quote type whose status is lock', () => {
                    visitPageWithFixture('erp/quotes/quotation_ordered.json')

                    cy.get('[data-context=quote-actions]').should('be.visible').click()

                    cy.get('[data-context=generate-pdf-btn]')
                        .should('be.visible')
                        .should(($a) => {
                            const url = new URL($a.attr('href'))
                            expect(url.searchParams.get('access_token')).to.eq('ACCESS_TOKEN')
                            expect(url.searchParams.get('options[quote_id]')).to.eq('1')
                            expect(url.searchParams.get('options[regenerate]')).to.not.exist
                            expect(url.searchParams.get('filename')).to.eq('SONVIDEO_DEVIS_1')
                        })
                })

                it('Check cant generate PDF for old offer', () => {
                    visitPageWithFixture('erp/quotes/old_offer_fully_complete.json')
                    cy.get('[data-context=quote-actions]').should('be.visible').click()

                    cy.get('[data-context=generate-pdf-btn]')
                        .should('be.visible')
                        .should('have.class', 'cursor-not-allowed')
                })
            })
        })
    })
})
