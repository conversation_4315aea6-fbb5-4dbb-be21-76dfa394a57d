describe('Display quote timeline', () => {
    const visitPageWithFixtureForEvent = (file) => {
        cy.intercept('POST', '**/api/erp/v1/quotes', { fixture: file }).as('cpost_quotes')

        cy.intercept('POST', '**/api/erp/v1/quote-subtype', {
            fixture: 'erp/quote_subtypes/quote_subtypes.json',
        })

        cy.visit('/quote/1')
        cy.wait('@cpost_quotes')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    it('Filter events', () => {
        cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
            fixture: 'erp/quotes/events/quote_comment_events.json',
        }).as('events')
        cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
            fixture: 'erp/quotes/events/quote_all_events.json',
        }).as('events')
        cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=history', {
            fixture: 'erp/quotes/events/quote_history_events.json',
        }).as('events')

        visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
        cy.wait('@events')

        //default comment
        cy.get('[data-context=event_filter]')
            .should('contain', 'Afficher uniquement les commentaires et les emails')
            .as('event_filter_select')

        cy.get('@event_filter_select').find('option').should('have.length', 3)

        cy.get('[data-context=event]').should('have.length', 2)

        cy.get('@event_filter_select').select('all')
        cy.get('[data-context=event]').should('have.length', 19)

        cy.get('@event_filter_select').select('comment')
        cy.get('[data-context=event]').should('have.length', 2)

        cy.get('@event_filter_select').select('history')
        cy.get('[data-context=event]').should('have.length', 17)
    })

    describe('Display event', () => {
        it('Display no event', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('event')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('event')
            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@event')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline]').as('timelineItem').should('not.be.visible')
        })

        it('Display event create', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('event_create')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_create.json',
            }).as('event_create')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@event_create')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-create]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', 'a créé le brouillon le 30/03/2022 à 16:17')
        })

        it('Display event clone to', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('event_clone_to')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_clone_to.json',
            }).as('event_clone_to')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@event_clone_to')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-clone]')
                .as('timelineItem')
                .should('be.visible')

            // split the string because of the DOM
            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', ' a cloné  ce devis vers ')

            cy.get('@timelineItem')
                .find('[data-context=description] [data-context=erp-link]')
                .should('have.attr', 'href', 'http://erp-client.lxc/quote/2')
                .should('have.attr', 'target', '_blank')
                .should('contain', 'le brouillon 2')

            cy.get('@timelineItem').find('[data-context=date]').should('contain', 'le 30/03/2022 à 16:48')
        })

        it('Display event clone with offer', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('event_clone_offer')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_clone_offer.json',
            }).as('event_clone_offer')

            visitPageWithFixtureForEvent('erp/quotes/offer.json')
            cy.wait('@event_clone_offer')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-clone]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .find('[data-context=erp-link]')
                .should('contain', "l'offre 1")
        })

        it('Display event clone from', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('event_clone_from')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_clone_from.json',
            }).as('event_clone_from')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@event_clone_from')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-clone]')
                .as('timelineItem')
                .should('be.visible')

            // split the string because of the DOM
            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')
            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', 'a cloné  ce brouillon depuis ')
            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'le devis 1')
            cy.get('@timelineItem').find('[data-context=date]').should('contain', 'le 30/03/2022 à 16:48')

            cy.get('@timelineItem')
                .find('[data-context=description] [data-context=erp-link]')
                .should('have.attr', 'href', 'http://erp-client.lxc/quote/1')
                .should('have.attr', 'target', '_blank')
                .should('contain', 'devis 1')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'le 30/03/2022 à 16:48')
        })

        it('Display event transform in customer order', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('event_customer_order')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_customer_order.json',
            }).as('event_customer_order')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@event_customer_order')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-customer-order]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'ce devis')

            cy.get('@timelineItem')
                .find('[data-context=description] [data-context=erp-link]')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=1234',
                )
                .should('have.attr', 'target', '_blank')
                .should('contain', 'commande 1234')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'le 30/03/2022 à 15:57')
        })

        it('Display event billing address', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_billing_address')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_billing_address.json',
            }).as('quote_billing_address')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_billing_address')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-update-billing-address]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', "a modifié l'adresse de facturation le 30/03/2022 à 16:37")

            cy.get('@timelineItem').find('[data-context=erp-table]').should('not.exist')

            cy.get('@timelineItem')
                .find('[data-context=toggle-details]')
                .should('contain', 'Voir les modifications')
                .click()

            cy.get('@timelineItem').find('[data-context=erp-table]').as('table').should('be.visible')

            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 3)

            cy.get('@header').eq(0).should('contain', ' ')
            cy.get('@header').eq(1).should('contain', 'avant')
            cy.get('@header').eq(2).should('contain', 'après')

            cy.get('@table')
                .find('[data-context=table-row]')
                .should('have.length', 10)
                .as('table_row')
                .eq(0)
                .as('row_city')

            cy.get('@row_city').find('[data-context=cell-key]').should('contain', 'Ville')

            cy.get('@row_city').find('[data-context=cell-old]').should('contain', 'NANTES')

            cy.get('@row_city').find('[data-context=cell-new]').should('contain', 'NANTES')

            cy.get('@table_row').eq(1).as('row_phone').find('[data-context=cell-key]').should('contain', 'Téléphone')

            cy.get('@row_phone').find('[data-context=cell-old]').should('contain', '')

            cy.get('@row_phone').find('[data-context=cell-new]').should('contain', '')

            cy.get('@table_row').eq(2).as('row_address').find('[data-context=cell-key]').should('contain', 'Adresse')

            cy.get('@row_address').find('[data-context=cell-old]').should('contain', '1 rue toto')

            cy.get('@row_address').find('[data-context=cell-new]').should('contain', '1 rue toto')

            cy.get('@table_row').eq(3).as('row_country').find('[data-context=cell-key]').should('contain', 'Pays')

            cy.get('@row_country').find('[data-context=cell-old]').should('contain', 'FRANCE')

            cy.get('@row_country').find('[data-context=cell-new]').should('contain', 'FRANCE')

            cy.get('@table_row').eq(4).as('row_civility').find('[data-context=cell-key]').should('contain', 'Civilité')

            cy.get('@row_civility').find('[data-context=cell-old]').should('contain', 'M.')

            cy.get('@row_civility').find('[data-context=cell-new]').should('contain', 'Mme')

            cy.get('@table_row').eq(5).as('row_lastname').find('[data-context=cell-key]').should('contain', 'Nom')

            cy.get('@row_lastname').find('[data-context=cell-old]').should('contain', 'Menvussa')

            cy.get('@row_lastname').find('[data-context=cell-new]').should('contain', 'Menvussa')

            cy.get('@table_row').eq(6).as('row_mobile').find('[data-context=cell-key]').should('contain', 'Mobile')

            cy.get('@row_mobile').find('[data-context=cell-old]').should('contain', '06 06 06 06 06')

            cy.get('@row_mobile').find('[data-context=cell-new]').should('contain', '06 06 06 06 06')

            cy.get('@table_row').eq(7).as('row_firstname').find('[data-context=cell-key]').should('contain', 'Prénom')

            cy.get('@row_firstname').find('[data-context=cell-old]').should('contain', 'Gerard')

            cy.get('@row_firstname').find('[data-context=cell-new]').should('contain', 'Gerard')

            cy.get('@table_row')
                .eq(8)
                .as('row_postal_code')
                .find('[data-context=cell-key]')
                .should('contain', 'Code postal')

            cy.get('@row_postal_code').find('[data-context=cell-old]').should('contain', '44000')

            cy.get('@row_postal_code').find('[data-context=cell-new]').should('contain', '44000')

            cy.get('@table_row')
                .eq(9)
                .as('row_company_name')
                .find('[data-context=cell-key]')
                .should('contain', 'Société')

            cy.get('@row_company_name').find('[data-context=cell-old]').should('contain', '')

            cy.get('@row_company_name').find('[data-context=cell-new]').should('contain', '')
        })

        it('Display event shipping address', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_shipping_address')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_shipping_address.json',
            }).as('quote_shipping_address')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_shipping_address')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-update-shipping-address]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', "a modifié l'adresse de livraison le 30/03/2022 à 16:37")

            cy.get('@timelineItem').find('[data-context=erp-table]').should('not.exist')

            cy.get('@timelineItem')
                .find('[data-context=toggle-details]')
                .should('contain', 'Voir les modifications')
                .click()

            cy.get('@timelineItem').find('[data-context=erp-table]').as('table').should('be.visible')

            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 3)

            cy.get('@header').eq(0).should('contain', ' ')
            cy.get('@header').eq(1).should('contain', 'avant')
            cy.get('@header').eq(2).should('contain', 'après')

            cy.get('@table')
                .find('[data-context=table-row]')
                .should('have.length', 10)
                .as('table_row')
                .eq(0)
                .as('row_city')

            cy.get('@row_city').find('[data-context=cell-key]').should('contain', 'Ville')

            cy.get('@row_city').find('[data-context=cell-old]').should('contain', 'NANTES')

            cy.get('@row_city').find('[data-context=cell-new]').should('contain', 'NANTES')

            cy.get('@table_row').eq(1).as('row_phone').find('[data-context=cell-key]').should('contain', 'Téléphone')

            cy.get('@row_phone').find('[data-context=cell-old]').should('contain', '')

            cy.get('@row_phone').find('[data-context=cell-new]').should('contain', '')

            cy.get('@table_row').eq(2).as('row_address').find('[data-context=cell-key]').should('contain', 'Adresse')

            cy.get('@row_address').find('[data-context=cell-old]').should('contain', '1 rue toto')

            cy.get('@row_address').find('[data-context=cell-new]').should('contain', '1 rue toto')

            cy.get('@table_row').eq(3).as('row_country').find('[data-context=cell-key]').should('contain', 'Pays')

            cy.get('@row_country').find('[data-context=cell-old]').should('contain', 'FRANCE')

            cy.get('@row_country').find('[data-context=cell-new]').should('contain', 'FRANCE')

            cy.get('@table_row').eq(4).as('row_civility').find('[data-context=cell-key]').should('contain', 'Civilité')

            cy.get('@row_civility').find('[data-context=cell-old]').should('contain', 'M.')

            cy.get('@row_civility').find('[data-context=cell-new]').should('contain', 'Mme')

            cy.get('@table_row').eq(5).as('row_lastname').find('[data-context=cell-key]').should('contain', 'Nom')

            cy.get('@row_lastname').find('[data-context=cell-old]').should('contain', 'Menvussa')

            cy.get('@row_lastname').find('[data-context=cell-new]').should('contain', 'Menvussa')

            cy.get('@table_row').eq(6).as('row_mobile').find('[data-context=cell-key]').should('contain', 'Mobile')

            cy.get('@row_mobile').find('[data-context=cell-old]').should('contain', '06 06 06 06 06')

            cy.get('@row_mobile').find('[data-context=cell-new]').should('contain', '06 06 06 06 06')

            cy.get('@table_row').eq(7).as('row_firstname').find('[data-context=cell-key]').should('contain', 'Prénom')

            cy.get('@row_firstname').find('[data-context=cell-old]').should('contain', 'Gerard')

            cy.get('@row_firstname').find('[data-context=cell-new]').should('contain', 'Gerard')

            cy.get('@table_row')
                .eq(8)
                .as('row_postal_code')
                .find('[data-context=cell-key]')
                .should('contain', 'Code postal')

            cy.get('@row_postal_code').find('[data-context=cell-old]').should('contain', '44000')

            cy.get('@row_postal_code').find('[data-context=cell-new]').should('contain', '44000')

            cy.get('@table_row')
                .eq(9)
                .as('row_company_name')
                .find('[data-context=cell-key]')
                .should('contain', 'Société')

            cy.get('@row_company_name').find('[data-context=cell-old]').should('contain', '')

            cy.get('@row_company_name').find('[data-context=cell-new]').should('contain', '')
        })

        it('Display event shipment method', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_shipment_method')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_shipment_method.json',
            }).as('quote_shipment_method')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_shipment_method')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-update-shipment-method]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', 'a modifié le mode de livraison le 31/03/2022 à 10:18')

            cy.get('@timelineItem').find('[data-context=erp-table]').should('not.exist')

            cy.get('@timelineItem')
                .find('[data-context=toggle-details]')
                .should('contain', 'Voir les modifications')
                .click()

            cy.get('@timelineItem').find('[data-context=erp-table]').as('table').should('be.visible')

            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 3)

            cy.get('@header').eq(0).should('contain', ' ')
            cy.get('@header').eq(1).should('contain', 'avant')
            cy.get('@header').eq(2).should('contain', 'après')

            cy.get('@table')
                .find('[data-context=table-row]')
                .should('have.length', 2)
                .as('table_row')
                .eq(0)
                .as('row_cost')
                .find('[data-context=cell-key]')
                .should('contain', 'Prix')

            cy.get('@row_cost').find('[data-context=cell-old]').should('contain', '')

            cy.get('@row_cost').find('[data-context=cell-new]').should('contain', '5.9')

            cy.get('@table_row').eq(1).as('row_name').find('[data-context=cell-key]').should('contain', 'Nom')

            cy.get('@row_name').find('[data-context=cell-old]').should('contain', '')

            cy.get('@row_name').find('[data-context=cell-new]').should('contain', 'GLS')
        })

        it('Display event subtype', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_subtypes.json',
            }).as('quote_subtypes')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_subtypes')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-update-subtype]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', 'a modifié le sous-type le 31/03/2022 à 10:18')

            cy.get('@timelineItem').find('[data-context=erp-table]').should('not.exist')

            cy.get('@timelineItem').find('[data-context=toggle-details]').should('contain', 'Voir les modifications')
            cy.get('@timelineItem').find('[data-context=toggle-details]').click()

            cy.get('@timelineItem').find('[data-context=erp-table]').as('table').should('be.visible')

            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 3)

            cy.get('@header').eq(0).should('contain', ' ')
            cy.get('@header').eq(1).should('contain', 'avant')
            cy.get('@header').eq(2).should('contain', 'après')

            cy.get('@table')
                .find('[data-context=table-row]')
                .should('have.length', 1)
                .as('table_row')
                .eq(0)
                .as('row_name')
                .find('[data-context=cell-key]')
                .should('contain', 'Libellé')

            cy.get('@row_name').find('[data-context=cell-old]').should('contain', 'Classique')

            cy.get('@row_name').find('[data-context=cell-new]').should('contain', 'B2B')
        })

        it('Display event quote line product insert', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('event_quote_line_product_insert')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_line_product_insert.json',
            }).as('event_quote_line_product_insert')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@event_quote_line_product_insert')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-line-product-insert]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', 'a ajouté une ligne de produit le 31/03/2022 à 16:51')

            cy.get('@timelineItem').find('[data-context=erp-article-item]').should('not.exist')

            cy.get('@timelineItem')
                .find('[data-context=toggle-details]')
                .should('contain', 'Voir les modifications')
                .click()

            cy.get('@timelineItem')
                .find('[data-context=sku]')
                .should('be.visible')
                .should('contain', 'YAMEPH100SISTB')
                .should('have.attr', 'target', '_blank')
        })

        it('Display event quote line delete', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('event_quote_line_delete')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_line_delete.json',
            }).as('event_quote_line_delete')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@event_quote_line_delete')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-line-delete]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', 'a supprimé une ligne de produit le 31/03/2022 à 17:34')

            cy.get('@timelineItem').find('[data-context=erp-article-item]').should('not.exist')

            cy.get('@timelineItem')
                .find('[data-context=toggle-details]')
                .should('contain', 'Voir les modifications')
                .click()

            cy.get('@timelineItem')
                .find('[data-context=sku]')
                .should('be.visible')
                .should('contain', 'FOCASIBEVO51NR')
                .should('have.attr', 'target', '_blank')
        })

        it('Display event internal comment', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_internal_comment')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_internal_comment.json',
            }).as('quote_internal_comment')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_internal_comment')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-internal-comment]')
                .as('timelineItem')
                .should('be.visible')

            // split the string because of the DOM
            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'le 30/03/2022 à 16:37')

            cy.get('@timelineItem').find('[data-context=message]').should('contain', 'Test du commentaire interne')
        })

        it('Display event quote line product update quantity', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_line_product_update_quantity')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_line_product_update_quantity.json',
            }).as('quote_line_product_update_quantity')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_line_product_update_quantity')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-line-product-update]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'a modifié la valeur de la')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'Quantité')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', '3')

            cy.get('@timelineItem')
                .find('[data-context=description] [data-context=erp-link]')
                .should('have.attr', 'href', '/articles/BOSESLCOLIINR')
                .should('have.attr', 'target', '_blank')
                .should('contain', 'BOSESLCOLIINR')
        })

        it('Display event quote line product update unit discount amount', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_line_product_update_unit_discount_amount')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_line_product_update_unit_discount_amount.json',
            }).as('quote_line_product_update_unit_discount_amount')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_line_product_update_unit_discount_amount')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-line-product-update]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'a modifié la valeur de la')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'Remise unitaire')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', '1.19')

            cy.get('@timelineItem')
                .find('[data-context=description] [data-context=erp-link]')
                .should('have.attr', 'href', '/articles/BOSESLCOLIINR')
                .should('have.attr', 'target', '_blank')
                .should('contain', 'BOSESLCOLIINR')
        })

        it('Display event quote line product update selected warranties', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('api_events')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_line_product_update_selected_warranties.json',
            }).as('api_events')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@api_events')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-line-product-update]')
                .as('timelineItems')
                .should('be.visible')

            // event when removing all warranties
            cy.get('@timelineItems').eq(0).find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')
            cy.get('@timelineItems')
                .eq(0)
                .find('[data-context=description]')
                .should('contain', 'a modifié la valeur de la')
            cy.get('@timelineItems').eq(0).find('[data-context=description]').should('contain', 'Garantie sélectionnée')
            cy.get('@timelineItems').eq(0).find('[data-context=description]').should('contain', 'Aucune')
            cy.get('@timelineItems')
                .eq(0)
                .find('[data-context=description] [data-context=erp-link]')
                .should('have.attr', 'href', '/articles/BOSESLCOLIINR')
                .should('have.attr', 'target', '_blank')
                .should('contain', 'BOSESLCOLIINR')

            // event after adding 1 warranty
            cy.get('@timelineItems')
                .eq(1)
                .find('[data-context=description]')
                .should('contain', 'Extension de garantie 5 ans')

            // event after adding 2 warranties
            cy.get('@timelineItems')
                .eq(2)
                .find('[data-context=description]')
                .should('contain', 'Extension de garantie 5 ans + Vol et casse 5 ans')
        })

        it('Display event quote email', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_email_comment.json',
            }).as('event_quote_email_comment')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_email_all.json',
            }).as('event_quote_email_all')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@event_quote_email_comment')

            cy.get('[data-context=event_filter]').select('all')
            cy.wait('@event_quote_email_all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-email]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', 'a envoyé le devis par mail le 31/03/2022 à 17:55')

            cy.get('@timelineItem').find('[data-context=mailjet-message-history]').should('not.exist')

            cy.get('@timelineItem')
                .find('[data-context=toggle-details]')
                .should('contain', `Voir l'historique`)
                .first()
                .click()

            cy.get('@timelineItem').find('[data-context=mailjet-message-history]').should('be.visible')

            // test with email.quote_quotation
            cy.get('[data-context=event_filter]').select('comment')
            cy.wait('@event_quote_email_comment')

            cy.get('[data-context=timeline]').find('li').should('have.length', 2)
            cy.get('@timelineItem')
                .find('[data-context=toggle-details]')
                .should('contain', `Voir l'historique`)
                .eq(1)
                .click()
            cy.get('@timelineItem').find('[data-context=mailjet-message-history]').eq(0).should('be.visible')
        })

        it('Display event expired at', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_expired_at')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_expired_at.json',
            }).as('quote_expired_at')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_expired_at')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-update-expired-at]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', "a ajouté une date d'expiration au")

            cy.get('@timelineItem').find('[data-context=description]').should('contain', '16/04/2022')

            cy.get('@timelineItem').find('[data-context=description]').should('contain', 'le 31/03/2022 à 10:18')
        })

        it('Display event message', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_message')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_message.json',
            }).as('quote_message')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_message')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-update-message]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=event-owner]').should('contain', 'Élise ÉMOI')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', 'a modifié le commentaire pour le client le 31/03/2022 à 10:18')

            cy.get('@timelineItem').find('[data-context=erp-table]').should('not.exist')

            cy.get('@timelineItem')
                .find('[data-context=toggle-details]')
                .should('contain', 'Voir les modifications')
                .click()

            cy.get('@timelineItem').find('[data-context=erp-table]').as('table').should('be.visible')

            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 3)

            cy.get('@header').eq(0).should('contain', ' ')
            cy.get('@header').eq(1).should('contain', 'avant')
            cy.get('@header').eq(2).should('contain', 'après')

            cy.get('@table')
                .find('[data-context=table-row]')
                .should('have.length', 1)
                .as('table_row')
                .eq(0)
                .as('row_name')
                .find('[data-context=cell-key]')
                .should('contain', 'Message')

            cy.get('@row_name').find('[data-context=cell-old]').should('contain', '')

            cy.get('@row_name')
                .find('[data-context=cell-new]')
                .should('contain', 'Suite à votre demande voici notre offre pour les produits suivants: test')
        })

        it('Display event update created_by', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_update_created_by')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_update_created_by.json',
            }).as('quote_update_created_by')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_update_created_by')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-update-created-by]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=new-creator]').should('contain', 'Thomas Price')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain', 'a désigné un nouveau créateur : Thomas Price le 13/03/2023 à 13:31')

            cy.get('@timelineItem').find('[data-context=erp-table]').should('not.exist')
            cy.get('@timelineItem').find('[data-context=toggle-details]').should('not.exist')
        })

        it('Display event update valid_until', () => {
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                fixture: 'erp/quotes/events/quote_no_event.json',
            }).as('quote_update_valid_until')
            cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=all', {
                fixture: 'erp/quotes/events/quote_update_valid_until.json',
            }).as('quote_update_valid_until')

            visitPageWithFixtureForEvent('erp/quotes/quotation_active.json')
            cy.wait('@quote_update_valid_until')

            cy.get('[data-context=event_filter]').select('all')

            cy.get('[data-context=quote-history-timeline] [data-context=quote-update-validity]')
                .as('timelineItem')
                .should('be.visible')

            cy.get('@timelineItem').find('[data-context=new-validity]').should('contain', '10 jours')

            cy.get('@timelineItem')
                .find('[data-context=description]')
                .should('contain.text', 'a modifié la durée de validité à 10 jours le 11/04/2023 à 16:03')
            cy.get('@timelineItem').find('[data-context=erp-table]').should('not.exist')
            cy.get('@timelineItem').find('[data-context=toggle-details]').should('not.exist')
        })
    })
})
