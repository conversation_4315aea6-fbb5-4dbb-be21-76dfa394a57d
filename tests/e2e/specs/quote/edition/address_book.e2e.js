import { visitPageWithFixture } from './helpers'
import { aliasQuery, GRAPHQL_ENDPOINT, hasOperationName } from '../../../utils/graphql-test-utils'
import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions'

describe('Quote - Customer address book', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([QUOTE_WRITE])

        cy.intercept('POST', '**/api/erp/v1/countries', { fixture: 'erp/country/france.json' }).as(
            'cpost_countries_by_name',
        )

        // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
        // alias the queries and mutations for our tests in a beforeEach
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomer')

            // Default
            if (hasOperationName(req, 'fetchCustomer')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomer')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_account_information_by_pk_12345.json',
                })
            }
        })

        cy.intercept('**/api-adresse.data.gouv.fr/search/?q=*', {
            statusCode: 200,
            body: {},
        })
    })

    it('display all customer addresses from billing section', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=open-address-editor]').eq(0).click()

        cy.get('[data-context=address-book-btn]').click()

        // opens address book
        cy.get('[data-context=customer-address-book]').should('be.visible')
        cy.get('[data-context=customer-address-book-entry]').should('have.length', 2).eq(1).as('my_address')
        cy.get('@my_address')
            .find('[data-context=address-content]')
            .as('address_content')
            .find('[data-context=address-label]')
            .should('contain', 'Bureau')
        cy.get('@address_content')
            .should('contain', 'PHC HOLDING')
            .should('contain', 'Mme Guy Liguili')
            .should('contain', '16 boulevard charles de gaulle')
            .should('contain', '92500 CHAMPIGNY FR')
            .should('contain', '0102030405')
            .should('contain', '0707070707')
    })

    it('display all customer addresses from shipping section', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=open-address-editor]').eq(1).click()

        cy.get('[data-context=address-book-btn]').click()

        // opens address book
        cy.get('[data-context=customer-address-book]').should('be.visible')
    })

    it('can select a billing address from addresses book', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=open-address-editor]').eq(0).click()
        cy.get('[data-context=address-book-btn]').click()

        // select second address
        cy.intercept('POST', '**/api/erp/v1/countries', { fixture: 'erp/country/cpost_countries_fr.json' }).as(
            'cpost_countries',
        )

        cy.get('[data-context=select-address]').should('have.attr', 'disabled', 'disabled')
        cy.get('[data-context=customer-address-book-entry]')
            .eq(1)
            .click()
            .find('[data-context=address-input]')
            .should('be.checked')
        cy.get('[data-context=select-address]').should('not.have.attr', 'disabled', 'disabled').click()
        cy.wait('@cpost_countries').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                country_code: {
                    _eq: 'FR',
                },
            })
        })

        // return on prefilled form
        cy.get('[data-context=customer-address-book]').should('not.exist')
        cy.get('[data-context=address-form]').should('be.visible')

        // form
        cy.get('[data-context=form-quote-address]').as('form')

        cy.get('@form').find('[data-context=civility] select').should('have.value', 'Mme')
        cy.get('@form').find('[data-context=company] input').should('have.value', 'PHC HOLDING')
        cy.get('@form').find('[data-context=firstname] input').should('have.value', 'Guy')
        cy.get('@form').find('[data-context=lastname] input').should('have.value', 'Liguili')
        cy.get('@form').find('[data-context=address] input').should('have.value', '16 boulevard charles de gaulle')
        cy.get('@form').find('[data-context=postal-code] input').should('have.value', '92500')
        cy.get('@form').find('[data-context=city] input').should('have.value', 'CHAMPIGNY')
        cy.get('@form').find('[data-context=country] .multiselect__single').should('contain', 'FRANCE')
        cy.get('@form').find('[data-context=cellphone] input').should('have.value', '0707070707')
        cy.get('@form').find('[data-context=phone] input').should('have.value', '0102030405')
    })

    it('send empty company name if address book has none', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=open-address-editor]').eq(0).click()
        cy.get('[data-context=address-book-btn]').click()
        cy.intercept('POST', '**/api/erp/v1/countries', { fixture: 'erp/country/cpost_countries_fr.json' }).as(
            'cpost_countries',
        )

        // select first address (no company name)
        cy.get('[data-context=customer-address-book-entry]').eq(0).click()
        cy.get('[data-context=select-address]').click()
        cy.wait('@cpost_countries').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                country_code: {
                    _eq: 'FR',
                },
            })
        })

        // return on prefilled form
        cy.get('[data-context=customer-address-book]').should('not.exist')
        cy.get('[data-context=address-form]').should('be.visible')

        // form
        cy.get('[data-context=form-quote-address]').as('form')

        cy.intercept('PUT', '**/api/erp/v1/quote/1', {
            statusCode: 200,
            body: {
                status: 'success',
                data: { updated: 1 },
            },
        }).as('update_quote')
        cy.get('@form').find('[data-context=submit]').click()
        cy.wait('@update_quote').then((xhr) => {
            expect(xhr.request.body.data.billing_address.company_name).to.eq('')
        })
    })

    it('display a message if no address', () => {
        // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
        // alias the queries and mutations for our tests in a beforeEach
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomer')

            // Default
            if (hasOperationName(req, 'fetchCustomer')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomer')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_account_information_by_pk_without_address.json',
                })
            }
        })

        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=open-address-editor]').eq(0).click()

        cy.get('[data-context=address-book-btn]').click()

        // opens address book
        cy.get('[data-context=customer-address-book]').should('be.visible')
        cy.get('[data-context=customer-address-book-entry]').should('have.length', 0)

        cy.get('[data-context=customer-address-book] [data-context=illustrated-message]')
            .should('be.visible')
            .should('contain', `Aucune adresse enregistrée`)

        cy.get('[data-context=select-address]').should('not.exist')
    })

    describe('Add an address', () => {
        // mock, visit and open the address book
        beforeEach(() => {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                // Queries
                aliasQuery(req, 'fetchCustomer')

                // Default
                if (hasOperationName(req, 'fetchCustomer')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCustomer')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/customer/customer_account_information_by_pk_without_address.json',
                    })
                }
            })
            visitPageWithFixture('erp/quotes/quotation_active.json')
            cy.get('[data-context=open-address-editor]').eq(0).click()
            cy.get('[data-context=address-book-btn]').click()
            cy.get('[data-context=customer-address-book]').should('be.visible')
        })

        it('has a button to open the addition form', () => {
            cy.get('[data-context=create-address-btn]').should('be.visible').click()
            cy.get('[data-context=customer-address-form]').should('be.visible')
        })

        const openAddAddressForm = () => {
            cy.get('[data-context=create-address-btn]').should('be.visible').click()
            cy.get('[data-context=customer-address-form]').should('be.visible').as('form')
        }

        it('has all the needed fields in the form', () => {
            openAddAddressForm()

            cy.get('@form')
                .find('[data-context=name]')
                .should('be.visible')
                .should('contain', 'Nom de l’adresse')
                .find('input')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=country]')
                .should('be.visible')
                .should('contain', 'Pays')
                .find('[data-context=country-autocomplete]')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=title]')
                .should('be.visible')
                .should('contain', 'Civilité')
                .find('select')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=firstname]')
                .should('be.visible')
                .should('contain', 'Prénom')
                .find('input')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=lastname]')
                .should('be.visible')
                .should('contain', 'Nom')
                .find('input')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=company]')
                .should('be.visible')
                .should('contain', 'Société')
                .find('input')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=address]')
                .should('be.visible')
                .should('contain', 'Adresse')
                .find('input')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=postal-code]')
                .should('be.visible')
                .should('contain', 'Code postal')
                .find('input')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=city]')
                .should('be.visible')
                .should('contain', 'Ville')
                .find('input')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=cellphone]')
                .should('be.visible')
                .should('contain', 'N° de mobile')
                .find('input')
                .should('be.visible')
            cy.get('@form')
                .find('[data-context=phone]')
                .should('be.visible')
                .should('contain', 'N° de téléphone')
                .find('input')
                .should('be.visible')
        })

        it('has some action buttons in the form', () => {
            openAddAddressForm()

            cy.get('@form').find('[data-context=submit-btn]').should('be.visible')
            cy.get('@form').find('[data-context=cancel-btn]').should('be.visible')
        })

        it('can cancel the creation', () => {
            openAddAddressForm()

            // cancel button should return to the list
            cy.get('@form').find('[data-context=cancel-btn]').click()
            cy.get('[data-context=customer-address-form]').should('not.exist')
            cy.get('[data-context=create-address-btn]').should('be.visible')
        })

        it('should trigger mandatory fields errors on save', () => {
            openAddAddressForm()

            // empty the country before submit
            cy.get('@form').find('[data-context=country-autocomplete]').multiselect('', 'FRANCE')
            cy.get('@form').find('[data-context=submit-btn]')
            cy.get('@form').find('[data-context=submit-btn]').click()

            cy.get('@form').find('[data-context=name]').should('contain', 'Ce champ est obligatoire')
            cy.get('@form').find('[data-context=country]').should('contain', 'Ce champ est obligatoire')
            cy.get('@form').find('[data-context=firstname]').should('contain', 'Ce champ est obligatoire')
            cy.get('@form').find('[data-context=lastname]').should('contain', 'Ce champ est obligatoire')
            cy.get('@form').find('[data-context=company]').should('not.contain', 'Ce champ est obligatoire')
            cy.get('@form').find('[data-context=address]').should('contain', 'Ce champ est obligatoire')
            cy.get('@form').find('[data-context=postal-code]').should('contain', 'Ce champ est obligatoire')
            cy.get('@form').find('[data-context=city]').should('contain', 'Ce champ est obligatoire')
            cy.get('@form').find('[data-context=cellphone]').should('contain', 'Ce champ est obligatoire')
            cy.get('@form').find('[data-context=phone]').should('not.contain', 'Ce champ est obligatoire')
        })

        it('should submit and return to the list on save', () => {
            openAddAddressForm()
            cy.intercept('POST', '**/api/erp/v1/customer/970481/address', {
                fixture: 'erp/customer/address/post_customer_address_970481.json',
            }).as('api_create_address')

            // fill the form
            cy.get('[data-context=customer-address-form] [data-context=name] input').type('Maison')
            cy.get('[data-context=customer-address-form] [data-context=firstname] input').type('Jean')
            cy.get('[data-context=customer-address-form] [data-context=lastname] input').type('Sérien')
            cy.get('[data-context=customer-address-form] [data-context=company] input').type('Nuka-Cola Corporation')
            cy.get('[data-context=customer-address-form] [data-context=address] input').type('5 bis rue infinie')
            cy.get('[data-context=customer-address-form] [data-context=postal-code] input').type('01210')
            cy.get('[data-context=customer-address-form] [data-context=city] input').type('Nulle Part')
            cy.get('[data-context=customer-address-form] [data-context=cellphone] input').type('+33 6 06 06 06 06')
            cy.get('[data-context=customer-address-form] [data-context=phone] input').type('0123456789')

            // check submitted data
            cy.get('[data-context=customer-address-form] [data-context=submit-btn]').click()
            cy.wait('@api_create_address').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    address: {
                        name: 'Maison',
                        title: 'Mr',
                        firstname: 'Jean',
                        lastname: 'Sérien',
                        company_name: 'Nuka-Cola Corporation',
                        address: '5 bis rue infinie',
                        city: 'Nulle Part',
                        postal_code: '01210',
                        country_code: 'FR',
                        cellphone: '+33 6 06 06 06 06',
                        phone: '0123456789',
                    },
                })
            })

            // form should be closed
            cy.get('[data-context=customer-address-form]').should('not.exist')

            // newly created address should be present and selected
            cy.get('[data-context=customer-address-book] [data-context=customer-address-book-entry]')
                .should('have.length', 1)
                .eq(0)
                .find('input[type=radio]')
                .should('be.checked')
        })

        it('send expected default values on optional fields', () => {
            openAddAddressForm()
            cy.intercept('POST', '**/api/erp/v1/customer/970481/address', {
                fixture: 'erp/customer/address/post_customer_address_970481.json',
            }).as('api_create_address')

            cy.get('[data-context=customer-address-form] [data-context=name] input').type('Maison')
            cy.get('[data-context=customer-address-form] [data-context=firstname] input').type('Jean')
            cy.get('[data-context=customer-address-form] [data-context=lastname] input').type('Sérien')
            cy.get('[data-context=customer-address-form] [data-context=address] input').type('5 bis rue infinie')
            cy.get('[data-context=customer-address-form] [data-context=postal-code] input').type('01210')
            cy.get('[data-context=customer-address-form] [data-context=city] input').type('Nulle Part')
            cy.get('[data-context=customer-address-form] [data-context=cellphone] input').type('+33 6 06 06 06 06')
            cy.get('[data-context=customer-address-form] [data-context=submit-btn]').click()
            cy.wait('@api_create_address').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    address: {
                        name: 'Maison',
                        title: 'Mr',
                        firstname: 'Jean',
                        lastname: 'Sérien',
                        company_name: '', // this is a default value
                        address: '5 bis rue infinie',
                        city: 'Nulle Part',
                        postal_code: '01210',
                        country_code: 'FR',
                        cellphone: '+33 6 06 06 06 06',
                        phone: '', // this is a default value
                    },
                })
            })
        })
    })

    describe('Delete an address', () => {
        // open the address list and mock it
        beforeEach(() => {
            visitPageWithFixture('erp/quotes/quotation_active.json')
            cy.get('[data-context=open-address-editor]').eq(0).click()
            cy.get('[data-context=address-book-btn]').click()
            cy.get('[data-context=customer-address-book]').should('be.visible')
            cy.get('[data-context=customer-address-book] [data-context=customer-address-book-entry]')
                .should('have.length', 2)
                .as('addresses')
        })

        it('can delete an address', () => {
            cy.get('@addresses').eq(1).find('[data-context=delete-btn]').should('be.visible').as('delete-btn')

            // display a confirmation on click
            cy.get('@delete-btn').click()
            cy.get('@addresses').eq(1).find('[data-context=delete-btn]').should('not.exist')
            cy.get('@addresses')
                .eq(1)
                .find('[data-context=confirmation]')
                .should('be.visible')
                .should('contain', 'Confirmez vous la suppression ?')
                .as('confirmation')

            // confirmation has a cancel button
            cy.get('@confirmation').find('[data-context=cancel-btn]').should('be.visible').click()
            cy.get('[data-context=confirmation]').should('not.exist')
            cy.get('@addresses').eq(1).find('[data-context=delete-btn]').should('be.visible').as('delete-btn')

            // confirmation has an approuve button with trigger the deletion
            cy.intercept('DELETE', '**/api/erp/v1/customer/970481/address/1', {
                statusCode: 204,
            }).as('api_delete_address')
            cy.get('@delete-btn').click()
            cy.get('@addresses').eq(1).find('[data-context=confirm-btn]').should('be.visible').click()
            cy.wait('@api_delete_address')
            cy.get('[data-context=customer-address-book] [data-context=customer-address-book-entry]')
                .should('have.length', 1)
                .should('not.contain', 'Bureau')
        })
    })

    describe('Edit an address', () => {
        // open the address list and mock it
        beforeEach(() => {
            visitPageWithFixture('erp/quotes/quotation_active.json')
            cy.get('[data-context=open-address-editor]').eq(0).click()
            cy.get('[data-context=address-book-btn]').click()
            cy.get('[data-context=customer-address-book]').should('be.visible')
            cy.get('[data-context=customer-address-book] [data-context=customer-address-book-entry]')
                .should('have.length', 2)
                .as('addresses')
        })
        it('can edit an address', () => {
            cy.intercept('PUT', '**/api/erp/v1/customer/970481/address/1', {
                fixture: 'erp/customer/address/put_customer_address_970481.json',
            }).as('api_edit_address')

            cy.get('@addresses').eq(1).find('[data-context=address-content]').should('contain', 'PHC HOLDING')

            cy.get('@addresses').eq(1).find('[data-context=edit-btn]').should('be.visible').as('edit-btn')

            cy.get('@edit-btn').click()

            cy.get('[data-context=customer-address-form]').should('be.visible').as('customer-address-form')

            // check if from is completed with the address information
            cy.get('@customer-address-form').find('[data-context=name] input').should('have.value', 'Bureau')

            cy.get('@customer-address-form')
                .find('[data-context=country] [data-context=country-autocomplete]')
                .eq(0)
                .should('contain', 'FRANCE')

            cy.get('@customer-address-form').find('[data-context=title] select').should('have.value', 'Ms')

            // change value
            cy.get('@customer-address-form')
                .find('[data-context=company] input')
                .should('have.value', 'PHC HOLDING')
                .clear()
                .type('son-video.com')

            cy.get('@customer-address-form').find('[data-context=submit-btn]').click()

            cy.wait('@api_edit_address').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    address: {
                        name: 'Bureau',
                        title: 'Ms',
                        company_name: 'son-video.com',
                        firstname: 'Guy',
                        lastname: 'Liguili',
                        address: '16 boulevard charles de gaulle',
                        postal_code: '92500',
                        city: 'CHAMPIGNY',
                        cellphone: '0707070707',
                        phone: '0102030405',
                        created_at: '2019-08-08 16:57:08',
                        country_code: 'FR',
                    },
                })
            })

            // form should be closed
            cy.get('[data-context=customer-address-form]').should('not.exist')

            // check if country has changed
            cy.get('[data-context=customer-address-book] [data-context=customer-address-book-entry]')
                .eq(1)
                .find('[data-context=address-content]')
                .should('contain', 'son-video.com')
        })
        it(`can't edit an address`, () => {
            cy.intercept('PUT', '**/api/erp/v1/customer/970481/address/1', {
                statusCode: 500,
            }).as('api_edit_address')

            cy.get('@addresses').eq(1).find('[data-context=edit-btn]').should('be.visible').click()

            cy.get('[data-context=customer-address-form]').find('[data-context=submit-btn]').click()

            cy.wait('@api_edit_address')

            cy.toast(`Une erreur est survenue`, 'danger')

            // form should not be closed
            cy.get('[data-context=customer-address-form]').should('exist')
        })
        it('Address suggestion', () => {
            cy.intercept('PUT', '**/api/erp/v1/customer/970481/address/1', {
                fixture: 'erp/customer/address/put_customer_address_970481.json',
            }).as('api_edit_address')
            cy.intercept('GET', '**/api-adresse.data.gouv.fr/search/**', {
                fixture: 'erp/housenumber/search.json',
            })

            cy.get('@addresses').eq(1).find('[data-context=address-content]').should('contain', 'PHC HOLDING')
            cy.get('@addresses').eq(1).find('[data-context=edit-btn]').should('be.visible').as('edit-btn')
            cy.get('@edit-btn').click()

            cy.get('[data-context=erp-input-suggestion]').as('addressSuggestion')
            cy.get('@addressSuggestion').find('input').should('exist').clear().type('5 rue')
            cy.get('@addressSuggestion').find('[data-context=erp-input-suggestion-options]').should('exist')
            cy.get('@addressSuggestion').find('ul li').eq(4).click()

            cy.get('[data-context=address]').find('input').should('have.value', '5 Rue plus loins')
            cy.get('[data-context=postal-code]').find('input').should('have.value', 22220)
            cy.get('[data-context=city]').find('input').should('have.value', 'Minihy-Tréguier')
        })
    })
})
