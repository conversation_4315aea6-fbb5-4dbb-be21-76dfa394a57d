import { visitPageWithFixture } from './helpers'
import { QUOTE_SHIPMENT_METHOD_CUSTOMIZE, QUOTE_WRITE } from '../../../../../src/apps/erp/permissions'

describe('Quote - Shipment methods', () => {
    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v1/quote/1/shipment-methods', {
            fixture: 'erp/quotes/shipment-method/quote_shipment_methods.json',
        }).as('get_shipment_methods')
    })

    describe('With permission', () => {
        beforeEach(() => {
            cy.mockErpUser([QUOTE_WRITE])
        })

        it('Can get eligible shipment methods', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

            cy.get('[data-context=shipment-method] [data-context=erp-button]')
                .should('contain', 'Sélectionner un mode de transport')
                .click()

            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-method-selector]').as('panel').should('be.visible')

            cy.get('@panel').find('[data-context=page-header]').should('contain', 'Sélection du transport')

            cy.get('@panel').find('[data-context=shipment-method]').should('have.length', 6).eq(4).as('shipment_method')

            cy.get('@shipment_method')
                .find('[data-context=shipment-method-label]')
                .should('contain', 'Chronopost - Chronopost livraison avant 13H')
            cy.get('@shipment_method')
                .find('[data-context=shipment-method-description]')
                .should('contain', 'Livraison J1 garantie partout en france.')
            cy.get('@shipment_method').find('[data-context=shipment-method-cost]').should('contain', '14,90 €')

            // first on list is selected by default
            cy.get('@panel')
                .find('[data-context=shipment-method]')
                .eq(0)
                .find('[data-context=shipment-method-radio-btn]')
                .should('be.checked')
        })

        it('Can choose from eligible shipment methods', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

            cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: { updated: 1 },
                },
            }).as('api_put')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()
            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-method-selector]').find('[data-context=shipment-method]').eq(5).click()
            cy.get('[data-context=shipment-method-selector]').find('[data-context=submit-btn]').click()

            cy.wait('@api_put').then((xhr) => {
                expect(xhr.request.body.data).to.deep.eq({
                    shipment_method: {
                        shipment_method_id: 68,
                        label: 'Chrono Precise RDV',
                        comment: 'Livraisons aux particuliers sur prise de RDV',
                        carrier_name: 'Chronopost',
                        is_retail_store: false,
                        cost: 19.9,
                    },
                })
            })
            cy.wait('@cpost_quotes')
        })

        it('Display the shipment weight', () => {
            visitPageWithFixture('erp/quotes/quotation_with_weight.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1044342/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 35,
                                label: 'Express',
                                comment: 'France Express - 120 kg sur rendez-vous',
                                carrier_name: 'France Express',
                                is_retail_store: false,
                                cost: 0,
                            },
                        ],
                    },
                },
            }).as('get_shipment_methods')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()
            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-weight]').should('contain', '686,20 kg')
        })

        it("Doesn't display the shipment weight if it's not set", () => {
            visitPageWithFixture('erp/quotes/quotation_without_weight.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1044342/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 35,
                                label: 'Express',
                                comment: 'France Express - 120 kg sur rendez-vous',
                                carrier_name: 'France Express',
                                is_retail_store: false,
                                cost: 0,
                            },
                        ],
                    },
                },
            }).as('get_shipment_methods')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()
            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-weight]').should('not.exist')
        })

        it('Display the shipment weight', () => {
            visitPageWithFixture('erp/quotes/quotation_with_weight.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1044342/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 35,
                                label: 'Express',
                                comment: 'France Express - 120 kg sur rendez-vous',
                                carrier_name: 'France Express',
                                is_retail_store: false,
                                cost: 0,
                            },
                        ],
                    },
                },
            }).as('get_shipment_methods')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()
            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-weight]').should('contain', '686,20 kg')
        })

        it("Doesn't display the shipment weight if it's not set", () => {
            visitPageWithFixture('erp/quotes/quotation_without_weight.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1044342/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 35,
                                label: 'Express',
                                comment: 'France Express - 120 kg sur rendez-vous',
                                carrier_name: 'France Express',
                                is_retail_store: false,
                                cost: 0,
                            },
                        ],
                    },
                },
            }).as('get_shipment_methods')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()
            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-weight]').should('not.exist')
        })

        it('Display outsize product warning', () => {
            visitPageWithFixture('erp/quotes/quotation_outsize.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1044344/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 35,
                                label: 'Express',
                                comment: 'France Express - 120 kg sur rendez-vous',
                                carrier_name: 'France Express',
                                is_retail_store: false,
                                cost: 0,
                            },
                        ],
                    },
                },
            }).as('get_shipment_methods')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()
            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-outsize]').should('have.text', 'Hors gabarit')
        })

        it("Doesn't display outsize product warning when not applicable", () => {
            visitPageWithFixture('erp/quotes/quotation_not_outsize.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1044344/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 65,
                                label: 'FlexDeliveryService',
                                comment: "Livraisons aux particuliers en France et sur certains pays d'Europe",
                                carrier_name: 'GLS',
                                is_retail_store: false,
                                cost: 6.9,
                            },
                        ],
                    },
                },
            }).as('get_shipment_methods')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()
            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-outsize]').should('not.exist')
        })

        it('Can cancel', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()
            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-method-selector] [data-context=cancel-btn]')
                .should('contain', 'Annuler')
                .click()

            cy.get('[data-context=shipment-method-selector]').should('not.exist')
        })

        const checkNoShipmentMessage = () => {
            cy.intercept('POST', '**/api/erp/v1/shipment-methods').as('cpost_shipment_methods')
            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()
            cy.wait('@get_shipment_methods')

            cy.get('[data-context=shipment-method-selector]').as('shipment-method-selector')

            cy.get('@shipment-method-selector').find('[data-context=shipment-method]').should('not.exist')
            cy.get('@shipment-method-selector').find('[data-context=submit-btn]').should('not.exist')

            cy.get('@shipment-method-selector').contains('div', `Aucun mode de transport éligible`)

            cy.get('@shipment-method-selector').find('[data-context=alert-content]').should('be.visible')
            cy.get('@cpost_shipment_methods.all').should('have.length', 0)
        }

        it('displays an alert if no eligible shipment method', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: { shipment_methods: [] },
                },
            }).as('get_shipment_methods')

            checkNoShipmentMessage()
        })

        it('display an alert when shipment method is cotation', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 99,
                                label: 'Cotation',
                                comment: 'Cotation de transport',
                                carrier_name: 'Indéfini',
                                is_retail_store: false,
                                cost: 0,
                            },
                        ],
                    },
                },
            }).as('get_shipment_methods')

            checkNoShipmentMessage()
        })

        describe('Offer shipping costs', () => {
            it('should display an option to offer shipping costs', () => {
                visitPageWithFixture('erp/quotes/quotation_active.json')
                cy.get('[data-context=page-header] [data-context=quote-actions]').click()

                // has an option
                cy.get('[data-context=dropdown-menu-item] [data-context=offer-shipping-costs-btn]')
                    .should('contain', 'Offrir les frais de port')
                    .as('offer-shipping-costs-btn')

                // option click to the job
                cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: { updated: 1 },
                    },
                }).as('api_update_quote')
                cy.get('@offer-shipping-costs-btn').click()
                cy.wait('@api_update_quote').then((xhr) => {
                    expect(xhr.request.body.data.shipment_method.cost).to.eq(0)
                    expect(xhr.request.body.data.shipment_method.initial_cost).to.eq(12.9)
                })
            })

            it('should disable the option if has no shipment method yet', () => {
                visitPageWithFixture('erp/quotes/quotation_active_with_address.json')
                cy.get('[data-context=page-header] [data-context=quote-actions]').click()

                cy.get('[data-context=dropdown-menu-item] [data-context=offer-shipping-costs-btn]').should(
                    'have.class',
                    'cursor-not-allowed',
                )
            })

            it('should disable the option if shipment method cost is already free (=0)', () => {
                visitPageWithFixture('erp/quotes/quotation_active_with_retail_store_address.json')
                cy.get('[data-context=page-header] [data-context=quote-actions]').click()

                cy.get('[data-context=dropdown-menu-item] [data-context=offer-shipping-costs-btn]').should(
                    'have.class',
                    'cursor-not-allowed',
                )
            })

            it('should disable the option if quote is locked', () => {
                visitPageWithFixture('erp/quotes/quotation_with_shipment2_locked.json')
                cy.get('[data-context=page-header] [data-context=quote-actions]').click()

                cy.get('[data-context=dropdown-menu-item] [data-context=offer-shipping-costs-btn]').should(
                    'have.class',
                    'cursor-not-allowed',
                )
            })
        })
    })

    describe('With shipment customization permission', () => {
        beforeEach(() => {
            cy.mockErpUser([QUOTE_WRITE, QUOTE_SHIPMENT_METHOD_CUSTOMIZE])
        })

        it('display quotation shipment methods', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 99,
                                label: 'Cotation',
                                comment: 'Cotation de transport',
                                carrier_name: 'Indéfini',
                                is_retail_store: false,
                                cost: 0,
                            },
                        ],
                    },
                },
            }).as('get_shipment_methods')
            cy.intercept('POST', '**/api/erp/v1/shipment-methods', {
                fixture: 'erp/shipment-methods/cpost_shipment_methods_quotation.json',
            }).as('cpost_shipment_methods')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()

            cy.wait('@get_shipment_methods')
            cy.wait('@cpost_shipment_methods').then((xhr) => {
                expect(xhr.request.body).to.deep.equals({
                    where: { tags: { _like: '%"quotation"%' } },
                    included_dependencies: ['carrier'],
                })
            })

            cy.get('[data-context=shipment-method-selector]').as('shipment-method-selector')

            cy.get('@shipment-method-selector').should('be.visible')
            cy.get('@shipment-method-selector')
                .find('[data-context=page-header]')
                .should('contain', 'Sélection du transport')
            cy.get('@shipment-method-selector').find('[data-context=shipment-method]').as('shipment-methods')
            cy.get('@shipment-methods').should('have.length', 4)
            cy.get('@shipment-methods').eq(0).as('shipment-method')

            cy.get('@shipment-method')
                .find('[data-context=shipment-method-label]')
                .should('contain', 'DHL - DHL Express 24h')
            cy.get('@shipment-method')
                .find('[data-context=shipment-method-description]')
                .should('contain', 'Livraison entre 1 et 5 jours (selon le pays) partout dans le monde')
            cy.get('@shipment-method')
                .find('[data-context=shipment-method-cost-input]')
                .should('be.visible')
                .should('not.be.disabled')
                .should('have.value', '0')

            cy.get('@shipment-methods').eq(1).find('[data-context=shipment-method-cost-input]').should('be.disabled')

            cy.get('@shipment-method-selector').find('[data-context=submit-btn]').should('be.visible')
            cy.get('@shipment-method-selector').find('[data-context=illustrated-message]').should('not.exist')
            cy.get('@shipment-method-selector').find('[data-context=alert-content]').should('not.exist')
        })

        it('choose quotation shipment methods', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

            cy.intercept('GET', '**/api/erp/v1/quote/1/shipment-methods', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 99,
                                label: 'Cotation',
                                comment: 'Cotation de transport',
                                carrier_name: 'Indéfini',
                                is_retail_store: false,
                                cost: 0,
                            },
                        ],
                    },
                },
            }).as('get_shipment_methods')
            cy.intercept('POST', '**/api/erp/v1/shipment-methods', {
                fixture: 'erp/shipment-methods/cpost_shipment_methods_quotation.json',
            }).as('cpost_shipment_methods')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').click()

            cy.wait('@get_shipment_methods')
            cy.wait('@cpost_shipment_methods')

            cy.get('[data-context=shipment-method-selector]').as('shipment-method-selector')
            cy.get('@shipment-method-selector').should('be.visible')

            cy.get('@shipment-method-selector').find('[data-context=shipment-method]').eq(1).as('shipment-method')
            cy.get('@shipment-method').click()
            cy.get('@shipment-method').find('[data-context=shipment-method-cost-input]').type('12.34')

            cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: { updated: 1 },
                },
            }).as('api_put')
            cy.get('@shipment-method-selector').find('[data-context=submit-btn]').click()

            cy.wait('@api_put').then((xhr) => {
                expect(xhr.request.body.data).to.deep.eq({
                    shipment_method: {
                        shipment_method_id: 13,
                        label: 'Express',
                        comment: 'Livraison pour Ile de France',
                        carrier_name: 'Novea',
                        is_retail_store: false,
                        is_quotation: true,
                        cost: 12.34,
                    },
                })
            })
            cy.wait('@cpost_quotes')
        })
    })

    describe('Without permission', () => {
        beforeEach(() => {
            cy.mockErpUser()
        })

        it('Cannot select a shipment method', () => {
            visitPageWithFixture('erp/quotes/quotation_active_with_address.json')

            cy.get('[data-context=shipment-method] [data-context=erp-button]').should('be.disabled')
        })

        it('should hide the option to offer shipping costs', () => {
            visitPageWithFixture('erp/quotes/quotation_active.json')
            cy.get('[data-context=page-header] [data-context=quote-actions]').should('not.exist')
        })
    })
})
