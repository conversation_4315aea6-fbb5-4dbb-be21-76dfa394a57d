import { visitPageWithFixture } from './helpers'
import { QUOTE_DISCOUNT_ADMIN, QUOTE_WRITE } from '../../../../../src/apps/erp/permissions.js'
import { getMagicSearchArticles, getMagicSearchOneArticle, getMagicSearchResponse } from '../../magic_search/helper'

describe('Quote - Type quotation', () => {
    const CDN_PREFIX = Cypress.env('cdn_static_images')

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    it('Check table headers', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=erp-table]').as('table')

        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 11)
        cy.get('@header').eq(1).should('contain', 'Article')
        cy.get('@header').eq(2).should('contain', '')
        cy.get('@header').eq(3).should('contain', 'Stock')
        cy.get('@header').eq(4).should('contain', 'Quantité')
        cy.get('@header').eq(5).should('contain', 'Prix Un. TTC')
        cy.get('@header').eq(6).should('contain', 'Remise Un. TTC')
        cy.get('@header').eq(7).should('contain', 'Remise %')
        cy.get('@header').eq(8).should('contain', 'Remise Totale')
        cy.get('@header').eq(9).should('contain', 'Prix TTC')
    })

    describe('Without permission', () => {
        it('Check header actions', () => {
            visitPageWithFixture('erp/quotes/quotation_active.json')

            cy.get('[data-context=valid-until]').as('valid-until')
            cy.get('@valid-until').find('[data-context=erp-multiselect] input').should('be.disabled')
            cy.get('@valid-until').tooltip("Vous n'avez pas la permission de modifier")

            cy.get('[data-context=transfer-button]').as('transfer-button')
            cy.get('@transfer-button').should('be.disabled')
            cy.get('@transfer-button').parent().tooltip("Vous n'avez pas la permission de modifier")
        })

        it('Check a quote line product', () => {
            visitPageWithFixture('erp/quotes/quotation_active.json')

            cy.intercept('GET', '**/api/erp/v1/article-stock/EPH100', {
                fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
            }).as('get_article_stock')

            cy.get('[data-context=erp-table]').as('table')

            cy.get('[data-context=table-row-product]').should('have.length', 2).eq(0).as('first_row')

            cy.get('@first_row').find('[data-context=cell-article]').as('cell_article')

            cy.get('@cell_article').find('[data-context=name]').should('contain', `Yam eph100`)

            cy.get('@cell_article').find('[data-context=article-buyers-availability-button]').click()

            cy.get('[data-context=article-buyers-availability]').should('be.visible')

            cy.wait('@get_article_stock')

            cy.get(
                '[data-context=article-buyers-availability] [data-context=stock-move-filtered-list-safety-stock-threshold]',
            ).should('not.exist')

            cy.get('[data-context=slide-out-container-close-btn]').click()

            cy.get('@cell_article')
                .find('[data-context=sku]')
                .should('contain', 'EPH100')
                .should('have.attr', 'target', '_blank')

            // dispo
            cy.get('@cell_article')
                .find('[data-context=erp-article-item] [data-context=status-delay-badge]')
                .should('contain', 'OUI')
                .should('contain', '3 à 5 jours')

            cy.get('@cell_article')
                .find('[data-context=image]')
                .should(($img) => {
                    expect($img)
                        .attr('src')
                        .to.match(
                            /.*\/images\/dynamic\/Enceintes\/articles\/Bose\/BOSESLCOLIINR\/Bose-SoundLink-Color-II-Noir_P_300_square.jpg/,
                        )
                })

            cy.get('@first_row')
                .find('[data-context=cell-promo-budget]')
                .find('[data-context=alert-warn]')
                .should('contain', 'Un budget promo est en cours. Activez le mode expert pour plus de détails.')

            cy.get('@first_row').find('[data-context=cell-stock]').should('contain', '1')

            cy.get('@first_row')
                .find('[data-context=cell-quantity] input')
                .should('have.value', '2')
                .should('be.disabled')

            cy.get('@first_row')
                .find('[data-context=cell-unit-selling-price-tax-included]')
                .should('contain', '99,00 €')

            cy.get('@first_row')
                .find('[data-context=cell-unit-discount-tax-included] input')
                .should('have.value', '10')
                .should('be.disabled')

            cy.get('@first_row')
                .find('[data-context=cell-discount-percent] input')
                .should('have.value', '10.1')
                .should('be.disabled')

            cy.get('@first_row').find('[data-context=cell-discount-total]').should('contain', '20,00 €')

            cy.get('@first_row').find('[data-context=cell-selling-price-tax-included]').should('contain', '178,00 €')

            cy.get('@cell_article').find('[data-context=group_brand]').should('contain', 'AVI')

            cy.get('[data-context=table-row-product]').eq(1).as('last_row')

            cy.get('@last_row').find('[data-context=cell-article]').as('cell_article')

            cy.get('@cell_article').find('[data-context=name]').should('contain', `Yamaha eph101`)

            cy.get('@cell_article').find('[data-context=status-delay-badge] svg.fa-shopping-cart').should('exist')

            cy.get('[data-context=add-product-or-section]').should('not.exist')
        })
    })

    describe('With permission', () => {
        beforeEach(() => {
            cy.mockErpUser([QUOTE_WRITE])
        })

        describe('Quotation is locked', () => {
            it('Check header actions', () => {
                visitPageWithFixture('erp/quotes/quotation_locked.json')

                cy.get('[data-context=valid-until]').as('valid-until')
                cy.get('@valid-until').find('[data-context=erp-multiselect] input').should('be.disabled')
                cy.get('@valid-until').tooltip('Action non autorisée, le devis est verrouillé')

                cy.get('[data-context=transfer-button]').as('transfer-button')
                cy.get('@transfer-button').should('be.disabled')
                cy.get('@transfer-button').parent().tooltip('Action non autorisée, le devis est verrouillé')

                cy.get('[data-context=drag-and-drop-button]').should('be.disabled')
            })

            it('Check a quote line product', () => {
                visitPageWithFixture('erp/quotes/quotation_locked.json')

                cy.get('[data-context=erp-table]').as('table')

                cy.get('[data-context=table-row-product]').should('have.length', 2).eq(0).as('first_row')

                cy.get('@first_row').find('[data-context=cell-article]').as('cell_article')

                cy.get('@cell_article').find('[data-context=name]').should('contain', `Yam eph100`)

                cy.get('@cell_article')
                    .find('[data-context=sku]')
                    .should('contain', 'EPH100')
                    .should('have.attr', 'target', '_blank')

                // dispo
                cy.get('@cell_article')
                    .find('[data-context=erp-article-item] [data-context=status-delay-badge]')
                    .should('contain', 'OUI')
                    .should('contain', '3 à 5 jours')

                cy.get('@cell_article')
                    .find('[data-context=image]')
                    .should(($img) => {
                        expect($img)
                            .attr('src')
                            .to.match(
                                /.*\/images\/dynamic\/Enceintes\/articles\/Bose\/BOSESLCOLIINR\/Bose-SoundLink-Color-II-Noir_P_300_square.jpg/,
                            )
                    })

                cy.get('@first_row').find('[data-context=cell-stock]').should('contain', '1')

                cy.get('@first_row')
                    .find('[data-context=cell-quantity] input')
                    .should('have.value', '2')
                    .should('be.disabled')

                cy.get('@first_row')
                    .find('[data-context=cell-unit-selling-price-tax-included]')
                    .should('contain', '99,00 €')

                cy.get('@first_row')
                    .find('[data-context=cell-unit-discount-tax-included] input')
                    .should('have.value', '10')
                    .should('be.disabled')

                cy.get('@first_row')
                    .find('[data-context=cell-discount-percent] input')
                    .should('have.value', '10.1')
                    .should('be.disabled')

                cy.get('@first_row').find('[data-context=cell-discount-total]').should('contain', '20,00 €')

                cy.get('@first_row')
                    .find('[data-context=cell-selling-price-tax-included]')
                    .should('contain', '178,00 €')

                cy.get('[data-context=add-product-or-section]').should('not.exist')
            })
        })

        describe('Quotation do not belong to the user', () => {
            it('Check header actions', () => {
                visitPageWithFixture('erp/quotes/quotation_active.json')

                cy.get('[data-context=transfer-button]').as('transfer-button')
                cy.get('@transfer-button').should('be.disabled')
                cy.get('@transfer-button').parent().tooltip('Seul le créateur peut transférer')
            })
        })

        describe('Quotation do belong to the user', () => {
            const visitPageForTransfer = (status) => {
                cy.fixture('erp/quotes/quotation_active.json').then((payload) => {
                    payload.data.quotes[0].created_by = 120
                    payload.data.quotes[0].created_by_name = 'Anakin Skywalker'
                    payload.data.quotes[0].status = status

                    cy.intercept('POST', '**/api/erp/v1/quotes', {
                        statusCode: 200,
                        body: payload,
                    }).as('cpost_quotes')

                    cy.visit('/quote/1')
                    cy.wait('@cpost_quotes')
                })
            }

            describe('Offer has been sent', () => {
                it('Check header actions', () => {
                    visitPageForTransfer('sent')

                    cy.get('[data-context=valid-until]').as('valid-until')
                    cy.get('@valid-until').find('[data-context=erp-multiselect] input').should('be.disabled')
                    cy.get('@valid-until').tooltip("L'offre a déjà été envoyée")

                    cy.get('[data-context=transfer-button]').as('transfer-button')
                    cy.get('@transfer-button').should('be.disabled')
                    cy.get('@transfer-button').parent().tooltip("L'offre a déjà été envoyée")
                })
            })
            describe('Offer is inactive', () => {
                it('Check header actions', () => {
                    visitPageForTransfer('inactive')

                    cy.get('[data-context=valid-until]').as('valid-until')
                    cy.get('@valid-until').find('[data-context=erp-multiselect] input').should('not.be.disabled')
                    cy.get('@valid-until').trigger('mouseenter')
                    cy.get('[role=tooltip]').should('not.exist')

                    cy.get('[data-context=transfer-button]').as('transfer-button')
                    cy.get('@transfer-button').should('not.be.disabled')
                    cy.get('@transfer-button').parent().tooltip('Transférer')
                })

                it('Check transfer', () => {
                    visitPageForTransfer('inactive')

                    cy.get('[data-context=transfer-button]').click()

                    cy.get('[data-context=transfer-quote]').as('transfer')

                    cy.get('@transfer').should('be.visible')
                    cy.get('@transfer')
                        .find('[data-context=title]')
                        .should('contain', 'Transférer le devis à un autre utilisateur')

                    cy.intercept('POST', '**/api/erp/v1/accounts', {
                        fixture: 'erp/account/accounts_for_erp_autocomplete.json',
                    }).as('accounts_request')

                    cy.get('@transfer')
                        .find('[data-context=erp-account-autocomplete]')
                        .multiselect('lau', 'Magasin Nantes - Lauriane PROVENZANO')
                    cy.wait('@accounts_request').then((xhr) => {
                        expect(xhr.request.body).to.deep.equals({
                            where: {
                                _or: {
                                    first_name: {
                                        _ilike: '%lau%',
                                    },
                                    last_name: {
                                        _ilike: '%lau%',
                                    },
                                    email: {
                                        _ilike: '%lau%',
                                    },
                                },
                                is_active: {
                                    _eq: 'active',
                                },
                                seller_commission_role: {
                                    _null: false,
                                },
                            },
                            order_by: 'first_name ASC, last_name ASC',
                            limit: 10,
                            included_dependencies: [],
                        })
                    })

                    cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                        statusCode: 200,
                        body: {
                            status: 'success',
                            data: { updated: 1 },
                        },
                    }).as('update_quote')
                    cy.get('@transfer').find('[data-context=submit-btn]').click()
                    cy.wait('@update_quote').then((xhr) => {
                        expect(xhr.request.body.data.created_by).to.eq(1424)
                    })

                    cy.wait('@cpost_quotes')
                })

                it('Check validity', () => {
                    visitPageForTransfer('inactive')
                    cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                        statusCode: 200,
                        body: {
                            status: 'success',
                            data: { updated: 1 },
                        },
                    }).as('update_quote')

                    cy.get('[data-context=valid-until] [data-context=erp-multiselect]').as('valid-until')
                    cy.get('@valid-until').erpMultiselect('', '18')

                    cy.wait('@update_quote').then((xhr) => {
                        expect(xhr.request.body.data.valid_until).to.eq(18)
                    })

                    cy.wait('@cpost_quotes')
                })
            })
        })

        describe('Add a quote line (product or section)', () => {
            const visitPageForAddingProduct = () => {
                cy.fixture('erp/quotes/quotation_active2.json').then((payload) => {
                    payload.data.quotes[0].quote_line_aggregates.pop()

                    cy.intercept('POST', '**/api/erp/v1/quotes', {
                        statusCode: 200,
                        body: payload,
                    }).as('cpost_quotes')

                    cy.visit('/quote/1')
                    cy.wait('@cpost_quotes')

                    cy.get('[data-context=erp-table]').as('table')

                    cy.get('[data-context=table-row-product]').should('have.length', 3)
                })
            }

            it('Check search input results', () => {
                visitPageForAddingProduct()

                cy.get('[data-context=add-product-or-section] [data-context=search-input]').should('have.value', '')
                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('not.exist')

                // Not enough chars
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('MO')
                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('not.exist')

                cy.intercept('GET', '**/api/erp/v1/magic-search**', {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: {
                            articles: {
                                results: [],
                                total: 0,
                            },
                        },
                    },
                }).as('get_magic_search')

                // No results found
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').clear()
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('DUMMY')

                cy.wait('@get_magic_search').then((xhr) => {
                    // check data sent to server
                    const url = new URL(xhr.request.url)

                    expect(url.searchParams.get('size')).to.eq('12')
                    expect(url.searchParams.get('context')).to.eq('articles')
                    expect(url.searchParams.get('from')).to.eq('0')
                    expect(url.searchParams.get('search_terms')).to.eq('DUMMY')
                })

                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('be.visible')
                cy.get(
                    '[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]',
                ).should('not.exist')
                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=no-result]')
                    .should('be.visible')
                    .should('contain', 'Aucun résultat trouvé')

                cy.intercept('GET', '**/api/erp/v1/magic-search**', {
                    statusCode: 200,
                    body: getMagicSearchResponse(getMagicSearchArticles(16), { from: 5, size: 15 }),
                }).as('get_magic_search')

                // X articles found
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').clear()
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('test')

                cy.wait('@get_magic_search').then((xhr) => {
                    // check data sent to server
                    const url = new URL(xhr.request.url)

                    expect(url.searchParams.get('size')).to.eq('12')
                    expect(url.searchParams.get('context')).to.eq('articles')
                    expect(url.searchParams.get('from')).to.eq('0')
                    expect(url.searchParams.get('search_terms')).to.eq('test')
                })

                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('be.visible')
                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                    .should('be.visible')
                    .should('have.length', 5)

                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                    .eq(0)
                    .find('[data-context=sku]')
                    .should('contain', 'ELTAXITEM3513')

                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                    .eq(0)
                    .find('[data-context=name]')
                    .should('contain', 'Eltax Boomer ELTAX ITEM 3513')

                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                    .eq(0)
                    .find('[data-context=price]')
                    .should('contain', '40,00 €')

                cy.get(
                    '[data-context=add-product-or-section] [data-context=suggestions] [data-context=no-result]',
                ).should('not.exist')
            })

            it('Add a product successfully', () => {
                visitPageForAddingProduct()

                cy.intercept('GET', '**/api/erp/v1/magic-search**', {
                    statusCode: 200,
                    body: getMagicSearchResponse(getMagicSearchArticles(16), { from: 5, size: 15 }),
                }).as('get_magic_search')

                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('test')
                cy.wait('@get_magic_search')

                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('be.visible')
                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                    .should('be.visible')
                    .should('have.length', 5)

                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                    .eq(1)
                    .find('[data-context=sku]')
                    .should('contain', 'SONOSARCULTRANR')

                cy.get(
                    '[data-context=add-product-or-section] [data-context=suggestions] [data-context=no-result]',
                ).should('not.exist')

                // add product
                cy.fixture('erp/quotes/quotation_active2.json').then((payload) => {
                    // form original fixture product list
                    cy.intercept('POST', '**/api/erp/v1/quote/1/quote-line-product', {
                        statusCode: 200,
                        body: { data: { quote_lines: payload.data.quotes[0].quote_line_aggregates } },
                    }).as('post_quote_lines')
                })

                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                    .eq(1)
                    .click()

                // check data sent to server
                cy.wait('@post_quote_lines').then((xhr) => {
                    expect(xhr.request.body.sku).to.eq('SONOSARCULTRANR')
                })

                // refresh data
                cy.wait('@cpost_quotes')
            })

            it('Cannot add a product not eligible for quote because of its status', () => {
                visitPageForAddingProduct()

                cy.intercept('GET', '**/api/erp/v1/magic-search**', {
                    statusCode: 200,
                    body: getMagicSearchResponse(getMagicSearchArticles(16), { from: 5, size: 15 }),
                }).as('get_magic_search')

                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('test')
                cy.wait('@get_magic_search')

                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                    .eq(0)
                    .should('have.class', 'cursor-not-allowed')
            })

            it('Display specific error if you try to add product not eligible', () => {
                cy.intercept('GET', '**/api/erp/v1/quote/1/events?type=comment', {
                    body: {
                        data: {
                            system_events: [],
                        },
                    },
                }).as('events')

                visitPageForAddingProduct()
                cy.wait(['@events'])

                cy.intercept('GET', '**/api/erp/v1/magic-search**', {
                    statusCode: 200,
                    body: getMagicSearchResponse(getMagicSearchArticles(16), { from: 5, size: 15 }),
                }).as('get_magic_search')

                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('test')

                cy.wait('@get_magic_search')

                // add product
                cy.fixture('erp/quotes/quotation_active2.json').then((payload) => {
                    // form original fixture product list
                    cy.intercept('POST', '**/api/erp/v1/quote/1/quote-line-product', {
                        statusCode: 400,
                        body: {
                            status: 'error',
                            code: 1017,
                            message: 'An error occurred while attempting to add the sku "SONOSARCULTRANR" to the quote',
                            data: {},
                        },
                    }).as('post_quote_lines')
                })

                cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                    .eq(1)
                    .click()

                cy.wait('@post_quote_lines')

                // error message
                cy.toast(
                    `Ajout impossible, le produit ou un des produits du composé n'a pas le bon statut ou a un PVGC égal à 0€`,
                    'danger',
                )
            })
        })

        describe('Remove a quote line (product or section)', () => {
            it('Remove quote line product', () => {
                visitPageWithFixture('erp/quotes/quotation_active.json')

                cy.intercept('DELETE', '**/api/erp/v1/quote/1/quote-line/1', {
                    statusCode: 204,
                }).as('delete_quote_line')

                cy.get('[data-context=table-row-product]')
                    .eq(0)
                    .find('[data-context=cell-actions]')
                    .find('[data-context=erp-button]')
                    .click()

                cy.wait('@delete_quote_line')

                cy.wait('@cpost_quotes')
            })
        })

        describe('Updates a quote line product', () => {
            it('updates quantity on a quotation', () => {
                visitPageWithFixture('erp/quotes/quotation_active.json')

                cy.intercept('PUT', '**/api/erp/v1/quote/1/quote-line-product/1', {
                    statusCode: 200,
                }).as('edit_quantity_success')

                // check no api call after loosing focus without update
                cy.get('[data-context=table-row-product]')
                    .eq(0)
                    .find('[data-context=cell-quantity] input')
                    .as('quantity_input')
                    .should('have.value', '2')
                    .should('not.have.class', 'ring-red-500')
                    .clear()
                    .type('2')
                    .blur()

                cy.wait(200)

                // just to calculate how many api calls have been made in order to check none happened
                cy.get('@edit_quantity_success.all').then((interceptions) => {
                    expect(interceptions).to.have.length(0)
                })

                // check if trying to type alpha characters
                cy.get('@quantity_input').clear().type('3A').should('have.class', 'ring-red-500')

                // check if trying to type negative numeric value
                cy.get('@quantity_input').clear().type('-3').should('have.class', 'ring-red-500')

                // check that correct value is sent after bluring
                cy.get('@quantity_input').clear().type('4').blur()

                // and api calls
                cy.wait('@edit_quantity_success').then((xhr) => {
                    expect(xhr.request.body.data.quantity).to.eq(4)
                })

                // to refresh data
                cy.wait('@cpost_quotes')

                // check that correct value is sent after typing enter
                cy.get('@quantity_input').should('not.be.disabled')
                cy.get('@quantity_input').clear()
                cy.get('@quantity_input').type('3{enter}')

                // and api calls
                cy.wait('@edit_quantity_success').then((xhr) => {
                    expect(xhr.request.body.data.quantity).to.eq(3)
                })
                // to refresh data
                cy.wait('@cpost_quotes')
            })

            it('updates unit discount amount on a quotation without admin permission', () => {
                visitPageWithFixture('erp/quotes/quotation_active.json')

                cy.intercept('PUT', '**/api/erp/v1/quote/1/quote-line-product/1', {
                    statusCode: 200,
                }).as('edit_discount_success')

                // // check no api call after loosing focus without update
                cy.get('[data-context=table-row-product]')
                    .eq(0)
                    .find('[data-context=cell-unit-discount-tax-included] input')
                    .as('unit_discount_input')
                    .should('have.value', '10')
                    .should('not.have.class', 'ring-red-500')
                    .clear()
                    .type('10')
                    .blur()
                cy.wait(200)
                // just to calculate how many api calls have been made in order to check none happened
                cy.get('@edit_discount_success.all').then((interceptions) => {
                    expect(interceptions).to.have.length(0)
                })

                cy.get(`[data-context=skeleton][data-status=active]`).should('not.exist')

                // check if trying to type alpha characters
                cy.get('@unit_discount_input').clear().type('3A').should('have.class', 'ring-red-500')

                // check if trying to type negative numeric value
                cy.get('@unit_discount_input').clear().type('-3').should('have.class', 'ring-red-500')

                // check we cannot update with a markup rate < 0.05 (discount amount >= 20.71)
                cy.get('@unit_discount_input').clear().type('20.72').should('have.class', 'ring-red-500')

                // check that correct value is sent after bluring
                cy.get('@unit_discount_input').clear().type('20.71').blur()
                // and api calls
                cy.wait('@edit_discount_success').then((xhr) => {
                    expect(xhr.request.body.data.unit_discount_amount).to.eq(-20.71)
                })

                // to refresh data
                cy.wait('@cpost_quotes')

                cy.get(`[data-context=skeleton][data-status=active]`).should('not.exist')

                // check that correct value is sent after typing enter
                cy.get('@unit_discount_input').clear().type('15{enter}')
                // and api calls
                cy.wait('@edit_discount_success').then((xhr) => {
                    expect(xhr.request.body.data.unit_discount_amount).to.eq(-15)
                })
                // to refresh data
                cy.wait('@cpost_quotes')
            })

            it('updates unit discount amount on a quotation with admin permission', () => {
                visitPageWithFixture('erp/quotes/quotation_active.json')
                cy.addPermissions([QUOTE_DISCOUNT_ADMIN])

                cy.intercept('PUT', '**/api/erp/v1/quote/1/quote-line-product/1', {
                    statusCode: 200,
                }).as('edit_discount_success')

                // check we can update until a markup rate === 0 (discount amount <= 24.5)
                cy.get('[data-context=table-row-product]')
                    .eq(0)
                    .find('[data-context=cell-unit-discount-tax-included] input')
                    .clear()
                    .type('24.5{enter}')
                // and api calls
                cy.wait('@edit_discount_success').then((xhr) => {
                    expect(xhr.request.body.data.unit_discount_amount).to.eq(-24.5)
                })
                // to refresh data
                cy.wait('@cpost_quotes')
            })

            it('updates discount percent on a quotation without admin permission', () => {
                visitPageWithFixture('erp/quotes/quotation_active.json')

                cy.intercept('PUT', '**/api/erp/v1/quote/1/quote-line-product/1', {
                    statusCode: 200,
                }).as('edit_discount_success')

                // check no api call after loosing focus without update
                cy.get('[data-context=table-row-product]')
                    .eq(0)
                    .find('[data-context=cell-discount-percent] input')
                    .as('discount_percent_input')
                    .should('have.value', '10.1')
                    .should('not.have.class', 'ring-red-500')
                    .clear()
                    .type('10.1')
                    .blur()
                cy.wait(200)
                // just to calculate how many api calls have been made in order to check none happened
                cy.get('@edit_discount_success.all').then((interceptions) => {
                    expect(interceptions).to.have.length(0)
                })

                cy.get(`[data-context=skeleton][data-status=active]`).should('not.exist')

                // check if trying to type alpha characters
                cy.get('@discount_percent_input').clear().type('3A').should('have.class', 'ring-red-500')

                // check if trying to type negative numeric value
                cy.get('@discount_percent_input').clear().type('-3').should('have.class', 'ring-red-500')

                // check we cannot update with a markup rate < 0.05 (discount amount >= 20.71)
                cy.get('@discount_percent_input').clear().type('20.92').should('have.class', 'ring-red-500')

                // check that correct value is sent after bluring
                cy.get('@discount_percent_input').clear().type('20.91').blur()
                // and api calls
                cy.wait('@edit_discount_success').then((xhr) => {
                    expect(xhr.request.body.data.unit_discount_amount).to.eq(-20.7)
                })
                // to refresh data
                cy.wait('@cpost_quotes')

                cy.get(`[data-context=skeleton][data-status=active]`).should('not.exist')

                // check that correct value is sent after typing enter
                cy.get('@discount_percent_input').clear().type('15{enter}')
                // and api calls
                cy.wait('@edit_discount_success').then((xhr) => {
                    expect(xhr.request.body.data.unit_discount_amount).to.eq(-14.85)
                })
                // to refresh data
                cy.wait('@cpost_quotes')
            })

            it('updates discount percent on a quotation with admin permission', () => {
                visitPageWithFixture('erp/quotes/quotation_active.json')
                cy.addPermissions([QUOTE_DISCOUNT_ADMIN])

                cy.intercept('PUT', '**/api/erp/v1/quote/1/quote-line-product/1', {
                    statusCode: 200,
                }).as('edit_discount_success')

                // check we can update until a markup rate === 0 (discount amount <= 27)
                cy.get('[data-context=table-row-product]')
                    .eq(0)
                    .find('[data-context=cell-discount-percent] input')
                    .as('input')
                    .clear()
                    .type('24.75{enter}')
                    .should('have.class', 'ring-red-500')
                    .clear()
                    .type('24.74{enter}')
                // and api calls
                cy.wait('@edit_discount_success').then((xhr) => {
                    expect(xhr.request.body.data.unit_discount_amount).to.eq(-24.49)
                })
                // to refresh data
                cy.wait('@cpost_quotes')
            })
        })

        describe('With eligible warranties', () => {
            it('should allow to edit an eligible warranty on a product', () => {
                const PRODUCT_LINE_WITH_WARRANTY_INDEX = 1
                const visitPageWithOneEligibleWarranty = () => {
                    cy.fixture('erp/quotes/quotation_active2.json').then((payload) => {
                        // alter the second product line
                        payload.data.quotes[0].quote_line_aggregates[
                            PRODUCT_LINE_WITH_WARRANTY_INDEX
                        ].data.product.eligible_warranties = [
                            {
                                type: 'extension',
                                label: 'Extension de garantie 5 ans',
                                unit_selling_price_tax_included: 79,
                                duration: 5,
                            },
                        ]

                        cy.intercept('POST', '**/api/erp/v1/quotes', {
                            statusCode: 200,
                            body: payload,
                        }).as('cpost_quotes')

                        cy.visit('/quote/1')
                        cy.wait('@cpost_quotes')
                        cy.wait('@cpost_quotes')

                        cy.get('[data-context=erp-table]').as('table')

                        cy.get('[data-context=table-row-product]').should('have.length', 5)
                    })
                }

                cy.intercept('GET', '**/api/erp/v1/quote/1/events', {
                    body: {
                        data: {
                            system_events: [],
                        },
                    },
                }).as('events')

                visitPageWithOneEligibleWarranty()

                // warranty line should be added on the product
                cy.get('@table').selectRow(PRODUCT_LINE_WITH_WARRANTY_INDEX + 1)

                //
                // Check field that should contain nothing
                //
                ;[
                    'cell-stock',
                    'cell-unit-discount-tax-included',
                    'cell-discount-percent',
                    'cell-discount-total',
                ].forEach((dc) => {
                    cy.get('@row').find(`[data-context=${dc}]`).should('contain', '-')
                })

                //
                // Check label and status when inactive
                //
                cy.get('@row').find(`[data-context=cell-label]`).should('contain', 'Extension de garantie 5 ans')
                cy.get('@row').find(`[data-context=warranty-is-active]`).should('not.exist')
                cy.get('@row').find(`[data-context=warranty-is-inactive]`).should('be.visible')

                //
                // Check prices
                //
                cy.get('@row').find(`[data-context=cell-unit-selling-price-tax-included]`).should('contain', '79,00 €')
                cy.get('@row').find(`[data-context=cell-selling-price-tax-included]`).should('contain', '-')
                cy.get(`[data-context=quote-amount-tax-included]`).should('contain', '8 627,32 € TTC')

                //
                // Check warranty selector on unselected
                //
                cy.get('@row')
                    .find(`[data-context=cell-quantity] [data-context=erp-switch-selector]`)
                    .should('be.visible')
                    .as('selector')
                cy.get('@selector')
                    .find('[data-context=erp-switch-selector-button]')
                    .should('have.length', 2)
                    .as('selector_buttons')

                //
                // select the warranty
                //
                cy.intercept('PUT', '**/api/erp/v1/quote/1/quote-line-product/18', {
                    statusCode: 200,
                }).as('api_edit_quote_line_product')
                cy.fixture('erp/quotes/quotation_active2.json').then((payload) => {
                    payload.data.quotes[0].quote_line_aggregates[
                        PRODUCT_LINE_WITH_WARRANTY_INDEX
                    ].data.product.eligible_warranties = [
                        {
                            type: 'extension',
                            label: 'Extension de garantie 5 ans',
                            unit_selling_price_tax_included: 79,
                            duration: 5,
                        },
                    ]
                    payload.data.quotes[0].quote_line_aggregates[
                        PRODUCT_LINE_WITH_WARRANTY_INDEX
                    ].data.selected_warranties = [
                        {
                            type: 'extension',
                            label: 'Extension de garantie 5 ans',
                            unit_selling_price_tax_included: 79,
                            duration: 5,
                        },
                    ]

                    cy.intercept('POST', '**/api/erp/v1/quotes', {
                        statusCode: 200,
                        body: payload,
                    }).as('cpost_quotes')
                })

                cy.get('@selector_buttons').eq(1).click()

                cy.wait('@api_edit_quote_line_product').then((xhr) => {
                    expect(xhr.request.body.data).to.deep.eq({
                        selected_warranties: [
                            {
                                type: 'extension',
                                label: 'Extension de garantie 5 ans',
                                unit_selling_price_tax_included: 79,
                                duration: 5,
                            },
                        ],
                    })
                })

                // maybe fetchData sucks, it loads quote data and previous customer's quote each time
                cy.wait(['@cpost_quotes', '@cpost_quotes'])

                //
                // Check warranty selected after data reload
                //
                cy.get('[data-context=erp-table]')
                    .selectRow(PRODUCT_LINE_WITH_WARRANTY_INDEX + 1)
                    .find(`[data-context=cell-quantity] [data-context=erp-switch-selector-button]`)

                //
                // Check label and status when active
                //
                cy.get(`[data-context=warranty-is-active]`).should('be.visible')
                cy.get(`[data-context=warranty-is-inactive]`).should('not.exist')

                //
                // Check prices
                //
                cy.get('@row').find(`[data-context=cell-unit-selling-price-tax-included]`).should('contain', '79,00 €')
                cy.get('@row').find(`[data-context=cell-selling-price-tax-included]`).should('contain', '237,00 €')
                cy.get(`[data-context=quote-amount-tax-included]`).should('contain', '8 627,32 € TTC')
            })

            it('should allow editing warranties on same line existing 2 times in the same quote separately', () => {
                const visitPageWithMoreThanOneEligibleWarrantyOnSameProduct = () => {
                    cy.fixture('erp/quotes/quotation_active2.json').then((payload) => {
                        const article = { ...payload.data.quotes[0].quote_line_aggregates[0] }
                        const lines = [JSON.parse(JSON.stringify(article)), JSON.parse(JSON.stringify(article))]

                        lines[0].data.product.eligible_warranties = [
                            {
                                type: 'extension',
                                label: 'Extension de garantie 5 ans',
                                unit_selling_price_tax_included: 79,
                                duration: 5,
                            },
                            {
                                type: 'extension',
                                label: 'Extension de garantie 2 ans',
                                unit_selling_price_tax_included: 50,
                                duration: 2,
                            },
                            {
                                type: 'theft_breakdown',
                                label: 'Vol et casse 5 ans',
                                unit_selling_price_tax_included: 150,
                                duration: 5,
                            },
                        ]
                        // has already some selected warranties
                        lines[0].data.selected_warranties = [
                            {
                                type: 'extension',
                                label: 'Extension de garantie 2 ans',
                                unit_selling_price_tax_included: 50,
                                duration: 2,
                            },
                            {
                                type: 'theft_breakdown',
                                label: 'Vol et casse 5 ans',
                                unit_selling_price_tax_included: 150,
                                duration: 5,
                            },
                        ]

                        lines[1].data.quote_line_product_id = 18
                        // Note: In a practical world, eligible_warranties should be the same on both product.
                        // For test purpose, this product as only one and not at the same selling price.
                        // We will select the warranty from this product and see what happen.
                        lines[1].data.product.eligible_warranties = [
                            {
                                type: 'extension',
                                label: 'Extension de garantie 5 ans',
                                unit_selling_price_tax_included: 42,
                                duration: 5,
                            },
                        ]

                        payload.data.quotes[0].quote_line_aggregates = lines

                        cy.intercept('POST', '**/api/erp/v1/quotes', {
                            statusCode: 200,
                            body: payload,
                        }).as('cpost_quotes')

                        cy.visit('/quote/1')
                        cy.wait(['@cpost_quotes', '@cpost_quotes'])

                        cy.get('[data-context=erp-table]').as('table')

                        cy.get('[data-context=table-row-product]').should('have.length', 6)
                    })
                }

                cy.intercept('GET', '**/api/erp/v1/quote/1/events', {
                    body: {
                        data: {
                            system_events: [],
                        },
                    },
                }).as('events')

                visitPageWithMoreThanOneEligibleWarrantyOnSameProduct()

                //
                // one selection should update the other
                //
                cy.fixture('erp/quotes/quotation_active2.json').then((payload) => {
                    const article = { ...payload.data.quotes[0].quote_line_aggregates[0] }
                    const lines = [JSON.parse(JSON.stringify(article)), JSON.parse(JSON.stringify(article))]
                    lines[0].data.product.eligible_warranties = [
                        {
                            type: 'extension',
                            label: 'Extension de garantie 5 ans',
                            unit_selling_price_tax_included: 79,
                            duration: 5,
                        },
                        {
                            type: 'extension',
                            label: 'Extension de garantie 2 ans',
                            unit_selling_price_tax_included: 50,
                            duration: 2,
                        },
                        {
                            type: 'theft_breakdown',
                            label: 'Vol et casse 5 ans',
                            unit_selling_price_tax_included: 150,
                            duration: 5,
                        },
                    ]
                    lines[1].data.quote_line_product_id = 18
                    lines[1].data.product.eligible_warranties = [
                        {
                            type: 'extension',
                            label: 'Extension de garantie 5 ans',
                            unit_selling_price_tax_included: 42,
                            duration: 5,
                        },
                    ]

                    lines[0].data.selected_warranties = [
                        {
                            type: 'theft_breakdown',
                            label: 'Vol et casse 5 ans',
                            unit_selling_price_tax_included: 150,
                            duration: 5,
                        },
                        {
                            type: 'extension',
                            label: 'Extension de garantie 5 ans',
                            unit_selling_price_tax_included: 79,
                            duration: 5,
                        },
                    ]
                    lines[1].data.selected_warranties = [
                        {
                            type: 'extension',
                            label: 'Extension de garantie 5 ans',
                            unit_selling_price_tax_included: 42,
                            duration: 5,
                        },
                    ]

                    payload.data.quotes[0].quote_line_aggregates = lines

                    cy.intercept('POST', '**/api/erp/v1/quotes', {
                        statusCode: 200,
                        body: payload,
                    }).as('cpost_quotes')
                })

                cy.intercept('PUT', '**/api/erp/v1/quote/1/quote-line-product/17', {
                    statusCode: 200,
                }).as('api_edit_quote_line_product_17')
                cy.intercept('PUT', '**/api/erp/v1/quote/1/quote-line-product/18', {
                    statusCode: 200,
                }).as('api_edit_quote_line_product_18')

                cy.get(`[data-context=cell-quantity] [data-context=erp-switch-selector]`)
                    .eq(3)
                    .find('[data-context=erp-switch-selector-button]')
                    .eq(1)
                    .click()

                cy.wait('@api_edit_quote_line_product_17').then((xhr) => {
                    expect(xhr.request.body.data).to.deep.eq({
                        selected_warranties: [
                            {
                                type: 'theft_breakdown',
                                label: 'Vol et casse 5 ans',
                                unit_selling_price_tax_included: 150,
                                duration: 5,
                            },
                            {
                                type: 'extension',
                                label: 'Extension de garantie 5 ans',
                                unit_selling_price_tax_included: 79,
                                duration: 5,
                            },
                        ],
                    })
                })
                cy.wait('@api_edit_quote_line_product_18').then((xhr) => {
                    expect(xhr.request.body.data).to.deep.eq({
                        selected_warranties: [
                            {
                                type: 'extension',
                                label: 'Extension de garantie 5 ans',
                                unit_selling_price_tax_included: 42,
                                duration: 5,
                            },
                        ],
                    })
                })

                cy.wait('@cpost_quotes')
                cy.wait('@cpost_quotes')

                //
                // one unselection should update the other
                //
                cy.get(`[data-context=skeleton][data-status=active]`).should('not.exist')
                cy.get(`[data-context=cell-quantity] [data-context=erp-switch-selector]`)
                    .eq(0)
                    .find('[data-context=erp-switch-selector-button]')
                    .should('not.be.disabled')
                    .eq(0)
                    .click()

                cy.wait('@api_edit_quote_line_product_17').then((xhr) => {
                    expect(xhr.request.body.data).to.deep.eq({
                        selected_warranties: [
                            {
                                type: 'theft_breakdown',
                                label: 'Vol et casse 5 ans',
                                unit_selling_price_tax_included: 150,
                                duration: 5,
                            },
                        ],
                    })
                })
                cy.wait('@api_edit_quote_line_product_18').then((xhr) => {
                    expect(xhr.request.body.data).to.deep.eq({
                        selected_warranties: [],
                    })
                })
            })

            describe('Drag and drop lines', () => {
                it('Activate drag and drop', () => {
                    cy.fixture('erp/quotes/quotation_active2.json').then((payload) => {
                        payload.data.quotes[0].quote_line_aggregates[1].data.product.eligible_warranties = [
                            {
                                type: 'extension',
                                label: 'Extension de garantie 5 ans',
                                unit_selling_price_tax_included: 79,
                                duration: 5,
                            },
                            {
                                type: 'theft_breakdown',
                                label: 'Vol et casse 5 ans',
                                unit_selling_price_tax_included: 150,
                                duration: 5,
                            },
                        ]

                        payload.data.quotes[0].quote_line_aggregates[2].data.product.eligible_warranties = [
                            {
                                type: 'extension',
                                label: 'Extension de garantie 5 ans',
                                unit_selling_price_tax_included: 79,
                                duration: 5,
                            },
                        ]

                        cy.intercept('POST', '**/api/erp/v1/quotes', {
                            statusCode: 200,
                            body: payload,
                        }).as('cpost_quotes')

                        cy.visit('/quote/1')
                        cy.wait('@cpost_quotes')
                    })

                    cy.intercept('GET', '**/api/erp/v1/quote/1/events', {
                        body: { data: { system_events: [] } },
                    }).as('events')

                    cy.intercept('PUT', '**/api/erp/v1/quote/1/quote-line/*').as('update_display_order')

                    cy.get('[data-context=table-row-product]').should('have.length', 7)
                    cy.get('[ data-context=quote-reorder-handle]').should('have.length', 0)

                    cy.get('[data-context=drag-and-drop-button]').should('not.be.disabled').click()
                    cy.get('[data-context=table-row-product]').should('have.length', 4)
                    cy.get('[data-context=quote-reorder-handle]').should('have.length', 4)

                    // initial positions
                    const line = (position) =>
                        cy.get(`[data-context=table-row-product]:nth-child(${position}) [data-context=sku]`)

                    line(1).text().should('contains', 'SENHD800S')
                    line(2).text().should('contains', 'NAIMUNIATOMHDMI')
                    line(3).text().should('contains', 'REGAPLANAR1PLUSCARBNRMT')
                    line(4).text().should('contains', 'CHORDMOJONR')

                    // move the 1st on the 4th position
                    cy.get('[data-context=table-row-product]:nth-child(1) [data-context=quote-reorder-handle]').drag(
                        '[data-context=table-row-product]:nth-child(4)',
                    )

                    cy.wait('@update_display_order').then((xhr) => {
                        expect(xhr.request.url)
                        expect(xhr.request.body).to.deep.equal({ display_order: 4 })
                    })
                })
            })
        })

        describe('Handle product line with a scanner', () => {
            const visitPageForAddingProductWithScanner = () => {
                cy.fixture('erp/quotes/empty_quotation_active.json').then((payload) => {
                    cy.intercept('POST', '**/api/erp/v1/quotes', {
                        statusCode: 200,
                        body: payload,
                    }).as('cpost_quotes')

                    cy.intercept('POST', '**/api/erp/v1/quote/1/quote-line-product', {
                        statusCode: 200,
                        body: { data: {} },
                    }).as('post_quote_lines')

                    cy.intercept('GET', '**/api/erp/v1/quote/1/events**', {
                        body: {
                            data: {
                                system_events: [],
                            },
                        },
                    }).as('events')

                    cy.intercept('POST', '**/api/erp/v1/quote-subtype', {
                        statusCode: 200,
                        body: { data: { quote_subtype: [] } },
                    }).as('subtype')

                    cy.visit('/quote/1')
                    cy.wait('@cpost_quotes')
                })
            }

            it('Add a quote line with code128', () => {
                visitPageForAddingProductWithScanner()

                // the search by must return one result
                cy.intercept(
                    'GET',
                    '**/api/erp/v1/magic-search?size=12&search_terms=Code128Code&context=articles&from=0**',
                    {
                        statusCode: 200,
                        body: getMagicSearchResponse(getMagicSearchOneArticle()),
                    },
                ).as('get_magic_search')

                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('not.exist')

                cy.mockDate(new Date())
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('Code128Code')
                cy.tick(400) // we use a debouncer of 360ms
                cy.clock().invoke('restore')

                cy.wait('@get_magic_search')

                // check data sent to server
                cy.wait('@post_quote_lines').then((xhr) => {
                    expect(xhr.request.body.sku).to.eq('CHORDMOJO2NR')
                })
                // empty the input
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').should('have.value', '')
            })

            it('Add a quote line with UPC-A', () => {
                visitPageForAddingProductWithScanner()

                // the search by must return one result
                cy.intercept(
                    'GET',
                    // UPC-A converted to EAN13 with a 0 ahead
                    '**/api/erp/v1/magic-search?size=12&search_terms=0UPC-AUPC-AUP&context=articles&from=0**',
                    {
                        statusCode: 200,
                        body: getMagicSearchResponse(getMagicSearchOneArticle()),
                    },
                ).as('get_magic_search')

                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('not.exist')

                cy.mockDate(new Date())
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('UPC-AUPC-AUP')
                cy.tick(400) // we use a debouncer of 360ms
                cy.clock().invoke('restore')

                cy.wait('@get_magic_search')

                // check data sent to server
                cy.wait('@post_quote_lines').then((xhr) => {
                    expect(xhr.request.body.sku).to.eq('CHORDMOJO2NR')
                })

                // empty the input
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').should('have.value', '')
            })

            it('Add a quote line with EAN13', () => {
                visitPageForAddingProductWithScanner()

                // the search must return one result
                cy.intercept(
                    'GET',
                    '**/api/erp/v1/magic-search?size=12&search_terms=EAN13EAN13EAN&context=articles&from=0',
                    {
                        statusCode: 200,
                        body: getMagicSearchResponse(getMagicSearchOneArticle()),
                    },
                ).as('get_magic_search')

                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('not.exist')
                cy.mockDate(new Date())
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('EAN13EAN13EAN')
                cy.tick(400) // we use a debouncer of 360ms
                cy.clock().invoke('restore')

                cy.wait('@get_magic_search')

                // check data sent to server
                cy.wait('@post_quote_lines').then((xhr) => {
                    expect(xhr.request.body.sku).to.eq('CHORDMOJO2NR')
                })

                // empty the input
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').should('have.value', '')
            })

            it('try to scan with multiples result', () => {
                visitPageForAddingProductWithScanner()

                // the search must return one result
                cy.intercept('GET', '**/api/erp/v1/magic-search**', {
                    statusCode: 200,
                    body: getMagicSearchResponse(getMagicSearchArticles(16), { from: 5, size: 15 }),
                }).as('get_magic_search')

                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('not.exist')
                cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('not.exist')
                cy.mockDate(new Date())
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('00000000000')
                cy.tick(400) // we use a debouncer of 360ms
                cy.clock().invoke('restore')

                // eslint-disable-next-line
                cy.wait(4000)
                cy.get('@post_quote_lines.all').then((xhrs) => {
                    expect(xhrs).to.have.length(0)
                })
                // empty the input
                cy.get('[data-context=add-product-or-section] [data-context=search-input]').should(
                    'have.value',
                    '00000000000',
                )
            })
        })
    })
})
