import { visitPageWithFixture, visitPageWithPayload } from './helpers'
import {
    QUOTE_WRITE,
    QUOTE_SUBTYPE_COMMUNICATION_SELECT,
    QUOTE_SUBTYPE_EVENT_SELECT,
    QUOTE_SUBTYPE_INTERNAL_USE_SELECT,
    QUOTE_SUBTYPE_INTRAGROUP_SELECT,
    QUOTE_SUBTYPE_LOAN_SELECT,
    QUOTE_SUBTYPE_MARKETING_GAME_SELECT,
    QUOTE_SUBTYPE_PARTNER_SALE_SELECT,
    QUOTE_SUBTYPE_STAFF_SALE_SELECT,
} from '../../../../../src/apps/erp/permissions'

describe('Quote - Action toolbar', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Without permission', () => {
        it('Display without permission', () => {
            visitPageWithFixture('erp/quotes/quotation_active.json')

            cy.get('[data-context=quote-subtype]')
                .should('contain', 'Usage interne')
                .find('input')
                .should('be.disabled')
            cy.get('[data-context=valid-until]').should('contain', '15 jours').find('input').should('be.disabled')
        })
    })

    describe('With permission', () => {
        beforeEach(() => {
            cy.mockErpUser([QUOTE_WRITE])
        })

        it('Display locked quote', () => {
            visitPageWithFixture('erp/quotes/quotation_locked.json')

            cy.get('[data-context=valid-until]').should('contain', '15 jours').find('input').should('be.disabled')

            cy.get('[data-context=quote-subtype]').should('contain', 'Classique').find('input').should('be.disabled')
        })

        it('Check available actions', () => {
            visitPageWithFixture('erp/quotes/quotation_active.json')

            cy.get('[data-context=valid-until]').should('contain', '15 jours')

            cy.get('[data-context=quote-subtype]').should('contain', 'Usage interne')

            cy.get('[data-context=created_by]').should('contain', 'Billy THE KID')
        })

        it('QUOTE_WRITE permission is not enough to change subtype', () => {
            cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: { updated: 1 },
                },
            }).as('update_quote')
            visitPageWithFixture('erp/quotes/quotation_active2.json')

            cy.get('[data-context=quote-subtype] input').should('be.disabled')
        })
    })

    describe("Subtype's weird permissions", () => {
        it('Can edit subtype if is undefined', () => {
            cy.mockErpUser([QUOTE_WRITE])
            cy.fixture('erp/quotes/quotation_inactive.json').then((payload) => {
                payload.data.quotes[0].quote_subtype = null

                visitPageWithPayload(payload)
            })

            cy.get('[data-context=quote-subtype]')
                .should('contain', 'Rechercher un sous-type')
                .find('input')
                .should('not.be.disabled')
        })

        it('Display without permissions', () => {
            cy.mockErpUser([QUOTE_WRITE])
            visitPageWithFixture('erp/quotes/quotation_inactive.json')
            cy.get('[data-context=quote-subtype]').click().as('input')

            cy.get('@input')
                .get('[data-context=suggestion]:contains(Classique)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Usage interne)')
                .should('have.attr', 'data-disabled', 'true')
                .should(
                    'have.attr',
                    'title',
                    "Vous n'avez pas la permission de sélectionner le sous-type Usage interne",
                )
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Communication)')
                .should('have.attr', 'data-disabled', 'true')
                .should(
                    'have.attr',
                    'title',
                    "Vous n'avez pas la permission de sélectionner le sous-type Communication",
                )
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Événement)')
                .should('have.attr', 'data-disabled', 'true')
                .should('have.attr', 'title', "Vous n'avez pas la permission de sélectionner le sous-type Événement")
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Intra-groupe)')
                .should('have.attr', 'data-disabled', 'true')
                .should('have.attr', 'title', "Vous n'avez pas la permission de sélectionner le sous-type Intra-groupe")
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Prêt)')
                .should('have.attr', 'data-disabled', 'true')
                .should('have.attr', 'title', "Vous n'avez pas la permission de sélectionner le sous-type Prêt")
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Concours marketing)')
                .should('have.attr', 'data-disabled', 'true')
                .should(
                    'have.attr',
                    'title',
                    "Vous n'avez pas la permission de sélectionner le sous-type Concours marketing",
                )
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Vente au personnel)')
                .should('have.attr', 'data-disabled', 'true')
                .should(
                    'have.attr',
                    'title',
                    "Vous n'avez pas la permission de sélectionner le sous-type Vente au personnel",
                )
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Vente à partenaire)')
                .should('have.attr', 'data-disabled', 'true')
                .should(
                    'have.attr',
                    'title',
                    "Vous n'avez pas la permission de sélectionner le sous-type Vente à partenaire",
                )
        })

        it('Display subtype with all permissions', () => {
            cy.mockErpUser([
                QUOTE_WRITE,
                QUOTE_SUBTYPE_COMMUNICATION_SELECT,
                QUOTE_SUBTYPE_EVENT_SELECT,
                QUOTE_SUBTYPE_INTERNAL_USE_SELECT,
                QUOTE_SUBTYPE_INTRAGROUP_SELECT,
                QUOTE_SUBTYPE_LOAN_SELECT,
                QUOTE_SUBTYPE_MARKETING_GAME_SELECT,
                QUOTE_SUBTYPE_PARTNER_SALE_SELECT,
                QUOTE_SUBTYPE_STAFF_SALE_SELECT,
            ])
            visitPageWithFixture('erp/quotes/quotation_inactive.json')

            cy.get('[data-context=quote-subtype]').click().as('input')

            cy.get('@input')
                .get('[data-context=suggestion]:contains(Classique)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Usage interne)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Communication)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Événement)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Intra-groupe)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input').get('[data-context=suggestion]:contains(Prêt)').should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Concours marketing)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Vente au personnel)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Vente à partenaire)')
                .should('not.have.attr', 'data-disabled')
        })

        it('Display subtype with internal use permission', () => {
            cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: { updated: 1 },
                },
            }).as('update_quote')
            cy.mockErpUser([QUOTE_WRITE, QUOTE_SUBTYPE_INTERNAL_USE_SELECT])
            visitPageWithFixture('erp/quotes/quotation_inactive.json')

            cy.get('[data-context=quote-subtype]').click().as('input')

            cy.get('@input')
                .get('[data-context=suggestion]:contains(Intra-groupe)')
                .should('have.attr', 'data-disabled', 'true')
                .should('have.attr', 'title', "Vous n'avez pas la permission de sélectionner le sous-type Intra-groupe")
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Usage interne)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Classique)')
                .should('not.have.attr', 'data-disabled')

            cy.get('[data-context=quote-subtype]').should('contain', 'Classique').erpMultiselect('', 'Usage interne')

            cy.wait('@update_quote').then((xhr) => {
                cy.log(xhr.request.body)
                expect(xhr.request.body.data.quote_subtype).to.eq('INTERNAL_USE')
            })

            cy.wait('@cpost_quotes')
        })

        it('Display subtype with only Intra permission', () => {
            cy.intercept('PUT', '**/api/erp/v1/quote/1', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: { updated: 1 },
                },
            }).as('update_quote')
            cy.mockErpUser([QUOTE_WRITE, QUOTE_SUBTYPE_INTRAGROUP_SELECT])
            visitPageWithFixture('erp/quotes/quotation_inactive.json')

            cy.get('[data-context=quote-subtype]').click().as('input')

            cy.get('@input')
                .get('[data-context=suggestion]:contains(Usage interne)')
                .should('have.attr', 'data-disabled', 'true')
                .should(
                    'have.attr',
                    'title',
                    "Vous n'avez pas la permission de sélectionner le sous-type Usage interne",
                )
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Intra-groupe)')
                .should('not.have.attr', 'data-disabled')
            cy.get('@input')
                .get('[data-context=suggestion]:contains(Classique)')
                .should('not.have.attr', 'data-disabled')

            cy.get('[data-context=quote-subtype]').should('contain', 'Classique').erpMultiselect('', 'Intra-groupe')

            cy.wait('@update_quote').then((xhr) => {
                cy.log(xhr.request.body)
                expect(xhr.request.body.data.quote_subtype).to.eq('INTRAGROUP')
            })

            cy.wait('@cpost_quotes')
        })

        it('Display subtype with only Intra-groupe permission while subtype is INTERNAL_USE', () => {
            cy.mockErpUser([QUOTE_WRITE, QUOTE_SUBTYPE_INTRAGROUP_SELECT])
            cy.fixture('erp/quotes/quotation_inactive.json').then((payload) => {
                payload.data.quotes[0].quote_subtype = 'INTERNAL_USE'

                visitPageWithPayload(payload)
            })
            cy.get('[data-context=quote-subtype]')
                .should('contain', 'Usage interne')
                .find('input')
                .should('be.disabled')
        })

        it('Display subtype with only INTERNAL USE permission while subtype is Intra-groupe', () => {
            cy.mockErpUser([QUOTE_WRITE, QUOTE_SUBTYPE_INTERNAL_USE_SELECT])
            cy.fixture('erp/quotes/quotation_inactive.json').then((payload) => {
                payload.data.quotes[0].quote_subtype = 'INTRAGROUP'

                visitPageWithPayload(payload)
            })

            cy.get('[data-context=quote-subtype]').should('contain', 'Intra-groupe').find('input').should('be.disabled')
        })

        it('Display subtype with all permissions', () => {
            cy.mockErpUser([QUOTE_WRITE, QUOTE_SUBTYPE_INTRAGROUP_SELECT, QUOTE_SUBTYPE_INTERNAL_USE_SELECT])
            visitPageWithFixture('erp/quotes/quotation_inactive.json')

            cy.get('[data-context=quote-subtype]').click().as('input')

            cy.get('@input')
                .get('[data-context=suggestion]:contains(Classique)')
                .should('not.have.attr', 'data-disabled')

            cy.get('@input')
                .get('[data-context=suggestion]:contains(Usage interne)')
                .should('not.have.attr', 'data-disabled')

            cy.get('@input')
                .get('[data-context=suggestion]:contains(Intra-groupe)')
                .should('not.have.attr', 'data-disabled')
        })
    })
})
