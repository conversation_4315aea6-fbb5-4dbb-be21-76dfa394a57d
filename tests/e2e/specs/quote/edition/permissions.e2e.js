import { visitPageWithFixture } from './helpers'
import { testPermissionInContext } from '../../../utils/permission-utils'

describe('Quote - Action toolbar', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    testPermissionInContext(
        '[data-context=permissions-in-header]',
        () => visitPageWithFixture('erp/quotes/quotation_active.json'),
        {
            _and: [
                {
                    permission_id: {
                        _ilike: 'QUOTE_%',
                    },
                },
                {
                    permission_id: {
                        _nilike: 'QUOTE_SUBTYPE_%_SELECT',
                    },
                },
            ],
        },
    )

    testPermissionInContext(
        '[data-context=quote-subtype-selector] [data-context=administrate-btn]',
        () => visitPageWithFixture('erp/quotes/quotation_active.json'),
        {
            _and: [
                {
                    permission_id: {
                        _ilike: 'QUOTE_SUBTYPE_%_SELECT',
                    },
                },
            ],
        },
    )
})
