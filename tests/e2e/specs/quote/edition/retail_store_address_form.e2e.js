import { visitPageWithFixture } from './helpers'
import { aliasQ<PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../../../utils/graphql-test-utils'
import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions'

describe('Quote - Customer address book', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([QUOTE_WRITE])

        cy.intercept('POST', '**/api/erp/v2/wms/warehouses', {
            fixture: 'erp/wms/warehouses/v2/warehouses_champigny2.json',
        }).as('cpost_warehouses')
    })

    it('Check retail store button not exist on billing form', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=open-address-editor]').eq(0).click()

        cy.get('[data-context=retail-store-form-btn]').should('not.exist')
    })

    it('Check retail store button exist on shipping form', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=open-address-editor]').eq(1).click()

        cy.get('[data-context=retail-store-form-btn]').should('be.visible')
    })

    it('Form is correctly displayed when no data is supplied and check validation messages', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=open-address-editor]').eq(1).click()

        cy.get('[data-context=retail-store-form-btn]').should('be.visible').click()

        cy.get('[data-context=retail-store-address-form]').should('be.visible').as('form')

        cy.get('@form').find('[data-context=submit]').click()

        cy.get('@form').find('[data-context=civility] label').should('contain', 'Civilité')
        cy.get('@form').find('[data-context=civility] [data-context=message]').should('not.exist')
        cy.get('@form').find('[data-context=civility] select').should('have.value', 'M.')
        // firstname
        cy.get('@form').find('[data-context=firstname] label').should('contain', 'Prénom')
        cy.get('@form')
            .find('[data-context=firstname] [data-context=message]')
            .should('contain', 'Ce champ est obligatoire')
        cy.get('@form').find('[data-context=firstname] input').should('have.value', '')
        // lastname
        cy.get('@form').find('[data-context=lastname] label').should('contain', 'Nom')
        cy.get('@form')
            .find('[data-context=lastname] [data-context=message]')
            .should('contain', 'Ce champ est obligatoire')
        cy.get('@form').find('[data-context=lastname] input').should('have.value', '')
        // retail store
        cy.get('@form').find('[data-context=store] label').should('contain', 'Magasin')
        cy.get('@form')
            .find('[data-context=store] [data-context=message]')
            .should('contain', 'Ce champ est obligatoire')
        cy.get('@form')
            .find('[data-context=store] [data-context=warehouse-autocomplete]')
            .should('contain', '')
            // some warehouses should be excluded: Havre, PHC Nantes…
            .find('.multiselect__element')
            .should('have.length', 17)
    })

    it('Add a retail store', () => {
        visitPageWithFixture('erp/quotes/quotation_active3.json')

        cy.intercept('PUT', '**/api/erp/v1/quote/1', {
            statusCode: 200,
            body: {
                status: 'success',
                data: { updated: 1 },
            },
        }).as('edit_success')

        cy.get('[data-context=open-address-editor]').eq(1).click()

        cy.get('[data-context=retail-store-form-btn]').should('be.visible').click()

        cy.get('[data-context=retail-store-address-form]').should('be.visible').as('form')

        cy.get('@form')
            .find('[data-context=civility] [data-context=civility-select]')
            .should('have.value', 'Mme')
            .select('M.')

        cy.get('@form').find('[data-context=firstname] input').should('have.value', 'Arthur').clear().type('Gérard')

        cy.get('@form').find('[data-context=lastname] input').should('have.value', 'LOLO').clear().type('Menvussa')

        cy.get('@form').find('[data-context=phone] input').should('have.value', '0678787878').clear().type('0606060606')

        cy.get('@form').find('[data-context=store] [data-context=warehouse-autocomplete]').multiselect('Nantes')

        cy.get('@form').find('[data-context=submit]').click()

        cy.wait('@edit_success').then((xhr) => {
            expect(xhr.request.body.data.shipping_address).to.deep.eq({
                civility: 'M.',
                firstname: 'Gérard',
                lastname: 'Menvussa',
                company_name: 'Son-Vidéo.com',
                address: '3 place de la Bourse',
                postal_code: '44000',
                city: 'Nantes',
                country: {
                    country_id: 67,
                    name: 'FRANCE',
                    country_code: 'FR',
                },
                cellphone: '0606060606',
                phone: '0606060606',
                shipment_method_id: 37,
            })
        })

        cy.wait('@cpost_quotes')

        cy.get('[data-context=retail-store-address-form]').should('not.exist')
    })

    it('Can cancel', () => {
        visitPageWithFixture('erp/quotes/quotation_active.json')

        cy.get('[data-context=open-address-editor]').eq(1).click()

        cy.get('[data-context=slide-out-container] [data-context=address-form]').should('be.visible')

        cy.get('[data-context=retail-store-form-btn]').should('be.visible').click()

        cy.get('[data-context=retail-store-address-form]').should('be.visible')

        cy.get('[data-context=retail-store-address-form] [data-context=cancel]')
            .should('be.visible')
            .should('contain', 'Annuler')
            .click()

        cy.get('[data-context=address-form]').should('be.visible')
    })
})
