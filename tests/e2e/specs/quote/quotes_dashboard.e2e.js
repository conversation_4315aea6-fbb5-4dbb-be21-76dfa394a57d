import format from 'date-fns/format'

const PAGE = '/quotes'
const MOCKED_DATE = new Date(2021, 8, 6, 11, 40, 0, 0)

describe('Quotes dashboard page', () => {
    describe('Page UI', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            cy.intercept('POST', '**/api/erp/v1/quotes', { fixture: 'erp/quotes/cpost_quotes' }).as('fetch_quotes')
        })

        it('should have a filter section and an empty table', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=filter-quote]').should('be.visible')
            cy.get('[data-context=filter-created-by]').should('be.visible')
            cy.get('[data-context=filter-customer]').should('be.visible')
            cy.get('[data-context=filter-article]').should('be.visible')
            cy.get('[data-context=filter-status]').should('be.visible')
            cy.get('[data-context=filter-created-at]').should('be.visible')

            // no data loaded by default, pagination is hidden
            cy.get('[data-context=erp-dashboard]').find('[data-context=pagination-info]').should('not.exist')

            // Check table headers
            const table_headers = [
                '#',
                'Compte client',
                'Date de création',
                'Statut',
                'Remise',
                'Montant',
                'Opérateur',
                'Commandes',
            ]

            cy.get('th[scope=col]').should('have.length', table_headers.length)

            table_headers.forEach((text, idx) => {
                cy.get('th[scope=col]').eq(idx).should('contain', text)
            })

            cy.get('[data-context=table-no-result]').should('contain', 'Aucun résultat')
        })

        it('should display info of a quote', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=filters-submit-btn]').click()
            cy.wait('@fetch_quotes')

            cy.get('[data-context=table-row]').should('have.length', 9)

            cy.get('[data-context=erp-table]').selectCell(4, 0).should('contain', 'DVS').should('contain', '5')

            cy.get('[data-context=erp-table]').selectCell(4, 1).should('contain', 'Ben DOVER')

            cy.get('[data-context=erp-table]').selectCell(4, 2).should('contain', '05/04/2022 à 09:43')

            cy.get('[data-context=erp-table]').selectCell(4, 3).should('contain', 'Commandé')

            cy.get('[data-context=erp-table]').selectCell(4, 4).should('contain', '224,00 €')

            cy.get('[data-context=erp-table]').selectCell(4, 5).should('contain', '2 985,90 €')

            cy.get('[data-context=erp-table]').selectCell(4, 6).should('contain', 'Gérard MANVUSSA')

            cy.get('[data-context=erp-table]')
                .selectCell(4, 7)
                .find('[data-context=scoped-badge] a')
                .should('have.length', 2)

            cy.get('[data-context=erp-table]')
                .selectCell(4, 7)
                .find('[data-context=scoped-badge] a')
                .eq(0)
                .should('contain', '3250211')

            cy.get('[data-context=erp-table]')
                .selectCell(4, 7)
                .find('[data-context=scoped-badge] a')
                .eq(1)
                .should('contain', '3250212')

            cy.get('[data-context=erp-table]').selectCell(7, 0).should('contain', 'OFR')

            cy.get('[data-context=erp-table]').selectCell(8, 0).should('contain', 'BRO')
        })

        it('should show the articles of a quote', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=filters-submit-btn]').click()
            cy.wait('@fetch_quotes')

            cy.get('[data-context=table-row]').should('have.length', 9)

            cy.get('[data-context=erp-table] [data-context=expander]')
                .eq(0)
                .find('[data-context=article-item-inline] [data-context=quantity]')
                .eq(0)
                .should('contain', '1')

            cy.get('[data-context=erp-table] [data-context=expander]')
                .eq(0)
                .find('[data-context=article-item-inline] [data-context=article]')
                .eq(0)
                .should('contain', 'KEF KC 62 noir mat')

            cy.get('[data-context=erp-table] [data-context=expander]')
                .eq(0)
                .find('[data-context=article-item-inline] [data-context=article]')
                .eq(1)
                .should('contain', 'Grado SR325x')

            cy.get('[data-context=erp-table] [data-context=expander]')
                .eq(2)
                .find('[data-context=article-item-inline] [data-context=quantity]')
                .eq(0)
                .should('contain', '4')

            cy.get('[data-context=erp-table] [data-context=expander]')
                .eq(2)
                .find('[data-context=article-item-inline] [data-context=article]')
                .eq(0)
                .should('contain', 'KEF R7 Noir laqué (la paire)')
        })
    })

    describe('Page filters', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            cy.intercept('POST', '**/api/erp/v1/quotes', { fixture: 'erp/quotes/cpost_quotes' }).as('fetch_quotes')
        })

        it('should be able to use the numeric filters successfully', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            // this fetch is triggered at page load for the currently logged user
            cy.wait('@fetch_quotes').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    page: 1,
                    limit: 25,
                    where: {
                        _and: [
                            {
                                _or: [
                                    { created_by_username: { _ilike: '%anakin.skywalker%' } },
                                    { created_by_firstname: { _ilike: '%anakin.skywalker%' } },
                                    { created_by_lastname: { _ilike: '%anakin.skywalker%' } },
                                ],
                            },
                        ],
                    },
                    order_by: 'created_at',
                    order_direction: 'desc',
                })
            })

            cy.get('[data-context=filter-quote]').should('be.visible').find('input').type(1)
            cy.get('[data-context=filter-created-by]').should('be.visible').find('input').clear().type(2)
            cy.get('[data-context=filter-customer]').should('be.visible').should('be.visible').find('input').type(3)
            cy.get('[data-context=filter-article]').should('be.visible').should('be.visible').find('input').type(4)

            cy.get('[data-context=filters-submit-btn]').click()
            cy.wait('@fetch_quotes').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    page: 1,
                    limit: 25,
                    where: {
                        quote_id: { _eq: '1' },
                        created_by: { _eq: '2' },
                        customer_id: { _eq: '3' },
                        product_id: { _eq: '4' },
                    },
                    order_by: 'created_at',
                    order_direction: 'desc',
                })
            })
        })
    })

    describe('Page filters (ordered)', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            cy.intercept('POST', '**/api/erp/v1/quotes', { fixture: 'erp/quotes/cpost_quotes_ordered' }).as(
                'fetch_quotes',
            )
        })

        it('should be able to use the non-numeric filters successfully', () => {
            // freeze clock - Today is 06/09/2021
            cy.mockDate(MOCKED_DATE)

            cy.visit(PAGE)
            cy.toggleMenu()

            // this fetch is triggered at page load for the currently logged user
            cy.wait('@fetch_quotes').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    page: 1,
                    limit: 25,
                    where: {
                        _and: [
                            {
                                _or: [
                                    { created_by_username: { _ilike: '%anakin.skywalker%' } },
                                    { created_by_firstname: { _ilike: '%anakin.skywalker%' } },
                                    { created_by_lastname: { _ilike: '%anakin.skywalker%' } },
                                ],
                            },
                        ],
                    },
                    order_by: 'created_at',
                    order_direction: 'desc',
                })
            })

            cy.get('[data-context=filter-created-by]').should('be.visible').find('input').clear().type('foo')
            cy.get('[data-context=filter-customer]').should('be.visible').find('input').type('bar')
            cy.get('[data-context=filter-article]').should('be.visible').find('input').type('sku')
            cy.get('[data-context=filter-status]')
                .should('be.visible')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'Envoyé')
            // opens on today's month (Remember: it's 06/09/2021)
            cy.get('[data-context=filter-created-at]').should('be.visible')

            cy.get('[data-context=filter-created-at]').find('[data-context=start-date]').click()
            cy.get('[data-context=filter-created-at]').find('[data-context=day]:contains(9)').eq(0).click()

            cy.get('[data-context=filter-created-at]').should('be.visible')
            cy.get('[data-context=filter-created-at]').find('[data-context=end-date]').click()
            cy.get('[data-context=filter-created-at]').find('[data-context=day]:contains(12)').eq(0).click()

            cy.get('[data-context=filters-submit-btn]').click()
            cy.wait('@fetch_quotes').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    page: 1,
                    limit: 25,
                    where: {
                        _and: [
                            {
                                _or: [
                                    { created_by_username: { _ilike: '%foo%' } },
                                    { created_by_firstname: { _ilike: '%foo%' } },
                                    { created_by_lastname: { _ilike: '%foo%' } },
                                ],
                            },
                            {
                                _or: [
                                    { customer_account_firstname: { _like: 'bar%' } },
                                    { customer_firstname: { _like: 'bar%' } },
                                    { customer_account_lastname: { _like: 'bar%' } },
                                    { customer_lastname: { _like: 'bar%' } },
                                ],
                            },
                            {
                                _and: [
                                    {
                                        is_ordered: { _eq: false },
                                    },
                                    {
                                        expired_at: { _null: 0 },
                                    },
                                    {
                                        expired_at: { _gt: format(MOCKED_DATE, 'YYYY-MM-DD HH:mm:ss') },
                                    },
                                    {
                                        sent_at: { _null: 0 },
                                    },
                                ],
                            },
                        ],
                        sku: { _like: 'SKU%' },
                        created_at: { _between: ['2021-09-09 00:00:00', '2021-09-12 23:59:59'] },
                    },
                    order_by: 'created_at',
                    order_direction: 'desc',
                })
            })
        })

        it('should reload filters from the url on page load', () => {
            // freeze clock - Today is 06/09/2021
            cy.mockDate(MOCKED_DATE)

            cy.visit(
                PAGE +
                    `?pager[filters]=%7B"quote_id"%3A"2"%2C"created_by"%3A"foo"%2C"customer"%3A"bar"%2C"article"%3A"baz"%2C"created_at_start"%3A"2022-04-01%2000%3A00%3A00"%2C"created_at_end"%3A"2022-04-30%2023%3A59%3A59"%2C"status"%3A"expired"%7D`,
            )
            cy.toggleMenu()

            cy.get('[data-context=filter-quote] input').should('have.value', '2')
            cy.get('[data-context=filter-created-by] input').should('have.value', 'foo')
            cy.get('[data-context=filter-customer] input').should('have.value', 'bar')
            cy.get('[data-context=filter-article] input').should('have.value', 'baz')
            cy.get('[data-context=filter-status] [data-context=single-value]').should('contain', 'Expiré')
            cy.get('[data-context=start-date] input').should('have.value', '01 avr. 2022 à 00:00')
            cy.get('[data-context=end-date] input').should('have.value', '30 avr. 2022 à 23:59')

            cy.wait('@fetch_quotes').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    page: 1,
                    limit: 25,
                    where: {
                        _and: [
                            {
                                _or: [
                                    { created_by_username: { _ilike: '%foo%' } },
                                    { created_by_firstname: { _ilike: '%foo%' } },
                                    { created_by_lastname: { _ilike: '%foo%' } },
                                ],
                            },
                            {
                                _or: [
                                    { customer_account_firstname: { _like: 'bar%' } },
                                    { customer_firstname: { _like: 'bar%' } },
                                    { customer_account_lastname: { _like: 'bar%' } },
                                    { customer_lastname: { _like: 'bar%' } },
                                ],
                            },
                            {
                                _and: [
                                    {
                                        is_ordered: { _eq: false },
                                    },
                                    {
                                        expired_at: { _null: 0 },
                                    },
                                    {
                                        expired_at: { _lte: format(MOCKED_DATE, 'YYYY-MM-DD HH:mm:ss') },
                                    },
                                ],
                            },
                        ],
                        quote_id: { _eq: '2' },
                        sku: { _like: 'BAZ%' },
                        created_at: { _between: ['2022-04-01 00:00:00', '2022-04-30 23:59:59'] },
                    },
                    order_by: 'created_at',
                    order_direction: 'desc',
                })
            })
        })
    })
})
