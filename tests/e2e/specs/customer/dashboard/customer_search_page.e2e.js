const { GRAPHQL_ENDPOINT, aliasQuery, hasOperationName } = require('../../../utils/graphql-test-utils')
const PAGE = '/legacy/prospect/prospectSearch'

describe('Customer dashboard page', function () {
    beforeEach(function () {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/magic-search**', { fixture: 'erp/magic-search/customers.json' }).as(
            'customers',
        )
    })

    it('Initialisation - Empty list', function () {
        cy.visit(PAGE)

        cy.get('[data-context=page-header]').should('contain', 'Recherche clients')
        cy.get('[data-context=show-add-prospect-btn').should('contain', 'Ajouter un prospect')
        cy.get('[data-context=filters-submit-btn]').should('contain', 'Rechercher')
        cy.get('[data-context=filter-input]').should('be.empty')
        cy.get('[data-context=erp-table]').should('be.visible')
    })

    it('filter customers', function () {
        cy.visit(PAGE)

        cy.get('[data-context=filter-input').clear().type('Du{enter}')

        cy.wait('@customers').then((xhr) => {
            const url = new URL(xhr.request.url)
            expect(url.searchParams.get('search_terms')).to.eq('Du')
            expect(url.searchParams.get('context')).to.eq('customers')
        })

        cy.get('[data-context=cell-customer-id]').as('customer_id')
        cy.get('[data-context=cell-computed-name]').as('computed_name')
        cy.get('[data-context=cell-email-address]').as('email_address')
        cy.get('[data-context=cell-company-name]').as('company_name')
        cy.get('[data-context=cell-phone]').as('phone')
        cy.get('[data-context=cell-mobile-phone]').as('mobile_phone')
        cy.get('[data-context=cell-country-name]').as('country')

        cy.get('@customer_id').eq(0).should('contain', '333586')
        cy.get('@computed_name')
            .eq(0)
            .should('contain', `M.`)
            .should('contain', `DUPONT DUPOND`)
            .should('contain', 'entreprise')
        cy.get('@email_address').eq(0).should('contain', '<EMAIL>')
        cy.get('@company_name').eq(0).should('contain', '')
        cy.get('@phone').eq(0).should('contain', '+33 2 03 04 05 06')
        cy.get('@mobile_phone').eq(0).should('contain', '+33 6 07 08 09 10')
        cy.get('@country').eq(0).should('contain', 'FRANCE')

        cy.get('@customer_id').eq(1).should('contain', '1661996')
        cy.get('@computed_name')
            .eq(1)
            .should('contain', `M.`)
            .should('contain', `DIDIER DUVAL`)
            .should('not.contain', 'entreprise')
        cy.get('@email_address').eq(1).should('contain', '<EMAIL>')
        cy.get('@company_name').eq(1).should('contain', 'toto')
        cy.get('@phone').eq(1).should('contain', '')
        cy.get('@mobile_phone').eq(1).should('contain', '')
        cy.get('@country').eq(1).should('contain', 'FRANCE')

        // Another filter
        cy.get('[data-context=filter-input]').clear().type('0607080910')

        cy.get('[data-context=filters-submit-btn]').click()
        cy.wait('@customers').then((xhr) => {
            const url = new URL(xhr.request.url)
            expect(url.searchParams.get('search_terms')).to.eq('0607080910')
            expect(url.searchParams.get('context')).to.eq('customers')
        })
    })

    it('create customer ok', function () {
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomerByEmail')
            if (hasOperationName(req, 'fetchCustomerByEmail')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomerByEmail')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_by_email__empty.json',
                })
            }
        })
        cy.visit(PAGE)

        cy.get('[data-context=slide-out-container').should('not.exist')
        cy.get('[data-context=show-add-prospect-btn').should('contain', 'Ajouter un prospect').click()
        cy.get('[data-context=slide-out-container').should('be.visible')
        cy.get('[data-context=prospect-add-title]').should('contain', "Création d'un prospect")
        cy.get('[data-context=profile-email-label]').should('contain', 'Email')
        cy.get('[data-context=profile-email-input]').should('be.empty')
        cy.get('[data-context=confirm-block-submit-btn]').should('contain', 'Créer le compte')
        cy.get('[data-context=confirm-block-cancel-btn]').should('contain', 'Annuler')

        cy.get('[data-context=profile-email-input]').clear().type('   <EMAIL>    ')
        cy.intercept('POST', '**/api/erp/v1/customer', { fixture: 'erp/customer/customerAddSuccess.json' }).as(
            'createCustomerSuccess',
        )
        cy.get('[data-context=confirm-block-submit-btn]').click()
        cy.wait('@createCustomerSuccess').then((xhr) =>
            expect(xhr.request.body.email).to.eq('<EMAIL>'),
        )
    })

    it('create a customer with an invalid email', function () {
        cy.visit(PAGE)
        cy.get('[data-context=slide-out-container').should('not.exist')
        cy.get('[data-context=show-add-prospect-btn').should('contain', 'Ajouter un prospect').click()
        cy.get('[data-context=profile-email-input]').clear().type('email.test.errorgmail.com')
        cy.get('[data-context=confirm-block-submit-btn]').click()
        cy.toast(`Le format de l'email est incorrect`, 'danger')
    })

    it('create customer failed', function () {
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomerByEmail')
            if (hasOperationName(req, 'fetchCustomerByEmail')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomerByEmail')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_by_email__empty.json',
                })
            }
        })
        cy.visit(PAGE)
        cy.get('[data-context=slide-out-container').should('not.exist')
        cy.get('[data-context=show-add-prospect-btn').should('contain', 'Ajouter un prospect').click()
        cy.get('[data-context=slide-out-container').should('be.visible')
        cy.get('[data-context=prospect-add-title]').should('contain', "Création d'un prospect")
        cy.get('[data-context=profile-email-label]').should('contain', 'Email')
        cy.get('[data-context=profile-email-input]').should('be.empty')
        cy.get('[data-context=confirm-block-submit-btn]').should('contain', 'Créer le compte')
        cy.get('[data-context=confirm-block-cancel-btn]').should('contain', 'Annuler')
        cy.get('[data-context=profile-email-input]').clear().type('<EMAIL>')
        cy.intercept('POST', '**/api/erp/v1/customer', {
            statusCode: 500,
            fixture: 'erp/customer/customerAddError.json',
        }).as('createCustomerError')
        cy.get('[data-context=confirm-block-submit-btn]').click()
        cy.wait('@createCustomerError').then((xhr) =>
            expect(xhr.request.body.email).to.eq('<EMAIL>'),
        )

        cy.toast(`Une erreur est survenue lors du chargement, veuillez réessayer.`, 'danger')
    })
    it('create customer with an email already used', () => {
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomerByEmail')
            if (hasOperationName(req, 'fetchCustomerByEmail')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomerByEmail')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_by_email_12345.json',
                })
            }
        })
        cy.visit(PAGE)
        cy.get('[data-context=show-add-prospect-btn').click()
        cy.get('[data-context=slide-out-container').as('content')
        cy.get('[data-context=profile-email-input]').clear().type('<EMAIL>')

        cy.get('@content').should('not.contain', 'Cette adresse email est déjà associée au compte client 12345.')
        cy.get('[data-context=confirm-block-submit-btn]').click()

        cy.get('@content').should('contain', 'Cette adresse email est déjà associée au compte client 12345.')
        cy.get('@content')
            .find('[data-context=alert-content-link]')
            .should('contain', 'Cliquez ici pour y accéder')
            .should('have.attr', 'href', '/customer/12345')
    })

    it('Can cancel', () => {
        cy.visit(PAGE)
        cy.get('[data-context=show-add-prospect-btn').click()
        cy.get('[data-context=slide-out-container').as('content')
        cy.get('[data-context=confirm-block-cancel-btn]').click()

        cy.get('[data-context=slide-out-container]').should('not.exist')
    })
})

describe('opening through Zultys', function () {
    beforeEach(function () {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/magic-search**', { fixture: 'erp/magic-search/customers.json' }).as(
            'customers',
        )
    })

    it('Opens search list with a prefiltered phone number', function () {
        cy.visit(PAGE + '?telephone=0607080910')
        cy.wait('@customers').then((xhr) => {
            const url = new URL(xhr.request.url)
            expect(url.searchParams.get('search_terms')).to.eq('0607080910')
            expect(url.searchParams.get('context')).to.eq('customers')
        })

        cy.get('[data-context=table-row]').should('have.length', 2)
    })

    describe('Redirects to customer page when a phone number is found and autoload is true', function () {
        it('With an exact match', function () {
            cy.visit(PAGE + '?autoload=true&telephone=0607080910')
            cy.wait('@customers')

            cy.location().should((loc) => {
                expect(loc.pathname).to.eq('/customer/333586')
            })
        })

        it('With a phone number starting by 0033', function () {
            cy.visit(PAGE + '?autoload=true&telephone=0033607080910')
            cy.wait('@customers')

            cy.location().should((loc) => {
                expect(loc.pathname).to.eq('/customer/333586')
            })
        })

        it('With a phone number starting by 33', function () {
            cy.visit(PAGE + '?autoload=true&telephone=33607080910')
            cy.wait('@customers')

            cy.location().should((loc) => {
                expect(loc.pathname).to.eq('/customer/333586')
            })
        })

        it('With a phone number starting by +33', function () {
            // "+" is encoded in the url : %2b
            cy.visit(PAGE + '?autoload=true&telephone=%2b33607080910')
            cy.wait('@customers')

            cy.location().should((loc) => {
                expect(loc.pathname).to.eq('/customer/333586')
            })
        })

        it('With a phone number which contain spaces', function () {
            // " " is encoded in the url : %20
            cy.visit(PAGE + '?autoload=true&telephone=06%2007%2008%2009%2010')
            cy.wait('@customers')

            cy.location().should((loc) => {
                expect(loc.pathname).to.eq('/customer/333586')
            })
        })
    })
})
