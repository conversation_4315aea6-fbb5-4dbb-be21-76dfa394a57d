import { GRAPHQL_ENDPOINT, aliasQuery, hasOperationName } from '../../../utils/graphql-test-utils'
import { CUSTOMER_EMAIL_UPDATE } from '../../../../../src/apps/erp/permissions.js'
import { visitPageCustomerEmpty } from './helpers'
describe('Customer update email', () => {
    beforeEach(() => {
        cy.intercept('POST', '**/api/erp/v2/customers', { fixture: 'erp/customer/customerV2.json' })

        // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
        // alias the queries and mutations for our tests in a beforeEach
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'fetchCustomerByEmail')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomerByEmail')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_by_email_12345.json',
                })
            }
        })
    })

    it('Displays without permission', () => {
        cy.authenticate()
        cy.mockErpUser()
        visitPageCustomerEmpty()

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=edit-customer-mail-btn]')
            .should('be.visible')
            .should('contain', "Changer l'adresse email du prospect")
            .click()

        cy.get('[data-context=customer-email-edit]').should('be.visible').as('content')

        // header content
        cy.get('@content').find('[data-context=page-header]').should('contain', "Changer l'adresse email du prospect")

        // ask change email
        cy.get('@content')
            .find('[data-context=ask_change_label]')
            .should('contain', "Demander le changement d'email pour le client")
            .should('be.visible')
            .as('ask_change')

        cy.get('@content').find('[data-context=ask_change_description]').should('be.visible')

        // force change email
        cy.get('@content')
            .find('[data-context=force_change_label]')
            .should('contain', "Forcer le changement de l'adresse email")
            .should('be.visible')
            .as('force_change')

        cy.get('@content').find('[data-context=force_change_description]').should('be.visible')

        cy.get('@ask_change').click()

        cy.get('@content').should('not.contain', "Vous n'avez pas la permission d'utiliser cette fonctionnalité")

        cy.get('@content').find('[data-context=new-email-label]').should('contain', 'Nouvelle adresse email du client')

        cy.get('@content')
            .find('[data-context=new-email-input]')
            .should('have.value', '<EMAIL>')

        cy.get('@content')
            .find('[data-context=confirm-block-submit-btn]')
            .should('not.be.disabled')
            .should('contain', "Envoyer la demande de changement d'email")

        cy.get('@content')
            .find('[data-context=confirm-block-cancel-btn]')
            .should('be.visible')
            .should('contain', 'Annuler')

        cy.get('@force_change').click()

        cy.get('@content').should('contain', "Vous n'avez pas la permission d'utiliser cette fonctionnalité")
    })

    it('Displays with permission', () => {
        cy.authenticate()
        cy.mockErpUser([CUSTOMER_EMAIL_UPDATE])
        visitPageCustomerEmpty()

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=edit-customer-mail-btn]')
            .should('be.visible')
            .should('contain', "Changer l'adresse email du prospect")
            .click()

        cy.get('[data-context=customer-email-edit]').should('be.visible').as('content')

        // header content
        cy.get('@content').find('[data-context=page-header]').should('contain', "Changer l'adresse email du prospect")

        // ask change email
        cy.get('@content')
            .find('[data-context=ask_change_label]')
            .should('contain', "Demander le changement d'email pour le client")
            .should('be.visible')
            .as('ask_change')

        cy.get('@content').find('[data-context=ask_change_description]').should('be.visible')

        // force change email
        cy.get('@content')
            .find('[data-context=force_change_label]')
            .should('contain', "Forcer le changement de l'adresse email")
            .should('be.visible')
            .as('force_change')

        cy.get('@content').find('[data-context=force_change_description]').should('be.visible')

        cy.get('@ask_change').click()

        cy.get('@content').should('not.contain', "Vous n'avez pas la permission d'utiliser cette fonctionnalité")

        cy.get('@content').find('[data-context=new-email-label]').should('contain', 'Nouvelle adresse email du client')

        cy.get('@content')
            .find('[data-context=new-email-input]')
            .should('have.value', '<EMAIL>')

        cy.get('@content')
            .find('[data-context=confirm-block-submit-btn]')
            .should('not.be.disabled')
            .should('contain', "Envoyer la demande de changement d'email")

        cy.get('@content')
            .find('[data-context=confirm-block-cancel-btn]')
            .should('be.visible')
            .should('contain', 'Annuler')

        cy.get('@force_change').click()

        cy.get('@content').should('not.contain', "Vous n'avez pas la permission d'utiliser cette fonctionnalité")

        cy.get('@content').find('[data-context=new-email-label]').should('contain', 'Nouvelle adresse email du client')

        cy.get('@content')
            .find('[data-context=new-email-input]')
            .should('have.value', '<EMAIL>')

        cy.get('@content')
            .find('[data-context=confirm-block-submit-btn]')
            .should('not.be.disabled')
            .should('contain', "Modifier l'adresse email")

        cy.get('@content')
            .find('[data-context=confirm-block-cancel-btn]')
            .should('be.visible')
            .should('contain', 'Annuler')
    })

    it('Can cancel', () => {
        cy.authenticate()
        cy.mockErpUser()
        visitPageCustomerEmpty()

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=edit-customer-mail-btn]').click()

        cy.get('[data-context=customer-email-edit]').should('be.visible').as('content')

        // header content
        cy.get('@content').find('[data-context=page-header]').should('contain', "Changer l'adresse email du prospect")

        cy.get('@content')
            .find('[data-context=confirm-block-cancel-btn]')
            .should('be.visible')
            .should('contain', 'Annuler')
            .click()

        cy.get('[data-context=customer-email-edit]').should('not.exist')
    })

    it('Send ask change email with an new email already used', () => {
        cy.authenticate()
        cy.mockErpUser()
        visitPageCustomerEmpty()

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=edit-customer-mail-btn]').should('be.visible').click()

        cy.get('[data-context=customer-email-edit]').should('be.visible').as('content')

        cy.get('@content').find('[data-context=ask_change_label]').should('be.visible').click()

        cy.get('@content')
            .find('[data-context=new-email-input]')
            .should('have.value', '<EMAIL>')
            .as('new_email')

        cy.get('@content').should('not.contain', 'Cette adresse email est déjà associée au compte client 12345.')

        cy.get('@content')
            .find('[data-context=confirm-block-submit-btn]')
            .should('not.be.disabled')
            .click()
            .should('not.be.disabled')

        cy.get('@content').should('contain', 'Cette adresse email est déjà associée au compte client 12345.')
        cy.get('@content')
            .find('[data-context=alert-content-link]')
            .should('contain', 'Cliquez ici pour y accéder')
            .should('have.attr', 'href', '/customer/12345')

        cy.get('@new_email').clear().type('<EMAIL>')

        cy.get('@content').should('not.contain', 'Cette adresse email est déjà associée au compte client 12345.')
    })

    it('Send ask change email success', () => {
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomerByEmail')
            if (hasOperationName(req, 'fetchCustomerByEmail')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomerByEmail')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_by_email__empty.json',
                })
            }
        })

        cy.intercept('POST', '**/v1/customer/ask-change-email', {
            statusCode: 204,
        }).as('customer-ask-change-email')
        cy.authenticate()
        cy.mockErpUser()
        cy.visit('/customer/12345')

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=edit-customer-mail-btn]').should('be.visible').click()

        cy.get('[data-context=customer-email-edit]').should('be.visible').as('content')

        cy.get('@content').find('[data-context=ask_change_label]').should('be.visible').click()

        cy.get('@content')
            .find('[data-context=new-email-input]')
            .should('have.value', '<EMAIL>')
            .clear()
            .type('<EMAIL>')

        cy.get('@content')
            .find('[data-context=confirm-block-submit-btn]')
            .should('not.be.disabled')
            .click()
            .should('not.be.disabled')

        cy.wait('@customer-ask-change-email').then((xhr) => {
            expect(xhr.request.body.old_email).to.eq('<EMAIL>')
            expect(xhr.request.body.new_email).to.eq('<EMAIL>')
        })

        cy.get('[data-context=slide-out-container]').should('not.exist')

        cy.toast("La demande de changement d'email a bien été envoyée", 'success')
    })

    it('Update email with an email address which already existing', () => {
        cy.authenticate()
        cy.mockErpUser([CUSTOMER_EMAIL_UPDATE])
        visitPageCustomerEmpty()

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=edit-customer-mail-btn]').should('be.visible').click()

        cy.get('[data-context=customer-email-edit]').should('be.visible').as('content')

        cy.get('@content').find('[data-context=force_change_label]').should('be.visible').click()

        // after click forced_update
        cy.get('@content')
            .find('[data-context=new-email-input]')
            .should('have.value', '<EMAIL>')
            .as('new_email')

        cy.get('@content').should('not.contain', 'Cette adresse email est déjà associée au compte client 12345.')

        cy.get('@content')
            .should('not.be.disabled')
            .find('[data-context=confirm-block-submit-btn]')
            .should('not.be.disabled')
            .click()
            .should('not.be.disabled')

        cy.get('@content').should('contain', 'Cette adresse email est déjà associée au compte client 12345.')

        cy.get('@content')
            .find('[data-context=alert-content-link]')
            .should('contain', 'Cliquez ici pour y accéder')
            .should('have.attr', 'href', '/customer/12345')

        cy.get('@new_email').clear().type('<EMAIL>')

        cy.get('@content').should('not.contain', 'Cette adresse email est déjà associée au compte client 12345.')
    })

    it('Update email success', () => {
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomerByEmail')
            if (hasOperationName(req, 'fetchCustomerByEmail')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomerByEmail')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_by_email__empty.json',
                })
            }
        })

        cy.intercept('PUT', '**/v1/customer/email', {
            statusCode: 204,
        }).as('customer-email-updated')
        cy.authenticate()
        cy.mockErpUser([CUSTOMER_EMAIL_UPDATE])
        cy.visit('/customer/12345')
        cy.initReloadTest()

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=edit-customer-mail-btn]').should('be.visible').click()

        cy.get('[data-context=customer-email-edit]').should('be.visible').as('content')

        cy.get('@content').find('[data-context=force_change_label]').should('be.visible').click()

        // after click forced_update
        cy.get('@content')
            .find('[data-context=new-email-input]')
            .should('have.value', '<EMAIL>')
            .clear()
            .type('     <EMAIL>      ')

        cy.get('@content').find('[data-context=confirm-block-submit-btn]').should('not.be.disabled').click()

        cy.wait('@customer-email-updated').then((xhr) => {
            expect(xhr.request.body.old_email).to.eq('<EMAIL>')
            expect(xhr.request.body.new_email).to.eq('<EMAIL>')
        })

        cy.checkHasBeenReloaded()
    })

    it('Update email with an invalid email', () => {
        cy.authenticate()
        cy.mockErpUser()
        visitPageCustomerEmpty()
        cy.initReloadTest()

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=edit-customer-mail-btn]').click()

        cy.get('[data-context=customer-email-edit]').as('content')

        // after click forced_update
        cy.get('@content')
            .find('[data-context=new-email-input]')
            .should('have.value', '<EMAIL>')
            .clear()
            .type('support-informatiquephc-holding.com')

        cy.get('[data-context=toast-message]').should('not.exist')

        cy.get('@content')
            .find('[data-context=confirm-block-submit-btn]')
            .should('not.be.disabled')
            .click()
            .should('be.disabled')

        cy.get('@content').should('contain', "Le format de l'email est incorrect")
    })
})
