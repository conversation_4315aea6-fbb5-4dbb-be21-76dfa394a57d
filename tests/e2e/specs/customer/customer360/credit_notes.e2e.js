import { aliasQuery, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../../../utils/graphql-test-utils'

describe('Customer credit notes panel', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v2/customers', { fixture: 'erp/customer/customerV2.json' }).as('customer')

        // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
        // alias the queries and mutations for our tests in a beforeEach
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomer')

            // Default
            if (hasOperationName(req, 'fetchCustomer')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomer')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_account_information_by_pk_12345.json',
                })
            }
        })

        cy.intercept('GET', '**/api/erp/v1/customer/12345/credit-notes', {
            fixture: 'erp/customer/credit-notes/has_credit_notes.json',
        }).as('credit_notes')

        cy.visit('/customer/12345')

        cy.wait(['@customer', getQueryAlias('fetchCustomer'), '@credit_notes'])
    })

    it('display credit notes in a panel', () => {
        // retrieve panel and check its title
        cy.get('[data-context=credit-notes-widget]')
            .as('panel')
            .find('[data-context=title]')
            .should('contain', 'Montants disponibles en avoir')

        // Check table header
        cy.get('@panel').find('[data-context=erp-table]').as('table')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 4)
        cy.get('@header').eq(0).should('contain', 'Commande')
        cy.get('@header').eq(1).should('contain', 'Montant initial')
        cy.get('@header').eq(2).should('contain', 'Montant restant')
        cy.get('@header').eq(3).should('contain', 'Date de création')

        // should have only 2 rows displayed (no remaining credit on others)
        cy.get('@table').find('tbody tr').should('have.length', 2)

        // content of first row
        cy.get('@table')
            .selectCell(0, 0)
            .find('[data-context=customer-order-origin]')
            .should('contain', '1076434')
            .should(
                'have.attr',
                'href',
                'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=1076434',
            )
        cy.get('@table').selectCell(0, 1).find('[data-context=initial-amount]').should('contain', '10,11 €')
        cy.get('@table').selectCell(0, 2).find('[data-context=available-amount]').should('contain', '10,11 €')
        cy.get('@table').selectCell(0, 3).find('[data-context=created-at]').should('contain', '23/12/2015 à 17:45')

        // content of second row
        cy.get('@table')
            .selectCell(1, 0)
            .find('[data-context=customer-order-origin]')
            .should('contain', '1076434')
            .should(
                'have.attr',
                'href',
                'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=1076434',
            )
        cy.get('@table').selectCell(1, 1).find('[data-context=initial-amount]').should('contain', '16,00 €')
        cy.get('@table').selectCell(1, 2).find('[data-context=available-amount]').should('contain', '12,86 €')
        cy.get('@table').selectCell(1, 3).find('[data-context=created-at]').should('contain', '23/12/2015 à 17:44')
    })
})
