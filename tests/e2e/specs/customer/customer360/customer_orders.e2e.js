const { GRAPHQL_ENDPOINT, aliasQuery, hasOperationName, getQuery<PERSON>lias } = require('../../../utils/graphql-test-utils')

describe('Customer orders panel', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v2/customers', { fixture: 'erp/customer/customerV2.json' }).as('customer')
        // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
        // alias the queries and mutations for our tests in a beforeEach
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomer')

            // Default
            if (hasOperationName(req, 'fetchCustomer')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomer')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_account_information_by_pk_12345.json',
                })
            }
        })
        cy.intercept('POST', '**/api/erp/v1/customer-orders', {
            fixture: 'erp/customer-order/customer_order_1572493.json',
        }).as('customer_orders')
        cy.visit('/customer/1572493', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })
        cy.wait(['@customer', getQueryAlias('fetchCustomer')])
    })

    it('display customer orders info in a panel', () => {
        cy.wait('@customer_orders').then((xhr) => {
            expect(xhr.request.body.where._and[0].customer_id._eq).to.eq(1572493)
            expect(xhr.request.body.limit).to.eq(5)
            expect(xhr.request.body.included_dependencies).to.deep.eq(['payments'])
        })
        cy.get('[data-context=customer-orders-widget]')
            .as('customer_orders_panel')
            .find('[data-context=title]')
            .should('contain', 'Commandes')

        // Check table header
        cy.get('@customer_orders_panel').find('[data-context=erp-table]').as('table')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 6)
        let i = 0
        cy.get('@header').eq(i++).should('contain', '#')
        cy.get('@header').eq(i++).should('contain', 'Date création')
        cy.get('@header').eq(i++).should('contain', 'Statut')
        cy.get('@header').eq(i++).should('contain', 'Montant TTC')
        cy.get('@header').eq(i++).should('contain', 'Détail Paiements')
        cy.get('@header').eq(i++).should('contain', 'Source')

        // content first row
        cy.get('[data-context=table-row]').should('have.length', 3).eq(0).as('first_row')
        cy.get('@first_row')
            .find('[data-context=customer_order_id]')
            .should('contain', '3078600')
            .should(
                'have.attr',
                'href',
                'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=3078600',
            )
        cy.get('@first_row').find('[data-context=date-formatter]').should('contain', '16/06/2021')
        cy.get('@first_row').find('[data-context=status] [data-context=badge]').should('have.length', 2).as('statuses')
        cy.get('@statuses').eq(0).should('contain', 'Paiement en attente')
        cy.get('@statuses').eq(1).should('contain', 'Paiement incohérent')
        cy.get('@first_row')
            .find('[data-context=source]')
            .should('contain', 'source')
            .should('contain', 'Son-Video.com')
            .should('contain', 'Web SV')
        cy.get('@first_row').find('[data-context=amount_all_tax_included]').should('contain', '184,85 €')
        cy.get('@first_row').find('[data-context=payment]').should('have.length', 1).as('payments')
        cy.get('@payments')
            .eq(0)
            .should('contain', 'ESP')
            .should('contain', '184,85 €')
            .find('[data-context=payments] [data-context=scope]')
            .should('have.class', 'bg-lime-50')

        // content second row
        cy.get('[data-context=table-row]').eq(1).as('second_row')
        cy.get('@second_row')
            .find('[data-context=customer_order_id]')
            .should('contain', '2045423')
            .should(
                'have.attr',
                'href',
                'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=2045423',
            )
        cy.get('@second_row').find('[data-context=date-formatter]').should('contain', '10/03/2021')
        cy.get('@second_row').find('[data-context=status] [data-context=badge]').should('have.length', 1).as('statuses')
        cy.get('@statuses').eq(0).should('contain', 'Clôturée').should('have.class', 'bg-lime-50')
        cy.get('@second_row').find('[data-context=amount_all_tax_included]').should('contain', '64,32 €')
        cy.get('@second_row').find('[data-context=payment]').should('have.length', 3).as('payments')
        cy.get('@payments')
            .eq(0)
            .should('contain', 'EMPT')
            .should('contain', '64,32 €')
            .find('[data-context=payments] [data-context=scope]')
            .should('have.class', 'bg-red-50')
        cy.get('@payments')
            .eq(1)
            .should('contain', 'BKDO')
            .should('contain', '60,00 €')
            .find('[data-context=payments] [data-context=scope]')
            .should('have.class', 'bg-lime-50')
        cy.get('@payments')
            .eq(2)
            .should('contain', 'CTPE')
            .should('contain', '4,32 €')
            .find('[data-context=payments] [data-context=scope]')
            .should('have.class', 'bg-lime-50')
        cy.get('@second_row')
            .find('[data-context=origin]')
            .should('contain', 'origine')
            .should('contain', 'sonvideo.com')
    })

    it('can open all customer orders page', () => {
        cy.get('[data-context=customer-orders-widget]:contains(Commandes)')
            .find('[data-context=actions] [data-context=customer-orders-link]')
            .should('contain', 'Voir toutes les commandes')
            .click()
        cy.get('@windowOpen').should(
            'be.calledWith',
            'http://erp-client.lxc/legacy/commande/recherche?filter=pct_id&value=1572493',
            '_blank',
        )
    })
})
