import { mockEmptyResult } from '../../../utils/erp-server-utils'

describe('Message panel', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v2/customers', { fixture: 'erp/customer/customerV2.json' }).as('customer')
        cy.intercept('POST', '**/api/erp/v1/customer-messages', (req) => {
            if ('id' in req.body.where._and[0]) {
                req.reply({
                    statusCode: 200,
                    fixture: 'erp/customer/messages/cpost_customer_messages_after_reply.json',
                })

                return
            }

            req.reply({
                statusCode: 200,
                fixture: 'erp/customer/messages/cpost_customer_messages.json',
            })
        }).as('customer-messages')

        // mock other blocks calls to avoid error messages fucking the tests
        cy.intercept('POST', '**/api/erp/v1/quotations', mockEmptyResult('quotations'))
        cy.intercept('POST', '**/api/erp/v1/quotes', mockEmptyResult('quotes'))
        cy.intercept('GET', '**/api/erp/v1/customer/12345/credit-notes', mockEmptyResult('credit-notes'))
        cy.intercept('POST', '**/api/erp/v1/customer-orders', mockEmptyResult('customer_orders'))
        cy.intercept('POST', '**/api/erp/v1/tasks', mockEmptyResult('tasks'))
        cy.intercept('POST', '**/api/erp/v1/after-sale-services', mockEmptyResult('after_sale_services'))
        cy.intercept(
            'GET',
            '**/api/erp/v1/customer/12345/bought-products?limit=20&page=1',
            mockEmptyResult('bought_products'),
        )
    })

    describe('view table messages', function () {
        it('display customer messages', () => {
            cy.visit('/customer/12345')
            cy.wait('@customer-messages').then((xhr) => {
                expect(xhr.request.body.where._and[0].customer_id._eq).to.eq(12345)
            })

            cy.get('[data-context=customer-message-widget]')
                .as('customer-message')
                .find('[data-context=title]')
                .should('contain', 'Messages')

            //check table message
            cy.get('@customer-message').find('[data-context=erp-table]').as('table')
            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 4)
            cy.get('@header').eq(0).should('contain', '#')
            cy.get('@header').eq(1).should('contain', 'Date création')
            cy.get('@header').eq(2).should('contain', 'motif')
            cy.get('@header').eq(3).should('contain', 'message')

            cy.get('[data-context=table-row]').should('have.length', 5).eq(0).as('first_row')

            // Column #
            cy.get('@first_row').find('[data-context=scoped-badge]').as('scoped-badges')
            cy.get('@scoped-badges').eq(0).find('[data-context=scope]').should('contain', 'REP')
            cy.get('@scoped-badges').eq(0).find('[data-context=content]').should('contain', '448486')
            cy.get('@first_row').find('[data-context=cell-message-id] [data-context=badge]').should('not.exist')

            // Column created-at
            cy.get('@first_row').find('[data-context=cell-created-at]').should('contain', '09/03/2023')

            // Column motif
            cy.get('@first_row').find('[data-context=cell-subject]').should('contain', 'Re: tmp')

            // Column message
            cy.get('@first_row').find('[data-context=cell-message]').find('p').should('have.class', 'truncate')

            // Second message has 1 reply
            cy.get('[data-context=table-row]').should('have.length', 5).eq(1).as('second_row')
            cy.get('@second_row')
                .find('[data-context=cell-message-id] [data-context=badge]')
                .should('be.visible')
                .should('contain', 1)

            // Pagination
            cy.get('@customer-message')
                .find('[data-context=pagination-info]')
                .should('be.visible')
                .should('contain', '1-5 sur 6')
        })
    })

    describe('view message in slide in', function () {
        it('open slide in and display details', function () {
            cy.visit('/customer/12345')
            cy.wait('@customer-messages')

            cy.get('[data-context=customer-message-widget] [data-context=table-row]').eq(1).as('message')

            cy.get('@message').find('[data-context=date-formatter]').should('contain', '07/03/2023')
            cy.get('@message').find('[data-context=cell-subject]').should('contain', `Besoin d'un conseil technique`)
            cy.get('@message').find('[data-context=cell-message-id]').click()

            cy.get('[data-context=customer-message-slide-in]').as('slide-in')

            cy.get('@slide-in').find('[data-context=page-header]').should('contain', `Besoin d'un conseil technique`)

            cy.get('@slide-in').find('[data-context=event]').as('messages')
            cy.get('@messages').should('have.length', 2)

            cy.get('@messages').eq(0).as('first-message')
            cy.get('@first-message').find('[data-context=title]').should('contain', 'Message du client')
            cy.get('@first-message').find('[data-context=date]').should('contain', '07/03/2023')
            cy.get('@first-message')
                .find('[data-context=message]')
                .should(
                    'contain',
                    'Bonjour,\r\nj\u0027envisage l\u0027achat d\u0027un DAC FIIO K9 pro ESS pour une utilisation comme DAC et comme ampli casque. Est-il possible de brancher le K9 sur les sorties de mon lecteur CD (Incadesign Katana) pour une utilisation ampli casque et sur quelle connectique ? Sinon comment le connecter comme ampli casque sur le lecteur ?\r\nJe vous remercie pour votre attention.\r\nBien cordialement\r\nPhilippe',
                )

            cy.get('@messages').eq(1).as('second-message')
            cy.get('@second-message').find('[data-context=title]').should('contain', '<EMAIL>')
            cy.get('@second-message').find('[data-context=date]').should('contain', '08/03/2023')
            cy.get('@second-message')
                .find('[data-context=message]')
                .should(
                    'contain',
                    `Bonjour, M.Bossi\n\nMalheureusement,  nous  ne  proposons pas  de  pi\u00e8ce detach\u00e9es.\nJe vous invite  \u00e0  contacter  le  service  apr\u00e8s vente  de  seenheiser. Voici l?adresse  mail <EMAIL>\nEn esp\u00e9rant avoir r\u00e9pondu \u00e0 vos attentes. Pour toute autre question, h\u00e9sitez pas \u00e0 nous joindre au ***********.30\r\n\r\nCordialement, Eric pour le service client\u00e8le.\r\nMerci de nous donner votre avis sur notre service client\u00e8le en\r\ncliquant ici :\r\n\r\nhttp:\/\/www.son-video.com\/Enquetes\/ServiceClient.html\r\n\r\n__________________________\r\n\r\nLe meilleur rapport \u00e9motion\/prix\r\n\r\nSon-Vid\u00e9o.com\r\nhttp:\/\/www.son-video.com\/\r\nService Client\u00e8le : 0826 960 290 (0,15 euro\/min.)`,
                )
        })

        it('prevent reply to a message of type response', function () {
            cy.visit('/customer/12345')
            cy.wait('@customer-messages')

            cy.get('[data-context=customer-message-widget] [data-context=table-row]')
                .eq(0)
                .find('[data-context=cell-message-id]')
                .click()

            cy.get('[data-context=customer-message-slide-in] textarea').should('not.exist')
            cy.get('[data-context=customer-message-slide-in] [data-context=erp-button]').should('not.exist')
        })

        it('reply successfully to a message', function () {
            cy.intercept('POST', '**/api/erp/v1/customer-message/448247/reply', { statusCode: 500 }).as('fetch-reply')

            cy.visit('/customer/12345')
            cy.wait('@customer-messages')

            cy.get('[data-context=customer-message-widget] [data-context=table-row]')
                .eq(1)
                .find('[data-context=cell-message-id]')
                .contains('448247')
                .click()

            cy.get('[data-context=customer-message-slide-in] textarea').as('textarea')
            cy.get('[data-context=customer-message-slide-in] [data-context=erp-button]').as('button')

            cy.get('@textarea').type('Salut les zozos')
            cy.get('@button').click()

            cy.wait('@fetch-reply')
            cy.toast('Une erreur est survenue, veuillez ré-essayer.', 'danger')
        })

        it('reply successfully to a message', function () {
            cy.intercept('POST', '**/api/erp/v1/customer-message/448247/reply', { statusCode: 204 }).as('fetch-reply')

            cy.visit('/customer/12345')
            cy.wait('@customer-messages')

            cy.get('[data-context=customer-message-widget] [data-context=table-row]').eq(1).as('message-row')
            cy.get('@message-row').find('[data-context=cell-message-id]').contains('448247').click()

            cy.get('[data-context=tracking-contact-svd-timeline-item]').should('have.length', 1)

            cy.get('[data-context=customer-message-slide-in] textarea').as('textarea')
            cy.get('[data-context=customer-message-slide-in] [data-context=erp-button]').as('button')

            cy.get('[data-context=error-message]').should('not.exist')
            cy.get('@button').should('not.be.disabled')

            cy.get('@button').click()
            cy.get('[data-context=error-message]').as('error-message')
            cy.get('@error-message').should('be.visible').should('contain', 'Ce champ est obligatoire')
            cy.get('@button').should('be.disabled')

            cy.get('@textarea').type('Salut les zozos')
            cy.get('@error-message').should('not.exist')
            cy.get('@button').click()

            cy.wait('@fetch-reply').then((xhr) => {
                expect(xhr.request.body.message).to.eq('Salut les zozos')
            })

            cy.wait('@customer-messages').then((xhr) => {
                expect(xhr.request.body.where._and[0].id._eq).to.eq(448247)
            })

            cy.get('[data-context=tracking-contact-svd-timeline-item]').as('svd-messages')
            cy.get('@svd-messages').should('have.length', 2)
            cy.get('@svd-messages').last().as('last-message')
            cy.get('@last-message').find('[data-context=title]').should('be.contain', '<EMAIL>')
            cy.get('@last-message').find('[data-context=date]').should('be.contain', 'le 20/03/2023 à 14:29')
            cy.get('@last-message').find('[data-context=message]').should('be.contain', 'Salut les zozos')

            cy.get('@message-row').find('[data-context=badge]').should('contain', 2)
        })
    })
})
