import { aliasQuery, GRAPHQL_ENDPOINT, hasOperationName } from '../../../utils/graphql-test-utils'

const mockEmptyResult = (key) => {
    const result = {
        status: 'success',
        data: {
            _pager: {
                from: 0,
                to: 0,
                total: 0,
                page: 1,
                limit: 5,
                last_page: 1,
            },
        },
    }

    result.data[key] = []

    return { body: result, statusCode: 200 }
}

export const visitPageCustomerEmpty = () => {
    cy.intercept('POST', '**/api/erp/v2/customers', { fixture: 'erp/customer/customerV2.json' })
    cy.intercept('POST', '**/api/erp/v1/quotes', mockEmptyResult('quotes'))
    cy.intercept('POST', '**/api/erp/v1/customer-orders', mockEmptyResult('customer_orders'))
    cy.intercept('POST', '**/api/erp/v1/customer-messages', mockEmptyResult('messages'))
    cy.intercept('POST', '**/api/erp/v1/tasks', mockEmptyResult('tasks'))
    cy.intercept('POST', '**/api/erp/v1/after-sale-services', mockEmptyResult('after_sale_services'))
    cy.intercept('GET', '**/api/erp/v1/customer/12345/credit-notes', mockEmptyResult('credit_notes'))
    cy.intercept('GET', '**/api/erp/v1/customer/12345/bought-products?*', mockEmptyResult('bought_products'))

    // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
    // alias the queries and mutations for our tests in a beforeEach
    cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
        // Queries
        aliasQuery(req, 'fetchCustomer')

        // Default
        if (hasOperationName(req, 'fetchCustomer')) {
            // Declare the alias from the initial intercept in the beforeEach
            aliasQuery(req, 'fetchCustomer')

            // Set req.fixture for the response
            req.reply({
                fixture: 'graphql/customer/customer_account_information_by_pk_12345.json',
            })
        }
    })

    cy.visit('customer/12345')
}
