const { GRAPHQL_ENDPOINT, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, alias<PERSON><PERSON>y, hasOperationName } = require('../../../utils/graphql-test-utils')

describe('Customer page', function () {
    const mockEmptyResult = (key) => {
        const result = {
            status: 'success',
            data: {
                _pager: {
                    from: 0,
                    to: 0,
                    total: 0,
                    page: 1,
                    limit: 5,
                    last_page: 1,
                },
            },
        }

        result.data[key] = []

        return { body: result, statusCode: 200 }
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v2/customers', { fixture: 'erp/customer/customerV2.json' }).as('customer')

        // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
        // alias the queries and mutations for our tests in a beforeEach
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomer')

            // Default
            if (hasOperationName(req, 'fetchCustomer')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomer')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_account_information_by_pk_12345.json',
                })
            }
        })

        cy.intercept('POST', '**/api/erp/v1/quotes', { fixture: 'erp/quotes/customer_quotes.json' }).as('cpost_quotes')
        cy.intercept('GET', '**/api/erp/v1/customer/12345/credit-notes', {
            fixture: 'erp/customer/credit-notes/has_credit_notes.json',
        }).as('credit_notes')

        // mock other blocks calls to avoid error messages fucking the tests
        cy.intercept('POST', '**/api/erp/v1/customer-orders', mockEmptyResult('customer_orders'))
        cy.intercept('POST', '**/api/erp/v1/tasks', mockEmptyResult('tasks'))
        cy.intercept('POST', '**/api/erp/v1/after-sale-services', mockEmptyResult('after_sale_services'))
        cy.intercept(
            'GET',
            '**/api/erp/v1/customer/12345/bought-products?limit=20&page=1',
            mockEmptyResult('bought_products'),
        )
        cy.intercept('POST', '**/api/erp/v1/customer-messages', mockEmptyResult('messages'))

        cy.visit('/customer/12345')
        cy.wait(['@credit_notes', '@customer', getQueryAlias('fetchCustomer')])
    })

    it('launches customer infos', () => {
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'fetchCustomer')) {
                expect(req.body.variables).to.deep.eq({
                    customer_id: 12345,
                })
            }
        })
        cy.visit('/customer/12345')
        cy.wait('@customer').then((xhr) => {
            expect(xhr.request.body.where._and[0].customer_id._eq).to.eq(12345)
        })
    })

    it('update tab title with customer name', () => {
        cy.title().should('eq', 'Guy Liguili Fiche 360 - Son-Vidéo.com ERP')
    })

    it('display customer main info in the page header', () => {
        // fix date for birthdate
        cy.mockDate(new Date(2022, 6, 1, 15, 44, 0, 0))

        cy.visit('/customer/12345')

        cy.get('[data-context=page-header]').as('customer_banner').find('[data-context=id]').should('contain', '12345')

        cy.get('@customer_banner').find('[data-context=fullname]').should('contain', 'Mme GUY LIGUILI')

        cy.get('@customer_banner').find('[data-context=birthdate]').should('contain', '(26 ans)')

        cy.get('@customer_banner').find('[data-context=company]').should('contain', 'PHC Holding')

        cy.get('@customer_banner').find('[data-context=blacklist-icon]').should('be.visible')

        cy.get('@customer_banner').find('[data-context=type]').should('contain', 'entreprise')

        cy.get('@customer_banner').find('[data-context=status]').should('contain', 'Actif')

        cy.get('@customer_banner')
            .find('[data-context=premium-warranty-tag]')
            .should('be.visible')
            .should('contain', 'Garantie premium')
    })

    it('display customer account info in a panel', () => {
        cy.get('[data-context=erp-panel]')
            .eq(0)
            .as('account_panel')
            .find('[data-context=title]')
            .should('contain', 'Infos compte')

        cy.get('@account_panel').find('[data-context=email-label]').should('contain', 'Adresse email')
        cy.get('@account_panel').find('[data-context=email]').should('contain', '<EMAIL>')
        cy.get('@account_panel').find('[data-context=phone-label]').should('contain', 'Téléphone')
        cy.get('@account_panel').find('[data-context=phone]').should('contain', '+33 1 02 03 04 05')
        cy.get('@account_panel').find('[data-context=mobile]').should('contain', '+33 6 07 08 09 00')
        cy.get('@account_panel').find('[data-context=address-label]').should('contain', 'Adresse')
        cy.get('@account_panel')
            .find('[data-context=address]')
            .should('contain', 'Guy Liguili')
            .should('contain', '38 rue de la ville en bois')
            .should('contain', '44100 NANTES')
            .should('contain', 'FR')
        cy.get('@account_panel').find('[data-context=open-address-editor]').should('contain', 'Éditer les adresses')
        cy.get('@account_panel').find('[data-context=creation-label]').should('contain', 'Date de création')
        cy.get('@account_panel').find('[data-context=creation-date]').should('contain', '12 août 2015')
        cy.get('@account_panel').find('[data-context=orders-label]').should('contain', 'Nombre de commandes payées')
        cy.get('@account_panel').find('[data-context=orders-total]').should('contain', '5')
        cy.get('@account_panel').find('[data-context=amount-label]').should('contain', 'Total TTC commandes')
        cy.get('@account_panel').find('[data-context=orders-amount]').should('contain', '405,93 €')
        cy.get('@account_panel')
            .find('[data-context=available-credit-label]')
            .should('contain', 'Montant disponible en avoir')
        cy.get('@account_panel').find('[data-context=available-credit-amount]').should('contain', '168,15 €')
        cy.get('@account_panel').find('[data-context=encours-interne-label]').should('contain', 'Encours')
        cy.get('@account_panel').find('[data-context=encours-interne-amount]').should('contain', '1 838,07 €')
        cy.get('@account_panel').find('[data-context=encours-sfac-label]').should('contain', 'Encours Sfac')
        cy.get('@account_panel').find('[data-context=encours-sfac-amount]').should('contain', '12 000,00 €')
    })

    it('display more actions in the account info panel', () => {
        cy.closeAllToasts()

        cy.get('[data-context=erp-panel]').eq(0).as('account_panel')

        cy.get('@account_panel')
            .find('[data-context=actions]')
            .as('actions')
            .find('[data-context=customer-from-btn]')
            .click()

        cy.get('[data-context=slide-out-container] [data-context=customer-form]')
            .should('be.visible')
            .should('contain', 'Édition compte')

        cy.get('[data-context=slide-out-container] [data-context=slide-out-container-close-btn]').click()

        cy.get('@account_panel').find('[data-context=open-address-editor]').click()
        cy.get('[data-context=slide-out-container] [data-context=customer-address-book]').as('customer-address-book')
        cy.get('@customer-address-book').should('be.visible').should('contain', "Carnet d'adresse")
        cy.get('@customer-address-book').find('[data-context=select-address]').should('not.exist')
        cy.get('@customer-address-book').find('[data-context=address-input]').should('not.exist')

        cy.get('[data-context=slide-out-container] [data-context=slide-out-container-close-btn]').eq(0).click()

        // check item on menu
        cy.get('@actions')
            .find('[data-context=dropdown-menu]')
            .click()
            .find('[data-context=dropdown-menu-item]')
            .should('have.length', 3)

        cy.get('[data-context=dropdown-menu-item]').eq(0).find('span').should('contain', 'Anonymiser le compte client')

        cy.get('[data-context=dropdown-menu-item]')
            .eq(1)
            .find('span')
            .should('contain', "Changer l'adresse email du prospect")

        cy.get('[data-context=dropdown-menu-item]')
            .eq(2)
            .find('a')
            .should('contain', 'Compte CMS')
            .should('have.attr', 'href', 'http://bo-cms.url/customer/edit/12345')

        // check open slid-out anonymize
        cy.get('[data-context=anonymize-prospect-btn]').click()
        cy.get('[data-context=slide-out-container] [data-context=customer-anonymize]')
            .should('be.visible')
            .should('contain', 'Anonymiser le client')
        cy.get('[data-context=slide-out-container] [data-context=slide-out-container-close-btn]').click()

        // check open slid-out edit email
        cy.get('@actions').find('[data-context=dropdown-menu]').click()

        cy.get('[data-context=edit-customer-mail-btn]').click()
        cy.get('[data-context=slide-out-container] [data-context=customer-email-edit]')
            .should('be.visible')
            .should('contain', "Changer l'adresse email du prospect")

        cy.get('[data-context=slide-out-container] [data-context=slide-out-container-close-btn]').click()
    })
})
