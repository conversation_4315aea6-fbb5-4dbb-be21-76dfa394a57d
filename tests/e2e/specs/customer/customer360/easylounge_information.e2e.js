import { visitPageCustomerEmpty } from './helpers'

describe('Customer - EasyLounge information', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/customer/12345', { fixture: 'erp/customer/getById_full' }).as(
            'fetch_customer',
        )
        cy.intercept('GET', '**/api/erp/v1/easy-lounge/customer/<EMAIL>', {
            fixture: 'erp/easylounge/customer_full',
        }).as('fetch_easylounge_customer')
        cy.intercept('GET', '**/api/erp/v1/easy-lounge/quotation/38311', {
            fixture: 'erp/easylounge/quotation_full',
        }).as('fetch_easylounge_quotation')

        visitPageCustomerEmpty()
    })

    it('display a button to show the EZL data', () => {
        cy.wait('@fetch_easylounge_customer')

        cy.get('[data-context=show-ezl-client-btn]')
            .should('be.visible')
            .should('contain', 'Devis EasyLounge')
            .as('btn')

        cy.get('@btn').click()

        cy.get('[data-context=slide-out-container] [data-context=customer-ezl]').should('be.visible')
    })

    it('display the EZL customer data', () => {
        cy.get('[data-context=show-ezl-client-btn]').click()
        cy.wait(['@fetch_customer', '@fetch_easylounge_customer'])
        cy.get('[data-context=customer-ezl]').should('be.visible').as('content')

        // header content
        cy.get('@content').find('[data-context=page-header]').should('contain', 'Informations client EasyLounge')

        // customer general data
        cy.get('@content').should('contain', 'Simon Fourticq')
        cy.get('@content').should('contain', '93016')
        cy.get('@content').should('contain', 'Client depuis 20/01/2012')
        cy.get('@content').should('contain', '5 906,06 € commandé au total')

        // customer's quotations
        cy.get('@content').should('contain', 'Liste des devis')
        cy.get('@content').find('[data-context=quotation]').should('have.length', 10)

        // show more quotations
        cy.get('@content').find('[data-context=show-more-btn]').should('contain', 'Afficher les 140 devis').click()
        cy.get('@content').find('[data-context=quotation]').should('have.length', 140)

        // show less quotations
        cy.get('@content').find('[data-context=show-more-btn]').should('contain', 'Masquer les devis').click()
        cy.get('@content').find('[data-context=quotation]').should('have.length', 10)

        // check a quotation content
        cy.get('@content').find('[data-context=quotation]').eq(0).as('quotation')
        cy.get('@quotation').should('contain', '10/06/2020')
        cy.get('@quotation').should('contain', '51282')
        cy.get('@quotation').should('contain', 'Envoyé')
        cy.get('@quotation').should('contain', '1 196,90 €')
        cy.get('@quotation').should('contain', 'Expire le 16/06/2020')
    })

    it('can display the quotation detail', () => {
        cy.get('[data-context=show-ezl-client-btn]').click()
        cy.wait(['@fetch_customer', '@fetch_easylounge_customer'])
        cy.get('[data-context=customer-ezl] [data-context=quotation]:contains(38311)')
            .should('be.visible')
            .as('quotation')

        // load detail
        cy.get('@quotation').find('[data-context=quotation-resume]').click()
        cy.wait('@fetch_easylounge_quotation')
        cy.get('[data-context=customer-ezl] [data-context=quotation]:contains(38311) [data-context=quotation-detail]')
            .as('detail')
            .should('be.visible')

        // detail content
        cy.get('@detail').should('contain.text', '1 809,00 € avant réduction, soit une remise de  812,00 € (-44.9%)')
        cy.get('@detail').should('contain.text', '1 × YAMRN803DNR')
        cy.get('@detail').should('contain.text', '789,00 €')
        cy.get('@detail').should('contain.text', '546,00 € (-30.8%)')
        cy.get('@detail').should('contain.text', '1 × ELIPSPRESFACPF14FBC')
        cy.get('@detail').should('contain.text', '990,00 €')
        cy.get('@detail').should('contain.text', '440,50 € (-55.5%)')
        cy.get('@detail').should('contain.text', '12 × NORSTCL2501M')
        cy.get('@detail').should('contain.text', '2,50 €')
        cy.get('@detail').should('contain.text', '0,88 € (-65%)')

        // hide detail
        cy.get('[data-context=customer-ezl] [data-context=quotation-resume]:contains(38311)').click()
        cy.get(
            '[data-context=customer-ezl] [data-context=quotation]:contains(38311) [data-context=quotation-detail]',
        ).should('not.exist')
    })

    it('display a different content when has no quotation', () => {
        cy.intercept('GET', '**/api/erp/v1/easy-lounge/customer/<EMAIL>', {
            fixture: 'erp/easylounge/customer_without_quotation',
        }).as('fetch_easylounge_customer')

        cy.get('[data-context=show-ezl-client-btn]').click()
        cy.wait(['@fetch_customer', '@fetch_easylounge_customer'])

        cy.get('[data-context=slide-out-container] [data-context=customer-ezl]').should('be.visible').as('content')
        cy.get('@content').should('contain', 'Simon Fourticq')
        cy.get('@content').should('contain', 'Aucun devis')
    })

    it('display a different content when easylounge user not found', () => {
        cy.intercept('GET', '**/api/erp/v1/easy-lounge/customer/<EMAIL>', {
            statusCode: 404,
            body: {},
        }).as('fetch_easylounge_customer')

        cy.wait(['@fetch_customer', '@fetch_easylounge_customer'])
        cy.get('[data-context=show-ezl-client-btn]').should('not.exist')
    })
})
