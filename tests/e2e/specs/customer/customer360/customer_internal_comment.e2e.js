import { visitPageCustomerEmpty } from './helpers'
import { CUSTOMER_COMMENT_WRITE } from '../../../../../src/apps/erp/permissions'

describe('Customer - customer internal comment', () => {
    beforeEach(function () {
        cy.intercept('POST', '**/api/erp/v1/system-events', {
            fixture: 'erp/customer/customer_internal_comments.json',
        }).as('fetch_comment')
        cy.intercept('POST', '**/api/erp/v1/customer/12345/internal-comment', {
            body: { status: 'success', data: [] },
        }).as('post_comment')
        cy.intercept('DELETE', '**/api/erp/v1/system-event/2070057', {
            body: { status: 'success', data: [{ deleted: 1 }] },
        }).as('delete_comment')

        cy.authenticate()
        cy.mockErpUser()
        visitPageCustomerEmpty()
    })

    describe('without permission', () => {
        beforeEach(function () {
            visitPageCustomerEmpty()
            cy.wait('@fetch_comment')

            cy.get('[data-context=history]').should('be.visible')
        })
        it('Cannot write an internal comment without permission', () => {
            cy.get('[data-context=history] [data-context=internal-comment-input]')
                .should('have.attr', 'placeholder', 'Votre commentaire')
                .should('be.disabled')
            cy.get('[data-context=history] [data-context=save-internal-comment]')
                .should('contain', 'Sauvegarder le commentaire')
                .should('be.disabled')
            cy.get('[data-context=customer-history] [data-context=delete-internal-comment]')
                .should('contain', 'Supprimer')
                .should('be.disabled')
        })
    })
    describe('with permission', () => {
        it('Display empty comment history', () => {
            cy.intercept('POST', '**/api/erp/v1/system-events', {
                fixture: 'erp/customer/customer_internal_comments_empty.json',
            }).as('fetch_empty_comment')

            cy.authenticate()
            cy.mockErpUser([CUSTOMER_COMMENT_WRITE])
            visitPageCustomerEmpty()

            cy.wait('@fetch_empty_comment')
            cy.get('[data-context=history]').should('be.visible')

            cy.get('[data-context=customer-history]').should('contain', "Il n'y pas de commentaire pour ce client")
        })

        beforeEach(function () {
            cy.authenticate()
            cy.mockErpUser([CUSTOMER_COMMENT_WRITE])
            visitPageCustomerEmpty()

            cy.wait('@fetch_comment')
        })

        it('Display comment history and delete a comment', () => {
            cy.get('[data-context=history]').should('be.visible')
            // display
            cy.get('[data-context=customer-history]').should('contain', 'Back office')
            cy.get('[data-context=customer-history]').should('contain', 'le 31/01/2024 à 12:59')
            cy.get('[data-context=customer-history]').should('contain', 'premier commentaire sur ce client')

            // delete
            cy.get('[data-context=delete-internal-comment]').should('not.be.disabled')
            cy.get('[data-context=delete-internal-comment]').click()
            cy.wait('@delete_comment')
            // success message
            cy.toast('Commentaire supprimé avec succès', 'success')
        })
        it('Can write a comment', () => {
            cy.get('[data-context=history]').should('be.visible').as('content')
            // write
            cy.get('@content').within(() => {
                cy.get('[data-context=save-internal-comment]').should('be.disabled')
                cy.get('[data-context=internal-comment-input]').type('Voici mon commentaire interne')
                cy.get('[data-context=save-internal-comment]').should('not.be.disabled')
                cy.get('[data-context=save-internal-comment]').click()
                cy.wait('@post_comment').then((xhr) => {
                    expect(xhr.request.body).to.deep.eq({
                        message: 'Voici mon commentaire interne',
                    })
                })
            })
            // success message
            cy.toast(`Commentaire sauvegardé`, 'success')
        })
    })
})
