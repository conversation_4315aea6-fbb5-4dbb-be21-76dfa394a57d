const { GRAPHQL_ENDPOINT, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aliasQ<PERSON>y, hasOperationName } = require('../../../utils/graphql-test-utils')

describe('Customer page', function () {
    const mockEmptyResult = (key) => {
        const result = {
            statusCode: 200,
            status: 'success',
            data: {
                _pager: {
                    from: 0,
                    to: 0,
                    total: 0,
                    page: 1,
                    limit: 5,
                    last_page: 1,
                },
            },
        }

        result.data[key] = []

        return { body: result }
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v2/customers', { fixture: 'erp/customer/customerV2.json' }).as('customer')
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomer')
            // Default
            if (hasOperationName(req, 'fetchCustomer')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomer')
                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_account_information_by_pk_12345.json',
                })
            }
        })

        // mock other blocks calls to avoid error messages fucking the tests
        cy.intercept('POST', '**/api/erp/v1/customer-orders', mockEmptyResult('customer_orders')).as('customer_orders')
        cy.intercept('POST', '**/api/erp/v1/tasks', mockEmptyResult('tasks')).as('tasks')
        cy.intercept('POST', '**/api/erp/v1/after-sale-services', mockEmptyResult('after_sale_services')).as(
            'after_sale_services',
        )
        cy.intercept('GET', '**/api/erp/v1/**/credit-notes', mockEmptyResult('credit_notes')).as('credit_notes')
        cy.intercept('POST', '**/api/erp/v1/**/quotations', mockEmptyResult('quotations')).as('quotations')
        cy.intercept(
            'GET',
            '**/api/erp/v1/customer/12345/bought-products?limit=20&page=1',
            mockEmptyResult('bought_products'),
        )

        cy.visit('/customer/12345')
        cy.wait(['@customer', getQueryAlias('fetchCustomer')])
    })

    it('display newsletter subscription status', () => {
        // customer subscriber to newsletter
        cy.get('[data-context=page-header]').as('customer_banner')
        cy.get('@customer_banner')
            .find('[data-context=newsletter-status]')
            .find('[data-context=scope]')
            .find('[data-icon=envelope]')
            .should('exist')
        cy.get('@customer_banner')
            .find('[data-context=newsletter-status]')
            .find('[data-context=content] span')
            .should('contain', 'Abonné')
            .should('have.class', 'text-green-700')

        // customer who has never subscribed to newsletter
        cy.intercept('POST', '**/api/erp/v2/customers', {
            fixture: 'erp/customer/newsletter/customerV2_no_newsletter.json',
        }).as('customer')
        cy.visit('/customer/12345')
        cy.wait('@customer')

        cy.get('[data-context=page-header]').as('customer_banner')
        cy.get('@customer_banner')
            .find('[data-context=newsletter-status]')
            .find('[data-context=content] span')
            .should('contain', 'Non')
            .should('have.class', 'text-red-700')

        // customer who has unsubscribed to newsletter
        cy.intercept('POST', '**/api/erp/v2/customers', {
            fixture: 'erp/customer/newsletter/customerV2_newsletter_unsubscribed.json',
        }).as('customer')
        cy.visit('/customer/12345')
        cy.wait('@customer')

        cy.get('[data-context=page-header]').as('customer_banner')
        cy.get('@customer_banner')
            .find('[data-context=newsletter-status]')
            .find('[data-context=content] span')
            .should('contain', 'Désabonné')
            .should('have.class', 'text-red-700')
    })

    it('display subscription details', () => {
        cy.initReloadTest()
        cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-details', {
            fixture: 'erp/customer/newsletter/newsletter_subscription_details.json',
        }).as('subscription_details')
        cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-change-status', {
            fixture: 'erp/customer/newsletter/newsletter_change_status.json',
            statusCode: 200,
        }).as('newsletter-subscription-change-status')
        cy.get('[data-context=subscription-details]').should('not.exist')
        cy.get('[data-context=page-header]').as('customer_banner')
        cy.get('@customer_banner')
            .find('[data-context=newsletter-status]')
            .find('[data-context=content]')
            .should('contain', 'Abonné')
            .click()
        cy.wait('@subscription_details')

        // check side panel opens
        cy.get('[data-context=subscription-details]').should('exist')
        // Check page title
        cy.get('[data-context=page-header]').should('contain', 'Abonnement newsletter')
        // Check details
        cy.get('[data-context=status-label]').should('contain', 'Statut')
        cy.get('[data-context=status]').should('contain', 'Abonné')
        cy.get('[data-context=date-label]').should('contain', 'Date abonnement')
        cy.get('[data-context=date]').should('contain', '29/11/2017')
        cy.get('[data-context=origin-label]').should('contain', 'Origine abonnement')
        cy.get('[data-context=origin]').should('contain', 'Rappel immédiat')
        cy.get('[data-context=last-activity-label]').should('contain', 'Dernière activité')
        cy.get('[data-context=last-activity]').should('contain', '29/11/2017 11:57')
        cy.get('[data-context=subscribe-button]').should('not.exist')
        cy.get('[data-context=unbounce-button]').should('not.exist')
        cy.get('[data-context=unsubscribe-button]').should('contain', 'Désabonner le client de la newsletter').click()

        cy.wait('@newsletter-subscription-change-status').then((xhr) => {
            expect(xhr.request.body.email).to.eq('<EMAIL>')
            expect(xhr.request.body.status).to.eq('subscribed')
        })
    })

    it('display subscription details with last activity', () => {
        cy.fixture('erp/customer/newsletter/newsletter_subscription_details.json').then((payload) => {
            payload.data.details.last_activity = '2024-03-20 15:30:00'
            cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-details', {
                statusCode: 200,
                status: 'success',
                body: payload,
            }).as('subscription_details')
        })

        cy.get('[data-context=page-header] [data-context=newsletter-status]').click()
        cy.wait(['@subscription_details'])
        cy.get('[data-context=last-activity-label]').should('contain', 'Dernière activité')
        cy.get('[data-context=last-activity]').should('contain', '20/03/2024 15:30')
    })

    it('update subscription failed', () => {
        cy.initReloadTest()
        cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-details', {
            fixture: 'erp/customer/newsletter/newsletter_subscription_details.json',
        }).as('subscription_details')
        cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-change-status', {
            fixture: 'erp/customer/newsletter/newsletter_change_status_failed.json',
            statusCode: 200,
        }).as('newsletter-subscription-change-status')
        cy.get('[data-context=subscription-details]').should('not.exist')
        cy.get('[data-context=page-header]').as('customer_banner')
        cy.get('@customer_banner')
            .find('[data-context=newsletter-status]')
            .find('[data-context=content]')
            .should('contain', 'Abonné')
            .click()
        cy.wait('@subscription_details')
        // check side panel opens
        cy.get('[data-context=subscription-details]').should('exist')

        cy.get('[data-context=unsubscribe-button]').should('contain', 'Désabonner le client de la newsletter').click()

        cy.wait('@newsletter-subscription-change-status').then((xhr) => {
            expect(xhr.request.body.email).to.eq('<EMAIL>')
            expect(xhr.request.body.status).to.eq('subscribed')
        })

        cy.toast(`Le compte n'existe pas`, 'danger')

        cy.checkHasNotBeenReloaded()
    })

    it('display subscription details when is bounce', () => {
        cy.intercept('POST', '**/api/erp/v2/customers', {
            fixture: 'erp/customer/newsletter/customerV2_newsletter_bounce.json',
        }).as('customer')

        cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-details', {
            fixture: 'erp/customer/newsletter/newsletter_subscription_details_bounce.json',
        }).as('subscription_details')
        cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-change-status', {
            fixture: 'erp/customer/newsletter/newsletter_change_status.json',
            statusCode: 200,
        }).as('newsletter-subscription-change-status')
        cy.visit('/customer/12345')
        cy.wait('@customer')
        cy.initReloadTest()

        cy.get('[data-context=subscription-details]').should('not.exist')
        cy.get('[data-context=page-header]').as('customer_banner')
        cy.get('@customer_banner')
            .find('[data-context=newsletter-status]')
            .find('[data-context=content]')
            .should('contain', 'Bouncé')
            .click()
        cy.wait('@subscription_details')

        // check side panel opens
        cy.get('[data-context=subscription-details]').should('exist')
        // Check page title
        cy.get('[data-context=page-header]').should('contain', 'Abonnement newsletter')

        cy.get('[data-context=alert-content]').should('exist')
        // Check details
        cy.get('[data-context=status-label]').should('contain', 'Statut')
        cy.get('[data-context=status]').should('contain', 'Bouncé')
        cy.get('[data-context=date-label]').should('contain', 'Date abonnement')
        cy.get('[data-context=date]').should('contain', '29/11/2017')
        cy.get('[data-context=origin-label]').should('contain', 'Origine abonnement')
        cy.get('[data-context=origin]').should('contain', 'Rappel immédiat')
        cy.get('[data-context=subscribe-button]').should('not.exist')
        cy.get('[data-context=unsubscribe-button]').should('not.exist')
        cy.get('[data-context=unbounce-button]').should('contain', 'Débouncer le client de la newsletter').click()

        cy.wait('@newsletter-subscription-change-status').then((xhr) => {
            expect(xhr.request.body.email).to.eq('<EMAIL>')
            expect(xhr.request.body.status).to.eq('bounced')
        })
    })

    it('does not display subscription details for a customer who has never subscribed', () => {
        cy.intercept('POST', '**/api/erp/v2/customers', {
            fixture: 'erp/customer/newsletter/customerV2_no_newsletter.json',
        }).as('customer')
        cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-details', {
            fixture: 'erp/customer/newsletter/newsletter_no_subscription_details.json',
        }).as('no_subscription_details')
        cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-change-status', {
            fixture: 'erp/customer/newsletter/newsletter_change_status.json',
            statusCode: 200,
        }).as('newsletter-subscription-change-status')

        cy.visit('/customer/12345')
        cy.wait('@customer')
        cy.initReloadTest()

        cy.get('[data-context=subscription-details]').should('not.exist')
        cy.get('[data-context=page-header]').as('customer_banner')
        cy.get('@customer_banner')
            .find('[data-context=newsletter-status]')
            .find('[data-context=content]')
            .should('contain', 'Non')
            .click()
        cy.wait('@no_subscription_details')
        cy.closeAllToasts()
        // details does not open
        cy.get('[data-context=subscription-details]').should('exist')
        cy.get('[data-context=status-label]').should('exist')
        cy.get('[data-context=status]').should('contain', 'Non abonné')
        cy.get('[data-context=date-label]').should('exist')
        cy.get('[data-context=no-date]').should('contain', 'N/A')
        cy.get('[data-context=origin-label]').should('exist')
        cy.get('[data-context=no-origin]').should('contain', 'N/A')
        cy.get('[data-context=last-activity-label]').should('exist')
        cy.get('[data-context=no-last-activity]').should('contain', 'N/A')
        cy.get('[data-context=unsubscribe-button]').should('not.exist')
        cy.get('[data-context=unbounce-button]').should('not.exist')
        cy.get('[data-context=subscribe-button]').should('contain', 'Abonner le client à la newsletter').click()

        cy.wait('@newsletter-subscription-change-status').then((xhr) => {
            expect(xhr.request.body.email).to.eq('<EMAIL>')
            expect(xhr.request.body.status).to.eq('inactive')
        })
    })

    it('does display subscription details for a non mapped key', () => {
        cy.fixture('erp/customer/newsletter/newsletter_subscription_details.json').then((payload) => {
            payload.data.details.origin = 'toto'

            cy.intercept('POST', '**/api/erp/v1/newsletter-subscription-details', {
                statusCode: 200,
                status: 'success',
                body: payload,
            }).as('subscription_details')
        })

        cy.get('[data-context=page-header] [data-context=newsletter-status]').click()
        cy.wait(['@subscription_details'])
        cy.get('[data-context=no-origin').should('contain', 'N/A')
    })
})
