const { GRAPHQL_ENDPOINT, aliasQuery, hasOperationName, getQueryAlias } = require('../../../utils/graphql-test-utils')

describe('Customer order products', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v2/customers', { fixture: 'erp/customer/customerV2.json' }).as('customer')

        // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
        // alias the queries and mutations for our tests in a beforeEach
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomer')

            // Default
            if (hasOperationName(req, 'fetchCustomer')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomer')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_account_information_by_pk_12345.json',
                })
            }
        })

        cy.intercept('POST', '**/api/erp/v1/customer-orders', {
            fixture: 'erp/customer-order/customer_order_1572493.json',
        }).as('customer_orders')
        cy.intercept('GET', '**/api/erp/v1/customer/1572493/bought-products?**', {
            fixture: 'erp/customer/bought-products/bought_products.json',
        }).as('bought_products')
        cy.visit('/customer/1572493')
        cy.wait(['@customer', getQueryAlias('fetchCustomer'), '@bought_products'])
    })

    it('display customer products', () => {
        // verification of product number
        cy.get('[data-context=bought-products-widget] [data-context=erp-article-item]').should('have.length', 10)

        // contents of the first product
        cy.get('[data-context=bought-products-widget] [data-context=erp-article-item]')
            .eq(0)
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://image.son-video.com/images/dynamic/Caissons_et_vibreurs/articles/Klipsch/KLIPSCHR115SWNR/Klipsch-R-115SW_P_300_square.jpg',
            )
        cy.get('[data-context=bought-products-widget] [data-context=erp-article-item]')
            .eq(0)
            .find('[data-context=sku]')
            .should('contain', 'KLIPSCHR115SWNR')

        cy.get('[data-context=bought-products-widget] [data-context=erp-article-item]')
            .eq(0)
            .find('[data-context=name]')
            .should('contain', 'R-115SW')

        cy.get('[data-context=bought-products-widget] [data-context=erp-article-item]')
            .eq(0)
            .find('[data-context=premium-warranty-tag]')
            .should('be.visible')
            .should('contain', 'Garantie premium')

        cy.get('[data-context=bought-products-widget] [data-context=cell-unit-price]').eq(0).should('contain', '469 €')

        // content of the third product
        cy.get('[data-context=bought-products-widget] [data-context=erp-article-item]')
            .eq(2)
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://image.son-video.com/images/dynamic/Enceintes/articles/Klipsch/KLIPSCHRP160MNR/Klipsch-RP-160M-Noir_P_300_square.jpg',
            )
        cy.get('[data-context=bought-products-widget] [data-context=erp-article-item]')
            .eq(2)
            .find('[data-context=sku]')
            .should('contain', 'KLIPSCHRP160MNR')
        cy.get('[data-context=bought-products-widget] [data-context=erp-article-item]')
            .eq(2)
            .find('[data-context=name]')
            .should('contain', 'RP-160M Noir')
        cy.get('[data-context=bought-products-widget] [data-context=cell-unit-price]').eq(2).should('contain', '349 €')
    })

    it('display all customer orders with specific products', () => {
        cy.intercept('POST', '**/api/erp/v1/customer-orders', {
            fixture: 'erp/customer-order/customer_order_1572493.json',
        }).as('customer_orders')

        // display product bought once in one order
        cy.get('[data-context=bought-products-widget]').find('[data-context=table-row]').eq(1).as('only_one_order')

        cy.get('@only_one_order')
            .find('[data-context=cell-customer-orders] [data-context=scope]')
            .should('contain', 'CMD')

        cy.get('@only_one_order')
            .find('[data-context=cell-customer-orders] [data-context=content]')
            .should('contain', '123456')

        // no button to see more
        cy.get('@only_one_order').find('[data-context=more-orders]').should('not.exist')

        // product bought multiple toimes (multiple orders with the same product)
        cy.get('[data-context=bought-products-widget]')
            .find('[data-context=table-row]')
            .eq(0)
            .as('with_multiple_orders')
        cy.get('@with_multiple_orders')
            .find('[data-context=cell-customer-orders] [data-context=scope]')
            .should('contain', 'CMD')
        cy.get('@with_multiple_orders')
            .find('[data-context=cell-customer-orders] [data-context=content]')
            .should('contain', '987456')

        // button to open side panel and see all customer orders with this product details
        cy.get('@with_multiple_orders').find('[data-context=more-orders]').click()
        cy.get('[data-context=slide-out-container] [data-context=pre-filtered-customer-orders]')
            .as('panel')
            .should('be.visible')
        // header
        cy.get('@panel').find('[data-context=customer]').find('[data-context=scope]').should('contain', 'CLIENT')
        cy.get('@panel').find('[data-context=customer]').find('[data-context=content]').should('contain', 'Guy Liguili')
        cy.get('@panel').find('[data-context=sku]').find('[data-context=scope]').should('contain', 'SKU')
        cy.get('@panel').find('[data-context=sku]').find('[data-context=content]').should('contain', 'KLIPSCHR115SWNR')

        // table
        cy.get('@panel').find('[data-context=customer-orders]').as('table')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 6)
        cy.get('@header').eq(0).should('contain', 'Commande')
        cy.get('@header').eq(1).should('contain', 'Date')
        cy.get('@header').eq(2).should('contain', 'Montant')
        cy.get('@header').eq(3).should('contain', 'Détails paiements')
        cy.get('@header').eq(4).should('contain', 'Livraison')
        cy.get('@header').eq(5).should('contain', 'Derniers messages')

        cy.get('@panel').find('[data-context=delivery-title]').should('have.length', 3)

        // content first row
        cy.get('@panel').find('[data-context=table-row]').eq(0).as('first_row')
        cy.get('@first_row')
            .find('[data-context=cell-customer-order]')
            .should('contain', '3078600')
            .find('[data-context=order-id]')
            .should('have.attr', 'href', '/legacy/v1/commandes/edition_commande.php?id_commande=3078600')
        cy.get('@first_row').find('[data-context=cell-created-at]').should('contain', '16/06/2021')
        cy.get('@first_row').find('[data-context=cell-amount]').should('contain', '184,85 €')
        cy.get('@first_row').find('[data-context=cell-payments]').should('contain', 'Espèces')
        cy.get('@first_row')
            .find('[data-context=cell-delivery]')
            .find('[data-context=embedded-delivery-notes]')
            .find('[data-context=table-row]')
            .as('delivery-rows')

        cy.get('@delivery-rows').eq(0).should('contain', 'Emport Depot Antibes')
        cy.get('@delivery-rows').eq(1).find('[data-context=delivery-note-id]').as('delivery-note')

        cy.get('@delivery-note').should('have.attr', 'href', '/erp/delivery-note/history/4652922')
        cy.get('@delivery-note').find('[data-context=scope]').should('contain', 'BL')
        cy.get('@delivery-note').find('[data-context=content]').should('contain', 4652922)

        cy.get('@first_row')
            .find('[data-context=cell-message]')
            .find('[data-context=internal-message]')
            .should('contain', 'Dernier commentaire interne')
            .find('[data-context=message]')
            .should('contain', 'ceci est un message interne')
    })
})
