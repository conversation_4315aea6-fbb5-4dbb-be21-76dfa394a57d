const { GRAPHQL_ENDPOINT, aliasQuery, hasOperationName, getQuery<PERSON>lias } = require('../../../utils/graphql-test-utils')

describe('Customer page', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v2/customers', { fixture: 'erp/customer/customerV2.json' }).as('customer')
        // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
        // alias the queries and mutations for our tests in a beforeEach
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchCustomer')

            // Default
            if (hasOperationName(req, 'fetchCustomer')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchCustomer')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/customer/customer_account_information_by_pk_12345.json',
                })
            }
        })
        cy.intercept('POST', '**/api/erp/v1/tasks', { fixture: 'erp/tasks/tasks.json' }).as('tasks')
    })

    it('check user tasks', () => {
        cy.visit('/customer/12345')
        cy.wait(['@customer', getQueryAlias('fetchCustomer'), '@tasks'])

        cy.get('[data-context=tasks-widget-active]').should('contain', 'Tâches actives')
        cy.get('[data-context=tasks-widget-inactive]').should('contain', 'Tâches cloturées')
        cy.get('[data-context=task-item]').should('have.length', 8)
        cy.get('[data-context=task-item]')
            .eq(0)
            .should('contain', 'Florent PETIT')
            .should('contain', 'Créée le 30/04/2020 à 15:07')
            .should('contain', 'Date limite 01/05/2020 à 15:07')
            .should('contain', ' Une question hifi ? ')
        cy.get('[data-context=task-item]')
            .eq(0)
            .find('[data-context=date-created] [data-context=erp-tooltip]')
            .should('have.class', 'bg-green-100 text-green-600')
        cy.get('[data-context=task-item]')
            .eq(0)
            .find('[data-context=date-limit] [data-context=erp-tooltip]')
            .should('have.class', 'bg-amber-100 text-amber-600')
        cy.get('[data-context=task-item]')
            .eq(0)
            .find('[data-context=customer-order-link]')
            .should('contain', 'CMD')
            .find('[data-context=erp-link]')
            .should('contain', '111222')
            .should(
                'have.attr',
                'href',
                'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=111222',
            )
            .should('have.attr', 'target', '_blank')

        cy.get('[data-context=task-item]')
            .eq(1)
            .should('contain', 'Hichem AÏSSAOUI')
            .should('contain', 'Créée le 30/04/2020 à 15:07')
            .should('contain', 'Date limite 01/05/2020 à 15:07')
            .should('contain', 'Votre opinion')
        cy.get('[data-context=task-item]')
            .eq(1)
            .find('[data-context=subject]')
            .should('have.class', 'bg-amber-100 text-amber-600')
        cy.get('[data-context=task-item]').eq(1).find('[data-context=customer-order-link]').should('not.exist')
    })
})
