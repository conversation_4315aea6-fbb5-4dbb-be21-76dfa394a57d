import { QUOTE_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Quotes panel', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/quotes', { fixture: 'erp/quotes/customer_quotes.json' }).as('cpost_quotes')
    })

    describe('With permissions QUOTE_WRITE', function () {
        beforeEach(() => {
            cy.mockErpUser([QUOTE_WRITE])
            cy.visit('/customer/12345', {
                onBeforeLoad(win) {
                    cy.stub(win, 'open').as('windowOpen')
                },
            })
        })
        it('display customer quotes info in a panel', () => {
            cy.wait('@cpost_quotes').then((xhr) => {
                expect(xhr.request.body.where._and[0].customer_id._eq).to.eq(12345)
            })
            cy.get('[data-context=quotes-widget]')
                .as('quotes_panel')
                .find('[data-context=title]')
                .should('contain', 'Devis / Offres')

            // Check table header
            cy.get('@quotes_panel').find('[data-context=erp-table]').as('table')
            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 6)
            cy.get('@header').eq(0).should('contain', '#')
            cy.get('@header').eq(1).should('contain', 'Date création')
            cy.get('@header').eq(2).should('contain', 'Statut')
            cy.get('@header').eq(3).should('contain', 'Remise')
            cy.get('@header').eq(4).should('contain', 'Montant')
            cy.get('@header').eq(5).should('contain', 'Opérateur')

            // ordered quote
            cy.get('[data-context=table-row]').eq(1).as('ordered_quote')
            cy.get('@ordered_quote')
                .find('[data-context=quote-badge]')
                .as('quote_badge')
                .find('[data-context=scope]')
                .should('contain', 'DVS')
            cy.get('@quote_badge')
                .find('[data-context=content]')
                .find('[data-context=erp-link]')
                .should('contain', '123')
                .should('have.attr', 'href', 'http://erp-client.lxc/quote/123')
            cy.get('@ordered_quote')
                .find('[data-context=customer-orders-badge]')
                .as('customer_orders_badge')
                .find('[data-context=scope]')
                .should('contain', 'cmd')
            cy.get('@customer_orders_badge')
                .find('[data-context=erp-link]')
                .should('have.length', 1)
                .should('contain', '234567')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=234567',
                )
            cy.get('@ordered_quote').find('[data-context=date-formatter]').should('contain', '04/02/2022 à 12:26')
            cy.get('@ordered_quote')
                .find('[data-context=badge]')
                .should('contain', 'Commandé')
                .should('have.class', 'bg-lime-50')
            cy.get('@ordered_quote').find('[data-context=discount]').should('contain', '65,00 €')
            cy.get('@ordered_quote').find('[data-context=discount-in-percentage]').should('contain', '13%')
            cy.get('@ordered_quote').find('[data-context=amount]').should('contain', '436,00 €')
            cy.get('@ordered_quote').find('[data-context=cell-created-by-name]').should('contain', 'Billy THE KID')

            // sent quote
            cy.get('[data-context=table-row]').should('have.length', 5).as('quotes')
            cy.get('@quotes').eq(0).as('sent_quote')
            cy.get('@sent_quote')
                .find('[data-context=quote-badge]')
                .as('quote_badge')
                .find('[data-context=scope]')
                .should('contain', 'DVS')
            cy.get('@quote_badge')
                .find('[data-context=content]')
                .find('[data-context=erp-link]')
                .should('contain', '111')
                .should('have.attr', 'href', 'http://erp-client.lxc/quote/111')
            cy.get('@sent_quote').find('[data-context=customer-orders-badge]').should('not.exist')
            cy.get('@sent_quote').find('[data-context=date-formatter]').should('contain', '05/02/2022 à 12:26')
            cy.get('@sent_quote').find('[data-context=badge]').should('contain', 'Envoyé')
            cy.get('@quotes')
                .eq(2)
                .find('[data-context=quote-badge]')
                .find('[data-context=scope]')
                .should('contain', 'BRO')
            cy.get('@quotes')
                .eq(3)
                .find('[data-context=quote-badge]')
                .find('[data-context=scope]')
                .should('contain', 'OFR')
            cy.get('@quotes')
                .eq(4)
                .find('[data-context=quote-badge]')
                .find('[data-context=scope]')
                .should('contain', 'A-DVS')
        })

        it('can create new quote ', () => {
            cy.intercept('POST', '**/api/erp/v1/quote', {
                statusCode: 200,
                body: { status: 'success', data: { quote_id: 150 } },
            }).as('api_quote_create')

            cy.get('[data-context=quotes-widget]').find('[data-context=actions] [data-context=new-quote-link]').click()
            cy.wait('@api_quote_create').then((xhr) => {
                expect(xhr.request.body).to.deep.equal({
                    customer_id: 12345,
                })
            })
            //  new quote page is open in another tab
            cy.get('@windowOpen').should('be.calledWith', 'http://erp-client.lxc/quote/150', '_blank')
        })
    })

    describe('Without permission', function () {
        beforeEach(() => {
            cy.mockErpUser()
            cy.visit('/customer/12345', {
                onBeforeLoad(win) {
                    cy.stub(win, 'open').as('open_quote_dashboard')
                },
            })
        })

        it('cannot create new quote', function () {
            cy.get('[data-context=quotes-widget]')
                .find('[data-context=actions]')
                .find('[data-context=new-quote-link]')
                .should('not.exist')
        })

        it(`should show a link to open the quotes prefilters on the customer's id`, () => {
            cy.get('[data-context=quotes-widget]')
                .find('[data-context=actions] [data-context=customer-quotes-link]')
                .should('be.visible')
                .click()

            cy.get('@open_quote_dashboard').should(
                'be.calledWith',
                '/quotes?pager[limit]=5&pager[filters]=%7B%22customer%22%3A12345%7D',
                '_blank',
            )
        })
    })
})
