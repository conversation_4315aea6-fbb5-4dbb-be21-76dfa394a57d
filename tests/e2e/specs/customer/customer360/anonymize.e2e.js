import { CUSTOMER_ANONYMIZE } from '../../../../../src/apps/erp/permissions.js'
import { visitPageCustomerEmpty } from './helpers'

describe('Customer anonymize', () => {
    it('Displays without permission', () => {
        cy.authenticate()
        cy.mockErpUser()
        visitPageCustomerEmpty()

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=anonymize-prospect-btn]')
            .should('be.visible')
            .should('contain', 'Anonymiser le compte client')
            .click()

        cy.get('[data-context=slide-out-container] [data-context=customer-anonymize]').should('be.visible')
        cy.get('[data-context=slide-out-container] strong').should(
            'contain',
            "Vous n'avez pas la permission d'accéder à cette fonctionnalité",
        )
    })

    it('Displays with permission', () => {
        cy.authenticate()
        cy.mockErpUser([CUSTOMER_ANONYMIZE])
        visitPageCustomerEmpty()

        cy.get('[data-context=erp-panel]')
            .eq(0)
            .find('[data-context=actions]')
            .find('[data-context=dropdown-menu]')
            .click()

        cy.get('[data-context=anonymize-prospect-btn]')
            .should('be.visible')
            .should('contain', 'Anonymiser le compte client')
            .click()

        cy.get('[data-context=customer-anonymize]').should('be.visible').as('content')

        // header content
        cy.get('@content').find('[data-context=page-header]').should('contain', 'Anonymiser le client')

        cy.get('[data-context=confirm-block-submit-btn]').should('be.visible').should('contain', 'Anonymiser le client')

        cy.get('[data-context=confirm-block-cancel-btn]').should('be.visible').should('contain', 'Annuler')
    })
})
