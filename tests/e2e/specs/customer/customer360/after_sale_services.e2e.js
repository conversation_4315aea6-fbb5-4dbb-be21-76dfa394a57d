describe('After-sale services panel', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.visit('/customer/12345', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })

        cy.intercept('POST', '**/api/erp/v1/after-sale-services', {
            fixture: 'erp/after_sale_service/customer_after_sale_services.json',
        }).as('after_sale_services')

        cy.toggleMenu()
    })

    it('display customer after sale services in a panel', () => {
        cy.wait('@after_sale_services').then((xhr) => {
            expect(xhr.request.body.where._and[0].customer_id._eq).to.eq(12345)
            expect(xhr.request.body.limit).to.eq(5)
            expect(xhr.request.body.included_dependencies).to.deep.eq(['article'])
        })
        cy.closeAllToasts()

        cy.get('[data-context=after-sale-services-widget]')
            .as('after_sale_services_panel')
            .find('[data-context=title]')
            .should('contain', 'Dossiers SAV')

        // Check table header
        cy.get('@after_sale_services_panel').find('[data-context=erp-table]').as('table')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 5)
        cy.get('@header').eq(0).should('contain', '#')
        cy.get('@header').eq(1).should('contain', 'Date création')
        cy.get('@header').eq(2).should('contain', 'Envoi station')
        cy.get('@header').eq(3).should('contain', 'Statut')
        cy.get('@header').eq(4).should('contain', 'Produit')

        // content
        cy.get('@table')
            .selectCell(0, 0)
            .find('[data-context=scoped-badge]')
            .should('have.length', 3)
            .as('scoped-badge')

        cy.get('@scoped-badge').eq(0).find('[data-context=scope]').should('contain', 'sav')

        cy.get('@scoped-badge')
            .eq(0)
            .find('[data-context=content]')
            .find('[data-context=erp-link]')
            .should('contain', '14761')
            .should('have.attr', 'href', 'http://erp-client.lxc/legacy/sav/articleEdit?id=14761')

        cy.get('@scoped-badge').eq(1).find('[data-context=scope]').should('contain', 'rma')

        cy.get('@scoped-badge').eq(1).find('[data-context=content]').should('contain', '96356')

        cy.get('@scoped-badge').eq(2).find('[data-context=scope]').should('contain', 'cmd')

        cy.get('@scoped-badge')
            .eq(2)
            .find('[data-context=content]')
            .find('[data-context=erp-link]')
            .should('contain', '1727969')
            .should(
                'have.attr',
                'href',
                'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=1727969',
            )

        cy.get('@table').selectCell(0, 1).find('[data-context=date-formatter]').should('contain', '30/10/2020')

        cy.get('@table').selectCell(0, 2).find('[data-context=date-formatter]').should('contain', '01/12/2020')

        cy.get('@table').selectCell(0, 3).find('[data-context=badge]').should('contain', 'En cours')

        cy.get('@table')
            .selectCell(0, 4)
            .find('[data-context=sku]')
            .should('have.attr', 'href', '/articles/SDR125/')
            .should('contain', 'SDR125')

        cy.get('@table').selectCell(0, 4).find('[data-context=name]').should('contain', 'Sony KD-65AG9')
    })

    it('does not display rma when its value is null', () => {
        cy.wait('@after_sale_services')
        cy.closeAllToasts()

        cy.get('[data-context=after-sale-services-widget] [data-context=erp-table]').as('table')

        cy.get('@table')
            .selectCell(1, 0)
            .find('[data-context=scoped-badge]')
            .should('have.length', 2)
            .as('scoped-badge')

        cy.get('@scoped-badge').eq(0).find('[data-context=scope]').should('contain', 'sav')

        cy.get('@scoped-badge')
            .eq(0)
            .find('[data-context=content]')
            .find('[data-context=erp-link]')
            .should('contain', '14798')
            .should('have.attr', 'href', 'http://erp-client.lxc/legacy/sav/articleEdit?id=14798')

        cy.get('@scoped-badge').eq(1).find('[data-context=scope]').should('contain', 'cmd')

        cy.get('@scoped-badge')
            .eq(1)
            .find('[data-context=content]')
            .find('[data-context=erp-link]')
            .should('contain', '1727856')
            .should(
                'have.attr',
                'href',
                'http://erp-client.lxc/legacy/v1/commandes/edition_commande.php?id_commande=1727856',
            )
    })

    it('display a status badge with different colors', () => {
        cy.wait('@after_sale_services')
        cy.closeAllToasts()

        cy.get('[data-context=after-sale-services-widget] [data-context=erp-table]').as('table')

        cy.get('@table').selectCell(0, 3).find('[data-context=badge]').should('contain', 'En cours')

        cy.get('@table').selectCell(1, 3).find('[data-context=badge]').should('contain', 'Terminé')

        cy.get('@table').selectCell(2, 3).find('[data-context=badge]').should('contain', '> 15 jours')
    })

    it('can open after-sale services page filtered on customer_id', () => {
        cy.wait('@after_sale_services')
        cy.closeAllToasts()

        cy.get('[data-context=after-sale-services-widget')
            .find('[data-context=actions] [data-context=customer-after-sale-service-link]')
            .should('contain', 'Voir tous les dossiers')
            .click()

        cy.get('@windowOpen').should(
            'be.calledWith',
            'http://erp-client.lxc/legacy/sav/listeArticles?customer_id=12345&status=ALL',
            '_blank',
        )
    })
})
