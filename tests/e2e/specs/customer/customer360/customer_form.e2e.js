import { visitPageCustomerEmpty } from './helpers'
import { CUSTOMER_UPDATE } from '../../../../../src/apps/erp/permissions.js'

describe('Customer - customer form', () => {
    describe('without permission', () => {
        beforeEach(function () {
            cy.intercept('POST', '**/api/erp/v1/system-events', {
                fixture: 'erp/customer/customer_events.json',
            }).as('cpost_customer_history_by_id')
            cy.authenticate()
            cy.mockErpUser()

            visitPageCustomerEmpty()

            cy.get('[data-context=customer-from-btn]')
                .should('be.visible')
                .should('contain', 'Éditer le prospect')
                .click()

            cy.get('[data-context=slide-out-container] [data-context=customer-form]').should('be.visible').as('content')
        })
        it('Displays form', () => {
            cy.get('@content').find('[data-context=page-header]').should('contain', 'Édition compte')
            cy.get('@content').find('[data-context=prospect-edit]').as('form')

            cy.get('@form').find('[data-context=type] label').should('contain', 'Type')
            cy.get('@form').find('[data-context=type] [data-context=erp-multiselect]').should('contain', 'Entreprise')

            cy.get('@form').find('[data-context=blacklisted] label')
            cy.get('@form').find('[data-context=blacklisted] [data-status=checked]').should('be.visible')

            cy.get('@content')
                .find('[data-context=submit]')
                .should('be.disabled')
                .parent()
                .tooltip('Vous ne disposez pas des droits nécessaires')
            cy.get('@content').find('[data-context=close]').should('not.be.disabled').click()

            cy.get('[data-context=slide-out-container] [data-context=customer-form]').should('not.exist')
        })
        it('Displays history', () => {
            cy.get('@content').find('[data-context=page-header]').as('header')
            cy.get('@header').find('h1').should('contain', 'Édition compte')
            cy.get('@header').find('h1').should('not.contain', 'Historique')
            cy.get('@header').find('[data-context=back]').should('not.exist')
            cy.get('@header').find('[data-context=history]').should('contain', 'Historique').click()

            // Trigger system-event with the comment history
            cy.wait('@cpost_customer_history_by_id')
            // New request to reload the form history
            cy.wait('@cpost_customer_history_by_id').then((xhr) => {
                expect(xhr.request.body.where).to.deep.eq({
                    _and: [
                        {
                            main_id: {
                                _eq: 12345,
                            },
                        },
                        { type: { _like: 'customer.%' } },
                    ],
                })
            })

            cy.get('@header').find('h1').should('not.contain', 'Édition compte')
            cy.get('@header').find('h1').should('contain', 'Historique')
            cy.get('@header').find('[data-context=back]').should('contain', 'Retour').as('back')
            cy.get('@header').find('[data-context=history]').should('not.exist')

            // timeline
            cy.get('[data-context=customer-history] > ul > li[data-context]').should('have.length', 3)
            cy.get('[data-context=customer-timeline-update]').should('have.length', 2)

            cy.get('[data-context=erp-timeline-event-item').eq(0).as('event')
            cy.get('@event')
                .find('[data-context=content]')
                .should('be.visible')
                .should('contain', 'a anonymisé le client')

            cy.get('[data-context=customer-timeline-update]').eq(0).as('event')
            cy.get('@event')
                .find('[data-context=content]')
                .should('be.visible')
                .should('contain', 'a modifié les informations client')
            cy.get('@event').find('[data-context=show-more]').should('be.visible').click()
            cy.get('@event')
                .find('[data-context=erp-table] tbody tr')
                .checkRows([
                    ['type', 'entreprise', 'particulier'],
                    ['blacklist', 'false', 'true'],
                    ['encours_sfac', '12 000,00', '15 000,00'],
                ])

            cy.get('@back').click()
            cy.get('@header').find('h1').should('contain', 'Édition compte')
            cy.get('@header').find('h1').should('not.contain', 'Historique')
            cy.get('@header').find('[data-context=back]').should('not.exist')
            cy.get('@header').find('[data-context=history]').should('contain', 'Historique').click()
        })

        it('Displays history empty', () => {
            cy.intercept('POST', '**/api/erp/v1/system-events', {
                statusCode: 404,
                body: {},
            }).as('cpost_customer_history_by_id')

            cy.get('@content').find('[data-context=page-header] [data-context=history]').click()

            cy.wait('@cpost_customer_history_by_id')
            cy.get('[data-context=customer-history]').should('contain', "Il n'y pas encore d'évènement pour ce client")
        })
    })

    it('Edit with permission', () => {
        cy.intercept('GET', '**/api/erp/v1/customer/12345', {
            fixture: 'erp/customer/getById_full',
        }).as('fetch_customer')
        cy.intercept('PUT', '**/api/erp/v1/customer/12345', {
            statusCode: 404,
        }).as('update-customer')

        cy.authenticate()
        cy.mockErpUser([CUSTOMER_UPDATE])
        visitPageCustomerEmpty()

        cy.get('[data-context=customer-from-btn]').should('be.visible').click()

        cy.get('[data-context=slide-out-container] [data-context=customer-form]').should('be.visible').as('content')

        cy.get('@content').find('[data-context=page-header]').should('contain', 'Édition compte')
        cy.get('@content').find('[data-context=prospect-edit]').as('form')

        cy.get('@form').find('[data-context=type] [data-context=erp-multiselect]').erpMultiselect('', 'Particulier')
        cy.get('@form').find('[data-context=blacklisted] button').click()

        // update fail
        cy.get('@content').find('[data-context=submit]').should('not.be.disabled').click()

        cy.wait('@update-customer').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({
                accept_marketing_emails: true,
                blacklist: false,
                company_name: 'PHC Holding',
                customer_id: 12345,
                encours_sfac: 12000,
                type: 'particulier',
            })
        })

        cy.toast('Une erreur est survenue', 'danger')

        cy.intercept('PUT', '**/v1/customer/12345', {
            statusCode: 204,
        }).as('update-customer')

        // update success
        cy.get('@content').find('[data-context=submit]').should('not.be.disabled').click()

        cy.toast(`Le compte client a bien été modifié`, 'success')
        cy.get('[data-context=slide-out-container] [data-context=customer-form]').should('not.exist')
    })
})
