import { NON_BREAKING_SPACE } from '../../utils/text-utils'

describe('Shipment dashboard page', () => {
    const PAGE = '/shipments'

    describe('Page UI', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()
        })

        it('should have a first page visible', () => {
            cy.intercept('POST', '**/v1/shipments', { fixture: 'erp/shipment/cpost_shipments' }).as('fetch_shipments')

            cy.intercept('PUT', '**/shipment/**/close', {}).as('close_shipment')

            cy.visit(PAGE)
            cy.toggleMenu()

            // Check table headers
            const table_headers = [
                '#',
                'Transporteur',
                'Compte client',
                'N° bordereau',
                'Date de clôture',
                'Statut',
                'Nombre de BL',
                'Rapport',
            ]

            cy.get('th[scope=col]').should('have.length', table_headers.length)

            table_headers.forEach((text, idx) => {
                cy.get('th[scope=col]').eq(idx).should('contain', text)
            })

            cy.wait('@fetch_shipments')

            cy.get('[data-context=table-row]').should('have.length', 6)
            cy.get('[data-context="erp-table"] tbody').within(() => {
                // display data
                cy.get('tr:eq(0) td:eq(0)').find('[data-context=shipment-badge] a').should('contain', '1111')
                cy.get('tr:eq(0) td:eq(1)').should('contain', 'DHL')
                cy.get('tr:eq(0) td:eq(2)').should('contain', 'EDNDHL')
                cy.get('tr:eq(0) td:eq(3)').should('contain', '1596')
                cy.get('tr:eq(0) td:eq(4)').should('contain', '10/03/2023 à 13:58')
                cy.get('tr:eq(0) td:eq(6)').should('contain', '0')

                // Normal carier, closed
                cy.get('tr:eq(0) td:eq(5)').should('contain', 'Fermé')
                cy.get('tr:eq(0) td:eq(2)').should('not.contain', 'NOVEA')
                cy.get('tr:eq(0) td:eq(1)').should('not.contain', 'France Express')
                cy.get('tr:eq(0) td:eq(7)')
                    .find('a[data-context=erp-button-link]')
                    .should('contain', 'Voir le rapport')
                    .invoke('attr', 'href')
                    .should('matches', /\/exportTransporteurs\/BordereauRemisePdfSoCo\/idexpedition\/1111$/)

                // France Express, closed
                cy.get('tr:eq(1) td:eq(5)').should('contain', 'Fermé')
                cy.get('tr:eq(1) td:eq(1)').should('contain', 'France Express')
                cy.get('tr:eq(1) td:eq(7)')
                    .find('a[data-context=erp-button-link]')
                    .should('contain', 'Rapport de collecte')
                    .invoke('attr', 'href')
                    .should('matches', /\/legacy\/expeditions\/rapportExpeditionFranceExpress$/)

                // Novea, closed
                cy.get('tr:eq(2) td:eq(5)').should('contain', 'Fermé')
                cy.get('tr:eq(2) td:eq(2)').should('contain', 'NOVEA')
                cy.get('tr:eq(2) td:eq(7)')
                    .find('a[data-context=erp-button-link]')
                    .should('contain', 'Rapport de séquence')
                    .invoke('attr', 'href')
                    .should('matches', /\/legacy\/expeditions\/sequenceNovea\?expedition=3333$/)

                // with errors
                cy.get('tr:eq(3) td:eq(7)')
                    // Shiping note not scaned
                    .should('contain', `BL${NON_BREAKING_SPACE}444441 non scanné`)
                    // missing tracking number
                    .should('contain', `BL${NON_BREAKING_SPACE}444442 N° de colis manquant`)
                    // not printed
                    .should('contain', `BL${NON_BREAKING_SPACE}444443 non${NON_BREAKING_SPACE}imprimé`)

                // ready to close
                cy.get('tr:eq(4) td:eq(7)').find('[data-context=erp-button]').should('contain', `Clôturer`).click()
                cy.wait('@close_shipment')
                cy.get('tr:eq(4) td:eq(7)').find('a[data-context=erp-button-link]').should('contain', 'Voir le rapport')

                // France Express on carriver v2, closed
                cy.get('tr:eq(5) td:eq(5)').should('contain', 'Fermé')
                cy.get('tr:eq(5) td:eq(1)').should('contain', 'France Express')
                cy.get('tr:eq(5) td:eq(7)').find('button').should('contain', 'Voir le rapport')
            })
        })
    })

    describe('Page filters', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()
        })

        it('should be able to use filters successfully', () => {
            // 06/09/2021
            cy.mockDate(new Date(2021, 8, 6, 11, 40, 0, 0))

            cy.intercept({ method: 'POST', url: '**/v1/shipments' }, { fixture: 'erp/shipment/cpost_shipments' }).as(
                'post_shipment',
            )

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@post_shipment')

            cy.get('[data-context=filter-with-delivery-note]').should('be.visible').find('input').type(1)
            cy.get('[data-context=filter-carrier-name]').should('be.visible').find('input').type('A')
            cy.get('[data-context=filter-status]').should('be.visible').find('select').select('Ouvert')

            // one date selected
            cy.get('[data-context=filter-closed-at] [data-context=start-date]').should('be.visible').click()
            cy.get('[data-context=filter-closed-at] [data-context=start-date]')
                .find('[data-context=day]:contains(9)')
                .eq(0)
                .click()
            cy.get('[data-context=validate').click()

            cy.get('[data-context=filters-submit-btn]').click()

            cy.wait('@post_shipment').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    included_dependencies: ['shipment_delivery_notes'],
                    page: 1,
                    limit: 50,
                    where: {
                        with_delivery_note: { _eq: '1' },
                        carrier_name: { _like: '%A%' },
                        status: { _eq: '1' },
                        closed_at: { _gte: '2021-09-09 00:00:00' },
                    },
                })
            })

            // two dates selected
            cy.get('[data-context=filter-closed-at] [data-context=start-date]').should('be.visible').click()
            cy.get('[data-context=filter-closed-at] [data-context=start-date]')
                .find('[data-context=day]:contains(10)')
                .eq(0)
                .click()
            cy.get('[data-context=validate').click()

            cy.get('[data-context=filter-closed-at] [data-context=end-date]').should('be.visible').click()
            cy.get('[data-context=filter-closed-at] [data-context=end-date]')
                .find('[data-context=day]:contains(12)')
                .eq(0)
                .click()
            cy.get('[data-context=validate').click()

            cy.get('[data-context=filters-submit-btn]').click()
            cy.wait('@post_shipment').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    included_dependencies: ['shipment_delivery_notes'],
                    page: 1,
                    limit: 50,
                    where: {
                        with_delivery_note: { _eq: '1' },
                        carrier_name: { _like: '%A%' },
                        status: { _eq: '1' },
                        closed_at: { _between: ['2021-09-10 00:00:00', '2021-09-12 00:00:00'] },
                    },
                })
            })
        })

        it('should reload filters from the url on page load', () => {
            cy.intercept('POST', '**/v1/shipments', { fixture: 'erp/shipment/cpost_shipments' }).as('fetch_shipments')

            cy.visit(
                PAGE +
                    `?pager[filters]=%7B%22with_delivery_note%22%3A%221%22%2C%22carrier_name%22%3A%22A%22%2C%22status%22%3A%221%22%2C%22closed_at_start%22%3A%222021-09-09%2000%3A00%3A00%22%2C%22closed_at_end%22%3A%222021-09-12%2023%3A59%3A59%22%7D`,
            )
            cy.toggleMenu()

            cy.get('[data-context=filter-with-delivery-note] input').should('have.value', 1)
            cy.get('[data-context=filter-carrier-name] input').should('have.value', 'A')

            cy.get('[data-context=filter-closed-at] [data-context=start-date] input').should(
                'have.value',
                '09 sept. 2021 à 00:00',
            )

            cy.get('[data-context=filter-closed-at] [data-context=end-date] input').should(
                'have.value',
                '12 sept. 2021 à 23:59',
            )

            cy.get('[data-context=filter-status] select').should('have.value', '1')

            cy.wait('@fetch_shipments').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    included_dependencies: ['shipment_delivery_notes'],
                    page: 1,
                    limit: 50,
                    where: {
                        with_delivery_note: { _eq: '1' },
                        carrier_name: { _like: '%A%' },
                        status: { _eq: '1' },
                        closed_at: { _between: ['2021-09-09 00:00:00', '2021-09-12 23:59:59'] },
                    },
                })
            })
        })
    })
})
