describe('Customer order - Store pickups', function () {
    const PAGE = '/customer-order/store-pickups'

    const selectRow = (idx) => {
        return cy
            .get('@customer_orders')
            .find('> table > tbody > tr:not([data-context=expanded-row])')
            .eq(idx)
            .as('row')
    }

    const selectExpandedRow = (idx) => {
        return cy.get('@customer_orders').find('> table > tbody > tr[data-context=expanded-row]').eq(idx).as('row')
    }

    const selectCell = (idx) => {
        return cy.get('@row').find('> td').eq(idx).as('cell')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v2/wms/warehouses', { fixture: 'erp/wms/warehouses/v2/warehouses.json' }).as(
            'retail_stores_request',
        )

        cy.intercept('POST', '**/api/erp/v1/customer-orders/with-store-pickup', {
            fixture: 'erp/customer-order/with-store-pickup/customer-orders.json',
        }).as('customer_orders_request')
    })

    describe('On page on load', function () {
        it('check table columns', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            // table header
            cy.get('[data-context=customer-orders]').as('customer_orders')
            cy.get('@customer_orders').find('thead tr th').as('customer_orders_header')

            cy.get('@customer_orders_header').should('have.length', 7)

            cy.get('@customer_orders_header').eq(0).should('contain', 'Commande')

            cy.get('@customer_orders_header').eq(1).should('contain', 'Date')

            cy.get('@customer_orders_header').eq(2).should('contain', 'Client')

            cy.get('@customer_orders_header').eq(3).should('contain', 'Montant')

            cy.get('@customer_orders_header').eq(4).should('contain', 'Détails paiements')

            cy.get('@customer_orders_header').eq(5).should('contain', 'BL')

            cy.get('@customer_orders_header').eq(6).should('contain', 'Dernier message interne')
        })

        it('check empty message when no warehouse is selected', function () {
            cy.visit(PAGE)

            localStorage.removeItem('customer-order-store-pickup-selected-warehouse')

            cy.toggleMenu()

            cy.wait('@retail_stores_request')

            // table header
            cy.get('[data-context=customer-orders]').as('customer_orders')
            cy.get('@customer_orders')
                .find('[data-context=no-warehouse-selected-message] > [data-context=title]')
                .should('contain', 'Veuillez sélectionner un magasin.')

            cy.get('@customer_orders')
                .find('[data-context=no-warehouse-selected-message] [data-context=text]')
                .should('contain', 'Le magasin sélectionné sera conservé lors de vos prochaines visites.')
        })
    })

    /**
     * Helper function to automatize actions done on each tests
     */
    const visitPageAndSeeCustomerOrders = () => {
        cy.visit(PAGE)
        cy.toggleMenu()

        cy.wait('@retail_stores_request')
        cy.get('[data-context=warehouse-selector]').erpMultiselect(null, 'Champigny')

        cy.wait('@customer_orders_request')
        cy.get('table td > [data-context=skeleton]').should('not.exist')
        cy.get('[data-context=customer-orders]').as('customer_orders')
    }

    describe('Table content', function () {
        it('should show expected content', function () {
            visitPageAndSeeCustomerOrders()

            // check table content with no comment

            selectRow(2)

            selectCell(0).find('a').should('contain', '2026095')

            selectCell(1).should('contain', '05/02/2021')

            selectCell(2)
                .find('a')
                .should('contain', 'François VESPERINI')
                .should('have.attr', 'href', '/customer/326181')

            selectCell(2).find('[data-context=last-customer-message]').should('not.exist')

            selectCell(3).should('contain', '239,20 €')

            selectCell(4).find('[data-context=embedded-payments] [data-context=amount]').should('have.length', 1)

            selectCell(4)
                .find('[data-context=embedded-payments] [data-context=amount]')
                .eq(0)
                .should('contain', '239,20 €')

            selectCell(5)
                .find('[data-context=embedded-delivery-notes] [data-context=delivery-note-id]')
                .should('have.length', 1)

            selectCell(5)
                .find('[data-context=embedded-delivery-notes] [data-context=delivery-note-id]')
                .eq(0)
                .should('have.attr', 'href', '/erp/delivery-note/history/4541387')
                .should('contain', 4541387)

            selectCell(5).should('contain', 'Emport Champigny')

            selectCell(6).should('contain', '-')

            selectExpandedRow(2)

            cy.get('@row').find('[data-context=embedded-articles]').should('have.length', 1)

            cy.get('@row').find('[data-context=embedded-articles] [data-context=article-qty]').should('contain', 1)

            cy.get('@row')
                .find('[data-context=embedded-articles] [data-context=article-link]')
                .should('have.attr', 'href', '/articles/DESTOCK-20210128005')
                .should('contain', 'Sony UBP-X800M2')

            // check table content with comments

            selectRow(0)

            selectCell(2).find('a').should('contain', 'Stephane LEENS').should('have.attr', 'href', '/customer/252081')

            selectCell(2).find('[data-context=last-customer-message] > div').should('contain', 'Bonsoir')

            selectCell(6).should('contain', 'Hervé VICTORIN')
            selectCell(6).should('contain', 'emport ok')

            // check table content with multiple BL

            selectRow(0)

            selectCell(5)
                .find('[data-context=embedded-delivery-notes] [data-context=delivery-note-id]')
                .should('have.length', 2)

            selectCell(5)
                .find('[data-context=embedded-delivery-notes] [data-context=delivery-note-id]')
                .eq(0)
                .should('have.attr', 'href', '/erp/delivery-note/history/4556304')
                .should('contain', 4556304)

            // check table content with multiple payments

            selectRow(1)

            selectCell(4).find('[data-context=embedded-payments] [data-context=amount]').should('have.length', 2)

            selectCell(4)
                .find('[data-context=embedded-payments] [data-context=amount]')
                .eq(0)
                .should('contain', '29 302,00 €')

            selectCell(4)
                .find('[data-context=embedded-payments] [data-context=amount]')
                .eq(1)
                .should('contain', '19 700,05 €')

            // check table content with multiple articles

            selectExpandedRow(0)

            cy.get('@row').find('[data-context=embedded-articles]').should('have.length', 4)

            cy.get('@row')
                .find('[data-context=embedded-articles] [data-context=article-qty]')
                .eq(1)
                .should('contain', 1)

            cy.get('@row')
                .find('[data-context=embedded-articles] [data-context=article-link]')
                .eq(1)
                .should('have.attr', 'href', '/articles/DENAVRX2700HDABNR')
                .should('contain', 'Denon AVR-X2700H DAB')
        })
    })

    describe('Table filter', function () {
        function resetFilters() {
            cy.get('[data-context=local-filter]').clear()
            cy.get('@lines').should('have.length', 4)
        }

        it('should filter successfully', function () {
            visitPageAndSeeCustomerOrders()

            cy.get('@customer_orders').find('> table > tbody > tr:not([data-context=expanded-row])').as('lines')

            cy.get('@lines').should('have.length', 4)

            // Filter on customer id
            cy.get('[data-context=local-filter]').type('03')

            cy.get('@lines').should('have.length', 2)

            selectRow(0)
            selectCell(0).find('a').should('contain', '2040305')

            selectRow(1)
            selectCell(0).find('a').should('contain', '2035197')

            // Filter on delivery note id
            resetFilters()

            cy.get('[data-context=local-filter]').type('413')

            cy.get('@lines').should('have.length', 1)

            selectRow(0)

            selectCell(5)
                .find('[data-context=embedded-delivery-notes] [data-context=delivery-note-id]')
                .eq(0)
                .should('contain', 4541387)

            // Filter on customer name
            resetFilters()

            cy.get('[data-context=local-filter]').type('ber')

            cy.get('@lines').should('have.length', 1)

            selectRow(0)

            selectCell(2).find('a').should('contain', 'Julian BERTIN')

            // Filter on articles
            resetFilters()

            cy.get('[data-context=local-filter]').type('DEnon')

            cy.get('@lines').should('have.length', 1)

            selectExpandedRow(0)

            cy.get('@row')
                .find('[data-context=embedded-articles] [data-context=article-link]')
                .eq(1)
                .should('contain', 'Denon AVR-X2700H DAB')

            // Filter on articles with numbers and letters
            resetFilters()

            cy.get('[data-context=local-filter]').type('x27')

            cy.get('@lines').should('have.length', 1)

            selectExpandedRow(0)

            cy.get('@row')
                .find('[data-context=embedded-articles] [data-context=article-link]')
                .eq(1)
                .should('contain', 'Denon AVR-X2700H DAB')

            // Filter with numbers (search in all filters)
            cy.get('[data-context=local-filter]').clear()
            cy.get('@lines').should('have.length', 4)

            cy.get('[data-context=local-filter]').type('48')

            cy.get('@lines').should('have.length', 2)

            selectExpandedRow(0)

            cy.get('@row')
                .find('[data-context=embedded-articles] [data-context=article-link]')
                .eq(3)
                .should('contain', 'Audioquest Pearl 48 HDMI (2 m)')

            selectRow(1)
            selectCell(0).find('a').should('contain', '2088948')
        })
    })

    describe('Table sort', function () {
        it('should sort the table', function () {
            visitPageAndSeeCustomerOrders()

            cy.get('[data-context=customer-orders]').find('> table > thead tr th').as('customer_orders_header')

            // Sort on customer id

            cy.get('@customer_orders_header').eq(0).should('contain', 'Commande').click()

            cy.wait('@customer_orders_request').then((xhr) => {
                expect(xhr.request.body.order_by).to.eq('customer_order_id')
                expect(xhr.request.body.order_direction).to.deep.eq('asc')
            })

            cy.get('@customer_orders_header').eq(0).should('contain', 'Commande').click()

            cy.wait('@customer_orders_request').then((xhr) => {
                expect(xhr.request.body.order_by).to.eq('customer_order_id')
                expect(xhr.request.body.order_direction).to.deep.eq('desc')
            })

            // Sort on date

            cy.get('@customer_orders_header').eq(1).should('contain', 'Date').click()

            cy.wait('@customer_orders_request').then((xhr) => {
                expect(xhr.request.body.order_by).to.eq('created_at')
                expect(xhr.request.body.order_direction).to.deep.eq('asc')
            })

            // Sort on amount

            cy.get('@customer_orders_header').eq(3).should('contain', 'Montant').click()

            cy.wait('@customer_orders_request').then((xhr) => {
                expect(xhr.request.body.order_by).to.eq('amount_all_tax_included')
                expect(xhr.request.body.order_direction).to.deep.eq('desc')
            })
        })
    })
})
