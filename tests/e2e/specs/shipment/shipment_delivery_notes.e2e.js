import { DELIVERY_NOTE_WRITE } from '../../../../src/apps/erp/permissions'

describe('Shipmennt detail page', () => {
    const PAGE = (id_shipment) => `/shipments/${id_shipment}`

    describe('Page on closed shipment', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            const shipment_id = 1111

            cy.fixture('erp/shipment/cpost_shipments')
                .then((payload) => {
                    cy.intercept('POST', '**/v1/shipments', {
                        statusCode: 200,
                        body: {
                            data: {
                                shipments: [
                                    payload.data.shipments.find((shipment) => shipment.shipment_id === shipment_id),
                                ],
                            },
                        },
                    })
                })
                .as('fetch_shipment')

            cy.intercept('POST', `**/v1/shipment/${shipment_id}/delivery-notes`, {
                fixture: 'erp/shipment/delivery_notes/cpost_shipment_delivery_notes',
            }).as('fetch_shipment_delivery_notes')

            cy.intercept('POST', '**/v1/printers', { fixture: 'erp/printer/printers_32x25.json' }).as('fetch_printers')

            cy.intercept('POST', '**/v1/countries', { fixture: 'erp/country/cpost_countries.json' }).as(
                'fetch_countries',
            )

            cy.intercept('POST', '**/v1//shipment-methods', {
                statusCode: 200,
                body: {
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: 24,
                                carrier_id: 35,
                                label: 'Express',
                            },
                        ],
                    },
                },
            }).as('fetch_shipment_method')
        })

        it('should have a first page visible', () => {
            cy.visit(PAGE(1111))
            cy.toggleMenu()

            // Check table headers
            const table_headers = [
                '#',
                'N° colis',
                'Nb colis',
                'Poids',
                'Transporteur',
                'Destination',
                'Statut',
                'Étiquettes',
            ]

            cy.wait('@fetch_shipment_delivery_notes')
            cy.wait('@fetch_shipment')
            cy.wait('@fetch_printers')

            cy.get('th[scope=col]').should('have.length', table_headers.length)

            table_headers.forEach((text, idx) => {
                cy.get('th[scope=col]').eq(idx).should('contain', text)
            })

            cy.get('[data-context=table-row]').should('have.length', 3)
            cy.selectTable()

            cy.get('[data-context="erp-table"] tbody').within(() => {
                // one parcel
                cy.get('tr:eq(0) td:eq(0)').find('[data-context=delivery-note-badge] a').should('contain', '1187')
                cy.get('tr:eq(0) td:eq(1)').should('contain', 'A')
                cy.get('tr:eq(0) td:eq(2)').should('contain', '1')
                cy.get('tr:eq(0) td:eq(3)').should('contain', '11 kg')
                cy.get('tr:eq(0) td:eq(4)').should('contain', 'DHL - Express')
                cy.get('tr:eq(0) td:eq(5)').should('contain', 'Donald DUCK')
                cy.get('tr:eq(0) td:eq(6)').should('contain', 'imprimé')
                cy.get('tr:eq(0) td:eq(7)')
                    .find('[data-context=delivery-notes-show-sticker]')
                    .should('contain', 'Voir les étiquettes')
                cy.get('[data-context=delivery-note-print-sticker]').should('not.exist')

                // Two parcels
                cy.get('tr:eq(1) td:eq(0)').find('[data-context=delivery-note-badge] a').should('contain', '2287')
                cy.get('tr:eq(1) td:eq(1)').should('contain', 'B')
                cy.get('tr:eq(1) td:eq(1)').should('contain', 'C')
                cy.get('tr:eq(1) td:eq(2)').should('contain', '2')
                cy.get('tr:eq(1) td:eq(3)').should('contain', '22 kg')
                cy.get('tr:eq(1) td:eq(4)').should('contain', 'DHL - Express')
                cy.get('tr:eq(1) td:eq(5)').should('contain', 'Balthazar PICSOU')
                cy.get('tr:eq(1) td:eq(6)').should('contain', 'imprimé')
                cy.get('tr:eq(1) td:eq(7)')
                    .find('[data-context=delivery-notes-show-sticker]')
                    .should('contain', 'Voir les étiquettes')

                // Status != 4
                cy.get('tr:eq(2) td:eq(0)').find('[data-context=delivery-note-badge] a').should('contain', '3387')
                cy.get('tr:eq(2) td:eq(1)').should('value', '')
                cy.get('tr:eq(2) td:eq(2)').should('contain', '1')
                cy.get('tr:eq(2) td:eq(3)').should('contain', '33 kg')
                cy.get('tr:eq(2) td:eq(4)').should('contain', 'DHL - Express')
                cy.get('tr:eq(2) td:eq(5)').should('contain', 'Mickey MOUSE')
                cy.get('tr:eq(2) td:eq(6)').should('contain', 'en cours')
                cy.get('tr:eq(2) td:eq(7)').should('value', '')
            })
        })
    })

    describe('Page on opened shipment', () => {
        beforeEach(() => {
            cy.authenticate()

            const shipment_id = 4444

            cy.fixture('erp/shipment/cpost_shipments')
                .then((payload) => {
                    cy.intercept('POST', '**/v1/shipments', {
                        statusCode: 200,
                        body: {
                            data: {
                                shipments: [
                                    payload.data.shipments.find((shipment) => shipment.shipment_id === shipment_id),
                                ],
                            },
                        },
                    })
                })
                .as('fetch_shipment')

            cy.intercept('POST', `**/v1/shipment/${shipment_id}/delivery-notes`, {
                fixture: 'erp/shipment/delivery_notes/cpost_shipment_delivery_notes',
            }).as('fetch_shipment_delivery_notes')

            cy.intercept('POST', '**/v1/printers', { fixture: 'erp/printer/printers_32x25.json' }).as('fetch_printers')

            cy.intercept('POST', '**/v1/countries', { fixture: 'erp/country/cpost_countries.json' }).as(
                'fetch_countries',
            )

            cy.intercept('POST', '**/v1//shipment-methods', {
                statusCode: 200,
                body: {
                    data: {
                        shipment_methods: [
                            {
                                shipment_method_id: '24',
                                carrier_id: 35,
                                label: 'Express',
                            },
                            {
                                shipment_method_id: '25',
                                carrier_id: 35,
                                label: 'Normal',
                            },
                        ],
                    },
                },
            }).as('fetch_shipment_method')
        })

        it('should send a reprint query', () => {
            cy.mockErpUser()
            cy.visit(PAGE(4444))
            cy.toggleMenu()

            cy.wait('@fetch_shipment_delivery_notes')
            cy.wait('@fetch_shipment')
            cy.wait('@fetch_printers')

            cy.intercept('POST', '**/v1/wms/sticker/reprint', {
                statusCode: 200,
                body: {
                    data: {},
                },
            }).as('print_stickers')

            cy.get('[data-context="erp-table"] tbody').within(() => {
                // one parcel - reprint
                cy.get('tr:eq(0) td:eq(7)').find('form[data-context=delivery-note-print-sticker]').should('be.visible')
                cy.get('tr:eq(0) td:eq(7)')
                    .find('form[data-context=delivery-note-print-sticker] select')
                    .should('be.visible')
                cy.get('tr:eq(0) td:eq(7)')
                    .find('form[data-context=delivery-note-print-sticker] [data-context=erp-button]')
                    .should('be.visible')
                    .click()

                cy.get('tr:eq(0) td:eq(7)')
                    .find('form[data-context=delivery-note-print-sticker] select')
                    .select('Zebra_128_1')

                cy.get('tr:eq(0) td:eq(7)')
                    .find('form[data-context=delivery-note-print-sticker] [data-context=erp-button]')
                    .should('be.visible')
                    .click()

                cy.wait('@print_stickers')
            })
        })

        it('should update delivery note', () => {
            cy.intercept('PUT', '**/v1/delivery-note/**', () => ({
                statusCode: 200,
                body: {
                    data: {},
                },
            })).as('update_delivery_note')

            cy.mockErpUser([DELIVERY_NOTE_WRITE])
            cy.visit(PAGE(4444))
            cy.toggleMenu()

            cy.wait('@fetch_shipment_delivery_notes')
            cy.wait('@fetch_shipment')
            cy.wait('@fetch_printers')
            cy.wait('@fetch_countries')
            cy.wait('@fetch_shipment_method')

            cy.get('[data-context="erp-table"] tbody').within(() => {
                cy.get('tr:eq(0) td:eq(8)').find('[data-context=delivery-notes-edit]').should('be.visible').click()
            })

            const form = '[data-context="form-edit-delivery-note"]'
            const field = (name) => `${form} [data-context="edit-delivery-note-${name}"]`

            cy.get(form).should('be.visible')

            cy.get(field('shipment-method')).should('be.visible')
            cy.get(field('shipment-method')).select('Normal')

            cy.get(field('civility')).should('be.visible').should('have.value', 'M.').select('Madame')

            cy.get(field('firstname')).should('be.visible').should('have.value', 'Donald').type('{selectall}Daisy')

            cy.get(field('lastname')).should('be.visible').should('have.value', 'DUCK')

            cy.get(field('email'))
                .should('be.visible')
                .should('have.value', '<EMAIL>')
                .type('{selectall}<EMAIL>')

            cy.get(field('company')).should('be.visible').should('have.value', 'PHC Holding')

            cy.get(field('cellphone')).should('be.visible').should('have.value', '0678912345')

            cy.get(field('phone')).should('be.visible').should('have.value', '0123456789')

            cy.get(field('address-line1')).should('be.visible').should('have.value', '38 rue des castors junior')

            cy.get(field('address-line2')).should('be.visible').should('have.value', 'riri').type('{selectall}lili')

            cy.get(field('address-line3')).should('be.visible').should('have.value', 'fifi').type('{selectall}lulu')

            cy.get(field('address-line4')).should('be.visible').should('have.value', 'loulou').type('{selectall}zizi')

            cy.get(field('postal-code')).should('be.visible').should('have.value', '11111')
            cy.get(field('city')).should('be.visible').should('have.value', 'DONALDCITY')
            cy.get(field('country')).should('be.visible').should('have.value', '67').select('GUADELOUPE')

            cy.get(form).submit()

            cy.wait('@update_delivery_note').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    address: ['38 rue des castors junior', 'lili', 'lulu', 'zizi'],
                    city: 'DONALDCITY',
                    civility: 'Mme',
                    cellphone: '0678912345',
                    country_id: 77,
                    email: '<EMAIL>',
                    firstname: 'Daisy',
                    lastname: 'DUCK',
                    phone: '0123456789',
                    postal_code: '11111',
                    shipment_method_id: 25,
                    company: 'PHC Holding',
                })
            })
        })

        it('should add a parcel number', () => {
            cy.mockErpUser([DELIVERY_NOTE_WRITE])
            cy.visit(PAGE(4444))
            cy.toggleMenu()

            cy.intercept('PUT', '**/v1/wms/parcel/5', {
                statusCode: 200,
                body: { data: {} },
            }).as('update_parcel_number')

            cy.wait('@fetch_shipment_delivery_notes')
            cy.wait('@fetch_shipment')
            cy.wait('@fetch_printers')
            cy.wait('@fetch_countries')
            cy.wait('@fetch_shipment_method')

            cy.get('[data-context="erp-table"] tbody').within(() => {
                cy.get('tr:eq(1) td:eq(1)')
                    .find('[data-context=delivery-note-update-parcel-number] input')
                    .should('be.visible')
                    .type('123456')

                cy.get('tr:eq(1) td:eq(1)').find('[data-context=delivery-note-update-parcel-number] button').click()
            })

            cy.wait('@update_parcel_number').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({ parcel_number: '123456' })
            })
        })

        it('should have a delete option', () => {
            cy.mockErpUser([DELIVERY_NOTE_WRITE])
            cy.visit(PAGE(4444))
            cy.toggleMenu()

            cy.intercept('DELETE', '**/v1/delivery-note/1187/detach', {
                statusCode: 200,
                body: {
                    data: {},
                },
            }).as('detach_delivery_note')

            cy.wait('@fetch_shipment_delivery_notes')
            cy.wait('@fetch_shipment')
            cy.wait('@fetch_printers')
            cy.wait('@fetch_countries')
            cy.wait('@fetch_shipment_method')

            cy.get('[data-context="erp-table"] tbody tr:eq(0) td:eq(8)')
                .find('[data-context=delivery-notes-delete]')
                .should('be.visible')
                .click()

            // cancel
            cy.get('[data-context=detach-delivery-note-panel]').should('be.visible')
            cy.get('[data-context=detach-delivery-note-cancel]').should('be.visible').click()
            cy.get('[data-context=detach-delivery-note-panel]').should('not.be.visible')

            // confirm
            cy.get('[data-context="erp-table"] tbody tr:eq(0) td:eq(8)')
                .find('[data-context=delivery-notes-delete]')
                .click()
            cy.get('[data-context=slide-out-container] [data-context=detach-delivery-note-confirm]:last')
                .should('be.visible')
                .click()
            cy.wait('@detach_delivery_note')

            cy.get('[data-context="erp-table"] tbody tr:eq(0) td:eq(0)').should('have.text', 'BL 2287 ')
        })
    })
})
