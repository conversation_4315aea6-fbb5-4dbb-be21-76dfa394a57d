import { ATTRIBUTE_READ } from '../../../../src/apps/erp/permissions'

describe('Subcategories attributes - Quick edition panel', function () {
    const PAGE = '/category/subcategories'

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('POST', '**/api/erp/v1/subcategories', { fixture: 'erp/category/subcategories' }).as(
            'fetch_subcategories',
        )
    })

    it('show list of associated attributes in slide panel', function () {
        cy.mockErpUser([ATTRIBUTE_READ])

        cy.intercept('POST', 'https://erp.graphql/v1/graphql', {
            fixture: 'graphql/eav/category/fetch_subcategory_attributes.json',
        }).as('fetch_subcategory_attributes')

        cy.visit(PAGE)
        cy.wait('@fetch_subcategories')
        cy.toggleMenu()

        cy.get('[data-context=quick-edit-btn]').should('be.visible').eq(0).click()

        cy.wait('@fetch_subcategory_attributes')

        cy.get('[data-context=slide-out-container] [data-context=attribute-item-wrapper]').should('be.visible')

        // Test on the content and its functionality are done in ./subcategory_edit_attribute.js
    })
})
