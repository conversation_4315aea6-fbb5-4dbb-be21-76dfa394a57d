import { SUBCATEGORY_UPDATE } from '../../../../src/apps/erp/permissions'
import { maxLength, required } from '@vuelidate/validators'

describe('Subcategory', function () {
    const PAGE = '/category/subcategory/310/edit'

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('POST', '**/api/erp/v1/subcategories', {
            fixture: 'erp/category/subcategories_filtered.json',
        }).as('fetch_subcategory')
        cy.intercept('POST', '**/api/erp/v1/categories', {
            fixture: 'erp/category/categories.json',
        }).as('fetch_categories')
        cy.intercept('POST', '**/api/erp/v1/warranty-types', {
            fixture: 'erp/category/warranty_types.json',
        }).as('fetch_warranty_types')
        cy.intercept('POST', '**/api/erp/v1/subcategory-types', {
            fixture: 'erp/category/subcategory_types.json',
        }).as('fetch_subcategory_types')
    })

    it('display subcategory edition page', function () {
        cy.mockErpUser()
        cy.visit(PAGE)
        cy.wait('@fetch_subcategory').then((xhr) => {
            expect(xhr.request.body.where.subcategory_id._eq).to.eq(310)
        })

        cy.get('[data-context=page-header]').should('contain', 'Édition sous-catégorie')
        cy.get('[data-context=page-header]').should('contain', 'Lecteurs Blu-ray UHD 4K (310)')

        cy.wait('@fetch_warranty_types')
        cy.wait('@fetch_categories').then((xhr) => {
            expect(xhr.request.body.limit).to.eq(5000)
        })

        cy.get('[data-context=default-section]').as('default-section')
        cy.get('@default-section')
            .find('[data-context=name] input')
            .should('have.value', 'Lecteurs Blu-ray UHD 4K')
            .should('be.visible')
        cy.get('@default-section').find('[data-context=category]').contains('Lecteurs Blu-ray/DVD').should('be.visible')
        cy.get('@default-section')
            .find('[data-context=custom-code] input')
            .should('have.value', '12345678')
            .should('be.visible')
        cy.get('@default-section')
            .find('[data-context=ecotax-code] input')
            .should('have.value', '12345')
            .should('be.visible')

        cy.get('[data-context=warranty-section]').as('warranty-section')
        cy.get('@warranty-section')
            .find('[data-context=sous-type-bbac] input')
            .should('have.value', 'LECTEUR BLU RAY #33')
            .should('be.disabled')
        cy.get('@warranty-section').find('[data-context=warranty-type] select').should('have.value', 'SON')

        cy.get('[data-context=logistics-section]').as('logistics-section')
        cy.get('@logistics-section')
            .find('[data-context=outsize] [data-status=checked].opacity-100')
            .should('be.visible')
        cy.get('@logistics-section').find('[data-context=charged-delivery] input').should('have.value', '9.99')

        cy.get('[data-context=seller-commission-section]').as('seller-commission-section')

        cy.get('@seller-commission-section').find('[data-context=info-text]').should('be.visible')
        cy.get('@seller-commission-section')
            .find('[data-context=subcategory-type] select')
            .should('have.value', 'IMAGE')
        cy.get('@seller-commission-section')
            .find('[data-context=cultural-product] [data-status=checked]')
            .should('be.visible')
        cy.get('@seller-commission-section')
            .find('[data-context=ldw-exclude] [data-status=not-checked]')
            .should('be.visible')

        cy.get('[data-context=sales-channel-commission-section').as('sales-channel-section')
        cy.get('@sales-channel-section').should('contain', 'Commissions des marketplaces')

        cy.get('@sales-channel-section').find('[data-context=sales-channels-table]').as('table').should('be.visible')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 2)
        cy.get('@header').eq(0).should('contain', 'canal de vente')
        cy.get('@header').eq(1).should('contain', 'commission')

        cy.get('@table').find('tbody tr').as('rows')
        cy.get('@rows').should('have.length', 2)

        let row_id = 0
        let cell_id = 0
        cy.get('@rows').eq(row_id++).find('td').as('row')
        cy.get('@row').eq(cell_id++).should('contain', 'amazon.fr')
        cy.get('@row')
            .eq(cell_id++)
            .find('[data-context=commission]')
            .find('[data-context=erp-input]')
            .should('have.value', '13.5')

        cell_id = 0
        cy.get('@rows').eq(row_id++).find('td').as('row')
        cy.get('@row').eq(cell_id++).should('contain', 'amazon.it')
        cy.get('@row')
            .eq(cell_id++)
            .find('[data-context=commission]')
            .find('[data-context=erp-input]')
            .should('have.value', '12')

        cy.get('[data-context=erp-button]').should('be.disabled')
    })

    it('can edit subcategory edition page', function () {
        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.intercept('PUT', '**/api/erp/v1/subcategory/310', {
            body: '',
            statusCode: 204,
            delay: 500,
        }).as('put_subcategory')

        cy.visit(PAGE)
        cy.wait(['@fetch_subcategory', '@fetch_warranty_types', '@fetch_subcategory_types'])

        cy.get('[data-context=name] input').clear().type('Mon nouveau nom')

        cy.get('[data-context=warranty-type] select').select('VIDEO')

        cy.get('[data-context=custom-code] input').clear().type('666')

        cy.get('[data-context=ecotax-code] input').clear().type('999')

        cy.get('[data-context=erp-button]').as('button')

        cy.get('@button').should('not.be.disabled')

        cy.get('[data-context=subcategory-type] select').select('HIFI')
        cy.get('[data-context=ldw-exclude] button').click()

        cy.get('@button').click()

        cy.get('@button').should('be.disabled')

        cy.wait('@put_subcategory').then((xhr) => {
            expect(xhr.request.body.data.subcategory_id).to.eq(310)
            expect(xhr.request.body.data.name).to.eq('Mon nouveau nom')
            expect(xhr.request.body.data.parent_category_id).to.eq(55)
            expect(xhr.request.body.data.bbac_subtype_id).to.eq(33)
            expect(xhr.request.body.data.subcategory_type).to.eq('HIFI')
            expect(xhr.request.body.data.warranty_type).to.eq('VIDEO')
            expect(xhr.request.body.data.outsize).to.eq(true)
            expect(xhr.request.body.data.charged_delivery).to.eq(9.99)
            expect(xhr.request.body.data.seller_commission_config.ldw_exclude).to.eq(true)
            expect(xhr.request.body.data.seller_commission_config.cultural_product).to.eq(true)
            expect(xhr.request.body.data.custom_code).to.eq('666')
            expect(xhr.request.body.data.ecotax_code).to.eq('999')
        })

        cy.toast(`La sous-catégorie a été modifiée`, 'success')
        cy.get('@button').should('not.be.disabled')
    })

    it('handle update Error with empty body', function () {
        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.intercept('PUT', '**/api/erp/v1/subcategory/310', {
            body: {},
            statusCode: 400,
        }).as('put_bad_subcategory')

        cy.visit(PAGE)
        cy.wait(['@fetch_subcategory', '@fetch_warranty_types', '@fetch_subcategory_types'])

        cy.get('[data-context=warranty-type] select').as('warranty_type')
        cy.get('@warranty_type').select('VIDEO')
        cy.get('[data-context=erp-button]').click()
        cy.wait('@put_bad_subcategory')

        cy.toast(`Une erreur est survenue`, 'danger')
    })

    it('handle update Error with validation_errors', function () {
        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.intercept('PUT', '**/api/erp/v1/subcategory/310', {
            body: {
                status: 'error',
                message: 'Invalid parameters',
                code: 1000,
                data: {
                    validation_errors: {
                        name: 'This value is not valid.',
                        parent_category_id: 'Mauvais parent',
                        warranty_type: 'NON',
                        charged_delivery: 'This value should not be blank.',
                    },
                },
            },
            statusCode: 400,
        }).as('put_bad_subcategory')

        cy.visit(PAGE)
        cy.wait(['@fetch_subcategory', '@fetch_warranty_types', '@fetch_subcategory_types'])

        cy.get('[data-context=warranty-type] select').as('warranty_type')
        cy.get('@warranty_type').select('VIDEO')
        cy.get('[data-context=erp-button]').click()
        cy.wait('@put_bad_subcategory')

        cy.get('[data-context=subcategory-name-error]')
            .should('be.visible')
            .should('contain', 'Veuillez entrer des caractères valides')
        cy.get('[data-context=parent-category-error]').should('be.visible').should('contain', 'Mauvais parent')
        cy.get('[data-context=warranty-type-error]').should('be.visible').should('contain', 'NON')
        cy.get('[data-context=charged-delivery-error]')
            .should('be.visible')
            .should('contain', 'Cette valeur ne doit pas être vide')
    })

    it('prevent enabling warranty type if bbac subtype is not set', function () {
        cy.intercept('POST', '**/api/erp/v1/subcategories', {
            fixture: 'erp/category/subcategories_without_bbac_subtype.json',
        }).as('fetch_subcategory')

        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.visit(PAGE)
        cy.wait(['@fetch_subcategory', '@fetch_warranty_types', '@fetch_subcategory_types'])

        cy.get('[data-context=sous-type-bbac] input').should('have.value', '')

        cy.get('[data-context=erp-button]').as('button')
        cy.get('[data-context=warranty-type] select').as('warranty_type')

        cy.get('@warranty_type').should('have.value', 'NON')
        cy.get('@warranty_type').should('not.have.class', 'ring-red-500')
        cy.get('[data-context=warranty-type] [data-context=erp-input-helper]').should('not.exist')
        cy.get('@button').should('not.be.disabled')

        cy.get('@warranty_type').select('VIDEO')
        cy.get('[data-context=erp-button]').click()

        cy.get('@warranty_type').should('have.class', 'ring-red-500')
        cy.get('[data-context=warranty-type] [data-context=erp-input-helper]').should(
            'contain',
            'Le sous-type BBAC doit être renseigné pour ajouter une GLD',
        )

        cy.get('@warranty_type').select('SON')
        cy.get('[data-context=erp-button]').click()

        cy.get('@warranty_type').should('have.class', 'ring-red-500')
        cy.get('[data-context=warranty-type] [data-context=erp-input-helper]').should(
            'contain',
            'Le sous-type BBAC doit être renseigné pour ajouter une GLD',
        )
    })

    it('prevent submitting form with empty name', function () {
        cy.intercept('POST', '**/api/erp/v1/subcategories', {
            fixture: 'erp/category/subcategories_without_bbac_subtype.json',
        }).as('fetch_subcategory')

        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.visit(PAGE)
        cy.wait(['@fetch_subcategory', '@fetch_warranty_types', '@fetch_subcategory_types'])

        cy.get('[data-context=name] input').as('name-input')
        cy.get('@name-input').should('have.value', 'Lecteurs Blu-ray UHD 4K')
        cy.get('@name-input').clear()

        cy.get('[data-context=erp-button]').click()

        cy.get('[data-context=subcategory-name-required]')
            .should('be.visible')
            .should('contain', 'Ce champ est obligatoire')
    })

    it('prevent submitting form with too long name', function () {
        cy.intercept('POST', '**/api/erp/v1/subcategories', {
            fixture: 'erp/category/subcategories_without_bbac_subtype.json',
        }).as('fetch_subcategory')

        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.visit(PAGE)
        cy.wait(['@fetch_subcategory', '@fetch_warranty_types', '@fetch_subcategory_types'])

        cy.get('[data-context=name] input')
            .clear()
            .type("Mon nouveau nom beaucoup trop long tellement qu'il compte plus de 64 caractères")

        cy.get('[data-context=erp-button]').click()

        cy.get('[data-context=subcategory-name-too-long]')
            .should('be.visible')
            .should('contain', 'Le nom doit contenir un maximum de 64 caractères')
    })

    it('prevent submitting form with sales channel commission out of range', function () {
        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.intercept('PUT', '**/api/erp/v1/subcategory/310', {
            body: {
                status: 'error',
                message: 'Invalid parameters',
                code: 1000,
                data: [
                    {
                        target: { sales_channel_id: 3 },
                        validation_errors: { commission_rate: '[key:value_out_of_range] value 120 : Out of range' },
                    },
                ],
            },
            statusCode: 400,
        }).as('put_bad_subcategory')

        cy.visit(PAGE)
        cy.wait(['@fetch_subcategory', '@fetch_warranty_types', '@fetch_subcategory_types'])

        cy.get('[data-context=sales-channel-commission-section').as('sales-channel-section')
        cy.get('@sales-channel-section').find('[data-context=sales-channels-table]').as('table')
        cy.get('@table').find('tbody tr').as('rows')
        cy.get('@rows').eq(0).find('td').as('row')

        cy.get('@row').eq(1).find('[data-context=commission]').find('[data-context=erp-input]').clear().type('-12')

        cy.get('[data-context=erp-button]').click()
        cy.wait('@put_bad_subcategory')

        cy.get('[data-context=commission-out-of-range]')
            .should('be.visible')
            .should('contain', 'Cette valeur doit être comprise entre 0 et 100')
    })
})
