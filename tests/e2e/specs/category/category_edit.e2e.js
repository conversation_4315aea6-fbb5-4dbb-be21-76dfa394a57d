import { SUBCATEGORY_UPDATE } from '../../../../src/apps/erp/permissions'

describe('Category', function () {
    const PAGE = '/category/category/55/edit'

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('POST', '**/api/erp/v1/categories', {
            fixture: 'erp/category/categories_filtered.json',
        }).as('fetch_category')
        cy.intercept('POST', '**/api/erp/v1/domains', {
            fixture: 'erp/category/domains.json',
        }).as('fetch_domains')
    })

    it('display category edition page', function () {
        cy.mockErpUser()
        cy.visit(PAGE)
        cy.wait('@fetch_category').then((xhr) => {
            expect(xhr.request.body.where.category_id._eq).to.eq(55)
        })

        cy.get('[data-context=page-header]').should('contain', 'Édition catégorie')
        cy.get('[data-context=page-header]').should('contain', 'Lecteurs Blu-ray/DVD (55)')

        cy.wait('@fetch_domains')

        cy.get('[data-context=default-section]').as('default-section')
        cy.get('@default-section')
            .find('[data-context=name] input')
            .should('have.value', 'Lecteurs Blu-ray/DVD')
            .should('be.visible')
        cy.get('@default-section').find('[data-context=domain]').contains('Télévision').should('be.visible')
        cy.get('@default-section')
            .find('[data-context=custom-code] input')
            .should('have.value', '85219000')
            .should('be.visible')

        cy.get('[data-context=erp-button]').should('be.disabled')
    })

    it('can edit category edition page', function () {
        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.intercept('PUT', '**/api/erp/v1/category/55', {
            body: '',
            statusCode: 204,
            delay: 500,
        }).as('put_category')

        cy.visit(PAGE)
        cy.wait('@fetch_category')

        cy.get('[data-context=name] input').clear().type('Mon nouveau nom')

        cy.get('[data-context=erp-button]').as('button')

        cy.get('@button').should('not.be.disabled')

        cy.get('@button').click()

        cy.get('@button').should('be.disabled')

        cy.wait('@put_category').then((xhr) => {
            expect(xhr.request.body.category_id).to.eq(55)
            expect(xhr.request.body.name).to.eq('Mon nouveau nom')
            expect(xhr.request.body.domain_id).to.eq(2)
        })

        cy.toast(`La catégorie a été modifiée`, 'success')
        cy.get('@button').should('not.be.disabled')
    })

    it('handle update Error', function () {
        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.intercept('PUT', '**/api/erp/v1/category/55', {
            body: '',
            statusCode: 400,
        }).as('put_bad_category')

        cy.visit(PAGE)
        cy.wait('@fetch_category')

        cy.get('[data-context=erp-button]').click()
        cy.wait('@put_bad_category')

        cy.toast(`Une erreur est survenue`, 'danger')
    })

    it('prevent submitting form with empty name', function () {
        cy.intercept('POST', '**/api/erp/v1/categories/55', {
            fixture: 'erp/category/categories.json',
        }).as('fetch_category')

        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.visit(PAGE)
        cy.wait('@fetch_category')

        cy.get('[data-context=name] input').as('name-input')
        cy.get('@name-input').should('have.value', 'Lecteurs Blu-ray/DVD')
        cy.get('@name-input').clear()

        cy.get('[data-context=erp-button]').click()

        cy.get('[data-context=category-name-required]')
            .should('be.visible')
            .should('contain', 'Ce champ est obligatoire')
    })

    it('prevent submitting form with too long name', function () {
        cy.intercept('POST', '**/api/erp/v1/categories/55', {
            fixture: 'erp/category/categories.json',
        }).as('fetch_category')

        cy.mockErpUser([SUBCATEGORY_UPDATE])
        cy.visit(PAGE)
        cy.wait('@fetch_category')

        cy.get('[data-context=name] input').clear().type('Mon nouveau nom beaucoup beaucoup trop long')

        cy.get('[data-context=erp-button]').click()

        cy.get('[data-context=category-name-too-long]')
            .should('be.visible')
            .should('contain', 'Le nom doit contenir un maximum de 32 caractères')
    })
})
