describe('Categories', function () {
    const PAGE = '/category/categories'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/categories', { fixture: 'erp/category/categories' }).as('fetch_categories')

        cy.intercept('POST', '**/api/erp/v1/domains', { fixture: 'erp/category/domains' }).as('fetch_domains')
    })

    it('should provides a page listing all categories for an authorized user', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_categories')

        cy.get('[data-context=search-filter]').should('exist')

        cy.get('a[data-context=external-link]')
            .should('exist')
            .should('have.attr', 'href')
            .and('match', /https?:\/\/.*\/category\/list/)

        // Check pagination infos
        cy.get('[data-context=pagination-info]').should('contain', '1-4 sur 4')

        // Check table header
        cy.get('[data-context=erp-table]').as('table')

        cy.get('@table').find('thead tr th').as('header')

        cy.get('@header').should('have.length', 2)

        cy.get('@header').eq(0).should('contain', 'Catégorie')

        cy.get('@header').eq(1).should('contain', 'Code douanier')
    })

    it('should show all rows unfiltered', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_categories')

        cy.selectTable()
        cy.selectCell(0, 0).should('contain', 'Câbles audio')
        cy.selectCell(0, 1).should('contain', '85444991')

        cy.selectCell(2, 0).should('contain', 'Téléviseurs')
        cy.selectCell(2, 1).should('contain', '85256000')
    })

    it('should have a correct result when filtered by category name', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_categories')

        cy.get('[data-context=erp-table]').as('table')

        cy.get('@table').find('tbody tr').should('have.length', 4)

        cy.selectTable()
        cy.selectCell(0, 0).should('contain', 'Câbles audio')
        cy.selectCell(1, 0).should('contain', 'Lecteurs Blu-ray/DVD')
        cy.selectCell(2, 0).should('contain', 'Téléviseurs')
        cy.selectCell(3, 0).should('contain', 'Vidéoprojection')

        cy.intercept('POST', '**/api/erp/v1/categories**', { fixture: 'erp/category/categories_filtered' }).as(
            'fetch_categories_filtered',
        )

        cy.get('[data-context=search-filter]').type('blu{enter}')
        cy.wait('@fetch_categories_filtered').then((xhr) => {
            expect(xhr.request.body.limit).to.eq(50)
            expect(xhr.request.body.page).to.eq(1)
            expect(xhr.request.body.where.name._ilike).to.eq(`%blu%`)
        })

        cy.get('@table').find('tbody tr').should('have.length', 1)

        cy.selectCell(0, 0).should('contain', 'Lecteurs Blu-ray/DVD')
    })

    it('should create a category successfully', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_categories')

        cy.get('[data-context=btn-add-category]').should('contain', 'Ajouter une catégorie').click()
        cy.wait('@fetch_domains')

        cy.get('[data-context=category-add]').should('be.visible').as('slide')

        // contains expected content in the slide
        cy.get('@slide').find('[data-context=page-header]').should('contain', "Création d'une catégorie")
        cy.get('@slide').find('[data-context=category-name]').should('exist')
        cy.get('@slide').find('[data-context=domain-list]').should('exist')
        cy.get('@slide').find('[data-context=create-btn]').should('contain', 'Créer la catégorie')

        cy.get('[data-context=category-name]').clear().type('K7 audio vintage')
        cy.get('[data-context=category-add]').find('[data-context=erp-multiselect]').erpMultiselect('', 'Vinyle')

        cy.intercept('POST', '**/api/erp/v1/category', {
            fixture: 'erp/category/categoryAddSuccess.json',
        }).as('createCategorySuccess')

        cy.get('[data-context=create-btn]').click()

        cy.toast('La catégorie a bien été créée', 'success')

        cy.get('@slide').should('not.exist')

        cy.wait('@createCategorySuccess').then((xhr) =>
            expect(xhr.request.body.category_name).to.eq('K7 audio vintage'),
        )
    })

    it('should fail to create a category because this name already exists', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_categories')

        cy.get('[data-context=btn-add-category]').should('contain', 'Ajouter une catégorie').click()
        cy.wait('@fetch_domains')

        cy.get('[data-context=category-add]').should('be.visible').as('slide')

        // contains expected content in the slide
        cy.get('@slide').find('[data-context=page-header]').should('contain', "Création d'une catégorie")
        cy.get('@slide').find('[data-context=category-name]').should('exist')
        cy.get('@slide').find('[data-context=domain-list]').should('exist')
        cy.get('@slide').find('[data-context=create-btn]').should('contain', 'Créer la catégorie')

        cy.get('[data-context=category-name]').clear().type('K7 nouvelle génération')
        cy.get('[data-context=category-add]').find('[data-context=erp-multiselect]').erpMultiselect('', 'Vinyle')

        cy.intercept('POST', '**/api/erp/v1/category', {
            statusCode: 409,
            body: { message: 'Ce nom de catégorie existe déjà' },
        })

        cy.get('[data-context=create-btn]').click()

        cy.toast('Ce nom de catégorie existe déjà', 'danger')
    })

    it('prevent submitting form with too long name', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_categories')

        cy.get('[data-context=btn-add-category]').should('contain', 'Ajouter une catégorie').click()
        cy.wait('@fetch_domains')

        cy.get('[data-context=category-add]').should('be.visible').as('slide')

        cy.get('[data-context=category-name]').clear().type('Mon nouveau nom beaucoup beaucoup trop long')

        cy.get('[data-context=create-btn]').click()

        cy.get('[data-context=category-name-too-long]')
            .should('be.visible')
            .should('contain', 'Le nom doit contenir un maximum de 32 caractères')
        cy.get('[data-context=current-domain-required]')
            .should('be.visible')
            .should('contain', 'Ce champ est obligatoire')
    })
})
