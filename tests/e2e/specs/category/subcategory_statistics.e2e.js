describe('Subcategory statistics page', function () {
    const PAGE = '/category/subcategory/310/statistic'

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('POST', '**/api/erp/v1/metabase/embed-dashboard-url', {
            statusCode: 200,
            body: {
                status: 'success',
                data: { url: 'https:metabase.lxc' },
            },
        }).as('post_metabase_embed_dashboard_url')
    })

    it('Display statistics dashboard from metabase', function () {
        cy.mockErpUser()
        cy.visit(PAGE)

        cy.wait('@post_metabase_embed_dashboard_url').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({
                dashboard: 17,
                params: { subcategory_id: 310 },
            })
        })

        cy.get('[data-context=metabase-wrapper]').should('be.visible')
    })
})
