import {
    ATTRIBUTE_CATEGORY_ASSOCIATION_CREATE,
    ATTRIBUTE_CATEGORY_ASSOCIATION_DELETE,
    ATTRIBUTE_FILTER_UPDATE,
    ATTRIBUTE_READ,
} from '../../../../src/apps/erp/permissions'
import {
    aliasMutation,
    aliasQuery,
    getMutationAlias,
    getQueryAlias,
    GRAPHQL_ENDPOINT,
    hasOperationName,
} from '../../utils/graphql-test-utils'

describe('Subcategories', function () {
    const PAGE = '/category/subcategory/310/attributes'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([ATTRIBUTE_READ])

        // Subcategory ID 310
        cy.intercept('POST', '**/api/erp/v1/subcategories', { fixture: 'erp/category/subcategories_filtered' }).as(
            'fetch_subcategories',
        )

        // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
        // alias the queries and mutations for our tests in a beforeEach
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            // Queries
            aliasQuery(req, 'fetchSubcategory')
            aliasQuery(req, 'fetchSubcategoryAttributes')

            // Mutations
            aliasMutation(req, 'createSubcategoryAttribute')
            aliasMutation(req, 'upsertSubcategory')

            // Default
            if (hasOperationName(req, 'fetchSubcategory')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchSubcategory')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/fetch_subcategory.json',
                })
            }

            if (hasOperationName(req, 'fetchSubcategoryAttributes')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchSubcategoryAttributes')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/fetch_subcategory_attributes.json',
                })
            }
        })
    })

    it('list associated attributes', function () {
        cy.visit(PAGE)
        cy.wait('@fetch_subcategories')
        cy.toggleMenu()

        // Check page title
        cy.get('[data-context=page-header]').should('contain', 'Édition sous-catégorie')
        cy.get('[data-context=page-header]').should('contain', 'Lecteurs Blu-ray UHD 4K (310)')

        cy.get('[data-context=attribute-item-wrapper]').as('subcategory_attributes').should('be.visible')

        // Check summary
        cy.get('@subcategory_attributes')
            .find('[data-context=summary]')
            .should('contain', '8')
            .should('contain', 'attribut(s) associé(s)')

        // Check table header
        cy.get('@subcategory_attributes')
            .find('thead')
            .should('contain', 'Attribut')
            .should('contain', 'Ordre d’affichage')
            .should('contain', 'Filtre')
            .should('contain', 'Ouvert')

        // Check attributes
        cy.get('@subcategory_attributes').find('[data-context=subcategory-attribute-item]').as('attributes')
        cy.get('@attributes').should('have.length', 8)

        // Check first attribute
        cy.get('@attributes').eq(0).as('attribute')

        cy.get('@attribute').find('[data-context=delete-btn]').should('not.exist')
        cy.get('@attribute').find('[data-context=attribute-name]').should('contain', 'Connectiques')
        cy.get('@attribute')
            .find('[data-context=display-order]')
            .should('contain', '100')
            .find('input')
            .should('not.exist')
        cy.get('@attribute').find('[data-context=filter-activator]').should('have.attr', 'data-disabled')
        cy.get('@attribute').find('[data-context=filter-activator] button').should('have.attr', 'aria-checked')
        cy.get('@attribute')
            .find('[data-context=filter-opener]')
            .should('be.visible')
            .should('have.attr', 'data-disabled')
        cy.get('@attribute').find('[data-context=filter-opener] button').should('have.attr', 'aria-checked')

        // check filters on second line
        cy.get('@attributes').eq(1).as('attribute')
        cy.get('@attribute').find('[data-context=filter-activator]').should('have.attr', 'data-disabled')
        cy.get('@attribute').find('[data-context=filter-activator] button').should('have.attr', 'aria-checked')
        cy.get('@attribute')
            .find('[data-context=filter-opener]')
            .should('be.visible')
            .should('have.attr', 'data-disabled')
        cy.get('@attribute').find('[data-context=filter-opener] button').should('not.have.attr', 'aria-checked')

        // check filters on third line
        cy.get('@attributes').eq(2).as('attribute')
        cy.get('@attribute').find('[data-context=filter-activator]').should('have.attr', 'data-disabled')
        cy.get('@attribute').find('[data-context=filter-activator] button').should('not.have.attr', 'aria-checked')
        cy.get('@attribute').find('[data-context=filter-opener]').should('not.exist')

        // Check associate form not there
        cy.get('[data-context=associate-form]').should('not.exist')
    })

    it('list associated attributes with additional permission', function () {
        cy.mockErpUser([
            ATTRIBUTE_READ,
            ATTRIBUTE_CATEGORY_ASSOCIATION_CREATE,
            ATTRIBUTE_CATEGORY_ASSOCIATION_DELETE,
            ATTRIBUTE_FILTER_UPDATE,
        ])

        cy.visit(PAGE)
        cy.wait('@fetch_subcategories')
        cy.toggleMenu()

        // Check first attribute
        cy.get('[data-context=attribute-item-wrapper] [data-context=subcategory-attribute-item]').eq(0).as('attribute')

        cy.get('@attribute').find('[data-context=delete-btn]').should('be.visible')
        cy.get('@attribute').find('[data-context=attribute-name]').should('contain', 'Connectiques')
        cy.get('@attribute')
            .find('[data-context=display-order]')
            .find('input')
            .should('be.visible')
            .should('have.value', '100')
        cy.get('@attribute').find('[data-context=filter-activator]').should('not.have.attr', 'data-disabled')
        cy.get('@attribute')
            .find('[data-context=filter-opener]')
            .should('be.visible')
            .should('not.have.attr', 'data-disabled')

        cy.get('[data-context=delete-btn]').should('be.visible')
        cy.get('[data-context=associate-form]').should('be.visible')
    })

    it('associate an attribute successfully', function () {
        cy.mockErpUser([ATTRIBUTE_READ, ATTRIBUTE_CATEGORY_ASSOCIATION_CREATE])

        cy.visit(PAGE)
        cy.wait('@fetch_subcategories')
        cy.toggleMenu()

        // Ui check
        cy.get('[data-context=subcategory-attribute-item]').as('attributes')
        cy.get('@attributes').should('have.length', 8)

        // No permission for deletion
        cy.get('[data-context=delete-btn]').should('not.exist')

        // Mock & check autocomplete parameters
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'fetchAttributes')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchAttributes')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/fetch_attributes__autocomplete.json',
                })

                expect(req.body.variables.offset).to.eq(0)
                expect(req.body.variables.limit).to.eq(10)
                expect(req.body.variables.search).to.eq('hdmi')
                expect(req.body.variables.order_by).to.deep.eq([{ name: 'asc' }])
                // Exclude already associated attribute from result
                expect(req.body.variables.where.attribute_id._nin).to.have.length(8)
            }
        })

        cy.get('[data-context=autocomplete]').multiselect('hdmi', 'Entrees HDMI 2.0')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'createSubcategoryAttribute')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasMutation(req, 'createSubcategoryAttribute')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/create_subcategory_attribute.json',
                })

                expect(req.body.variables).to.deep.eq({
                    subcategory_attribute: {
                        attribute_id: 910,
                        subcategory_id: 310,
                        display_order: 110,
                    },
                })
            }

            if (hasOperationName(req, 'fetchSubcategoryAttributes')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchSubcategoryAttributes')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/fetch_subcategory_attributes__new.json',
                })

                expect(req.body.variables.offset).to.eq(0)
                expect(req.body.variables.limit).to.eq(1000)
                expect(req.body.variables.where.subcategory_id._eq).to.eq(310)
            }
        })

        // Trigger the associate queries
        cy.get('[data-context=associate-form]').submit()
        cy.wait(getMutationAlias('createSubcategoryAttribute'))
        cy.wait(getQueryAlias('fetchSubcategoryAttributes'))

        // Ui check - No attribute associated
        cy.get('table [data-context=skeleton]').should('not.exist')
        cy.get('[data-context=subcategory-attribute-item]').should('have.length', 9)
        cy.get('[data-context=subcategory-attribute-item]').eq(8).should('contain', 'Entrees HDMI 2.0')
    })

    it('de-associate an attribute successfully', function () {
        cy.mockErpUser([ATTRIBUTE_READ, ATTRIBUTE_CATEGORY_ASSOCIATION_DELETE])

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'fetchSubcategoryAttributes')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchSubcategoryAttributes')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/fetch_subcategory_attributes__new.json',
                })
            }
        })

        cy.visit(PAGE)
        cy.wait('@fetch_subcategories')
        cy.toggleMenu()

        // Ui check
        cy.get('[data-context=subcategory-attribute-item]').as('attributes')
        cy.get('@attributes').should('have.length', 9)
        cy.get('@attributes').should('contain', 'Entrees HDMI 2.0')

        // No permission for creation
        cy.get('[data-context=associate-form]').should('not.exist')

        // Check delete btns
        cy.get('[data-context=delete-btn]').should('be.visible')

        // Trigger the deassociate queries
        cy.get('@attributes').eq(8).should('contain', 'Entrees HDMI 2.0')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'deleteSubcategoryAttribute')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasMutation(req, 'deleteSubcategoryAttribute')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/delete_subcategory_attribute.json',
                })

                expect(req.body.variables.where.attribute_id._eq).to.eq(910)
                expect(req.body.variables.where.subcategory_id._eq).to.eq(310)
            }

            if (hasOperationName(req, 'fetchSubcategoryAttributes')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchSubcategoryAttributes')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/fetch_subcategory_attributes.json',
                })
            }
        })

        cy.get('[data-context=delete-btn]').eq(8).click()

        // Ui check - No attribute associated
        cy.get('@attributes').should('have.length', 8)
        cy.get('@attributes').should('not.contain', 'Entrees HDMI 2.0')
    })

    it('handle filter activation per category', function () {
        cy.mockErpUser([ATTRIBUTE_READ, ATTRIBUTE_CATEGORY_ASSOCIATION_DELETE])
        cy.visit(PAGE)
        cy.wait('@fetch_subcategories')
        cy.toggleMenu()

        // header contains expected text and toggler
        cy.get('[data-context=attribute-item-wrapper] [data-context=header]')
            .should('contain', 'Afficher les filtres')
            .find('[data-context=erp-toggle]')
            .as('toggler')
            .should('be.visible')

        // toggler should be loaded and disabled (no proper right to edit)
        cy.get('@toggler').should('not.have.attr', 'data-is-loading')
        cy.get('@toggler').should('have.attr', 'data-disabled')
        cy.get('@toggler').find('button').should('have.attr', 'aria-checked')

        // enabled with proper right
        cy.addPermissions([ATTRIBUTE_FILTER_UPDATE])
        cy.get('@toggler').should('not.have.attr', 'data-disabled')

        // test toggler click
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'upsertSubcategory')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasMutation(req, 'upsertSubcategory')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/upsert_subcategory.json',
                })

                expect(req.body.variables).to.deep.eq({
                    subcategory: {
                        subcategory_id: 310,
                        use_filters: false,
                    },
                })
            }
        })

        cy.get('@toggler').find('button').click()
        cy.get('@toggler').find('button').should('not.have.attr', 'aria-checked')
    })

    it('update the display order', function () {
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'fetchSubcategoryAttributes')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchSubcategoryAttributes')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/fetch_subcategory_attributes.json',
                })
            }
        })

        cy.mockErpUser([ATTRIBUTE_READ, ATTRIBUTE_FILTER_UPDATE])
        cy.visit(PAGE)
        cy.wait('@fetch_subcategories')
        cy.wait(getQueryAlias('fetchSubcategoryAttributes'))
        cy.toggleMenu()

        // prepare fixtures
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'updateSubcategoryAttribute')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasMutation(req, 'updateSubcategoryAttribute')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/update_subcategory_attribute__display_order.json',
                })

                expect(req.body.variables).to.deep.eq({
                    subcategory_id: 310,
                    attribute_id: 287,
                    changes: {
                        display_order: 60,
                    },
                })
            }

            if (hasOperationName(req, 'fetchSubcategoryAttributes')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasQuery(req, 'fetchSubcategoryAttributes')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/fetch_subcategory_attributes__display_order_updated.json',
                })
            }
        })

        cy.get('[data-context=subcategory-attribute-item]')
            .eq(1)
            .should('contain', 'Dimensions Hauteur')
            .find('[data-context=display-order] input')
            .focus()
        cy.get('[data-context=subcategory-attribute-item]')
            .eq(1)
            .should('contain', 'Dimensions Hauteur')
            .find('[data-context=display-order] input')
            .clear()
        cy.get('[data-context=subcategory-attribute-item]')
            .eq(1)
            .should('contain', 'Dimensions Hauteur')
            .find('[data-context=display-order] input')
            .type('60{enter}')

        cy.wait(getMutationAlias('updateSubcategoryAttribute'))
        cy.wait(getQueryAlias('fetchSubcategoryAttributes'))

        // data has been refreshed
        cy.get('[data-context=subcategory-attribute-item]')
            .eq(0)
            .should('contain', 'Dimensions Hauteur')
            .find('[data-context=display-order] input')
            .should('have.value', '60')
    })

    it('update the filter status', function () {
        cy.mockErpUser([ATTRIBUTE_READ, ATTRIBUTE_FILTER_UPDATE])
        cy.visit(PAGE)
        cy.wait('@fetch_subcategories')
        cy.toggleMenu()

        // click on opener when false => open the filter
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'updateSubcategoryAttribute')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasMutation(req, 'updateSubcategoryAttribute')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/update_subcategory_attribute__filter_open.json',
                })

                expect(req.body.variables).to.deep.eq({
                    subcategory_id: 310,
                    attribute_id: 287,
                    changes: {
                        filter_status: 'ACTIVE_OPENED',
                    },
                })
            }
        })
        cy.get('[data-context=attribute-item-wrapper] [data-context=subcategory-attribute-item]')
            .eq(1)
            .as('attribute')
            .should('contain', 'Dimensions Hauteur')
            .find('[data-context=filter-opener] button')
            .click()

        // data has been refreshed
        cy.get('@attribute').find('[data-context=filter-activator] button').should('have.attr', 'aria-checked')
        cy.get('@attribute')
            .find('[data-context=filter-opener] button')
            .should('be.visible')
            .should('have.attr', 'aria-checked')

        // click on opener when already true => close the filter
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'updateSubcategoryAttribute')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasMutation(req, 'updateSubcategoryAttribute')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/update_subcategory_attribute__filter_closed.json',
                })

                expect(req.body.variables).to.deep.eq({
                    subcategory_id: 310,
                    attribute_id: 287,
                    changes: {
                        filter_status: 'ACTIVE_CLOSED',
                    },
                })
            }
        })
        cy.get('@attribute').find('[data-context=filter-opener] button').click()

        // click on activator when already true => deactivate the filter
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'updateSubcategoryAttribute')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasMutation(req, 'updateSubcategoryAttribute')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/update_subcategory_attribute__filter_inactive.json',
                })

                expect(req.body.variables).to.deep.eq({
                    subcategory_id: 310,
                    attribute_id: 287,
                    changes: {
                        filter_status: 'INACTIVE',
                    },
                })
            }
        })
        cy.get('@attribute').find('[data-context=filter-activator] button').click()

        // click on activator when false => activate the filter
        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'updateSubcategoryAttribute')) {
                // Declare the alias from the initial intercept in the beforeEach
                aliasMutation(req, 'updateSubcategoryAttribute')

                // Set req.fixture for the response
                req.reply({
                    fixture: 'graphql/eav/category/update_subcategory_attribute__filter_closed.json',
                })

                expect(req.body.variables).to.deep.eq({
                    subcategory_id: 310,
                    attribute_id: 287,
                    changes: {
                        filter_status: 'ACTIVE_CLOSED',
                    },
                })
            }
        })
        cy.get('@attribute').find('[data-context=filter-activator] button').click()
    })
})
