describe('Subcategories', function () {
    const PAGE = '/category/subcategories'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/subcategories', { fixture: 'erp/category/subcategories' }).as(
            'fetch_subcategories',
        )

        cy.intercept('POST', '**/api/erp/v1/categories', { fixture: 'erp/category/categories' }).as('fetch_categories')
    })

    it('should provides a page listing all subcategories for an authorized user', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_subcategories')

        cy.get('[data-context=search-filter]').should('exist')

        cy.get('a[data-context=external-link]')
            .should('exist')
            .should('have.attr', 'href')
            .and('match', /https?:\/\/.*\/category\/list/)

        // Check pagination infos
        cy.get('[data-context=pagination-info]').should('contain', '1-3 sur 3')

        // Check table header
        cy.get('[data-context=erp-table]').as('table')

        cy.get('@table').find('thead tr th').as('header')

        cy.get('@header').should('have.length', 8)

        cy.get('@header').eq(0).should('contain', 'Catégorie')

        cy.get('@header').eq(1).should('contain', 'Sous-catégorie')

        cy.get('@header').eq(2).should('contain', 'Code douanier')

        cy.get('@header').eq(3).should('contain', 'GLD')

        cy.get('@header').eq(4).should('contain', 'Univers')

        cy.get('@header').eq(5).should('contain', 'Produits vendables / Produits totaux')

        cy.get('@header').eq(6).should('contain', 'Frais de port Marketplaces')

        cy.get('@header').eq(7).should('contain', 'Actions')
    })

    it('should show all rows unfiltered', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_subcategories')

        cy.selectTable()
        cy.selectCell(0, 0).should('contain', 'Lecteurs Blu-ray/DVD')
        cy.selectCell(0, 1).should('contain', 'Lecteurs Blu-ray UHD 4K')
        cy.selectCell(0, 2).should('contain', '12345678')
        cy.selectCell(0, 3).should('contain', 'NON')
        cy.selectCell(0, 4).should('contain', 'IMAGE')
        cy.selectCell(0, 5).should('contain', '54 / 137')
        cy.selectCell(0, 6).should('contain', '9.99 €')
        cy.selectCell(0, 7).should('contain', '-')

        cy.selectCell(2, 0).should('contain', 'Vidéoprojection')
        cy.selectCell(2, 1).should('contain', 'Vidéoprojecteurs UHD-4K')
        cy.selectCell(2, 2).should('contain', '666')
        cy.selectCell(2, 3).should('contain', 'NON')
        cy.selectCell(2, 4).should('contain', 'IMAGE')
        cy.selectCell(2, 5).should('contain', '12 / 159')
        cy.selectCell(2, 6).should('contain', '3.9 €')
        cy.selectCell(2, 7).should('contain', '-')
    })

    it('should have a correct result when filtered by subcategory name', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_subcategories')

        cy.get('[data-context=erp-table]').as('table')

        cy.get('@table').find('tbody tr').should('have.length', 3)

        cy.selectTable()
        cy.selectCell(0, 1).should('contain', 'Lecteurs Blu-ray UHD 4K')
        cy.selectCell(1, 1).should('contain', 'Téléviseurs UHD-4K')
        cy.selectCell(2, 1).should('contain', 'Vidéoprojecteurs UHD-4K')

        cy.intercept('POST', '**/api/erp/v1/subcategories**', { fixture: 'erp/category/subcategories_filtered' }).as(
            'fetch_subcategories_filtered',
        )

        cy.get('[data-context=search-filter]').type('blu{enter}')
        cy.wait('@fetch_subcategories_filtered').then((xhr) => {
            expect(xhr.request.body.limit).to.eq(50)
            expect(xhr.request.body.page).to.eq(1)
            expect(xhr.request.body.where.name._ilike).to.eq(`%blu%`)
        })

        cy.get('@table').find('tbody tr').should('have.length', 1)

        cy.selectCell(0, 1).should('contain', 'Lecteurs Blu-ray UHD 4K')
    })

    it('should create a subcategory successfully', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_subcategories')

        cy.get('[data-context=btn-add-subcategory]').should('contain', 'Ajouter une sous-catégorie').click()
        cy.wait('@fetch_categories')

        cy.get('[data-context=subcategory-add]').should('be.visible').as('slide')

        // contains expected content in the slide
        cy.get('@slide').find('[data-context=page-header]').should('contain', "Création d'une sous-catégorie")
        cy.get('@slide').find('[data-context=subcategory-name]').should('exist')
        cy.get('@slide').find('[data-context=category-list]').should('exist')
        cy.get('@slide').find('[data-context=create-btn]').should('contain', 'Créer la sous-catégorie')

        cy.get('[data-context=subcategory-name]').clear().type('Câbles roses pailletés')
        cy.get('[data-context=subcategory-add]')
            .find('[data-context=erp-multiselect]')
            .erpMultiselect('', 'Câbles audio')

        cy.intercept('POST', '**/api/erp/v1/category/18/subcategory', {
            fixture: 'erp/subcategory/subcategoryAddSuccess.json',
        }).as('createSubcategorySuccess')

        cy.get('[data-context=create-btn]').click()

        cy.toast('La sous-catégorie a bien été créée', 'success')

        cy.get('@slide').should('not.exist')

        cy.wait('@createSubcategorySuccess').then((xhr) =>
            expect(xhr.request.body.subcategory_name).to.eq('Câbles roses pailletés'),
        )
    })

    it('should fail to create a subcategory because this name already exists', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_subcategories')

        cy.get('[data-context=btn-add-subcategory]').should('contain', 'Ajouter une sous-catégorie').click()
        cy.wait('@fetch_categories')

        cy.get('[data-context=subcategory-add]').should('be.visible').as('slide')

        // contains expected content in the slide
        cy.get('@slide').find('[data-context=page-header]').should('contain', "Création d'une sous-catégorie")
        cy.get('@slide').find('[data-context=subcategory-name]').should('exist')
        cy.get('@slide').find('[data-context=category-list]').should('exist')
        cy.get('@slide').find('[data-context=create-btn]').should('contain', 'Créer la sous-catégorie')

        cy.get('[data-context=subcategory-name]').clear().type('Câbles verts pailletés')
        cy.get('[data-context=subcategory-add]')
            .find('[data-context=erp-multiselect]')
            .erpMultiselect('', 'Câbles audio')

        cy.intercept('POST', '**/api/erp/v1/category/18/subcategory', {
            statusCode: 409,
            body: { message: 'Ce nom de sous-catégorie existe déjà' },
        })

        cy.get('[data-context=create-btn]').click()

        cy.toast('Ce nom de sous-catégorie existe déjà', 'danger')
    })

    it('prevent submitting form with too long name', function () {
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_subcategories')

        cy.get('[data-context=btn-add-subcategory]').should('contain', 'Ajouter une sous-catégorie').click()
        cy.wait('@fetch_categories')

        cy.get('[data-context=subcategory-add]').should('be.visible').as('slide')

        cy.get('[data-context=subcategory-name]')
            .clear()
            .type("Mon nouveau nom beaucoup trop long tellement qu'il compte plus de 64 caractères")

        cy.get('[data-context=create-btn]').click()

        cy.get('[data-context=subcategory-name-too-long]')
            .should('be.visible')
            .should('contain', 'Le nom doit contenir un maximum de 64 caractères')
        cy.get('[data-context=current-category-required]')
            .should('be.visible')
            .should('contain', 'Ce champ est obligatoire')
    })
})
