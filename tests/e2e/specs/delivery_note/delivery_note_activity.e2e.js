describe('ERP - Delivery note history', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    // TODO: rewrite these tests which are incorrect and/or inconsistent
    // it('access the page without permission', function() {
    //     cy.visit('erp/delivery-note/history')
    //
    //     cy.get('[data-context=skeleton]').should('not.exist')
    //     cy.get('[data-context=page-header]').should('contain', '<PERSON> de livraison')
    //     cy.get('[data-context=search]').should('be.visible')
    //     cy.get('[data-context=submit-button]').should('be.visible')
    // })
    //
    // TODO: this might be the inconsistency noted above. Probably due to bad naming - but :
    // TODO: - a BL can be deleted but have an history, hence not exists and have an history
    // TODO: - a BL can be deleted but not have an history, hence not exists and have an history
    // it('shows an error message if the delivery note does not exists and does not have any history', function() {
    //     cy.visit('erp/delivery-note/history/666')
    //
    //     cy.get('[data-context=skeleton]').should('not.exist')
    //     cy.get('[data-context=skeleton]').should('be.visible')
    //     cy.get('[data-context=error-message]').should('contain', 'Le BL 666 est inexistant et sans historique.')
    // })

    it('shows an error message if the delivery note has been deleted but has some history', function () {
        cy.intercept('GET', '**/api/erp/v1/delivery-note/123', {
            fixture: 'erp/delivery_note/delivery_note_123.json',
        }).as('delivery_note_request')
        cy.intercept('GET', '**/api/erp/v1/delivery-note/123/activity', {
            statusCode: 200,
            body: { status: 'success', data: [] },
        }).as('delivery_note_activity_request')

        cy.visit('erp/delivery-note/history')

        cy.get('[data-context=search]').type('123')
        cy.get('[data-context=submit-button]').click()

        cy.get('[data-context=skeleton]').should('be.visible')

        cy.wait('@delivery_note_request')
        cy.wait('@delivery_note_activity_request')

        cy.get('[data-context=error-message]').should(
            'contain',
            "La chronologie n'est pas encore disponible pour ce bon de livraison",
        )

        cy.get('[data-context=activity]').should('not.exist')
    })

    it('display the statuses timeline for older delivery note without history', function () {
        cy.intercept('GET', '**/api/erp/v1/delivery-note/456', {
            statusCode: 200,
            body: { status: 'success', data: [] },
        }).as('delivery_note_request')

        cy.intercept('GET', '**/api/erp/v1/delivery-note/456/activity', {
            fixture: 'erp/delivery_note/delivery_note_activity_456.json',
        }).as('delivery_note_activity_request')

        cy.intercept('GET', '**/api/erp/v1/delivery-note/456/tracking', {
            fixture: 'erp/delivery_note/delivery_note_tracking_456.json',
        }).as('delivery_note_tracking_request')

        cy.visit('erp/delivery-note/history')

        cy.get('[data-context=search]').type('456')
        cy.get('[data-context=submit-button]').click()

        cy.get('[data-context=skeleton]').should('be.visible')

        cy.wait('@delivery_note_request')
        cy.wait('@delivery_note_activity_request')
        cy.wait('@delivery_note_tracking_request')

        cy.get('[data-context=error-message]').should(
            'contain',
            'Le BL 456 a été supprimé, son historique est disponible ci-dessous',
        )
        cy.get('[data-context=horizontal-timeline]').should('not.exist')

        cy.get('[data-context=activity]').as('activity')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(0)
            .get('[data-context=history-label]')
            .should('contain', 'Création')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(1)
            .get('[data-context=history-label]')
            .should('contain', 'Impression')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(2)
            .get('[data-context=history-label]')
            .should('contain', 'Supprimé')
    })

    it('display the statuses timeline and the history', function () {
        cy.intercept('GET', '**/api/erp/v1/delivery-note/789', {
            fixture: 'erp/delivery_note/delivery_note_789.json',
        }).as('delivery_note_request')
        cy.intercept('GET', '**/api/erp/v1/delivery-note/789/activity', {
            fixture: 'erp/delivery_note/delivery_note_activity_789.json',
        }).as('delivery_note_activity_request')

        cy.intercept('GET', '**/api/erp/v1/delivery-note/789/tracking', {
            fixture: 'erp/delivery_note/delivery_note_tracking_789.json',
        }).as('delivery_note_tracking_request')

        cy.visit('erp/delivery-note/history')
        cy.get('[data-context=menu-toggler]').click()

        cy.get('[data-context=search]').type('789')
        cy.get('[data-context=submit-button]').click()

        cy.get('[data-context=skeleton]').should('be.visible')

        cy.wait('@delivery_note_request')
        cy.wait('@delivery_note_activity_request')
        cy.wait('@delivery_note_tracking_request')

        cy.get('[data-context=horizontal-timeline]').as('vertical-timeline')

        cy.get('@vertical-timeline').find('[data-context=timeline-label]').eq(0).should('contain', 'Crée')

        cy.get('@vertical-timeline').find('[data-context=timeline-label]').eq(1).should('contain', 'Pické')

        cy.get('@vertical-timeline').find('[data-context=timeline-label]').eq(2).should('contain', 'Préparé')

        cy.get('@vertical-timeline').find('[data-context=timeline-label]').eq(3).should('contain', 'Éxpédié')

        cy.get('@vertical-timeline').find('[data-context=timeline-label]').eq(4).should('contain', 'Livré')

        cy.get('[data-context=activity]').as('activity')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(0)
            .get('[data-context=history-label]')
            .should('contain', 'Création')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(1)
            .get('[data-context=history-label]')
            .should('contain', 'Impression')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(2)
            .get('[data-context=history-label]')
            .should('contain', 'Début de picking')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(3)
            .get('[data-context=history-label]')
            .should('contain', 'Fin de picking')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(4)
            .get('[data-context=history-label]')
            .should('contain', 'Validation du BL et facturation de la commande')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(5)
            .get('[data-context=history-label]')
            .should('contain', 'Notification de suivi de colis')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(6)
            .get('[data-context=history-label]')
            .should('contain', 'En cours de livraison')
            .get('[data-context=history-description]')
            .should('contain', "En cours - Expédition en cours d'acheminement")

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(7)
            .get('[data-context=history-label]')
            .should('contain', 'En cours de livraison')
            .get('[data-context=history-description]')
            .should('contain', 'Livraison programmée - Date de rendez-vous fixée')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(8)
            .get('[data-context=history-label]')
            .should('contain', 'En cours de livraison')
            .get('[data-context=history-description]')
            .should('contain', 'Livraison en cours')

        cy.get('@activity')
            .eq(0)
            .get('[data-context=details')
            .eq(9)
            .get('[data-context=history-label]')
            .should('contain', 'livraison')
            .get('[data-context=history-description]')
            .should('contain', 'Livrée')
    })
})
