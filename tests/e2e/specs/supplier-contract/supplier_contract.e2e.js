import { ARTICLE_PRICES_WRITE } from '../../../../src/apps/erp/permissions'

describe('Supplier contract', function () {
    const PAGE = '/legacy/fournisseur/edit?id=987'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([ARTICLE_PRICES_WRITE])

        cy.intercept('POST', '**/api/erp/v1/suppliers', {
            fixture: 'erp/supplier-contract/supplier.json',
        }).as('fetch-supplier')

        cy.intercept('GET', '**/api/erp/v1/supplier/987/brands', {
            fixture: 'erp/supplier-contract/supplier_contract_brands.json',
        }).as('fetch-supplier-contract-brands')

        cy.intercept('POST', '**/api/erp/v1/supplier-contracts', {
            fixture: 'erp/supplier-contract/supplier_contracts_2.json',
        }).as('fetch-supplier-contracts')

        cy.visit(PAGE)
        cy.wait('@fetch-supplier')

        cy.get('[data-context=supplier-tab]')
            .find('[data-context=tabs]')
            .contains('[data-context=item]', 'Contrat fournisseur')
            .click()
    })

    it('display supplier contract', function () {
        cy.wait('@fetch-supplier-contract-brands')
        cy.wait('@fetch-supplier-contracts')

        cy.get('[data-context=supplier-contract]').should('exist')

        cy.get('[data-context=supplier-contract-payment]').should('exist')
        cy.get('[data-context=supplier-contract-pam]').should('exist')
        cy.get('[data-context=supplier-contract-rfa]').should('exist')

        cy.get('[data-context=supplier-contract-title]').should('exist').as('title')
        cy.get('@title').find('h2').should('contain', 'Tralala pouet')
        cy.get('@title').find('[data-context=single-value]').should('contain', '2025')

        cy.get('[data-context=supplier-contract-actions]').should('exist').as('actions')
        cy.get('@actions').find('button').eq(0).should('contain', 'Rechercher')
        cy.get('@actions').find('button').eq(1).should('contain', 'Sauvegarder')

        const expected_headings = ['Contrat fournisseur global', 'APart', 'Kanto', 'Ortofon']

        cy.get('[data-context=supplier-contract-brand-discount-description]')
            .as('supplier-contract-brand-discount-description')
            .should('have.length', expected_headings.length)

        expected_headings.forEach((text, index) => {
            cy.get('@supplier-contract-brand-discount-description').eq(index).find('h6').should('contain', text)
        })

        cy.get('@supplier-contract-brand-discount-description').eq(2).find('h6').click()

        cy.get('@supplier-contract-brand-discount-description')
            .eq(0)
            .find('[data-context=erp-textarea]')
            .should('be.disabled')
    })

    it('display supplier contract PAM', function () {
        cy.get('[data-context=supplier-contract-pam]').as('pam').should('exist')

        const expected_data = [
            { heading: 'Mise en avant site Web', value: 0 },
            { heading: 'Newsletters', value: 0 },
            { heading: 'Blog', value: 3 },
            { heading: 'Présence Magasins', value: 1 },
            { heading: 'Formation', value: 1 },
            { heading: 'Reporting des ventes/Stocks', value: 1 },
        ]

        expected_data.forEach((data, index) => {
            cy.get('@pam')
                .find('[data-context=table-row]')
                .eq(index)
                .within(() => {
                    cy.get('[data-context=cell-pam]').should('contain', data.heading)
                    cy.get('[data-context=erp-input-number]').should('have.value', data.value)
                })
        })

        cy.get('@pam').find('[data-context=global-pam]').as('global-pam').should('be.visible')
        cy.get('@global-pam').find('[data-context=erp-input-number]').should('have.value', 6)

        cy.get('@pam').find('[data-context=erp-input-number]').eq(1).clear().type('7')
        cy.get('@global-pam').find('[data-context=erp-input-number]').should('have.value', 13)

        cy.get('@pam').find('label:contains(Commentaire)').should('be.visible')
    })

    it('display supplier contract RFA', function () {
        cy.get('[data-context=supplier-contract-rfa]').as('rfa').should('exist')

        const expected_data = [
            { heading: 'Palier 1', starting_value: 0, end_value: 100000, rate_value: 0 },
            { heading: 'Palier 2', starting_value: 100001, end_value: 200000, rate_value: 0 },
            { heading: 'Palier 3', starting_value: 200001, end_value: 300000, rate_value: 0 },
            { heading: 'Palier 4', starting_value: 300001, end_value: 400000, rate_value: 0 },
            { heading: 'Palier 5', starting_value: 400001, end_value: 500000, rate_value: 0 },
        ]

        expected_data.forEach((data, index) => {
            cy.get('@rfa')
                .find('[data-context=table-row]')
                .eq(index)
                .within(() => {
                    cy.get('[data-context=cell-landing]').should('contain', data.heading)
                    cy.get('[data-context=cell-starting-value]')
                        .find('[data-context=erp-input-number]')
                        .should('have.value', data.starting_value)
                    cy.get('[data-context=cell-end-value]')
                        .find('[data-context=erp-input-number]')
                        .should('have.value', data.end_value)
                    cy.get('[data-context=cell-rate-value]')
                        .find('[data-context=erp-input-number]')
                        .should('have.value', data.rate_value)
                })
        })

        cy.get('@rfa').find('label:contains(Commentaire)').should('be.visible')
    })

    it('Edit global supplier contract', function () {
        cy.get('[data-context=supplier-contract-pam]').as('pam')
        cy.get('@pam').find('[data-context=table-row]').eq(1).find('[data-context=erp-input-number]').clear().type(2)
        cy.get('@pam').find('[data-context=table-row]').eq(2).find('[data-context=erp-input-number]').clear().type(4)

        cy.get('@pam').find('[data-context=global-pam]').as('global-pam').should('be.visible')
        cy.get('@global-pam').find('[data-context=erp-input-number]').should('have.value', 9)

        cy.get('@pam').find(`label:contains(Commentaire)`).siblings('[data-context=erp-textarea]').type('test pam')

        cy.get('[data-context=supplier-contract-unconditional-discount]').as('unconditional-discount')
        cy.get('@unconditional-discount').find('[data-context=unconditional-discount-input]').type('2.2')

        cy.get('[data-context=supplier-contract-rfa]').as('rfa')

        cy.get('@rfa').find('[data-context=cell-rate-value] [data-context=erp-input-number]').eq(0).clear().type(2)
        cy.get('@rfa').find('[data-context=cell-rate-value] [data-context=erp-input-number]').eq(1).clear().type(3)

        cy.get('@rfa').find(`label:contains(Commentaire)`).siblings('[data-context=erp-textarea]').type('test rfa')

        cy.intercept('PUT', '**/api/erp/v1/supplier-contract/1', {
            statusCode: 204,
        }).as('put_supplier_contract')

        cy.get('[data-context=supplier-contract-actions]').find('button').eq(1).click()

        cy.wait('@put_supplier_contract').then((xhr) => {
            expect(xhr.request.body.pam.blog).to.eq(4)
            expect(xhr.request.body.pam.newsletters).to.eq(2)
            expect(xhr.request.body.pam.comment).to.eq('test pam')
            expect(xhr.request.body.rfa.comment).to.eq('test rfa')
            expect(xhr.request.body.brand_id).to.eq(null)
            expect(xhr.request.body.unconditional_discount).to.eq(2.2)
        })
    })

    it('Create brand supplier contract', function () {
        cy.get('[data-context=supplier-contract-brand-discount-description]')
            .eq(1)
            .find('h6')
            .should('contain', 'APart')
            .click()

        cy.get('[data-context=supplier-contract-pam]').as('pam')
        cy.get('@pam').find('[data-context=table-row]').eq(2).find('[data-context=erp-input-number]').clear().type(1)
        cy.get('@pam').find('[data-context=table-row]').eq(3).find('[data-context=erp-input-number]').clear().type(1)

        cy.get('@pam').find('[data-context=global-pam]').as('global-pam').should('be.visible')
        cy.get('@global-pam').find('[data-context=erp-input-number]').should('have.value', 4)

        cy.get('@pam').find(`label:contains(Commentaire)`).siblings('[data-context=erp-textarea]').type('test pam')

        cy.get('[data-context=supplier-contract-rfa]').as('rfa')

        cy.get('@rfa').find('[data-context=cell-rate-value] [data-context=erp-input-number]').eq(0).clear().type(2)
        cy.get('@rfa').find('[data-context=cell-rate-value] [data-context=erp-input-number]').eq(1).clear().type(3)

        cy.get('@rfa').find(`label:contains(Commentaire)`).siblings('[data-context=erp-textarea]').type('test rfa')

        cy.intercept('POST', '**/api/erp/v1/supplier-contract', {
            statusCode: 200,
            body: {
                status: 'success',
                data: { supplier_contract_id: 2 },
            },
        }).as('post_supplier_contract')

        cy.get('[data-context=supplier-contract-actions]').find('button').eq(1).click()

        cy.wait('@post_supplier_contract').then((xhr) => {
            expect(xhr.request.body.pam.blog).to.eq(1)
            expect(xhr.request.body.pam.newsletters).to.eq(0)
            expect(xhr.request.body.pam.comment).to.eq('test pam')
            expect(xhr.request.body.rfa.comment).to.eq('test rfa')
            expect(xhr.request.body.brand_id).to.eq(571)
        })
    })
})
