import { ARTICLE_PRICES_WRITE } from '../../../../src/apps/erp/permissions'

describe('Supplier contract payment', function () {
    const PAGE = '/legacy/fournisseur/edit?id=987'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([ARTICLE_PRICES_WRITE])

        cy.intercept('POST', '**/api/erp/v1/suppliers', {
            fixture: 'erp/supplier-contract/supplier.json',
        }).as('fetch-supplier')

        cy.intercept('GET', '**/api/erp/v1/supplier/987/brands', {
            fixture: 'erp/supplier-contract/supplier_contract_brands.json',
        }).as('fetch-supplier-contract-brands')

        cy.intercept('POST', '**/api/erp/v1/supplier-contracts', {
            fixture: 'erp/supplier-contract/supplier_contracts.json',
        }).as('fetch-supplier-contracts')
    })

    it('display supplier contract payment', function () {
        cy.visit(PAGE)
        cy.wait('@fetch-supplier')

        cy.get('[data-context=supplier-tab]')
            .find('[data-context=tabs]')
            .contains('[data-context=item]', 'Contrat fournisseur')
            .click()

        cy.wait('@fetch-supplier-contract-brands')
        cy.wait('@fetch-supplier-contracts')

        cy.get('[data-context=supplier-contract]').should('exist')
        cy.get('[data-context=supplier-contract-payment]').should('exist').as('payment')

        cy.get('@payment').find('[data-context=erp-table]').as('table')
        cy.get('@table').selectCell(0, 0).should('be.visible').should('contain', 'Mode de paiement')
        cy.get('@table')
            .selectCell(0, 1)
            .find('[data-context=payment]')
            .should('be.visible')
            .should('contain', 'Virement')
        cy.get('@table').selectCell(2, 0).should('be.visible').should('contain', 'Franco')
        cy.get('@table').selectCell(2, 1).find('input').should('be.visible').should('have.value', '450')

        cy.get('@payment').find('[data-context=erp-form-block]').should('contain', 'Commentaire')
    })
})
