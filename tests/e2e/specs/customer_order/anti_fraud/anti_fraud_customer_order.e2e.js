import { customerOrderErpV2Mock } from '../../../utils/erp-server-utils'

describe('Customer order anti fraud module for customer order', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: customerOrderErpV2Mock(123456),
            },
        }).as('customer-order-legacy-123456')
    })

    it('should show nothing', () => {
        cy.intercept('GET', '**/api/erp/v1/anti-fraud/123456', {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    meta: [],
                    statuses: {
                        customer_order_id: 123456,
                        anti_fraud_customer_order: null,
                        anti_fraud_customer_order_payments: [],
                    },
                },
            },
        }).as('load-anti-fraud')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud'])

        cy.get('[data-context=customer-order-anti-fraud-module]').should('not.exist')
    })

    it('should show the anti-fraud module feedback bar', () => {
        cy.intercept('GET', '**/api/erp/v1/anti-fraud/123456', {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    meta: [],
                    statuses: {
                        customer_order_id: 123456,
                        anti_fraud_customer_order: {
                            status: 'REJECTED',
                            created_at: '2024-10-23 11:19:51.000000',
                            reason: { name: 'SHIPPING_AND_BILLING_COUNTRIES_ARE_DIFFERENT' },
                        },
                        anti_fraud_customer_order_payments: [],
                    },
                },
            },
        }).as('load-anti-fraud')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud'])

        cy.get('[data-context=customer-order-anti-fraud-module]').should('be.visible')
        cy.get('[data-context=customer-order-anti-fraud-module]').should('contain', 'Alerte module anti-fraude.')
        cy.get('[data-context=customer-order-anti-fraud-module]').should(
            'contain',
            'Les paiements de la commande ne seront pas acceptés automatiquement.',
        )
    })
})
