import { customerOrderErpV2Mock } from '../../utils/erp-server-utils'

const emitReloadEvent = () => {
    cy.getWrapper().then((wrapper) => {
        wrapper.$emit('receiveMessage', {
            action: 'content-fully-loaded',
        })
    })
}

describe('Show origin of the customer order in the title bar', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456),
                    sales_channel_origin: 'svd.or.any.Marketplace',
                    carrier_id: 1,
                    warehouse_id: 5,
                    store_label: 'Nantes',
                    tags: [
                        {
                            meta: [],
                            name: 'source.sonvideo.web',
                            label: 'Web Son-Video',
                            taxonomy_meta: { source: 'Web SV', source_group: 'Son-Video.com' },
                        },
                    ],
                },
            },
        }).as('customer-order-legacy-123456')

        cy.intercept('GET', '**/api/erp/v1/customer-order/147852/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(147852),
                    has_inconsistent_carrier: true,
                },
            },
        }).as('customer-order-legacy-147852')

        cy.intercept('GET', '**/api/erp/v1/customer-order/987654/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(987654),
                    carrier_id: '20',
                },
            },
        }).as('customer-order-legacy-987654')

        cy.intercept('GET', '**/api/erp/v1/customer-order/111111/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(111111),
                    carrier_id: null,
                },
            },
        }).as('customer-order-legacy-111111')

        cy.intercept('GET', '**/api/erp/v1/customer-order/*********/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(*********),
                    sales_channel_origin: 'amazon.fr',
                    tags: [
                        { meta: { HT: true }, name: 'amazon_business', label: 'Amazon Business', taxonomy_meta: null },
                    ],
                },
            },
        }).as('customer-order-legacy-*********')

        cy.intercept('GET', '**/api/erp/v1/customer-order/*********/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(*********),
                    sales_channel_origin: 'amazon.fr',
                    tags: [
                        { meta: { HT: false }, name: 'amazon_business', label: 'Amazon Business', taxonomy_meta: null },
                    ],
                },
            },
        }).as('customer-order-legacy-*********')

        cy.intercept('GET', '**/api/erp/v1/customer-order/222222/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(222222),
                    sales_channel_origin: 'SON-VIDEO',
                    carrier_id: '20',
                    warehouse_id: 5,
                    store_label: 'Nantes',
                    tags: [{ name: 'status.import' }],
                },
            },
        }).as('customer-order-legacy-222222')

        cy.intercept('GET', '**/api/erp/v1/customer-order/statuses', {
            fixture: 'erp/customer-order/statuses.json',
        }).as('get-statuses')
    })

    it('display the status and origin successfully', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
        emitReloadEvent()
        cy.wait(['@customer-order-legacy-123456', '@get-statuses'])

        cy.get('[data-context=customer-order-status]').should('contain', 'Ouverte')
        cy.get('[data-context=source]')
            .should('contain', 'source')
            .should('contain', 'Son-Video.com')
            .should('contain', 'Web SV')
        cy.get('[data-context=origin]')
            .should('contain', 'origine')
            .should('contain', 'svd.or.any.Marketplace')
            .should('contain', 'Nantes')

        cy.get('[data-context=open-ezl-invoice]').should('not.exist')
    })

    it('display the premium warranty tag successfully', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456123456),
                    sales_channel_origin: 'svd.or.any.Marketplace',
                    carrier_id: 1,
                    warehouse_id: 5,
                    store_label: 'Nantes',
                },
            },
        }).as('customer-order-legacy-123456123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456123456')
        emitReloadEvent()

        cy.wait('@customer-order-legacy-123456123456')
        cy.get('[data-context=premium-warranty-tag]').should('not.exist')

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456123456),
                    sales_channel_origin: 'svd.or.any.Marketplace',
                    carrier_id: 1,
                    has_ongoing_premium_warranty: true,
                    warehouse_id: 5,
                    store_label: 'Nantes',
                },
            },
        }).as('customer-order-legacy-123456123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456123456')
        emitReloadEvent()

        cy.wait('@customer-order-legacy-123456123456')
        cy.get('[data-context=premium-warranty-tag]').should('be.visible').should('contain', 'Garantie premium')
    })

    it('do not display the estimated delivery date when we do not know the initial delivery date', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/555555/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(555555),
                    effective_shipping_date: null,
                    initial_estimated_delivery_date: null,
                    current_estimated_shipping_date: '2023-01-20',
                },
            },
        }).as('customer-order-legacy-555555')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=555555')
        emitReloadEvent()
        cy.wait('@customer-order-legacy-555555')

        cy.get('[data-context=delivery-date-badge]').should('not.exist')
    })

    it('do not display the estimated delivery date when we already sent everything', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/555555/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(555555),
                    effective_shipping_date: '2023-01-01',
                    initial_estimated_delivery_date: '2023-01-10',
                    current_estimated_shipping_date: '2023-01-20',
                },
            },
        }).as('customer-order-legacy-555555')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=555555')
        emitReloadEvent()
        cy.wait('@customer-order-legacy-555555')

        cy.get('[data-context=delivery-date-badge]').should('not.exist')
    })

    it('do display the estimated delivery date when appropriate', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/555555/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(555555),
                    effective_shipping_date: null,
                    initial_estimated_delivery_date: '2023-01-10',
                    current_estimated_shipping_date: '2023-01-20',
                },
            },
        }).as('customer-order-legacy-555555')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=555555')
        emitReloadEvent()
        cy.wait('@customer-order-legacy-555555')

        // in-depth testing of the component is done in DeliveryDateBadge.cy.js
        cy.get('[data-context=delivery-date-badge]').should('contain', '10/01/2023')
        cy.get('[data-context=delivery-date-badge]').should('contain', 'Retard: 10 jrs.')
    })

    it('display a message if carrier is not defined', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
        emitReloadEvent()
        cy.wait('@customer-order-legacy-123456')
        cy.get('[data-context=undefined_carrier_message]')
            .should('be.visible')
            .should('contain', 'La commande ne peut pas être préparée car le transporteur est indéfini.')
    })

    it('display a message if carrier set as null', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=111111')
        emitReloadEvent()
        cy.wait('@customer-order-legacy-111111')
        cy.get('[data-context=undefined_carrier_message]')
            .should('be.visible')
            .should('contain', 'La commande ne peut pas être préparée car le transporteur est indéfini.')
    })

    it('display a message if carrier inconsistent', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=147852')
        emitReloadEvent()
        cy.wait('@customer-order-legacy-147852')
        cy.get('[data-context=inconsistent_carrier_message]')
            .should('be.visible')
            .should('contain', 'La commande ne peut pas être préparée car le transporteur est incohérent.')
    })

    it('does not display a message if carrier ok', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=987654')
        emitReloadEvent()
        cy.wait('@customer-order-legacy-987654')
        cy.get('[data-context=undefined_carrier_message]').should('not.exist')
    })

    it('does display business and duty free badge', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=*********')
        emitReloadEvent()
        cy.wait('@customer-order-legacy-*********')
        cy.get('[data-context=business]').should('exist')
        cy.get('[data-context=duty-free]').should('exist')
        cy.get('[data-context=all-tax-included]').should('not.exist')
    })

    it('does display business and all tax included badge', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=*********')
        emitReloadEvent()
        cy.wait('@customer-order-legacy-*********')
        cy.get('[data-context=business]').should('exist')
        cy.get('[data-context=duty-free]').should('not.exist')
        cy.get('[data-context=all-tax-included]').should('exist')
    })

    it('should display an information message if the customer order is in status "import"', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=222222')
        // no emit reload since the iframe is not loaded with those customer orders
        cy.wait('@customer-order-legacy-222222')
        cy.get('[data-context=import-message]').should('contain', 'Paiement en cours')
    })

    it('should display ezl invoice', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/44/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(44),
                    sales_channel_origin: 'easylounge.com',
                    original_customer_order_id: 'UN_TRUC_DU_SUD',
                },
            },
        }).as('customer-order-legacy-44')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=44')
        cy.wait('@customer-order-legacy-44')

        cy.get('[data-context=page-header]')
            .as('header')
            .should('be.visible')
            .should('contain', 'Commande')
            .should('contain', '44')

        cy.get('@header').find('[data-context=origin]').should('contain', 'easylounge.com')

        cy.get('@header').find('[data-context=open-ezl-invoice]').should('contain', 'Factures EZL').click()

        cy.get('[data-context=slide-out-container] [data-context=invoice-ezl]').should('be.visible')
    })
})
