import { customerOrderErpV2Mock, customerOrderPaymentMock } from '../../utils/erp-server-utils'

describe('Customer order page - Via external customer order id', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.visit('/legacy/v1/commandes/edition_commande.php?no_commande_origine=EXTERNAL_ID')
    })

    it('should redirect to 404 if the customer order was not found', () => {
        cy.get('[data-context=error-code]').should('contain', '404')
        cy.get('[data-context=error-message-general]').should('contain', 'Oops... Une erreur est survenue')
        cy.get('[data-context=error-message-code]').should(
            'contain',
            `La commande avec numéro d'origine "EXTERNAL_ID" n'a pas été trouvée`,
        )
        cy.get('[data-context=back-home-link]').should('contain', "Retour sur la page d'accueil")
    })

    it('should redirect the customer order with our customer order id successfully', () => {
        cy.intercept('POST', '**/api/erp/v1/customer-orders', {
            statusCode: 200,
            body: {
                data: {
                    customer_orders: [{ customer_order_id: 123456 }],
                },
            },
        }).as('customer-orders')

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456),
                    original_customer_order_id: 'EXTERNAL_ID',
                    payments: [
                        {
                            ...customerOrderPaymentMock(),
                        },
                    ],
                },
            },
        }).as('customer-order-legacy-123456')

        cy.intercept('GET', '**/api/erp/v1/anti-fraud/123456', {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    meta: [],
                    statuses: {
                        customer_order_id: 123456,
                        anti_fraud_customer_order: null,
                        anti_fraud_customer_order_payments: [],
                    },
                },
            },
        }).as('load-anti-fraud')

        cy.wait(['@customer-orders', '@customer-order-legacy-123456', '@load-anti-fraud'])

        cy.get('[data-context=page-header]').should('contain', 'EXTERNAL_ID')
        cy.get('[data-context=page-header]').should('contain', '123456')
    })
})
