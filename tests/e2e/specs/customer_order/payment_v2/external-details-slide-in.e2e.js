import {
    customerOrderContextForPaymentsMock,
    customerOrderErpV2Mock,
    customerOrderPaymentMock,
    operationMock,
} from '../../../utils/erp-server-utils'
import { SUPER_ADMIN } from '../../../../../src/apps/erp/permissions'

const OPERATION_ID = '123ea648-5726-4e39-880d-9d39f5eb1e8f'

describe('Customer order payment V2 - External details slide-in', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([SUPER_ADMIN])

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456),
                    payments: [
                        {
                            ...customerOrderPaymentMock(),
                            operation_id: OPERATION_ID,
                        },
                    ],
                },
            },
        }).as('customer-order-legacy-123456')

        cy.intercept('GET', '**/api/erp/v1/anti-fraud/123456', {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    meta: [],
                    statuses: {
                        customer_order_id: 123456,
                        anti_fraud_customer_order: null,
                        anti_fraud_customer_order_payments: [],
                    },
                },
            },
        }).as('load-anti-fraud')

        cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderContextForPaymentsMock(123456),
                    operations: [
                        {
                            ...operationMock(123456, OPERATION_ID),
                            available_actions: [],
                            refundable_amount: '45',
                        },
                    ],
                },
            },
        }).as('customer-order-context-for-payments-123456')

        cy.intercept('GET', '**/api/erp/v1/customer-order/statuses', {
            fixture: 'erp/customer-order/statuses.json',
        }).as('get-statuses')
    })

    it('should close on api error', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        cy.intercept('GET', `**/api/erp-payment/v1/operation/${OPERATION_ID}/external-details`, {
            statusCode: 500,
        }).as('external-details')

        cy.get('[data-context=payment-v2-operation-actions] [data-context=external-details-btn]')
            .should('be.visible')
            .click()
        cy.wait('@external-details')

        cy.toast('Une erreur est survenue', 'danger')
        cy.get('[data-context=payment-external-details-slide-in]').should('not.exist')
    })

    it('should display mandatory fields', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        cy.intercept('GET', `**/api/erp-payment/v1/operation/${OPERATION_ID}/external-details`, {
            statusCode: 200,
            body: { data: { response: 'body' } },
        }).as('external-details')

        cy.get('[data-context=payment-v2-operation-actions] [data-context=external-details-btn]')
            .should('be.visible')
            .click()
        cy.wait('@external-details')
        cy.get('[data-context=payment-external-details-slide-in]')
            .should('be.visible')
            .within(() => {
                cy.get('[data-context=http-error-code]').should('not.exist')
                cy.get('[data-context=external-status]').should('not.exist')
                cy.get('[data-context=request]').should('contain.text', 'body')
            })
        cy.get('[data-context=slide-out-container-close-btn]:visible').click()
        cy.get('[data-context=payment-external-details-slide-in]').should('not.be.visible')
    })

    it('should display all fields', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        cy.intercept('GET', `**/api/erp-payment/v1/operation/${OPERATION_ID}/external-details`, {
            statusCode: 200,
            body: {
                data: {
                    http_error_code: 501,
                    external_status: 'SMOOTHED',
                    response: '{"color":"blue"}',
                },
            },
        }).as('external-details')

        cy.get('[data-context=payment-v2-operation-actions] [data-context=external-details-btn]')
            .should('be.visible')
            .click()
        cy.wait('@external-details')
        cy.get('[data-context=payment-external-details-slide-in]')
            .should('be.visible')
            .within(() => {
                cy.get('[data-context=http-error-code]').should('contain', 501)
                cy.get('[data-context=external-status]').should('contain', 'SMOOTHED')
                cy.get('[data-context=request]').should('contain', '"color": "blue"') // NB: the json is formatted
            })
        cy.get('[data-context=slide-out-container-close-btn]:visible').click()
        cy.get('[data-context=payment-external-details-slide-in]').should('not.be.visible')
    })
})
