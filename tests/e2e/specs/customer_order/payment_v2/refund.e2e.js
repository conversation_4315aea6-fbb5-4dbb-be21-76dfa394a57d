import {
    customerOrderContextForPaymentsMock,
    customerOrderErpV2Mock,
    customerOrderPaymentMock,
} from '../../../utils/erp-server-utils'
import { CUSTOMER_ORDER_PAYMENT_REFUND } from '../../../../../src/apps/erp/permissions'

const OPERATION_ID = '123ea648-5726-4e39-880d-9d39f5eb1e8f'

describe('Customer order payment - Refund', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([CUSTOMER_ORDER_PAYMENT_REFUND])

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456),
                    payments: [
                        {
                            ...customerOrderPaymentMock(),
                            operation_id: OPERATION_ID,
                            payment_method_description: 'Crédit Cetelem Presto',
                            payment_method_code: 'PRESTO',
                        },
                    ],
                },
            },
        }).as('customer-order-legacy-123456')

        cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderContextForPaymentsMock(123456),
                    operations: [
                        {
                            payment_id: '5eea348e-b37b-440c-9b34-28958fb4fec2',
                            code: 'PRESTO',
                            label: 'Cetelem Presto (Paiement fractionn\u00e9)',
                            amount: '1349',
                            refundable_amount: '123.45',
                            histories: [
                                {
                                    description: 'Irrelevent',
                                    internal_status: 'AWAITING',
                                    external_status: 'INITIATE_JOURNEY',
                                    operation_history_id: 2,
                                    operation_id: OPERATION_ID,
                                },
                            ],
                            type: 'PAYMENT',
                            status: 'AWAITING',
                            meta: {
                                agreement_id:
                                    'Nyf3W3EMrlF_4JcyUFGEKhdf269wb3MTPJmtu0_D8gNRPP7H8mZVeBK_MTiw57fs9jiR1adxs3uqyKWRZRBX2w==',
                                application_id: '0jyv9DGfR_8CkePxF4xmVural2-PByv-byrBV4jUHPXFbxPEatPQccN2Bg==',
                            },
                            operation_id: OPERATION_ID,
                            available_actions: ['REFUND'],
                        },
                    ],
                },
            },
        }).as('customer-order-context-for-payments-123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@customer-order-context-for-payments-123456'])

        cy.getWrapper().then((wrapper) => {
            wrapper.$emit('receiveMessage', {
                action: 'handle-payment',
                payload: {
                    action: 'accept',
                    operationId: OPERATION_ID,
                    paymentMethod: 'Crédit Cetelem Presto',
                    paymentCode: 'PRESTO',
                },
            })
        })

        cy.get('[data-context=handle-payment-slide-in]').should('be.visible')
        cy.get('[data-context=handle-payment-slide-in] [data-context=refund-btn]').should('be.visible').click()
    })

    it('should display the slide-in', () => {
        cy.get('[data-context=payment-refund-slide-in]')
            .should('be.visible')
            .within(() => {
                cy.get('[data-context=alert-info]').should(
                    'contain',
                    'Le montant du remboursement ne doit pas excéder 123,45 €',
                )
                cy.get('[data-context=amount] input').should('have.value', 123.45)
            })
    })

    it('should display an error when the customer order is not refundable', () => {
        cy.intercept('POST', `**/api/erp-payment/v1/operation/${OPERATION_ID}/refund`, {
            statusCode: 400,
            body: {
                data: {
                    amount: 'PBL:NOT_REFUNDABLE',
                },
            },
        }).as('refund')

        cy.get('[data-context=payment-refund-slide-in]').within(() => {
            cy.get('[data-context=amount] input').within(() => {
                cy.root().clear()
                cy.root().type('42')
            })
            cy.get('button[type=submit]').click()
            cy.wait('@refund')
            cy.get('[data-context=amount] [data-context=erp-input-helper]').should(
                'contain',
                'Ce paiement ne peut pas être remboursé.',
            )
        })
    })

    it('should display an error when the amount is too high', () => {
        cy.intercept('POST', `**/api/erp-payment/v1/operation/${OPERATION_ID}/refund`, {
            statusCode: 400,
            body: {
                data: {
                    amount: 'PBL:GREATER_THAN_REMAINING_BALANCE',
                },
            },
        }).as('refund')

        cy.get('[data-context=payment-refund-slide-in]').within(() => {
            cy.get('[data-context=amount] input').type('42')
            cy.get('button[type=submit]').click()
            cy.wait('@refund')
            cy.get('[data-context=amount] [data-context=erp-input-helper]').should(
                'contain',
                'Le montant saisi est supérieur au montant remboursable pour ce paiement',
            )
        })
    })

    it('should refund', () => {
        cy.intercept('POST', `**/api/erp-payment/v1/operation/${OPERATION_ID}/refund`, {
            statusCode: 204,
        }).as('refund')

        cy.get('[data-context=payment-refund-slide-in]').within(() => {
            cy.get('[data-context=amount] input').within(() => {
                cy.root().clear()
                cy.root().type('42.54')
            })
            cy.get('button[type=submit]').click()
        })
        cy.wait('@refund').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({ amount: 42.54 })
        })
        cy.get('[payment-refund-slide-in]').should('not.exist')
    })
})
