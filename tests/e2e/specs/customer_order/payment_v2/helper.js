export const SHOULD_NOT_BE_DISABLED = false
export function testAllPaymentV2Actions(OPERATION_ID, data_context, is_disabled = true) {
    cy.get(data_context).should('be.visible')

    // ACCEPT

    cy.intercept('PUT', `**/api/erp-payment/v1/operation/${OPERATION_ID}/accept`, {
        statusCode: 204,
    }).as('accept_payment_v2')

    if (is_disabled) {
        cy.get(data_context).within(() => {
            cy.get('[data-context=payment-v2-operation-actions] [data-context=accept-btn]').should('be.disabled')
        })
    } else {
        cy.get(data_context).within(() => {
            cy.get('[data-context=payment-v2-operation-actions] [data-context=accept-btn]').should('be.visible').click()
        })

        cy.wait('@accept_payment_v2')
        // reload the page underneath
        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])
    }

    // CANCEL

    if (is_disabled) {
        cy.get(data_context).within(() => {
            cy.get('[data-context=payment-v2-operation-actions] [data-context=cancel-btn]').should('be.disabled')
        })
    } else {
        cy.intercept('PUT', `**/api/erp-payment/v1/operation/${OPERATION_ID}/cancel`, {
            statusCode: 204,
        }).as('cancel_payment_v2')

        cy.get(data_context).within(() => {
            cy.get('[data-context=payment-v2-operation-actions] [data-context=cancel-btn]').should('be.visible').click()
        })

        cy.get('[data-context=payment-cancel-confirm-slide-in]')
            .should('be.visible')
            .within(() => {
                cy.get('[data-context=alert-warn]').should(
                    'contain',
                    'Le paiement sera annulé auprès du prestataire. Il sera impossible de le réactiver.',
                )
                cy.get('[data-context=confirm-btn]').click()
                cy.root().should('not.be.visible')
            })

        cy.wait('@cancel_payment_v2')
        // reload the page underneath
        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])
    }

    // REFRESH

    cy.intercept('PUT', `**/api/erp-payment/v1/operation/${OPERATION_ID}/refresh`, {
        statusCode: 204,
    }).as('refresh_payment_v2')

    cy.get(data_context).within(() => {
        cy.get('[data-context=payment-v2-operation-actions] [data-context=refresh-btn]').should('be.visible').click()
    })

    cy.wait('@refresh_payment_v2')
    // reload the page underneath
    cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

    // REFUND

    if (is_disabled) {
        cy.get(data_context).within(() => {
            cy.get('[data-context=payment-v2-operation-actions] [data-context=refund-btn]').should('be.disabled')
        })
    } else {
        cy.intercept('POST', `**/api/erp-payment/v1/operation/${OPERATION_ID}/refund`, {
            statusCode: 204,
        }).as('refund_payment_v2')

        cy.get(data_context).within(() => {
            cy.get('[data-context=payment-v2-operation-actions] [data-context=refund-btn]').should('be.visible').click()
        })

        cy.get('[data-context=payment-refund-slide-in]').should('be.visible')

        cy.get('[data-context=payment-refund-slide-in]').within(() => {
            cy.get('[data-context=amount] input').clear()
            cy.get('[data-context=amount] input').type('42.54')
            cy.get('button[type=submit]').click()
        })

        cy.wait('@refund_payment_v2')
        // reload the page underneath
        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])
    }
}
