import {
    customerOrderContextForPaymentsMock,
    customerOrderErpV2Mock,
    customerOrderPaymentMock,
    operationMock,
} from '../../../utils/erp-server-utils'
import { SHOULD_NOT_BE_DISABLED, testAllPaymentV2Actions } from './helper'
import {
    CUSTOMER_ORDER_PAYMENT_ACCEPT,
    CUSTOMER_ORDER_PAYMENT_CANCEL,
    CUSTOMER_ORDER_PAYMENT_REFUND,
    CUSTOMER_ORDER_PAYMENT_REMIT_REQUEST,
    SUPER_ADMIN,
} from '../../../../../src/apps/erp/permissions'

const OPERATION_ID = '123ea648-5726-4e39-880d-9d39f5eb1e8f'

describe('Customer order payment V2 - Operation action in payment block', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456),
                    payments: [
                        {
                            ...customerOrderPaymentMock(),
                            operation_id: OPERATION_ID,
                        },
                    ],
                },
            },
        }).as('customer-order-legacy-123456')

        cy.intercept('GET', '**/api/erp/v1/anti-fraud/123456', {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    meta: [],
                    statuses: {
                        customer_order_id: 123456,
                        anti_fraud_customer_order: null,
                        anti_fraud_customer_order_payments: [],
                    },
                },
            },
        }).as('load-anti-fraud')

        cy.intercept('GET', '**/api/erp/v1/customer-order/statuses', {
            fixture: 'erp/customer-order/statuses.json',
        }).as('get-statuses')
    })

    it('should have no available action', () => {
        cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderContextForPaymentsMock(123456),
                    operations: [
                        {
                            ...operationMock(123456, OPERATION_ID),
                        },
                    ],
                },
            },
        }).as('customer-order-context-for-payments-123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table]').should('be.visible')

        cy.get('[data-context=payment-v2-operation-actions] [data-context=accept-btn]').should('not.exist')
        cy.get('[data-context=payment-v2-operation-actions] [data-context=cancel-btn]').should('not.exist')
        cy.get('[data-context=payment-v2-operation-actions] [data-context=refresh-btn]').should('not.exist')
        cy.get('[data-context=payment-v2-operation-actions] [data-context=refund-btn]').should('not.exist')
        cy.get('[data-context=payment-v2-operation-actions] [data-context=external-details-btn]').should('not.exist')
    })

    it('should show the payment mode', () => {
        cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderContextForPaymentsMock(123456),
                    operations: [
                        {
                            ...operationMock(123456, OPERATION_ID),
                            code: 'PRESTO',
                            label: 'Cetelem Presto (Paiement fractionn\u00e9)',
                            meta: {
                                payment_id: `${OPERATION_ID}_0`,
                                internal_label: 'PRESTO_10X',
                            },
                        },
                    ],
                },
            },
        }).as('customer-order-context-for-payments-123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table]').should('be.visible')

        cy.get('[data-context=erp-table]').as('table')
        cy.get('@table').find('tbody tr').eq(0).as('row')
        cy.get('@row').find('td').eq(0).get('[data-context=erp-table]').eq(0).should('contain', 'Presto 10x')
    })

    it('should have disabled actions and can only refresh', () => {
        cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderContextForPaymentsMock(123456),
                    operations: [
                        {
                            ...operationMock(123456, OPERATION_ID),
                            available_actions: ['ACCEPT', 'CANCEL', 'REFRESH', 'REFUND'],
                            refundable_amount: '45',
                        },
                    ],
                },
            },
        }).as('customer-order-context-for-payments-123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        testAllPaymentV2Actions(OPERATION_ID, '[data-context=customer-order-payments-block] [data-context=erp-table]')
        cy.get('[data-context=payment-v2-operation-actions] [data-context=external-details-btn]').should('not.exist')

        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table] [data-context=view-details-btn]')
            .should('be.visible')
            .click()

        // Check selected payment

        cy.get('[data-context=handle-payment-slide-in]').should('be.visible')

        cy.get('[data-context=handle-payment-slide-in]').within(() => {
            cy.get('[data-context=payment-v2-operation-actions] [data-context=accept-btn]').should('be.visible')
            cy.get('[data-context=payment-v2-operation-actions] [data-context=cancel-btn]').should('be.visible')
            cy.get('[data-context=payment-v2-operation-actions] [data-context=refresh-btn]').should('be.visible')
            cy.get('[data-context=payment-v2-operation-actions] [data-context=refund-btn]').should('be.visible')
        })

        // More tests for the slide in actions are done in another file...
    })

    it('should perform all available actions successfully', () => {
        cy.mockErpUser([
            CUSTOMER_ORDER_PAYMENT_ACCEPT,
            CUSTOMER_ORDER_PAYMENT_CANCEL,
            CUSTOMER_ORDER_PAYMENT_REFUND,
            CUSTOMER_ORDER_PAYMENT_REMIT_REQUEST,
        ])

        cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderContextForPaymentsMock(123456),
                    operations: [
                        {
                            ...operationMock(123456, OPERATION_ID),
                            available_actions: ['ACCEPT', 'CANCEL', 'REFRESH', 'REFUND'],
                            refundable_amount: '45',
                        },
                    ],
                },
            },
        }).as('customer-order-context-for-payments-123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        testAllPaymentV2Actions(
            OPERATION_ID,
            '[data-context=customer-order-payments-block] [data-context=erp-table]',
            SHOULD_NOT_BE_DISABLED,
        )

        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table] [data-context=view-details-btn]')
            .should('be.visible')
            .click()

        // Check selected payment

        cy.get('[data-context=handle-payment-slide-in]').should('be.visible')

        cy.get('[data-context=handle-payment-slide-in]').within(() => {
            cy.get('[data-context=payment-v2-operation-actions] [data-context=accept-btn]').should('be.visible')
            cy.get('[data-context=payment-v2-operation-actions] [data-context=cancel-btn]').should('be.visible')
            cy.get('[data-context=payment-v2-operation-actions] [data-context=refresh-btn]').should('be.visible')
            cy.get('[data-context=payment-v2-operation-actions] [data-context=refund-btn]').should('be.visible')
            cy.get('[data-context=payment-v2-operation-actions] [data-context=external-details-btn]').should(
                'not.exist',
            )
        })

        // More tests for the slide in actions are done in another file...
    })

    it('should perform external details actions successfully with super admin privileges', () => {
        cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderContextForPaymentsMock(123456),
                    operations: [
                        {
                            ...operationMock(123456, OPERATION_ID),
                            available_actions: [],
                            refundable_amount: '45',
                        },
                    ],
                },
            },
        }).as('customer-order-context-for-payments-123456')

        cy.mockErpUser([SUPER_ADMIN])

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        cy.intercept('GET', `**/api/erp-payment/v1/operation/${OPERATION_ID}/external-details`, {
            statusCode: 200,
            body: { data: { response: 'body' } },
        }).as('external-details')

        cy.get('[data-context=payment-v2-operation-actions] [data-context=external-details-btn]')
            .should('be.visible')
            .click()
        cy.wait('@external-details')
        cy.get('[data-context=payment-external-details-slide-in]').should('be.visible')
        cy.get('[data-context=slide-out-container-close-btn]:visible').click()
        cy.get('[data-context=payment-external-details-slide-in]').should('not.be.visible')
    })
})
