import { CUSTOMER_ORDER_PAYMENT_ADD, SUPER_ADMIN } from '../../../../../../src/apps/erp/permissions'
import {
    customerOrderContextForPaymentsMock,
    customerOrderErpV2Mock,
    customerOrderPaymentMock,
    operationMock,
} from '../../../../utils/erp-server-utils'

const OPERATION_ID = '123ea648-5726-4e39-880d-9d39f5eb1e8f'

describe('Customer order payment - Worldline Pay by link', () => {
    describe('No action possible', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            cy.intercept('GET', '**/api/erp/v1/anti-fraud/123456', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        meta: [],
                        statuses: {
                            customer_order_id: 123456,
                            anti_fraud_customer_order: null,
                            anti_fraud_customer_order_payments: [],
                        },
                    },
                },
            }).as('load-anti-fraud')
        })

        it('should test when the customer order is not flux is "not ongoing"', () => {
            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
                statusCode: 200,
                body: {
                    data: {
                        ...customerOrderErpV2Mock(123456),
                        status: 'CLOSED',
                    },
                },
            }).as('customer-order-legacy-123456')

            cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
                statusCode: 200,
                body: {
                    data: {
                        ...customerOrderContextForPaymentsMock(123456),
                    },
                },
            }).as('customer-order-context-for-payments-123456')

            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

            cy.wait([
                '@customer-order-legacy-123456',
                '@load-anti-fraud',
                '@customer-order-context-for-payments-123456',
            ])
            cy.get('[data-context="open-add-payment-slide-in"]').should('not.exist')
        })

        it('should disable the button when the customer order is "ongoing" but the user does not have the permission to use it', () => {
            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
                statusCode: 200,
                body: {
                    data: {
                        ...customerOrderErpV2Mock(123456),
                    },
                },
            }).as('customer-order-legacy-123456')

            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

            cy.get('[data-context="open-add-payment-slide-in"]').should('be.visible').should('be.disabled')
        })
    })

    describe('Slide-in workflow', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([CUSTOMER_ORDER_PAYMENT_ADD, SUPER_ADMIN])

            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
                statusCode: 200,
                body: {
                    data: {
                        ...customerOrderErpV2Mock(123456),
                        payments: [
                            {
                                ...customerOrderPaymentMock(),
                                operation_id: OPERATION_ID,
                            },
                        ],
                    },
                },
            }).as('customer-order-legacy-123456')

            cy.intercept('GET', '**/api/erp/v1/anti-fraud/123456', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        meta: [],
                        statuses: {
                            customer_order_id: 123456,
                            anti_fraud_customer_order: null,
                            anti_fraud_customer_order_payments: [],
                        },
                    },
                },
            }).as('load-anti-fraud')
        })

        function visit() {
            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

            cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud'])

            cy.get('[data-context="open-add-payment-slide-in"]').should('be.visible').should('not.be.disabled')
            cy.get('[data-context="open-add-payment-slide-in"]').click()
            cy.get('[data-context=worldline-add-pay-by-link-payment]').should('be.visible')
        }

        describe('When the form is displayed', () => {
            it('should show a message when the customer order is not eligible ', () => {
                cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: {
                            ...customerOrderContextForPaymentsMock(123456),
                            operations: [operationMock(123456, OPERATION_ID)],
                            remaining_balance: 0,
                            has_remaining_balance: false,
                        },
                    },
                }).as('load')

                visit()
                cy.wait('@load')

                cy.get('[data-context=alert-info]').should(
                    'contain',
                    "La commande n'est pas éligible à l'ajout d'un nouveau paiement via PayByLink",
                )
            })

            it('should handle server side validation', () => {
                cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: {
                            ...customerOrderContextForPaymentsMock(123456),
                            operations: [operationMock(123456, OPERATION_ID)],
                            remaining_balance: '123.45',
                            has_remaining_balance: true,
                        },
                    },
                }).as('load')

                visit()
                cy.wait('@load')

                cy.intercept('POST', '**/api/erp-payment/v1/payment/pay-by-link', {
                    statusCode: 500,
                }).as('submit')

                cy.get(
                    '[data-context=worldline-add-pay-by-link-payment] [data-context=pay-by-link-form] [data-context=erp-input-number]',
                ).type(1)
                cy.get(
                    '[data-context=worldline-add-pay-by-link-payment] [data-context=pay-by-link-form] [data-context=days-of-validity]',
                )
                    .find('[data-context=erp-multiselect]')
                    .erpMultiselect('', '2 jours')
                cy.get(
                    '[data-context=worldline-add-pay-by-link-payment] [data-context=pay-by-link-form] button[type=submit]',
                ).click()

                cy.wait('@submit')

                cy.get('[data-context=erp-input-helper][data-invalid]').should('have.length', 1)

                cy.get('label:contains(Montant du paiement)')
                    .siblings('[data-context=erp-input-helper]')
                    .should('contain', 'Une erreur est survenue, veuillez réessayer')

                // Validation errors
                // Test only one match, the rest use the same mechanism

                cy.intercept('POST', '**/api/erp-payment/v1/payment/pay-by-link', {
                    statusCode: 400,
                    body: {
                        data: {
                            amount: 'PBL:INVALID_AMOUNT',
                        },
                    },
                }).as('submit')

                cy.get(
                    '[data-context=worldline-add-pay-by-link-payment] [data-context=pay-by-link-form] [data-context=erp-input-number]',
                ).type(1)
                cy.get(
                    '[data-context=worldline-add-pay-by-link-payment] [data-context=pay-by-link-form] [data-context=days-of-validity]',
                )
                    .find('[data-context=erp-multiselect]')
                    .erpMultiselect('', '2 jours')
                cy.get(
                    '[data-context=worldline-add-pay-by-link-payment] [data-context=pay-by-link-form] button[type=submit]',
                ).click()

                cy.wait('@submit')

                cy.get('[data-context=erp-input-helper][data-invalid]').should('have.length', 1)

                cy.get('label:contains(Montant du paiement)')
                    .siblings('[data-context=erp-input-helper]')
                    .should('contain', 'Montant invalide')
            })

            it('Complete workflow successfully', () => {
                cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: {
                            ...customerOrderContextForPaymentsMock(123456),
                            operations: [operationMock(123456, OPERATION_ID)],
                            remaining_balance: 123.45,
                            has_remaining_balance: true,
                        },
                    },
                }).as('load')

                cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456', {
                    onBeforeLoad(win) {
                        cy.stub(win, 'open').as('windowOpen')
                    },
                })

                cy.get('[data-context="open-add-payment-slide-in"]').should('be.visible').should('not.be.disabled')
                cy.get('[data-context="open-add-payment-slide-in"]').click()

                cy.intercept('POST', '**/api/erp-payment/v1/payment/pay-by-link', {
                    statusCode: 200,
                    body: {
                        data: {
                            url: 'https://dummy.url',
                            expiration_date: 'A DATE',
                        },
                    },
                }).as('submit')

                cy.get('[data-context=worldline-add-pay-by-link-payment]').within(() => {
                    cy.root().should('be.visible')

                    cy.wait('@load')

                    cy.get('[data-context=pay-by-link-form]').within(() => {
                        cy.get('[data-context=days-of-validity] [data-context=erp-multiselect]').erpMultiselect(
                            '',
                            '4 jours',
                        )
                        cy.get('[data-context=payment-methods] [data-context=erp-toggle]')
                            .should('be.visible')
                            .should('have.attr', 'data-disabled')
                        cy.get('[data-context=payment-methods] span:contains(Floa)').tooltip(
                            'Disponible uniquement pour un financement intégral de la commande',
                        )
                        cy.get('button[type=submit]').click()
                    })

                    // submit succeeds, the underlying iframe should be reloaded
                    cy.wait('@submit').then((xhr) => {
                        expect(xhr.request.body.amount).to.eq(123.45)
                        expect(xhr.request.body.validity).to.eq(4)
                        expect(xhr.request.body.payment_method_codes).to.deep.eq(['CBS-O', 'AMX-O', 'WBCTC'])
                    })

                    cy.window().then((win) => {
                        cy.stub(win, 'prompt') // for copy-to-clipboard which opens a prompt when running cypress...
                    })

                    cy.get('[data-context=pay-by-link-form]').should('not.exist')

                    cy.get('[data-context="preview-url"]').should('be.visible').should('contain', 'https://dummy.url')

                    cy.get('[data-context="preview-url"]').click()
                    cy.toast(`Le lien à été copié dans le presse-papier`, 'default')
                    cy.get('[data-context=toast-message]').should('not.exist')

                    cy.get('[data-context="open-in-new-tab-btn"]').click()
                    cy.get('@windowOpen').should('be.calledWithMatch', /https:\/\/dummy.url/, '_blank')

                    cy.get('[data-context="copy-to-clipboard-btn"]').click()
                    cy.toast(`Le lien à été copié dans le presse-papier`, 'default')
                    cy.get('[data-context=toast-message]').should('not.exist')

                    cy.get('[data-context=worldline-send-payment-link-by-email]').should('not.exist')
                    cy.get('[data-context="close-btn"]').click()

                    cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@load'])
                    cy.get('[data-context=worldline-add-pay-by-link-payment]').should('not.exist')
                })
            })

            it('Complete workflow successfully with floa and bancontact', () => {
                cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: {
                            ...customerOrderContextForPaymentsMock(123456),
                            remaining_balance: '100.00',
                            has_remaining_balance: true,
                        },
                    },
                }).as('load')

                cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456', {
                    onBeforeLoad(win) {
                        cy.stub(win, 'open').as('windowOpen')
                    },
                })

                cy.get('[data-context="open-add-payment-slide-in"]')
                    .should('be.visible')
                    .should('not.have.attr', 'data-disabled')
                cy.get('[data-context="open-add-payment-slide-in"]').click()

                cy.intercept('POST', '**/api/erp-payment/v1/payment/pay-by-link', {
                    statusCode: 200,
                    body: {
                        data: {
                            url: 'https://dummy.url',
                            expiration_date: 'A DATE',
                        },
                    },
                }).as('submit')

                cy.get('[data-context=worldline-add-pay-by-link-payment]').within(() => {
                    cy.root().should('be.visible')

                    cy.wait('@load')

                    cy.get('[data-context=pay-by-link-form]').within(() => {
                        cy.get('[data-context=payment-methods] [data-context=erp-toggle]')
                            .should('be.visible')
                            .should('not.have.attr', 'data-disabled')
                        cy.get('[data-context=amount] input').type('1')
                        cy.get('[data-context=payment-methods] [data-context=erp-toggle]').should(
                            'have.attr',
                            'data-disabled',
                        )
                        cy.get('[data-context=amount] input').clear()
                        cy.get('[data-context=amount] input').type('100')
                        cy.get('[data-context=payment-methods] [data-context=erp-toggle]').should(
                            'not.have.attr',
                            'data-disabled',
                        )
                        cy.get('[data-context=payment-methods] [data-context=erp-toggle]').click()
                        cy.get('button[type=submit]').click()
                    })

                    // submit succeeds, the underlying iframe should be reloaded
                    cy.wait('@submit').then((xhr) => {
                        expect(xhr.request.body.amount).to.eq(100.0)
                        expect(xhr.request.body.validity).to.eq(1)
                        expect(xhr.request.body.payment_method_codes).to.deep.eq(['CBS-O', 'AMX-O', 'WBCTC', 'FLOA'])
                    })

                    cy.window().then((win) => {
                        cy.stub(win, 'prompt') // for copy-to-clipboard which opens a prompt when running cypress...
                    })

                    cy.get('[data-context=pay-by-link-form]').should('not.exist')

                    cy.get('[data-context="preview-url"]').should('be.visible')
                })
            })
        })
    })
})
