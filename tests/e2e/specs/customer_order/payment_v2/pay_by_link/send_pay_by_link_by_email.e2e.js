import { CUSTOMER_ORDER_PAYMENT_ADD } from '../../../../../../src/apps/erp/permissions'
import {
    customerOrderContextForPaymentsMock,
    customerOrderErpV2Mock,
    operationMock,
} from '../../../../utils/erp-server-utils'

const OPERATION_ID = '123ea648-5726-4e39-880d-9d39f5eb1e8f'

describe('Customer order payment - Worldline send payment link by email', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([CUSTOMER_ORDER_PAYMENT_ADD])

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456),
                },
            },
        }).as('customer-order-legacy-123456')

        cy.intercept('GET', '**/api/erp/v1/anti-fraud/123456', {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    meta: [],
                    statuses: {
                        customer_order_id: 123456,
                        anti_fraud_customer_order: null,
                        anti_fraud_customer_order_payments: [],
                    },
                },
            },
        }).as('load-anti-fraud')

        cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderContextForPaymentsMock(123456),
                    operations: [operationMock(123456, OPERATION_ID)],
                    remaining_balance: '123.45',
                    has_remaining_balance: true,
                },
            },
        }).as('customer-order-context-for-payments-123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        cy.get('[data-context="open-add-payment-slide-in"]').should('be.visible').should('not.be.disabled')
        cy.get('[data-context="open-add-payment-slide-in"]').click()
        cy.get('[data-context=worldline-add-pay-by-link-payment]').should('be.visible')

        cy.intercept('POST', '**/api/erp-payment/v1/payment/pay-by-link', {
            statusCode: 200,
            body: {
                data: {
                    url: 'https://dummy.url',
                    expiration_date: '2000/01/01 01:01:01',
                },
            },
        }).as('submit')

        cy.get(
            '[data-context=worldline-add-pay-by-link-payment] [data-context=pay-by-link-form] button[type=submit]',
        ).click()

        cy.wait('@submit')

        cy.get('[data-context=worldline-add-pay-by-link-payment] [data-context=send-by-email-btn]').click()
        cy.get('[data-context=worldline-send-payment-link-by-email]').should('be.visible')
    })

    it('Can be closed', () => {
        cy.get('[data-context=worldline-send-payment-link-by-email] [data-context=close-btn]').click()
        cy.get('[data-context=worldline-send-payment-link-by-email]').should('not.exist')
    })

    it('Display a toast on server error', () => {
        cy.get('erp-input-helper').should('not.exist')
        cy.intercept('POST', '**/api/erp/v1/send-mailing', {
            statusCode: 500,
        }).as('send-mailing')

        cy.get('[data-context=worldline-send-payment-link-by-email] button[type=submit]').click()
        cy.wait('@send-mailing')

        cy.toast('Une erreur est survenue', 'danger')
        cy.get('[data-context=worldline-send-payment-link-by-email]').should('be.visible')
    })

    it('Display validation errors', () => {
        cy.get('[data-context=worldline-send-payment-link-by-email]').within(() => {
            cy.get('erp-input-helper').should('not.exist')
            cy.get('label:contains(Destinataire)')
                .siblings('input')
                .within(() => {
                    cy.root().clear()
                    cy.root().type("mais c'est pas un email !")
                })
            cy.get('label:contains(Message)').siblings('textarea').clear()

            cy.get('button[type=submit]').click()

            cy.get('label:contains(Destinataire)')
                .siblings('[data-context=erp-input-helper]')
                .should('be.visible')
                .should('contain.text', "Le format de l'e-mail est incorrect")
            cy.get('label:contains(Destinataire)').siblings('input').clear()
            cy.get('label:contains(Destinataire)')
                .siblings('[data-context=erp-input-helper]')
                .should('be.visible')
                .should('contain.text', 'Ce champ ne peut être vide')
            cy.get('label:contains(Message)')
                .siblings('[data-context=erp-input-helper]')
                .should('be.visible')
                .should('contain.text', 'Ce champ ne peut être vide')

            cy.get('label:contains(Destinataire)')
                .siblings('input')
                .within(() => {
                    cy.root().clear()
                    cy.root().type('<EMAIL>')
                })
            cy.get('label:contains(Message)')
                .siblings('textarea')
                .within(() => {
                    cy.root().clear()
                    cy.root().type('Quelque-chose')
                })
            cy.get('erp-input-helper').should('not.exist')
        })
    })

    it('Complete workflow successfully', () => {
        cy.get('[data-context=worldline-send-payment-link-by-email]').within(() => {
            cy.get('erp-input-helper').should('not.exist')
            cy.get('label:contains(Destinataire)')
                .siblings('input')
                .within(() => {
                    cy.root().should('have.value', '<EMAIL>')
                    cy.root().clear()
                    cy.root().type('<EMAIL>')
                })
            cy.get('label:contains(Message)')
                .siblings('textarea')
                .within(() => {
                    cy.root().should(
                        'have.value',
                        'Nous vous remercions  d’avoir passé votre commande numéro 123456 chez Son-video.com\n' +
                            "Pour finaliser votre achat, nous vous invitons à effectuer votre paiement en cliquant sur le bouton ci-dessous (ce lien est valable jusqu'au 01/01/2000 à 01:01) :",
                    )
                    cy.root().clear()
                    cy.root().type('On a dit de cliquer !')
                })

            cy.intercept('POST', '**/api/erp/v1/send-mailing', {
                statusCode: 200,
                body: {
                    data: {
                        status: 'success',
                        data: {},
                    },
                },
            }).as('send-mailing')

            cy.get('button[type=submit]').click()

            cy.wait('@send-mailing').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    key: 'payment_link',
                    data: {
                        to: '<EMAIL>',
                        from: { name: 'Son-Video', email: '<EMAIL>' },
                        context: {
                            payment_link: 'https://dummy.url',
                            subject: 'Votre lien de paiement',
                            content: 'On a dit de cliquer !',
                            customer_order_id: 123456,
                        },
                        _rel: { customer: 970481, customer_order: 123456 },
                        _sent_by: 120,
                    },
                })
            })

            cy.toast('Un email a été envoyé au client', 'success')
            cy.root().should('not.exist')
        })
    })
})
