import { customerOrderErpV2Mock } from '../../../utils/erp-server-utils'

const emitReloadEvent = () => {
    cy.getWrapper().then((wrapper) => {
        wrapper.$emit('receiveMessage', {
            action: 'content-fully-loaded',
        })
    })
}

describe('Customer Order - Store pickup worflow', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/customer-order/3023614/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(3023614),
                    carrier_id: 53,
                    shipment_method_id: 72,
                    pickup_store_id: 21,
                    delivery_note_ids: [4536912, 4536913],
                },
            },
        }).as('api_customer_order_3023614')

        cy.intercept('GET', '**/api/erp/v1/delivery-note/4536912', {
            fixture: 'erp/delivery_note/delivery_note_4536912.json',
        }).as('api_delivery_note_4536912')
        cy.intercept('GET', '**/api/erp/v1/delivery-note/4536913', {
            fixture: 'erp/delivery_note/delivery_note_4536913.json',
        }).as('api_delivery_note_4536913')
    })

    it('display a button to begin store pickup if conditions are met', function () {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=3023614')

        cy.closeAllToasts()

        emitReloadEvent()
        cy.wait('@api_customer_order_3023614')
        cy.wait('@api_delivery_note_4536912')
        cy.wait('@api_delivery_note_4536913')

        // button displayed
        cy.get('[data-context=page-header] [data-context=collect-products-btn]')
            .should('be.visible')
            .should('contain', 'Retirer les produits')
    })

    it('show expected popin on button click', function () {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=3023614')

        cy.closeAllToasts()

        emitReloadEvent()
        cy.wait('@api_customer_order_3023614')
        cy.wait('@api_delivery_note_4536912')
        cy.wait('@api_delivery_note_4536913')

        cy.get('[data-context=collect-products-btn]').as('collect_btn')
        cy.get('@collect_btn').click()

        // form displayed
        cy.get('[data-context=slide-out-container] [data-context=begin-store-pickup-form]')
            .should('be.visible')
            .as('form')

        // form header
        cy.get('@form').find('[data-context=page-header]').should('contain', 'Retirer les produits')

        // delivery note selector
        cy.get('@form').find('[data-context=delivery-note]').should('have.length', 1).eq(0).as('delivery_note')
        // - display a toggle, selected by default
        cy.get('@delivery_note').find('[data-context=erp-toggle] button').should('have.attr', 'aria-checked')
        // - display its number
        cy.get('@delivery_note').should('contain', '4536912')
        // - display its products
        cy.get('@delivery_note').should('contain', '1 × KEFQ350NR')
        cy.get('@delivery_note').should('contain', "Paire d'enceintes")
        cy.get('@delivery_note').should('contain', '25 × NORSTCL2501M')
        cy.get('@delivery_note').should('contain', "Câble d'enceintes")

        // toggle to open pdfs if selected, not selected by default
        cy.get('@form')
            .find('[data-context=print-selector]')
            .should('be.visible')
            .find('[data-context=erp-toggle] button')
            .should('not.have.attr', 'aria-checked')

        // submit btn
        cy.get('@form')
            .find('[data-context=submit-btn]')
            .should('be.visible')
            .should('not.be.disabled')
            .should('contain', 'Valider')
    })

    it('can submit only selected delivery notes', function () {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=3023614')

        cy.closeAllToasts()

        emitReloadEvent()
        cy.wait('@api_customer_order_3023614')
        cy.wait('@api_delivery_note_4536912')
        cy.wait('@api_delivery_note_4536913')

        cy.get('[data-context=collect-products-btn]').click()
        cy.get('[data-context=slide-out-container] [data-context=begin-store-pickup-form]')
            .should('be.visible')
            .as('form')

        // submit btn
        cy.get('@form').find('[data-context=submit-btn]').should('not.be.disabled')

        // unselect
        cy.get('@form').find('[data-context=delivery-note] [data-context=erp-toggle] button').click()

        // is now disabled
        cy.get('@form').find('[data-context=submit-btn]').should('be.disabled')
    })

    it('handle submission of selected delivery note', function () {
        cy.intercept('PUT', '**/api/erp/v1/delivery-note/4536912/start-store-pickup', {
            fixture: 'erp/delivery_note/start-store-pickup-4536912.json',
        }).as('api_customer_order_3023614')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=3023614', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })

        cy.closeAllToasts()

        emitReloadEvent()
        cy.wait('@api_customer_order_3023614')
        cy.wait('@api_delivery_note_4536912')
        cy.wait('@api_delivery_note_4536913')

        cy.get('[data-context=collect-products-btn]').click()
        cy.get('[data-context=slide-out-container] [data-context=begin-store-pickup-form]')
            .should('be.visible')
            .as('form')

        // toggle pdf open
        cy.get('@form').find('[data-context=print-selector] [data-context=erp-toggle] button').click()

        // submit form
        cy.get('@form').find('[data-context=submit-btn]').click()

        // pdf is open in another tab
        cy.get('@windowOpen').should(
            'be.calledWith',
            'http://legacy.lxc/pdf/showDocument?type=bon-livraison&id=4536912',
            '_blank',
        )

        // delivery notes are reloaded
        cy.wait('@api_delivery_note_4536912')
        cy.wait('@api_delivery_note_4536913')

        // toast success
        cy.toast(`Mise à jour effectuée`, 'success')
    })
})
