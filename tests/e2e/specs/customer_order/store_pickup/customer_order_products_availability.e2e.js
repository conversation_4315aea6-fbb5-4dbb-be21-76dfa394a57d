import { customerOrderErpV2Mock } from '../../../utils/erp-server-utils'
import { NON_BREAKING_SPACE } from '../../../utils/text-utils'

describe('Customer order products availability', () => {
    const visit_page = (file) => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: customerOrderErpV2Mock(123456),
            },
        }).as('customer_order')

        cy.intercept('POST', '**/api/erp/v1/customer-order-products', { fixture: file }).as(
            'cpost_customer_order_products',
        )

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait('@customer_order')
        cy.emitEvent('open-products-availability', 123456)

        cy.wait('@cpost_customer_order_products')

        cy.closeAllToasts()
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Show availability on a customer order with store pickup in side panel', () => {
        it('displays all products availability for a VALID customer order', () => {
            visit_page('erp/customer-order-products/customer_order_products_availability_for_store_pickup.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .find('[data-context=page-header] [data-context=badge]')
                .should('contain', 'Non livrable')

            // content product with supplier order (2011)
            cy.get('@panel').find('[data-context=table-row]').should('have.length', 11).eq(0).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ONSAITPAS ')
                .should('have.attr', 'href', '/articles/ONSAITPAS/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Avec date inconnue')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Délai inconnu')
            // supplier order
            cy.get('@product')
                .find('[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=scope]')
                .should('contain', 'cmd frn')
            cy.get('@product')
                .find(
                    '[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=content] [data-context=erp-link]',
                )
                .should('contain', '123456')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456',
                )
            // link to other customer orders for this product
            cy.get('[data-context=more-customer-orders] [data-context=erp-link]')
                .eq(0)
                .should('contain', 'Voir autres commandes en attente')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/commande/recherche?filter=sku_attente&value=ONSAITPAS',
                )

            // content product with unknown availability status
            cy.get('@panel').find('[data-context=table-row]').eq(1).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ONSAITVRAIMENTPAS')
                .should('have.attr', 'href', '/articles/ONSAITVRAIMENTPAS/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Date et cmd frn inconnues')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Délai inconnu')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product with replenishment
            cy.get('@panel').find('[data-context=table-row]').eq(2).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENREAPPRO ')
                .should('have.attr', 'href', '/articles/ENREAPPRO/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit en réappro')
            cy.get('@product')
                .find('[data-context=cell-availability]')
                .should('contain', 'En cours de réapprovisionnement')
            cy.get('@product')
                .find('[data-context=cell-expected-at] [data-context=content]')
                .should('contain', '25/12/2072')

            // content product available in another store
            cy.get('@panel').find('[data-context=table-row]').eq(3).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENDISPOAUTREMAG ')
                .should('have.attr', 'href', '/articles/ENDISPOAUTREMAG/')
            cy.get('@article_item')
                .find('[data-context=name]')
                .should('contain', 'Produit en stock dans un autre magasin')
            cy.get('@product')
                .find('[data-context=cell-availability]')
                .should('contain', 'Disponible dans un autre magasin')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product available in Champigny and transfer in progress
            cy.get('@panel').find('[data-context=table-row]').eq(4).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'CESTPARTI')
                .should('have.attr', 'href', '/articles/CESTPARTI/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Emport dépôt avec transfert')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'En transfert')
            // transfer
            cy.get('@product')
                .find('[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=scope]')
                .should('contain', 'trf')
            cy.get('@product')
                .find(
                    '[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=content] [data-context=erp-link]',
                )
                .should('contain', '456365')
                .should('have.attr', 'href', 'http://erp-client.lxc/legacy/stock/bonTransfertEdit?id=456365')

            // content product available in Champigny and transfer in progress - with no stock left
            cy.get('@panel').find('[data-context=table-row]').eq(5).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENTRANSFERTPLUSDESTOCK')
                .should('have.attr', 'href', '/articles/ENTRANSFERTPLUSDESTOCK/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit en transfert - stock à 0')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'En transfert')
            // transfer
            cy.get('@product')
                .find('[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=scope]')
                .should('contain', 'trf')
            cy.get('@product')
                .find(
                    '[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=content] [data-context=erp-link]',
                )
                .should('contain', '456365')
                .should('have.attr', 'href', 'http://erp-client.lxc/legacy/stock/bonTransfertEdit?id=456365')

            // content product available in Champigny but no transfer yet
            cy.get('@panel').find('[data-context=table-row]').eq(6).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ALENTREPOT')
                .should('have.attr', 'href', '/articles/ALENTREPOT/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Emport dépôt sans transfert')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Disponible')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product available in Champigny but transfer not sent yet
            cy.get('@panel').find('[data-context=table-row]').eq(7).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'CESTPRESQUEPARTI')
                .should('have.attr', 'href', '/articles/CESTPRESQUEPARTI/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Emport dépôt non expédié')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Disponible')
            // transfer
            cy.get('@product')
                .find('[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=scope]')
                .should('contain', 'trf')
            cy.get('@product')
                .find(
                    '[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=content] [data-context=erp-link]',
                )
                .should('contain', '456365')
                .should('have.attr', 'href', 'http://erp-client.lxc/legacy/stock/bonTransfertEdit?id=456365')

            // content available product
            cy.get('@panel').find('[data-context=table-row]').eq(8).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENDISPOMAG')
                .should('have.attr', 'href', '/articles/ENDISPOMAG/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit en stock en magasin')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Disponible')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product already delivered
            cy.get('@panel').find('[data-context=table-row]').eq(9).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'DEJARETIRE')
                .should('have.attr', 'href', '/articles/DEJARETIRE/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit retiré')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Retiré')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product already delivered
            cy.get('@panel').find('[data-context=table-row]').eq(10).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'DEJARETIREAPRESTRANSFERT')
                .should('have.attr', 'href', '/articles/DEJARETIREAPRESTRANSFERT/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit retiré')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Retiré')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)
        })

        it('displays all products availability for an INVALID customer order', () => {
            visit_page('erp/customer-order-products/invalid_customer_order_products_availability_for_store_pickup.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .find('[data-context=page-header] [data-context=badge]')
                .should('contain', 'Non livrable')

            // content product with supplier order (2011)
            cy.get('@panel').find('[data-context=table-row]').should('have.length', 8).eq(0).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ONSAITPAS ')
                .should('have.attr', 'href', '/articles/ONSAITPAS/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Avec date inconnue')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Délai inconnu')
            // supplier order
            cy.get('@product')
                .find('[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=scope]')
                .should('contain', 'cmd frn')
            cy.get('@product')
                .find(
                    '[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=content] [data-context=erp-link]',
                )
                .should('contain', '123456')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456',
                )
            // link to other customer orders for this product
            cy.get('[data-context=more-customer-orders] [data-context=erp-link]')
                .eq(0)
                .should('contain', 'Voir autres commandes en attente')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/commande/recherche?filter=sku_attente&value=ONSAITPAS',
                )

            // content product with unknown availability status
            cy.get('@panel').find('[data-context=table-row]').eq(1).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ONSAITVRAIMENTPAS')
                .should('have.attr', 'href', '/articles/ONSAITVRAIMENTPAS/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Date et cmd frn inconnues')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Délai inconnu')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product with replenishment
            cy.get('@panel').find('[data-context=table-row]').eq(2).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENREAPPRO ')
                .should('have.attr', 'href', '/articles/ENREAPPRO/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit en réappro')
            cy.get('@product')
                .find('[data-context=cell-availability]')
                .should('contain', 'En cours de réapprovisionnement')
            cy.get('@product')
                .find('[data-context=cell-expected-at]  [data-context=content]')
                .should('contain', '25/12/2072')

            // content product available in Champigny but no transfer yet
            cy.get('@panel').find('[data-context=table-row]').eq(3).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ALENTREPOT')
                .should('have.attr', 'href', '/articles/ALENTREPOT/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Emport dépôt sans transfert')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Réservable')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product available in another store
            cy.get('@panel').find('[data-context=table-row]').eq(4).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENDISPOAUTREMAG ')
                .should('have.attr', 'href', '/articles/ENDISPOAUTREMAG/')
            cy.get('@article_item')
                .find('[data-context=name]')
                .should('contain', 'Produit en stock dans un autre magasin')
            cy.get('@product')
                .find('[data-context=cell-availability]')
                .should('contain', 'Réservable dans un autre magasin')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content available product
            cy.get('@panel').find('[data-context=table-row]').eq(5).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENDISPOMAG')
                .should('have.attr', 'href', '/articles/ENDISPOMAG/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit en stock en magasin')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Réservable')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product already delivered
            cy.get('@panel').find('[data-context=table-row]').eq(6).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'DEJARETIRE')
                .should('have.attr', 'href', '/articles/DEJARETIRE/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit retiré')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Retiré')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product already delivered
            cy.get('@panel').find('[data-context=table-row]').eq(7).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'DEJARETIREAPRESTRANSFERT')
                .should('have.attr', 'href', '/articles/DEJARETIREAPRESTRANSFERT/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit retiré')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Retiré')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)
        })
    })
})
