import { NON_BREAKING_SPACE } from '../../utils/text-utils'

describe('Customer order products availability', () => {
    const VISIT_PAGE = (file) => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {},
            },
        }).as('customer_order')

        cy.intercept('POST', '**/api/erp/v1/customer-order-products', { fixture: file }).as(
            'cpost_customer_order_products',
        )

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.emitEvent('open-products-availability', 123456)
        cy.wait('@customer_order', 123456)
        cy.wait('@cpost_customer_order_products')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Show availability on a customer order with delivery in side panel', () => {
        it('displays all products availability for a VALID customer order', () => {
            VISIT_PAGE('erp/customer-order-products/customer_order_products_availability.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .find('[data-context=page-header] [data-context=badge]')
                .should('contain', 'Non livrable')

            cy.closeAllToasts()

            // Check table header
            cy.get('@panel').find('[data-context=erp-table]').as('table')
            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 4)
            cy.get('@header').eq(0).should('contain', 'Produit')
            cy.get('@header').eq(1).should('contain', 'Qté')
            cy.get('@header').eq(2).should('contain', 'Dispo')
            cy.get('@header').eq(3).should('contain', 'Livraisons prévues')

            // content product with supplier order (2011)
            cy.get('@panel').find('[data-context=table-row]').should('have.length', 6).eq(0).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ONSAITPAS ')
                .should('have.attr', 'href', '/articles/ONSAITPAS/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Avec date inconnue')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Délai inconnu')
            // supplier order
            cy.get('@product')
                .find('[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=scope]')
                .should('contain', 'cmd frn')
            cy.get('@product')
                .find(
                    '[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=content] [data-context=erp-link]',
                )
                .should('contain', '123456')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456',
                )
            // link to other customer orders for this product
            cy.get('[data-context=more-customer-orders] [data-context=erp-link]')
                .eq(0)
                .should('contain', 'Voir autres commandes en attente')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/commande/recherche?filter=sku_attente&value=ONSAITPAS',
                )

            // content product with unknown availability status
            cy.get('@panel').find('[data-context=table-row]').eq(1).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ONSAITVRAIMENTPAS ')
                .should('have.attr', 'href', '/articles/ONSAITVRAIMENTPAS/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Date et cmd frn inconnues')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Délai inconnu')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product with replenishment
            cy.get('@panel').find('[data-context=table-row]').eq(2).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENREAPPRO ')
                .should('have.attr', 'href', '/articles/ENREAPPRO/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit en réappro')
            cy.get('@product')
                .find('[data-context=cell-availability]')
                .should('contain', 'En cours de réapprovisionnement')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', '25/12/2072')

            // content available product in a store
            cy.get('@panel').find('[data-context=table-row]').eq(3).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENDISPOMAG ')
                .should('have.attr', 'href', '/articles/ENDISPOMAG/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit dispo dans un magasin')
            cy.get('@product').find('[data-context=cell-quantity]').should('contain', '1')
            cy.get('@product')
                .find('[data-context=cell-availability]')
                .should('contain', 'Indisponible entrepôts / Disponible dans un autre magasin')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content available product
            cy.get('@panel').find('[data-context=table-row]').eq(4).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENDISPO ')
                .should('have.attr', 'href', '/articles/ENDISPO/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit dispo')
            cy.get('@product').find('[data-context=cell-quantity]').should('contain', '1')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Disponible')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product already delivered
            cy.get('@panel').find('[data-context=table-row]').eq(5).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'DEJAEXPEDIE ')
                .should('have.attr', 'href', '/articles/DEJAEXPEDIE/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit expédié')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Expédié')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)
        })

        it('displays all products availability for an INVALID customer order', () => {
            VISIT_PAGE('erp/customer-order-products/invalid_customer_order_products_availability.json')

            cy.get('[data-context=slide-out-container] [data-context=products-availability]')
                .as('panel')
                .find('[data-context=page-header] [data-context=badge]')
                .should('contain', 'Non livrable')

            cy.closeAllToasts()

            // Check table header
            cy.get('@panel').find('[data-context=erp-table]').as('table')
            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 4)
            cy.get('@header').eq(0).should('contain', 'Produit')
            cy.get('@header').eq(1).should('contain', 'Qté')
            cy.get('@header').eq(2).should('contain', 'Dispo')
            cy.get('@header').eq(3).should('contain', 'Livraisons prévues')

            // content product with supplier order (2011)
            cy.get('@panel').find('[data-context=table-row]').should('have.length', 6).eq(0).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ONSAITPAS ')
                .should('have.attr', 'href', '/articles/ONSAITPAS/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Avec date inconnue')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Délai inconnu')
            // supplier order
            cy.get('@product')
                .find('[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=scope]')
                .should('contain', 'cmd frn')
            cy.get('@product')
                .find(
                    '[data-context=cell-expected-at] [data-context=scoped-badge] [data-context=content] [data-context=erp-link]',
                )
                .should('contain', '123456')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456',
                )
            // link to other customer orders for this product
            cy.get('[data-context=more-customer-orders] [data-context=erp-link]')
                .eq(0)
                .should('contain', 'Voir autres commandes en attente')
                .should(
                    'have.attr',
                    'href',
                    'http://erp-client.lxc/legacy/commande/recherche?filter=sku_attente&value=ONSAITPAS',
                )

            // content product with unknown availability status
            cy.get('@panel').find('[data-context=table-row]').eq(1).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ONSAITVRAIMENTPAS ')
                .should('have.attr', 'href', '/articles/ONSAITVRAIMENTPAS/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Date et cmd frn inconnues')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Délai inconnu')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product with replenishment
            cy.get('@panel').find('[data-context=table-row]').eq(2).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENREAPPRO ')
                .should('have.attr', 'href', '/articles/ENREAPPRO/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit en réappro')
            cy.get('@product')
                .find('[data-context=cell-availability]')
                .should('contain', 'En cours de réapprovisionnement')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', '25/12/2072')

            // content available product in a store
            cy.get('@panel').find('[data-context=table-row]').eq(3).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENDISPOMAG ')
                .should('have.attr', 'href', '/articles/ENDISPOMAG/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit dispo dans un magasin')
            cy.get('@product').find('[data-context=cell-quantity]').should('contain', '1')
            cy.get('@product')
                .find('[data-context=cell-availability]')
                .should('contain', 'Indisponible entrepôts / Réservable dans un magasin')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content available product
            cy.get('@panel').find('[data-context=table-row]').eq(4).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'ENDISPO ')
                .should('have.attr', 'href', '/articles/ENDISPO/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit dispo')
            cy.get('@product').find('[data-context=cell-quantity]').should('contain', '1')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Réservable')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)

            // content product already delivered
            cy.get('@panel').find('[data-context=table-row]').eq(5).as('product')
            cy.get('@product')
                .find('[data-context=cell-article]')
                .as('article_item')
                .find('[data-context=image]')
                .should('have.attr', 'src')
                .should('include', '/images/article/jamo/JAMOS810SUBNR/s810-sub-bois-noir_5a213d64dc592_300_square.jpg')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', 'DEJAEXPEDIE ')
                .should('have.attr', 'href', '/articles/DEJAEXPEDIE/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Produit expédié')
            cy.get('@product').find('[data-context=cell-availability]').should('contain', 'Expédié')
            cy.get('@product').find('[data-context=cell-expected-at]').should('contain', NON_BREAKING_SPACE)
        })
    })
})
