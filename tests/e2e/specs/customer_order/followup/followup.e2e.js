describe('Customer order followup', function () {
    const PAGE = '/customer-order/followup'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/v1/customer-order/sources', {
            fixture: 'erp/accounting/payment/sources.json',
        }).as('sources')

        cy.intercept('POST', '**/api/erp/v1/customer-orders/followup', {
            fixture: 'erp/customer-order/followup/orders.json',
        }).as('orders')
    })

    const selectRow = (idx) => {
        cy.get('[data-context=orders]').as('order_list')
        return cy.get('@order_list').find('tbody tr').eq(idx).as('row')
    }

    const selectCell = (idx) => {
        return cy.get('@row').find('td').eq(idx).as('cell')
    }

    const visitPageAndLoadOrders = (qs = '') => {
        cy.visit(PAGE + qs)
        cy.toggleMenu()

        if (qs !== '') {
            cy.wait(['@sources'])
        } else {
            cy.wait(['@sources', '@orders'])
        }

        cy.get('[data-context=dashboard-filters]').should('be.visible')
    }

    describe('Page setup', function () {
        it('check filters', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait(['@sources', '@orders'])

            cy.get('[data-context=dashboard-filters]').as('filters')

            cy.get('@filters').find('[data-context=filter-group-source]').should('exist')
            cy.get('@filters').find('[data-context=filter-source]').should('exist')
            cy.get('@filters').find('[data-context=filter-status]').should('exist')
            cy.get('@filters').find('[data-context=filter-period]').should('exist')
        })

        it('check table columns', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait(['@sources', '@orders'])

            // table header
            cy.get('[data-context=orders]').as('order_list')
            cy.get('@order_list').find('thead tr th').as('orders_table_header')

            cy.get('@orders_table_header').should('have.length', 9)

            let i = 0
            cy.get('@orders_table_header').eq(i++).should('contain', 'N° commande')
            cy.get('@orders_table_header').eq(i++).should('contain', 'N° origine')
            cy.get('@orders_table_header').eq(i++).should('contain', 'Source')
            cy.get('@orders_table_header').eq(i++).should('contain', 'Date')
            cy.get('@orders_table_header').eq(i++).should('contain', 'Montant')
            cy.get('@orders_table_header').eq(i++).should('contain', 'Magasin')
            cy.get('@orders_table_header').eq(i++).should('contain', 'Statut')
            cy.get('@orders_table_header').eq(i++).should('contain', 'Livraison')
            cy.get('@orders_table_header').eq(i).should('contain', 'Actions')
        })
    })

    describe('View orders', function () {
        it('loads orders successfully with default filters', function () {
            visitPageAndLoadOrders()

            cy.get('[data-context=filter-status] [data-context=erp-multiselect] [data-context=single-value]').should(
                'contain',
                'Payée',
            )
            cy.get('[data-context=filter-period] [data-context=erp-multiselect] [data-context=single-value]').should(
                'contain',
                "Aujourd'hui",
            )

            cy.get('[data-context=orders]').as('orders')
            cy.get('@orders').find('tbody tr').should('have.length', 10)

            selectRow(0)

            selectCell(1).should('contain', '2407813')
            selectCell(2).should('contain', 'Magasins').should('contain', 'Magasin SV')
            selectCell(4).should('contain', '2 917,08 €')
        })

        it('selects some filters and triggers a search', function () {
            // freeze clock - Today is 13/12/2024
            cy.mockDate(new Date(2024, 11, 13))

            visitPageAndLoadOrders()

            cy.get('[data-context=dashboard-filters]').as('filters')

            cy.get('@filters')
                .get('[data-context=filter-period]')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', '> 2 mois')

            cy.get('@filters')
                .get('[data-context=filter-status]')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'Ouverte')

            cy.get('@filters').get('[data-context=filters-submit-btn]').click()
            cy.wait('@orders').then((xhr) => {
                expect(xhr.request.body.where._and[0].created_at._between[0]).to.eq('2021-01-01 00:00:00')
                expect(xhr.request.body.where._and[0].created_at._between[1]).to.eq('2024-10-13 23:59:59')
                expect(xhr.request.body.where._and[1].order_status._eq).to.eq('Ouverte')
            })
        })

        it('Check url is handled correctly', function () {
            // freeze clock - Today is 13/12/2024
            cy.mockDate(new Date(2024, 11, 13))

            const qs =
                '?pager[limit]=25&pager[filters]=%7B%22source_group%22%3A%5B%7B%22id%22%3A%22magasins%22%2C%22label%22%3A%22Magasins%22%7D%5D%2C%22source%22%3A%5B%7B%22id%22%3A%22source.shop.sonvideo_pro%22%2C%22label%22%3A%22Magasin%20Pro%20SV%22%2C%22group_id%22%3A%22magasins%22%7D%2C%7B%22id%22%3A%22source.shop.sonvideo%22%2C%22label%22%3A%22Magasin%20SV%22%2C%22group_id%22%3A%22magasins%22%7D%5D%2C%22status%22%3A%22Cr%C3%A9dit%C3%A9e%22%2C%22period_identifier%22%3A%22older_than_2_months%22%7D'

            visitPageAndLoadOrders(qs)

            cy.get(
                '[data-context=filter-group-source] [data-context=erp-multiselect] [data-context=multiple] [data-context=tag]',
            )
                .eq(0)
                .should('contain', 'Magasins')

            cy.get(
                '[data-context=filter-source] [data-context=erp-multiselect] [data-context=multiple] [data-context=tag]',
            )
                .eq(0)
                .should('contain', 'Magasin Pro SV')
            cy.get(
                '[data-context=filter-source] [data-context=erp-multiselect] [data-context=multiple] [data-context=tag]',
            )
                .eq(1)
                .should('contain', 'Magasin SV')

            cy.get('[data-context=filter-status] [data-context=erp-multiselect] [data-context=single-value]').should(
                'contain',
                'Créditée',
            )

            cy.get('[data-context=filter-period] [data-context=erp-multiselect] [data-context=single-value]').should(
                'contain',
                '> 2 mois',
            )

            cy.wait('@orders').then((xhr) => {
                expect(xhr.request.body.where._and[0].created_at._between[0]).to.eq('2021-01-01 00:00:00')
                expect(xhr.request.body.where._and[0].created_at._between[1]).to.eq('2024-10-13 23:59:59')
                expect(xhr.request.body.where._and[1].source._in[0]).to.eq('Magasin Pro SV')
                expect(xhr.request.body.where._and[1].source._in[1]).to.eq('Magasin SV')
                expect(xhr.request.body.where._and[2].order_status._eq).to.eq('Créditée')
            })
        })
    })
})
