import {
    customerOrderContextForPaymentsMock,
    customerOrderErpV2Mock,
    customerOrderPaymentMock,
    operationMock,
} from '../../utils/erp-server-utils'

const OPERATION_ID = '123ea648-5726-4e39-880d-9d39f5eb1e8f'

describe('Customer order payments - Operation action in payment block', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456),
                    payments: [
                        {
                            ...customerOrderPaymentMock(),
                            operation_id: OPERATION_ID,
                        },
                    ],
                },
            },
        }).as('customer-order-legacy-123456')

        cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderContextForPaymentsMock(123456),
                },
            },
        }).as('customer-order-context-for-payments-123456')

        cy.intercept('GET', '**/api/erp/v1/anti-fraud/123456', {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    meta: [],
                    statuses: {
                        customer_order_id: 123456,
                        anti_fraud_customer_order: null,
                        anti_fraud_customer_order_payments: [],
                    },
                },
            },
        }).as('load-anti-fraud')
    })

    it('should be displayed correctly when there is no payment on the customer order yet', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456),
                },
            },
        }).as('customer-order-legacy-123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table]').should('not.exist')
        cy.get('[data-context=customer-order-payments-block] [data-context=toggle-payment-block]').should('be.disabled')
    })

    it('should not show an actions column when there is no payment v2', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: {
                    ...customerOrderErpV2Mock(123456),
                    payments: [
                        {
                            ...customerOrderPaymentMock(),
                            workflow: 'legacy',
                        },
                    ],
                },
            },
        }).as('customer-order-legacy-123456')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table]').should('be.visible')
        cy.get(
            '[data-context=customer-order-payments-block] [data-context=erp-table] [data-context=cell-actions]',
        ).should('not.exist')
    })

    it('should show an actions column when there is a payment v2', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.wait(['@customer-order-legacy-123456', '@load-anti-fraud', '@customer-order-context-for-payments-123456'])

        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table]').should('be.visible')
        cy.get(
            '[data-context=customer-order-payments-block] [data-context=erp-table] [data-context=cell-actions]',
        ).should('be.visible')

        cy.get(
            '[data-context=customer-order-payments-block] [data-context=erp-table] [data-context=view-details-btn]',
        ).should('be.visible')
    })

    it('should keep the toggle state of the payments block upon page reload', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table]').should('be.visible')
        cy.get('[data-context=customer-order-payments-block] [data-context=toggle-payment-block]').click()
        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table]').should('not.exist')

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table]').should('not.exist')
        cy.get('[data-context=customer-order-payments-block] [data-context=toggle-payment-block]').click()
        cy.get('[data-context=customer-order-payments-block] [data-context=erp-table]').should('be.visible')
    })

    it('should display an error when a payment is uncertain', () => {
        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

        cy.get('[data-context=customer-order-payments-block]').within(() => {
            cy.get('[data-context=erp-table]').should('be.visible')
            cy.get('[data-context=toggle-payment-block]').parent().tooltip(null)
            cy.get('[data-context=toggle-payment-block]').click()
            cy.get('[data-context=erp-table]').should('not.exist')

            cy.intercept('GET', '**/api/erp-payment/v1/customer-order/123456/context', {
                statusCode: 200,
                body: {
                    data: {
                        ...customerOrderContextForPaymentsMock(123456),
                        operations: [
                            {
                                ...operationMock(123456, OPERATION_ID),
                                code: 'CBS-O',
                                meta: {
                                    is_uncertain: true,
                                },
                            },
                        ],
                    },
                },
            }).as('customer-order-context-for-payments-123456')

            cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')

            cy.get('[data-context=erp-table]').should('be.visible')
            cy.get('[data-context=toggle-payment-block]').should('be.disabled')
            cy.get('[data-context=toggle-payment-block]')
                .parent()
                .tooltip('Des informations importantes sont affichées ci-dessus.')
        })
    })
})
