describe('Affiliation consolidation', () => {
    const PAGE = 'customer-order/affiliation'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Show correctly the webpage', () => {
        it('display the temporary file uploader', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=temporary-file-uploader-input]')
                .as('temporary-file-uploader-input')
                .should('be.not.visible')

            cy.get('[data-context=temporary-file-uploader]')
                .find('button')
                .as('temporary-file-uploader-button')
                .should('be.visible')
                .should('be.not.empty')
        })

        it('display an error on failure', () => {
            cy.intercept('POST', '**/api/erp/v1/temporary-file/upload', {
                body: {
                    status: 'success',
                    data: {
                        target_path: '090920221133-customer_order_1572493.json',
                    },
                },
            }).as('api_post_temporary_file')

            cy.intercept('POST', '**/api/erp/v1/customer-order/affiliation-consolidation', {
                statusCode: 400,
                body: {
                    status: 'error',
                    message: 'File must be a CSV.',
                },
            }).as('api_post_customer_order_affiliation_consolidation')

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=temporary-file-uploader-input]').as('temporary-file-uploader-input')

            cy.get('[data-context=temporary-file-uploader]')
                .find('button')
                .as('temporary-file-uploader-button')
                .should('be.visible')
                .should('be.not.empty')
                .click()

            cy.get('@temporary-file-uploader-input').selectFile(
                'tests/e2e/fixtures/erp/customer-order/customer_order_1572493.json',
                {
                    force: true,
                },
            )

            cy.wait('@api_post_temporary_file')

            cy.wait('@api_post_customer_order_affiliation_consolidation')

            cy.toast(`Une erreur est survenue`, 'danger')
        })

        it('download the file on success', () => {
            cy.intercept('POST', '**/api/erp/v1/temporary-file/upload', {
                body: {
                    status: 'success',
                    data: {
                        target_path: '090920221133-conversion_mai_conversion_mai.csv',
                    },
                },
            }).as('api_post_temporary_file')

            cy.intercept('POST', '**/api/erp/v1/customer-order/affiliation-consolidation', {
                body: {
                    status: 'error',
                    statusCode: 400,
                    data: '"Commande ID",Statut,"Détail statut",Origine,Canal,Clef\n2262552,Annulée,,son-video.com,,\n2262614,Annulée,,son-video.com,,\n2262622,Annulée,,son-video.com,,\n2262642,Annulée,,son-video.com,,\n2262652,Annulée,,son-video.com,CallCenter,comparateur\n2262684,Annulée,,son-video.com,,\n2262755,Annulée,,son-video.com,CallCenter,\n2262930,Annulée,,son-video.com,,comparateur\n2262947,Annulée,,son-video.com,,comparateur\n2263221,Annulée,,son-video.com,,\n2263401,Annulée,,son-video.com,,editorial-content\n226341…t\n2274338,Validée,,son-video.com,CallCenter,\n2274340,Validée,,son-video.com,,\n2274423,Validée,,son-video.com,,comparateur\n2274429,Validée,,son-video.com,,editorial-content\n2274463,Validée,,son-video.com,,editorial-content\n2274472,Validée,,son-video.com,,twenga\n2274485,Validée,,son-video.com,CallCenter,twenga\n2274515,Validée,,son-video.com,,\n2274516,Validée,,son-video.com,,\n2274517,Validée,,son-video.com,,comparateur\n2274533,Validée,,son-video.com,,comparateur\n2274545,Validée,,son-video.com,,\n',
                },
            }).as('api_post_customer_order_affiliation_consolidation')

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=temporary-file-uploader]')
                .find('button')
                .as('temporary-file-uploader-button')
                .should('be.visible')
                .should('be.not.empty')
                .click()
            cy.get('[data-context=temporary-file-uploader-input]').selectFile(
                'tests/e2e/fixtures/erp/customer-order/affiliation.csv',
                {
                    force: true,
                },
            )

            cy.wait('@api_post_temporary_file')

            cy.wait('@api_post_customer_order_affiliation_consolidation')
        })
    })
})
