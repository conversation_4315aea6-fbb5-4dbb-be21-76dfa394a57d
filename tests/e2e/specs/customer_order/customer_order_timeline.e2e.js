import { customerOrderErpV2Mock } from '../../utils/erp-server-utils'

describe('Display customer order timeline', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: customerOrderErpV2Mock(123456),
            },
        }).as('customer_order_123456')

        // freeze clock
        cy.mockDate(new Date(2022, 1, 1, 15, 44, 0, 0))

        cy.visit('/legacy/v1/commandes/edition_commande.php?id_commande=123456')
        cy.wait('@customer_order_123456')
    })

    it('opens all customer order messages in a side panel', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            body: {
                data: {
                    events: [],
                },
            },
        }).as('timeline_123456')

        cy.get('[data-context=open-timeline]').should('contain', 'Voir tous les messages').click()
        cy.wait('@timeline_123456')

        cy.get('[data-context=slide-out-container] [data-context=customer-order-timeline]')
            .as('timeline')
            .should('be.visible')
        // header
        cy.get('@timeline').find('[data-context=page-header]').should('contain', 'Historique des messages')

        // when no message
        cy.get('@timeline')
            .find('[data-context=no-message]')
            .should('contain', 'Aucun message')
            .find('svg[data-name="Void picture"]')
            .should('be.visible')
    })

    it('display all messages timeline sort by creation date', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_confirm.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').should('contain', 'Voir tous les messages').click()
        cy.wait('@customer_order_timeline')

        // content
        cy.get('[data-context=slide-out-container] [data-context=customer-order-timeline]')
            .find('[data-context=event]')
            .should('have.length', 2)
            .as('events')
            .eq(0)
            .find('[data-context=customer-order-confirmation] [data-context=date]')
            .should('contain', 'le 14/01/2022 à 15:55')
        cy.get('@events')
            .eq(1)
            .find('[data-context=customer-order-confirmation] [data-context=date]')
            .should('contain', 'le 13/01/2022 à 15:55')
    })

    it('display customer order confirmation email', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_confirm.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')

        // content
        cy.get('[data-context=slide-out-container] [data-context=customer-order-timeline]')
            .find('[data-context=event]')
            .eq(1)
            .find('[data-context=customer-order-confirmation]')
            .as('order_confirmation_email')
            .find('[data-context=title]')
            .should('contain', 'Email de confirmation de commande')
        cy.get('@order_confirmation_email').find('[data-context=date]').should('contain', 'le 13/01/2022 à 15:55')
        cy.get('@order_confirmation_email').find('[data-context=erp-table]').as('table')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 3)
        cy.get('@header').eq(0).should('contain', 'article')
        cy.get('@header').eq(1).should('contain', 'quantité')
        cy.get('@header').eq(2).should('contain', 'total TTC')

        // content first row
        cy.get('@order_confirmation_email')
            .find('[data-context=table-row]')
            .should('have.length', 2)
            .eq(0)
            .as('article')
        cy.get('@article')
            .find('[data-context=erp-article-item]')
            .as('article_item')
            .find('[data-context=sku]')
            .should('contain', ' EPH100 ')
            .should('have.attr', 'href', '/articles/EPH100/')
        cy.get('@article_item')
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://www.son-video.com/images/article/lg/LGOLED65C1/oled65c1_60618637861d6_450_square.jpg',
            )
        cy.get('@article_item').find('[data-context=name]').should('contain', 'Enceintes Test')
        cy.get('@article').find('[data-context=cell-quantity]').should('contain', '10')
        cy.get('@article').find('[data-context=cell-price]').should('contain', '1 000,00 €')

        // content seconde row
        cy.get('@order_confirmation_email')
            .find('[data-context=table-row]')
            .should('have.length', 2)
            .eq(1)
            .as('article')
        cy.get('@article')
            .find('[data-context=erp-article-item]')
            .as('article_item')
            .find('[data-context=sku]')
            .should('contain', ' LGOLED65C1 ')
            .should('have.attr', 'href', '/articles/LGOLED65C1/')
        cy.get('@article_item')
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://www.son-video.com/images/article/lg/LGOLED65C1/oled65c1_60618637861d6_450_square.jpg',
            )
        cy.get('@article_item').find('[data-context=name]').should('contain', 'Téléviseur OLED LG OLED65C1')
        cy.get('@article').find('[data-context=cell-quantity]').should('contain', '1')
        cy.get('@article').find('[data-context=cell-price]').should('contain', '1 508,76 €')
    })

    it('display customer order in replenishment email', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_replenishment.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')

        // content
        cy.get('[data-context=slide-out-container] [data-context=customer-order-timeline]')
            .find('[data-context=event]')
            .find('[data-context=customer-order-replenishment]')
            .as('replenishment_email')
            .find('[data-context=title]')
            .should('contain', "Email d'information d'approvisionnement")
        cy.get('@replenishment_email').find('[data-context=date]').should('contain', 'le 14/01/2022 à 15:55')

        // available table content
        cy.get('@replenishment_email')
            .find('[data-context=erp-table]')
            .should('have.length', 2)
            .eq(0)
            .as('available_table')
        cy.get('@available_table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 2)
        cy.get('@header').eq(0).should('contain', 'article')
        cy.get('@header').eq(1).should('contain', 'quantité')

        // content first row
        cy.get('@available_table').find('[data-context=table-row]').should('have.length', 1).eq(0).as('article')
        cy.get('@article')
            .find('[data-context=erp-article-item]')
            .as('article_item')
            .find('[data-context=sku]')
            .should('contain', ' DLINETRUNK30151MSR ')
            .should('have.attr', 'href', '/articles/DLINETRUNK30151MSR/')
        cy.get('@article_item')
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://www.son-video.com/images/dynamic/Cables_video/articles/D_Line/DLINETRUNK30151MSR/D-Line-Trunk-30-15-Silver_P_450_square.jpg',
            )
        cy.get('@article_item')
            .find('[data-context=name]')
            .should('contain', 'Gaine passe-câble D-Line Trunk 30/15 mm silver, longueur 1 m')
        cy.get('@article').find('[data-context=cell-quantity]').should('contain', '1')

        // replenishment table content
        cy.get('@replenishment_email').find('[data-context=erp-table]').eq(1).as('replenishment_table')
        cy.get('@replenishment_table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 3)
        cy.get('@header').eq(0).should('contain', 'article')
        cy.get('@header').eq(1).should('contain', 'quantité')
        cy.get('@header').eq(2).should('contain', 'livraison estimée')

        // content first row
        cy.get('@replenishment_table').find('[data-context=table-row]').should('have.length', 1).eq(0).as('article')
        cy.get('@article')
            .find('[data-context=erp-article-item]')
            .as('article_item')
            .find('[data-context=sku]')
            .should('contain', ' BOSESLCOLIINR ')
            .should('have.attr', 'href', '/articles/BOSESLCOLIINR/')
        cy.get('@article_item')
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://www.son-video.com/images/dynamic/Enceintes/articles/Bose/BOSESLCOLIINR/Bose-SoundLink-Color-II-Noir_P_300_square.jpg',
            )
        cy.get('@article_item')
            .find('[data-context=name]')
            .should('contain', 'Enceinte Bluetooth Bose SoundLink Color II Noir')
        cy.get('@article').find('[data-context=cell-quantity]').should('contain', '1')
        cy.get('@article').find('[data-context=cell-estimated-delivery-date]').should('contain', '24/01/2022')
    })

    it('display customer order in replenishment email without available article', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_replenishment2.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')

        // content
        cy.get('[data-context=slide-out-container] [data-context=customer-order-timeline]')
            .find('[data-context=event]')
            .find('[data-context=customer-order-replenishment]')
            .as('replenishment_email')
            .find('[data-context=title]')
            .should('contain', "Email d'information d'approvisionnement")
        cy.get('@replenishment_email').find('[data-context=date]').should('contain', 'le 14/01/2022 à 15:55')

        // replenishment table content
        cy.get('@replenishment_email').find('[data-context=erp-table]').eq(0).as('replenishment_table')
        cy.get('@replenishment_table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 3)
        cy.get('@header').eq(0).should('contain', 'article')
        cy.get('@header').eq(1).should('contain', 'quantité')
        cy.get('@header').eq(2).should('contain', 'livraison estimée')

        // content first row
        cy.get('@replenishment_table').find('[data-context=table-row]').should('have.length', 1).eq(0).as('article')
        cy.get('@article')
            .find('[data-context=erp-article-item]')
            .as('article_item')
            .find('[data-context=sku]')
            .should('contain', ' BOSESLCOLIINR ')
            .should('have.attr', 'href', '/articles/BOSESLCOLIINR/')
        cy.get('@article_item')
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://www.son-video.com/images/dynamic/Enceintes/articles/Bose/BOSESLCOLIINR/Bose-SoundLink-Color-II-Noir_P_300_square.jpg',
            )
        cy.get('@article_item')
            .find('[data-context=name]')
            .should('contain', 'Enceinte Bluetooth Bose SoundLink Color II Noir')
        cy.get('@article').find('[data-context=cell-quantity]').should('contain', '1')
        cy.get('@article').find('[data-context=cell-estimated-delivery-date]').should('contain', '24/01/2022')
    })

    it('display customer order availability email', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_availability.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')

        // content
        cy.get('[data-context=slide-out-container] [data-context=customer-order-timeline]')
            .find('[data-context=event]')
            .eq(0)
            .find('[data-context=customer-order-availability]')
            .as('order_confirmation_email')
            .find('[data-context=title]')
            .should('contain', "Email d'information de disponibilité")
        cy.get('@order_confirmation_email').find('[data-context=date]').should('contain', 'le 15/01/2022 à 15:55')
        cy.get('@order_confirmation_email').find('[data-context=erp-table]').as('table')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 2)
        cy.get('@header').eq(0).should('contain', 'article')
        cy.get('@header').eq(1).should('contain', 'quantité')

        // content first row
        cy.get('@order_confirmation_email')
            .find('[data-context=table-row]')
            .should('have.length', 2)
            .eq(0)
            .as('article')
        cy.get('@article')
            .find('[data-context=erp-article-item]')
            .as('article_item')
            .find('[data-context=sku]')
            .should('contain', ' BOSESLCOLIINR ')
            .should('have.attr', 'href', '/articles/BOSESLCOLIINR/')
        cy.get('@article_item')
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://www.son-video.com/images/dynamic/Enceintes/articles/Bose/BOSESLCOLIINR/Bose-SoundLink-Color-II-Noir_P_300_square.jpg',
            )
        cy.get('@article_item')
            .find('[data-context=name]')
            .should('contain', 'Enceinte Bluetooth Bose SoundLink Color II Noir')
        cy.get('@article').find('[data-context=cell-quantity]').should('contain', '1')
        cy.get('[data-context=total-cost]').should('contain', 'Montant total : 119,00 €')
    })

    it('display customer order pickup store availability email', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_pickup_store_availability.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')

        // content
        cy.get('[data-context=slide-out-container] [data-context=customer-order-timeline]')
            .find('[data-context=event]')
            .eq(0)
            .find('[data-context=customer-order-availability]')
            .as('order_confirmation_email')
            .find('[data-context=title]')
            .should('contain', "Email d'information de disponibilité en magasin")
        cy.get('@order_confirmation_email').find('[data-context=date]').should('contain', 'le 03/02/2022 à 12:10')
        cy.get('@order_confirmation_email').find('[data-context=erp-table]').as('table')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 2)
        cy.get('@header').eq(0).should('contain', 'article')
        cy.get('@header').eq(1).should('contain', 'quantité')

        // content first row
        cy.get('@order_confirmation_email')
            .find('[data-context=table-row]')
            .should('have.length', 2)
            .eq(0)
            .as('article')
        cy.get('@article')
            .find('[data-context=erp-article-item]')
            .as('article_item')
            .find('[data-context=sku]')
            .should('contain', ' BOSESLCOLIINR ')
            .should('have.attr', 'href', '/articles/BOSESLCOLIINR/')
        cy.get('@article_item')
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://www.son-video.com/images/dynamic/Enceintes/articles/Bose/BOSESLCOLIINR/Bose-SoundLink-Color-II-Noir_P_300_square.jpg',
            )
        cy.get('@article_item')
            .find('[data-context=name]')
            .should('contain', 'Enceinte Bluetooth Bose SoundLink Color II Noir')
        cy.get('@article').find('[data-context=cell-quantity]').should('contain', '1')
        cy.get('[data-context=store]')
            .find('[data-context=address]')
            .should('contain', 'magasin (Toulouse)')
            .should('contain', '5 Boulevard Lazare Carnot')
            .should('contain', '31000 Toulouse')
    })

    it('display customer messages', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_contact.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')

        // should have 3 messages from the customer
        cy.get('[data-context=tracking-contact-customer-timeline-item]')
            .should('have.length', 3)
            .eq(0)
            .as('customer_contact')

        // content of the first message
        cy.get('@customer_contact').find('[data-context=title]').should('contain', 'Message du client')
        cy.get('@customer_contact').find('[data-context=date]').should('contain', 'le 13/07/2020 à 19:19')
        cy.get('@customer_contact').find('[data-context=message]').should('contain', 'Super. Merci.')
        cy.get('@customer_contact').hasIcon('user', 'fas')
    })

    it('display messages from svd', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_contact.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')

        // should have 2 messages from son-video
        cy.get('[data-context=tracking-contact-svd-timeline-item]').should('have.length', 2).eq(0).as('svd_contact')

        // content of the first message
        cy.get('@svd_contact').find('[data-context=title]').should('contain', 'Alain TERIEUR')
        cy.get('@svd_contact').find('[data-context=date]').should('contain', 'le 13/07/2020 à 15:00')
        cy.get('@svd_contact')
            .find('[data-context=message]')
            .should('contain', 'Pour tout renseignement concernant votre commande')
        cy.get('@svd_contact').find('[data-context=avatar]').should('be.visible')
    })

    it('display shipping confirmation email', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_confirm_shipping.json',
        }).as('customer_order_timeline')

        cy.intercept('POST', '**/api/erp/v1/shipment-methods', {
            fixture: 'erp/shipment-methods/cpost_shipment_method_65.json',
        }).as('shipment_method')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')
        cy.wait('@shipment_method')

        // content
        cy.get('[data-context=slide-out-container] [data-context=customer-order-timeline]')
            .find('[data-context=event]')
            .eq(0)
            .find('[data-context=confirm-shipping]')
            .as('shipping_confirmation_email')
            .find('[data-context=title]')
            .should('contain', "Email de confirmation d'expédition")
        cy.get('@shipping_confirmation_email').find('[data-context=date]').should('contain', 'le 19/01/2022 à 19:00')
        cy.get('@shipping_confirmation_email').find('[data-context=erp-table]').as('table')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 2)
        cy.get('@header').eq(0).should('contain', 'article')
        cy.get('@header').eq(1).should('contain', 'quantité')

        // content first row
        cy.get('@shipping_confirmation_email')
            .find('[data-context=table-row]')
            .should('have.length', 3)
            .eq(0)
            .as('article')
        cy.get('@article')
            .find('[data-context=erp-article-item]')
            .as('article_item')
            .find('[data-context=sku]')
            .should('contain', ' BOSESLCOLIINR ')
            .should('have.attr', 'href', '/articles/BOSESLCOLIINR/')
        cy.get('@article_item')
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://www.son-video.com/images/dynamic/Enceintes/articles/Bose/BOSESLCOLIINR/Bose-SoundLink-Color-II-Noir_P_300_square.jpg',
            )
        cy.get('@article_item')
            .find('[data-context=name]')
            .should('contain', 'Enceinte Bluetooth Bose SoundLink Color II Noir')
        cy.get('@article').find('[data-context=cell-quantity]').should('contain', '1')

        cy.get('@shipping_confirmation_email')
            .find('[data-context=delivery-details]')
            .should('contain', 'Envoyé par GLS le 19/01/2022')
        cy.get('@shipping_confirmation_email')
            .find('[data-context=tracking-numbers]')
            .should('have.length', '2')
            .eq(0)
            .should('contain', '00FMRLC1')
    })

    it('display store pickup confirmation email', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_confirm_store_pickup.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')

        // content
        cy.get('[data-context=slide-out-container] [data-context=customer-order-timeline]')
            .find('[data-context=event]')
            .eq(0)
            .find('[data-context=confirm-store-pickup]')
            .as('store_pickup_confirmation_email')
            .find('[data-context=title]')
            .should('contain', 'Email de confirmation de facture pour emport dépôt')
        cy.get('@store_pickup_confirmation_email')
            .find('[data-context=date]')
            .should('contain', 'le 19/01/2022 à 17:43')
        cy.get('@store_pickup_confirmation_email').find('[data-context=erp-table]').as('table')
        cy.get('@table').find('thead tr th').as('header')
        cy.get('@header').should('have.length', 2)
        cy.get('@header').eq(0).should('contain', 'article')
        cy.get('@header').eq(1).should('contain', 'quantité')

        // content first row
        cy.get('@store_pickup_confirmation_email')
            .find('[data-context=table-row]')
            .should('have.length', 3)
            .eq(0)
            .as('article')
        cy.get('@article')
            .find('[data-context=erp-article-item]')
            .as('article_item')
            .find('[data-context=sku]')
            .should('contain', ' BOSESLCOLIINR ')
            .should('have.attr', 'href', '/articles/BOSESLCOLIINR/')
        cy.get('@article_item')
            .find('[data-context=image]')
            .should('have.attr', 'src')
            .should(
                'include',
                'https://www.son-video.com/images/dynamic/Enceintes/articles/Bose/BOSESLCOLIINR/Bose-SoundLink-Color-II-Noir_P_300_square.jpg',
            )
        cy.get('@article_item')
            .find('[data-context=name]')
            .should('contain', 'Enceinte Bluetooth Bose SoundLink Color II Noir')
        cy.get('@article').find('[data-context=cell-quantity]').should('contain', '1')

        // invoice
        cy.get('@store_pickup_confirmation_email')
            .find('[data-context=invoice]')
            .should('contain', 'Facture : ')
            .find('[data-context=invoice-id]')
            .should('have.attr', 'href', 'http://legacy.lxc/facture/showFacturePdf?id=1603885')
    })

    it('display payment link messages', () => {
        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
            fixture: 'erp/customer-order-timeline/customer_order_payment_link.json',
        }).as('customer_order_timeline')

        cy.get('[data-context=open-timeline]').click()
        cy.wait('@customer_order_timeline')

        cy.get('[data-context=email-payment-link-timeline-item]').within(() => {
            cy.get('[data-context=title]').should('contain', 'Email lien de paiement')
            cy.get('[data-context=date]').should('contain', 'le 24/10/2024 à 10:17')
            cy.get('[data-context=message]').should('contain', 'Il faut cliquer monsieur')
            cy.get('[data-context=payment-link] a:contains(Lien de paiement)').should(
                'have.attr',
                'href',
                'https://example.com',
            )
            cy.get('[data-context=avatar]').should('be.visible').tooltip('Alain TERIEUR')
        })
    })

    describe('Mailjet message history', () => {
        it('does not display the mailjet history if has no mailjet_message_id in payload', () => {
            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
                fixture: 'erp/customer-order-timeline/customer_order_availability.json',
            }).as('customer_order_timeline')

            cy.get('[data-context=open-timeline]').click()
            cy.wait('@customer_order_timeline')

            cy.get('[data-context=slide-out-container]').should('be.visible')
            cy.get('[data-context=slide-out-container] [data-context=mailjet-message-history]').should('not.exist')
        })

        it('display the mailjet history if has mailjet_message_id in payload', () => {
            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
                fixture: 'erp/customer-order-timeline/customer_order_availability_with_mailjet_history.json',
            }).as('customer_order_timeline')
            cy.intercept('GET', '**/api/erp/v1/mailjet/message/123456789123456789', {
                fixture: 'erp/mailjet/message/message_history_event_empty.json',
            }).as('api_mailjet_message')

            cy.get('[data-context=open-timeline]').click()
            cy.wait('@customer_order_timeline')

            cy.get('[data-context=slide-out-container]').should('be.visible')
            cy.get('[data-context=slide-out-container] [data-context=mailjet-message-history]')
                .should('be.visible')
                .as('message_history')

            // test on content
            cy.get('@message_history').should('contain', `Historique de l'email`)
            cy.get('@message_history').find('[data-context=actions]').should('exist').hasIcon('chevron-left', 'fas')

            // on click, should call mailjet api
            cy.get('@message_history').click()
            cy.wait('@api_mailjet_message')
            cy.get('@message_history')
                .find('[data-context=actions]')
                .should('exist')
                .hasIcon('chevron-double-down', 'fas')

            // has an empty history
            cy.get('@message_history')
                .find('[data-context=illustrated-message]')
                .should('be.visible')
                .should('contain', `Pas d'événement sur cet email`)
        })

        it('display the mailjet history with a message history', () => {
            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
                fixture: 'erp/customer-order-timeline/customer_order_availability_with_mailjet_history.json',
            }).as('customer_order_timeline')
            cy.intercept('GET', '**/api/erp/v1/mailjet/message/123456789123456789', {
                fixture: 'erp/mailjet/message/message_history_event.json',
            }).as('api_mailjet_message')

            cy.get('[data-context=open-timeline]').click()
            cy.wait('@customer_order_timeline')

            cy.get('[data-context=slide-out-container] [data-context=mailjet-message-history]')
                .should('be.visible')
                .as('message_history')
            cy.get('@message_history').click()
            cy.wait('@api_mailjet_message')

            // message history should have 1 element
            cy.get('@message_history')
                .find('[data-context=event]')
                .should('have.length', 1)
                .eq(0)
                .should('contain', `Message ouvert`)
                .should('contain', `le 01/02/2022 à 15:40`)
        })

        it('display the mailjet history containing all types of events', () => {
            cy.intercept('GET', '**/api/erp/v1/customer-order/123456/communication-timeline', {
                fixture: 'erp/customer-order-timeline/customer_order_availability_with_mailjet_history.json',
            }).as('customer_order_timeline')
            cy.intercept('GET', '**/api/erp/v1/mailjet/message/123456789123456789', {
                fixture: 'erp/mailjet/message/message_history_all_events.json',
            }).as('api_mailjet_message')

            cy.get('[data-context=open-timeline]').click()
            cy.wait('@customer_order_timeline')

            cy.get('[data-context=slide-out-container] [data-context=mailjet-message-history]')
                .should('be.visible')
                .as('message_history')
            cy.get('@message_history').click()
            cy.wait('@api_mailjet_message')

            // message history should have 5 elements
            cy.get('@message_history').find('[data-context=event]').as('events').should('have.length', 7)
            cy.get('@events').eq(0).should('contain', `Message mis en spam`).should('contain', `le 01/02/2022 à 15:42`)
            cy.get('@events').eq(1).should('contain', `Message bounce`).should('contain', `le 01/02/2022 à 15:42`)
            cy.get('@events').eq(2).should('contain', `Message hard bounce`).should('contain', `le 01/02/2022 à 15:42`)
            cy.get('@events').eq(3).should('contain', `Message soft bounce`).should('contain', `le 01/02/2022 à 15:42`)
            cy.get('@events').eq(4).should('contain', `Désabonné`).should('contain', `le 01/02/2022 à 15:42`)
            cy.get('@events').eq(5).should('contain', `Message bloqué`).should('contain', `le 01/02/2022 à 15:42`)
            cy.get('@events').eq(6).should('contain', `Message ouvert`).should('contain', `le 01/02/2022 à 15:40`)
        })
    })
})
