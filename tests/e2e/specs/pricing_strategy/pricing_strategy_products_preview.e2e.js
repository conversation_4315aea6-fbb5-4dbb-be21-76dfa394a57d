import { ARTICLE_PRICES_WRITE } from '../../../../src/apps/erp/permissions.js'

describe('Preview the engine run', () => {
    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page)
    }

    const testCell = (cell, index) => {
        if (undefined === cell.element) {
            cy.get('@cells').eq(index).should('contain', cell.text)

            return
        }

        cy.get('@cells').eq(index).find(cell.element).as('cell')

        if (undefined !== cell.text) {
            cy.get('@cell').should('contain', cell.text)
        }
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12', {
            fixture: 'erp/pricing-strategies/get_pricing_strategy.json',
        }).as('fetch-pricing-strategy')

        cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12/engine', {
            fixture: 'erp/pricing-strategies/get_pricing_strategy_engine.json',
        }).as('fetch-pricing-strategy-low-margin-products')
    })

    it('Display the table headers and rows with permission', () => {
        visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/12/preview')

        cy.wait(['@fetch-pricing-strategy-low-margin-products', '@fetch-pricing-strategy'])

        cy.get('[data-context=erp-table]').as('table')

        cy.get('@table').find('thead th').as('headers')
        ;['produit', 'catégorie', 'stock', 'concurrent le plus bas', 'canaux de vente', ''].forEach((header, index) => {
            if (Array.isArray(header)) {
                header.forEach((h) => cy.get('@headers').eq(index).should('contain', h))

                return
            }

            cy.get('@headers').eq(index).should('contain', header)
        })
        ;[
            [
                [{ text: 'DENAVCX3800HNR', element: '[data-context=erp-article-item]' }],
                [{ text: 'Amplificateurs' }],
                [{ text: '31' }],
                [{ text: 'SONVIDEO' }, { text: '123,34 € TTC' }],
            ],
            [
                [{ text: 'MARMCR612NR', element: '[data-context=erp-article-item]' }],
                [{ text: 'Amplificateurs' }],
                [{ text: '103' }],
                [{ text: 'AMAZON' }, { text: '456,78 € TTC' }],
            ],
            [
                [{ text: 'MARMCR612SG', element: '[data-context=erp-article-item]' }],
                [{ text: 'Amplificateurs' }],
                [{ text: '34' }],
                [{ text: 'SONVIDEO' }, { text: '123,34 € TTC' }],
            ],
        ].forEach((row, row_index) => {
            cy.get('@table').find('> tbody > tr').eq(row_index).find('td').as('cells')
            row.forEach((cell_elements, index) => {
                cell_elements.forEach((cell) => {
                    if (Array.isArray(cell)) {
                        cell.forEach((el) => {
                            testCell(el, index)
                        })
                    } else {
                        testCell(cell, index)
                    }
                })
            })
        })
        ;[
            [
                [
                    [{ text: 'son-video.com' }],
                    [{ text: '950,00 €' }, { text: '20.99 %' }],
                    [{ text: '945,00 €' }, { text: '31.00 %' }],
                    [{ text: '940,00 €' }, { text: '32.00 %' }],
                ],
                [
                    [{ text: 'easylounge.com' }],
                    [{ text: '950,00 €' }, { text: '30.00 %' }],
                    [{ text: '944,00 €' }, { text: '20.99 %' }],
                    [{ text: '940,00 €' }, { text: '20.99 %' }],
                ],
            ],
        ].forEach((row) => {
            cy.get('[data-context=sales-channel-prices]').eq(0).find('tbody tr').as('rows')

            row.forEach((cell_elements, rowIndex) => {
                cell_elements.forEach((content, cellIndex) => {
                    content.forEach((item) => {
                        cy.get('@rows').eq(rowIndex).find('td').eq(cellIndex).should('contain', item.text)
                    })
                })
            })
        })

        cy.get('[data-context=assign-to-new-strategy]').should('not.exist')
        cy.get('[data-context=assign-to-other-strategy]').should('not.exist')
    })
})
