import { ARTICLE_PRICES_WRITE } from '../../../../src/apps/erp/permissions.js'

describe('View pricing strategy products list with low margin', () => {
    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page)
    }

    const testCell = (cell, index) => {
        if (undefined === cell.element) {
            cy.get('@cells').eq(index).should('contain', cell.text)

            return
        }

        cy.get('@cells').eq(index).find(cell.element).as('cell')

        if (undefined !== cell.text) {
            cy.get('@cell').should('contain', cell.text)
        }
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12', {
            fixture: 'erp/pricing-strategies/get_pricing_strategy.json',
        }).as('fetch-pricing-strategy')

        cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12/engine', {
            fixture: 'erp/pricing-strategies/get_pricing_strategy_engine.json',
        }).as('fetch-pricing-strategy-low-margin-products')
    })

    it('Display the table headers and rows with permission', () => {
        visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/12/low-margins')

        cy.wait(['@fetch-pricing-strategy-low-margin-products', '@fetch-pricing-strategy'])

        cy.get('[data-context=erp-table]').as('table')

        cy.get('@table').find('thead th').as('headers')
        cy.get('@table').find('tr:first-of-type td').as('cells')
        ;['produit', 'catégorie', 'stock', 'concurrent le plus bas', 'canaux de vente', ''].forEach((header, index) => {
            if (Array.isArray(header)) {
                header.forEach((h) => cy.get('@headers').eq(index).should('contain', h))

                return
            }

            cy.get('@headers').eq(index).should('contain', header)
        })
        ;[
            [
                { text: 'MARMCR612NR', element: '[data-context=erp-article-item]' },
                { text: 'Marantz', element: '[data-context=erp-article-item]' },
                { text: '479,00 €', element: '[data-context=erp-article-item]' },
            ],
            [{ text: 'Amplificateurs' }],
            [{ text: '103' }],
            [{ text: 'AMAZON' }, { text: '456,78 € TTC' }],
        ].forEach((cell_elements, index) => {
            cell_elements.forEach((cell) => {
                if (Array.isArray(cell)) {
                    cell.forEach((el) => {
                        testCell(el, index)
                    })
                } else {
                    testCell(cell, index)
                }
            })
        })
        ;[
            [
                [
                    [{ text: 'son-video.com' }],
                    [{ text: '369,22 €' }, { text: '2.00 %' }],
                    [{ text: '372,53 €' }, { text: '1.33 %' }],
                ],
                [
                    [{ text: 'easylounge.com' }],
                    [{ text: '369,22 €' }, { text: '2.00 %' }],
                    [{ text: '372,53 €' }, { text: '1.33 %' }],
                ],
            ],
        ].forEach((row) => {
            cy.get('[data-context=sales-channel-prices]').find('tbody tr').as('rows')

            row.forEach((cell_elements, rowIndex) => {
                cell_elements.forEach((content, cellIndex) => {
                    content.forEach((item) => {
                        cy.get('@rows').eq(rowIndex).find('td').eq(cellIndex).should('contain', item.text)
                    })
                })
            })
        })

        cy.get('[data-context=assign-to-new-strategy]').should('not.exist')
        cy.get('[data-context=assign-to-other-strategy]').should('not.exist')
    })
})
