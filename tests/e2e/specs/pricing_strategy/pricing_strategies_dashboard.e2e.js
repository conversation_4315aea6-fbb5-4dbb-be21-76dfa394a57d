describe('View pricing strategies dashboard', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/pricing-strategies', {
            fixture: 'erp/pricing-strategies/cpost_pricing_strategies.json',
        }).as('fetch-pricing-strategies')

        cy.visit('/pricing-strategy/pricing-strategies')

        cy.get('[data-context=erp-page]').should('be.visible')
        cy.wait('@fetch-pricing-strategies')
    })

    it('Filters are well displayed', () => {
        cy.get('[data-context=dashboard-filters]').should('be.visible')

        cy.get('[data-context=status]')
            .should('contain', 'Statut')
            .find('[data-context=erp-multiselect]')
            .within(() => {
                const statuses = ['CREATED', 'ACTIVATED', 'DEACTIVATED', 'EXPIRED']
                cy.get('[data-context=tag]').should('have.length', statuses.length)
                statuses.forEach((s) => {
                    cy.get(`[data-context=tag]:contains(${s})`).should('be.visible')
                })
            })
    })

    it('Display the table headers and rows', () => {
        cy.get('[data-context=erp-table').as('table')

        cy.get('@table').find('thead th').as('headers')
        cy.get('@table').find('tr:first-of-type td').as('cells')
        ;['nom', 'produits', 'date de debut', 'date de fin', 'status'].forEach((header, index) => {
            if (Array.isArray(header)) {
                header.forEach((h) => cy.get('@headers').eq(index).should('contain', h))

                return
            }

            cy.get('@headers').eq(index).should('contain', header)
        })
        ;[
            [{ text: 'YAMAHA', element: '[data-context=erp-link]' }],
            [{ text: '0' }],
            [{ text: '27/07/2023' }],
            [{ text: '12/10/2024' }],
            [{ element: '[data-context=status-text]' }],
            [{ text: '', element: '[data-context=erp-toggle]' }],
        ].forEach((cell_elements, index) => {
            cell_elements.forEach((cell) => {
                if (undefined === cell.element) {
                    cy.get('@cells').eq(index).should('contain', cell.text)

                    return
                }

                cy.get('@cells').eq(index).find(cell.element).as('cell')

                if (undefined !== cell.text) {
                    cy.get('@cell').should('contain', cell.text)
                }

                if (undefined !== cell.attr) {
                    cell.attr.forEach((attr) => cy.get('@cell').attribute(attr.name).should('contain', attr.value))
                }
            })
        })
    })

    it('Should sort results', () => {
        cy.get('[data-context=erp-table]').find('thead th').as('headers')

        const columns_to_test = [
            { index: 0, field: 'name' },
            { index: 1, field: 'count_products' },
            { index: 2, field: 'starts_at' },
            { index: 3, field: 'ends_at' },
            { index: 4, field: 'activation_status' },
        ]

        columns_to_test.forEach(({ index, field }) => {
            cy.get('@headers').eq(index).click()
            cy.wait('@fetch-pricing-strategies').its('request.body').should('include', {
                limit: 30,
                order_by: field,
                order_direction: 'asc',
            })
            cy.get('@headers').eq(index).click()
            cy.wait('@fetch-pricing-strategies').its('request.body').should('include', {
                limit: 30,
                order_by: field,
                order_direction: 'desc',
            })
        })
    })
})
