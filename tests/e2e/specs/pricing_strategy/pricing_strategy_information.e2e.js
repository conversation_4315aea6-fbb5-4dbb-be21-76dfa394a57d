import { ARTICLE_PRICES_WRITE } from '../../../../src/apps/erp/permissions.js'

describe('View pricing strategy information', () => {
    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page, {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })
    }

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('POST', '**/api/erp/v1/sales-channel', {
            fixture: 'erp/sales-channel/cpost_sales_channels_light',
        }).as('cpost_sales_channels_light')

        cy.intercept('POST', '**/api/erp/v1/competitors', {
            fixture: 'erp/competitor/cpost_competitors',
        }).as('cpost_competitors')
    })

    context('With permission', function () {
        it('Create pricing strategy', function () {
            // freeze clock
            cy.clock(new Date(2024, 9, 1, 11, 40, 0, 0))

            visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/pricing-strategies')
            cy.get('[data-context=add-new-pricing-strategy-btn]').click()

            cy.wait(['@cpost_sales_channels_light', '@cpost_competitors'])

            cy.get('[data-context=slide-in-content]').should('be.visible')

            cy.get('[data-context=page-header]').find('h1').eq(1).should('contain', "Création d'une Stratégie de prix")

            cy.get('label:contains(Nom de la stratégie)')
                .closest('[data-context=name]')
                .find('input')
                .should('have.value', '')
                .type('test')

            cy.get('label:contains(Canaux de vente)')
                .closest('[data-context=sales-channels]')
                .should('be.visible')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'son-video.com')

            //remove focus
            cy.get('[data-context=weekend_min_margin_rate]').click()

            cy.get('label:contains(Concurrents)')
                .closest('[data-context=competitors]')
                .should('be.visible')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'AUDITORIUM')

            //leave multiselect
            cy.get('label:contains(Nom de la stratégie)').closest('[data-context=name]').find('input').click()

            cy.get('label:contains(Concurrents)').closest('[data-context=competitors]').should('be.visible')

            cy.get('label:contains(Date de début)')
                .closest('[data-context=starts-at]')
                .should('be.visible')
                .erpDatePicker('2')

            cy.get('label:contains(Date de fin)')
                .closest('[data-context=ends-at]')
                .should('be.visible')
                .erpDatePicker('7')

            cy.get('label:contains(Taux de marque minimum)')
                .closest('[data-context=weekdays_min_margin_rate]')
                .find('[data-context=erp-input-number]')
                .type('25')

            cy.get('label:contains(Écart)')
                .closest('[data-context=weekdays_increment_amount]')
                .find('[data-context=erp-input-number]')
                .type('2')

            cy.get('label:contains(Taux de marque minimum)')
                .closest('[data-context=weekend_min_margin_rate]')
                .find('[data-context=erp-input-number]')
                .type('35')

            cy.get('label:contains(Écart)')
                .closest('[data-context=weekend_increment_amount]')
                .find('[data-context=erp-input-number]')
                .type('3')

            cy.get('[data-context=pricing-strategy-information]').as('form')
            cy.get('@form').find('[data-context=submit-button]').as('button')
            cy.get('@form').find('[data-context=activate-button]').should('not.exist')
            cy.get('@form').find('[data-context=deactivate-button]').should('not.exist')

            cy.intercept('POST', '**/api/erp/v1/pricing-strategy', {
                delay: 200,
                statusCode: 200,
                body: { data: { pricing_strategy_id: 99 } },
            }).as('post_pricing_strategy')

            cy.get('@button').click()

            cy.wait('@post_pricing_strategy').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    activation_status: 'CREATED',
                    name: 'test',
                    sales_channels: [1],
                    competitors: ['AUDITORIUM'],
                    ends_at: '2024-10-07 00:00:00',
                    starts_at: '2024-10-02 00:00:00',
                    weekdays_increment_amount: 2,
                    weekdays_min_margin_rate: 25,
                    weekend_increment_amount: 3,
                    weekend_min_margin_rate: 35,
                })
            })

            cy.get('@windowOpen').should('be.calledWithMatch', /.*\/pricing-strategy\/99\/information/, '_blank')
        })

        it('Update pricing strategy', function () {
            cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12', {
                fixture: 'erp/pricing-strategies/get_pricing_strategy.json',
            }).as('fetch-pricing-strategy')

            visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/12/information')

            cy.get('[data-context=pricing-strategy-information]').as('form').should('be.visible')

            cy.wait('@fetch-pricing-strategy')

            cy.get('[data-context=page-header]').find('h1').should('contain', 'Edition de stratégie de prix')

            cy.get('label:contains(Nom de la stratégie)')
                .closest('[data-context=name]')
                .find('input')
                .should('have.value', 'Strategy SONOS')

            cy.get('label:contains(Canaux de vente)')
                .closest('[data-context=sales-channels]')
                .within(() => {
                    cy.root().should('be.visible')
                    cy.root().erpMultiselect('amaz', 'amazon.de') // add
                    cy.root().erpMultiselect('easy', 'easylounge.com') // remove
                })
            cy.get('[data-context=page-header]').click() // close multiselect

            cy.get('label:contains(Concurrents)')
                .closest('[data-context=competitors]')
                .within(() => {
                    cy.root().should('be.visible')
                    cy.root().erpMultiselect('amaz', 'AMAZON') // add
                    cy.root().erpMultiselect('cobr', 'COBRA') // remove
                })
            cy.get('[data-context=page-header]').click() // close multiselect

            cy.get('label:contains(Date de début)')
                .closest('[data-context=starts-at]')
                .within(() => {
                    cy.get('input').should('have.value', '03 sept. 2024 à 00:00')
                    cy.get('[data-context=erp-date-picker]').erpDatePicker('1')
                })

            cy.get('label:contains(Date de fin)')
                .closest('[data-context=ends-at]')
                .within(() => {
                    cy.get('input').should('have.value', '09 févr. 2025 à 00:00')
                    cy.get('[data-context=erp-date-picker]').erpDatePicker('16')
                })

            cy.get('label:contains(Taux de marque minimum)')
                .closest('[data-context=weekdays_min_margin_rate]')
                .find('[data-context=erp-input-number]')
                .should('have.value', '20')

            cy.get('label:contains(Écart)')
                .closest('[data-context=weekdays_increment_amount]')
                .find('[data-context=erp-input-number]')
                .should('have.value', '3')

            cy.get('label:contains(Taux de marque minimum)')
                .closest('[data-context=weekend_min_margin_rate]')
                .find('[data-context=erp-input-number]')
                .should('have.value', '10')

            cy.get('label:contains(Écart)')
                .closest('[data-context=weekend_increment_amount]')
                .find('[data-context=erp-input-number]')
                .should('have.value', '3')

            cy.get('@form').find('[data-context=submit-button]').as('button')

            cy.intercept('PUT', '**/api/erp/v1/pricing-strategy/12', {
                delay: 200,
            }).as('put_pricing_strategy')

            cy.get('@button').click()

            cy.wait('@put_pricing_strategy').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    pricing_strategy_id: 12,
                    activation_status: 'CREATED',
                    sales_channels: [7],
                    competitors: ['AMAZON'],
                    ends_at: '2025-02-16 00:00:00',
                    name: 'Strategy SONOS',
                    starts_at: '2024-09-01 00:00:00',
                    weekdays_increment_amount: 3,
                    weekdays_min_margin_rate: 20,
                    weekend_increment_amount: 3,
                    weekend_min_margin_rate: 10,
                })
            })
        })

        it('Activate pricing strategy', function () {
            cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12', {
                fixture: 'erp/pricing-strategies/get_pricing_strategy.json',
            }).as('fetch-pricing-strategy')

            cy.intercept('PUT', '**/api/erp/v1/pricing-strategy/12/activate', {
                statusCode: 204,
            }).as('activate-pricing-strategy')

            visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/12/information')

            cy.wait('@fetch-pricing-strategy')

            cy.get('[data-context=pricing-strategy-information]').should('be.visible')
            cy.get('[data-context=activate-button]').should('be.visible').should('not.be.disabled').click()

            cy.wait(['@activate-pricing-strategy', '@fetch-pricing-strategy'])
        })

        it('Deactivate pricing strategy', function () {
            cy.fixture('erp/pricing-strategies/get_pricing_strategy.json').then((fixture) => {
                fixture.data.pricing_strategy.activation_status = 'ACTIVATED'

                cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12', {
                    statusCode: 200,
                    body: fixture,
                }).as('fetch-activated-pricing-strategy')
            })

            cy.intercept('PUT', '**/api/erp/v1/pricing-strategy/12/deactivate', {
                statusCode: 204,
            }).as('deactivate-pricing-strategy')

            visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/12/information')

            cy.wait('@fetch-activated-pricing-strategy')

            cy.get('[data-context=pricing-strategy-information]').should('be.visible')
            cy.get('[data-context=deactivate-button]').should('be.visible').should('not.be.disabled')
            cy.get('[data-context=deactivate-button]').click()

            cy.wait('@deactivate-pricing-strategy')
            cy.wait('@fetch-activated-pricing-strategy')
        })

        it('Activation/deactivation buttons should be disabled when form is dirty', function () {
            cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12', {
                fixture: 'erp/pricing-strategies/get_pricing_strategy.json',
            }).as('fetch-pricing-strategy')

            visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/12/information')

            cy.wait('@fetch-pricing-strategy')

            cy.get('[data-context=pricing-strategy-information]').should('be.visible')

            cy.get('[data-context=activate-button]').should('not.be.disabled')

            cy.get('[data-context=name]').find('input').type('modified')
            cy.get('[data-context=activate-button]').should('be.disabled')
            cy.get('[data-context=activate-button]').parent().tooltip("Vous devez d'abord sauvegarder la stratégie")
        })
    })
    context('Without permission', function () {
        it('Create pricing strategy page', function () {
            visitPage([], '/pricing-strategy/pricing-strategies')
            cy.get('[data-context=add-new-pricing-strategy-btn]').click()

            cy.wait(['@cpost_sales_channels_light', '@cpost_competitors'])

            cy.get('[data-context=pricing-strategy-information]').as('form').should('be.visible')

            cy.get('[data-context=page-header]').find('h1').eq(1).should('contain', "Création d'une Stratégie de prix")

            cy.get('label:contains(Nom de la stratégie)')
                .closest('[data-context=name]')
                .find('input')
                .should('be.disabled')

            cy.get('label:contains(Canaux de vente)')
                .closest('[data-context=sales-channels]')
                .find('input')
                .should('be.disabled')

            cy.get('label:contains(Concurrents)')
                .closest('[data-context=competitors]')
                .find('input')
                .should('be.disabled')

            cy.get('label:contains(Date de début)')
                .closest('[data-context=starts-at]')
                .find('input')
                .should('be.disabled')

            cy.get('label:contains(Date de fin)').closest('[data-context=ends-at]').find('input').should('be.disabled')

            cy.get('label:contains(Taux de marque minimum)')
                .closest('[data-context=weekdays_min_margin_rate]')
                .find('[data-context=erp-input-number]')
                .should('be.disabled')

            cy.get('label:contains(Écart)')
                .closest('[data-context=weekdays_increment_amount]')
                .find('[data-context=erp-input-number]')
                .should('be.disabled')

            cy.get('label:contains(Taux de marque minimum)')
                .closest('[data-context=weekend_min_margin_rate]')
                .find('[data-context=erp-input-number]')
                .should('be.disabled')

            cy.get('label:contains(Écart)')
                .closest('[data-context=weekend_increment_amount]')
                .find('[data-context=erp-input-number]')
                .should('be.disabled')

            cy.get('@form').find('[data-context=submit-button]').should('be.disabled')
            cy.get('@form').find('[data-context=activate-button]').should('not.exist')
            cy.get('@form').find('[data-context=deactivate-button]').should('not.exist')
        })

        it('Update pricing strategy page', function () {
            cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12', {
                fixture: 'erp/pricing-strategies/get_pricing_strategy.json',
            }).as('fetch-pricing-strategy')

            visitPage([], '/pricing-strategy/12/information')

            cy.wait('@fetch-pricing-strategy')

            cy.get('[data-context=pricing-strategy-information]').as('form').should('be.visible')

            cy.get('[data-context=page-header]')
                .find('h1')
                .should('contain', 'Edition de stratégie de prix')
                .should('contain', 'Strategy SONOS')
                .should('contain', 'Créée')

            cy.get('label:contains(Nom de la stratégie)')
                .closest('[data-context=name]')
                .find('input')
                .should('have.value', 'Strategy SONOS')
                .should('be.disabled')

            cy.get('label:contains(Canaux de vente)')
                .closest('[data-context=sales-channels]')
                .find('input')
                .should('be.disabled')

            cy.get('label:contains(Concurrents)')
                .closest('[data-context=competitors]')
                .find('input')
                .should('be.disabled')

            cy.get('label:contains(Date de début)')
                .closest('[data-context=starts-at]')
                .find('input')
                .should('have.value', '03 sept. 2024 à 00:00')
                .should('be.disabled')

            cy.get('label:contains(Date de fin)')
                .closest('[data-context=ends-at]')
                .find('input')
                .should('have.value', '09 févr. 2025 à 00:00')
                .should('be.disabled')

            cy.get('label:contains(Taux de marque minimum)')
                .closest('[data-context=weekdays_min_margin_rate]')
                .find('[data-context=erp-input-number]')
                .should('have.value', '20')
                .should('be.disabled')

            cy.get('label:contains(Écart)')
                .closest('[data-context=weekdays_increment_amount]')
                .find('[data-context=erp-input-number]')
                .should('have.value', '3')
                .should('be.disabled')

            cy.get('label:contains(Taux de marque minimum)')
                .closest('[data-context=weekend_min_margin_rate]')
                .find('[data-context=erp-input-number]')
                .should('have.value', '10')
                .should('be.disabled')

            cy.get('label:contains(Écart)')
                .closest('[data-context=weekend_increment_amount]')
                .find('[data-context=erp-input-number]')
                .should('have.value', '3')
                .should('be.disabled')

            cy.get('@form').find('[data-context=submit-button]').should('be.disabled')
            cy.get('@form').find('[data-context=activate-button]').should('be.disabled')
            cy.get('@form').find('[data-context=deactivate-button]').should('not.exist')
        })
    })
})
