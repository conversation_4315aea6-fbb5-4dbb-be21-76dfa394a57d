import { ARTICLE_PRICES_WRITE } from '../../../../src/apps/erp/permissions.js'

describe('View pricing strategy products list', () => {
    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page)
    }

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('POST', '**/api/erp/v1/pricing-strategy/products*', {
            fixture: 'erp/pricing-strategies/cpost_pricing_strategy_products.json',
        }).as('fetch-pricing-strategy-products')

        cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12/products*', {
            body: {
                status: 'success',
                data: {
                    product_ids: [117735],
                },
            },
        }).as('get-pricing-strategy-products')

        cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12', {
            fixture: 'erp/pricing-strategies/get_pricing_strategy.json',
        }).as('fetch-pricing-strategy')

        cy.intercept('POST', '**/api/erp/v1/pricing-strategy/12/unassign-products', {
            statusCode: 200,
            body: { success: true },
        }).as('unassign-products')
    })

    context('With permission', () => {
        it('Should display products table with correct data', () => {
            visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/12/articles')

            cy.wait(['@fetch-pricing-strategy-products', '@fetch-pricing-strategy'])

            // Verify table is displayed
            cy.get('[data-context=erp-table]').as('table')
            cy.get('@table').find('thead th').as('headers')

            // Verify column headers
            ;['', 'Produit', 'Catégorie', 'Prix', 'Stock', 'Scrap'].forEach((header, index) => {
                cy.get('@headers').eq(index).should('contain', header)
            })

            // Verify product data in first row
            cy.get('@table').find('tbody tr').first().as('firstRow')
            cy.get('@firstRow').find('td').eq(1).should('contain', 'Q350 Noir')
            cy.get('@firstRow').find('td').eq(2).should('contain', 'Enceintes')
            cy.get('@firstRow').find('td').eq(3).should('contain', '559,50 €')
            cy.get('@firstRow').find('td').eq(3).should('contain', '50,8')
            cy.get('@firstRow').find('td').eq(4).should('contain', '3')
            cy.get('@firstRow').find('td').eq(5).should('contain', '04/07/2024')
            cy.get('@firstRow').find('td').eq(5).should('contain', '12:28')
        })

        it('Should allow selecting and removing products', () => {
            visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/12/articles')

            cy.wait(['@fetch-pricing-strategy-products', '@fetch-pricing-strategy'])

            // Verify delete button is initially disabled
            cy.get('[data-context=unassign-btn]').should('be.disabled')

            // Select first product
            cy.get('[data-context=erp-checkbox]').first().click()

            // Verify delete button is enabled after selection
            cy.get('[data-context=unassign-btn]').should('not.be.disabled')

            // Click delete button
            cy.get('[data-context=unassign-btn]').click()

            // Confirm deletion in modal
            cy.get('[data-context=confirm-modal]').should('be.visible')
            cy.get('[data-context=confirm-btn]').click()

            // Verify API call was made
            cy.wait('@unassign-products')

            // Verify table is refreshed
            cy.wait('@fetch-pricing-strategy-products')
        })
    })

    context('Without permission', () => {
        it('Should display products table in read-only mode', () => {
            visitPage([], '/pricing-strategy/12/articles')

            cy.wait(['@fetch-pricing-strategy-products', '@fetch-pricing-strategy'])

            // Verify table is displayed
            cy.get('[data-context=erp-table]').should('be.visible')

            // Verify action buttons are disabled
            cy.get('[data-context=add-products-btn]').should('be.disabled')
            cy.get('[data-context=unassign-btn]').should('be.disabled')

            // Verify checkboxes does not exists
            cy.get('[data-context=erp-checkbox]').should('not.exist')
        })

        it('Should display correct column names', () => {
            visitPage([], '/pricing-strategy/12/articles')

            cy.wait(['@fetch-pricing-strategy-products', '@fetch-pricing-strategy'])

            cy.get('[data-context=erp-table]').find('thead th').as('headers')

            // Verify all column headers are present and have correct text
            ;['Produit', 'Catégorie', 'Prix', 'Stock', 'Scrap'].forEach((header, index) => {
                cy.get('@headers').eq(index).should('contain', header)
            })
        })

        it('Should sort results', () => {
            visitPage([], '/pricing-strategy/12/articles')

            cy.wait(['@fetch-pricing-strategy-products', '@fetch-pricing-strategy'])

            cy.get('[data-context=erp-table]').find('thead th').as('headers')

            const columns_to_test = [
                { index: 0, field: 'article_name' },
                { index: 1, field: 'category' },
                { index: 2, field: 'selling_price' },
                { index: 3, field: 'stock' },
                { index: 4, field: 'last_scrapping_date' },
            ]

            columns_to_test.forEach(({ index, field }) => {
                cy.get('@headers').eq(index).click()
                cy.wait('@fetch-pricing-strategy-products').its('request.body').should('include', {
                    order_by: field,
                    order_direction: 'desc',
                })
                cy.get('@headers').eq(index).click()
                cy.wait('@fetch-pricing-strategy-products').its('request.body').should('include', {
                    order_by: field,
                    order_direction: 'asc',
                })
            })
        })
    })
})
