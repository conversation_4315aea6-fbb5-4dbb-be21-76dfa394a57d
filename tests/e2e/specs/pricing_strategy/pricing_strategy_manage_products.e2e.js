import { ARTICLE_PRICES_WRITE } from '../../../../src/apps/erp/permissions.js'
import {
    getMagicSearchArticles,
    getMagicSearchArticlesAggregations,
    getMagicSearchResponse,
} from '../magic_search/helper'

describe('View pricing strategy products list', () => {
    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page)
    }

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('POST', '**/api/erp/v1/pricing-strategy/products*', {
            fixture: 'erp/pricing-strategies/cpost_pricing_strategy_products.json',
        }).as('fetch-pricing-strategy-products')

        cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12/product-ids', {
            body: {
                status: 'success',
                data: {
                    product_ids: [123456],
                },
            },
        }).as('get-pricing-strategy-product-ids')

        cy.intercept('GET', '**/api/erp/v1/pricing-strategy/12', {
            fixture: 'erp/pricing-strategies/get_pricing_strategy.json',
        }).as('fetch-pricing-strategy')

        cy.intercept('POST', '**/api/erp/v1/pricing-strategy/12/unassign-products', {
            statusCode: 200,
            body: { success: true },
        }).as('unassign-products')

        cy.intercept('PUT', '**/api/erp/v1/pricing-strategy/12/products', {
            statusCode: 200,
            body: { success: true },
        }).as('assign-products')

        cy.intercept('GET', '**/api/erp/v1/magic-search**', {
            statusCode: 200,
            body: getMagicSearchArticlesAggregations(),
        }).as('articles-aggregations')

        cy.intercept('POST', '**/api/erp/v1/magic-search**', {
            statusCode: 200,
            body: getMagicSearchResponse(getMagicSearchArticles(16), { from: 5, size: 15 }),
        }).as('post_magic_search')
    })

    context('With permission', () => {
        it('Add products to pricing strategy successfully', () => {
            visitPage([ARTICLE_PRICES_WRITE], '/pricing-strategy/12/articles')

            // load
            cy.wait(['@fetch-pricing-strategy-products', '@fetch-pricing-strategy'])

            // open the manager modal overlay
            cy.get('[data-context=add-products-btn]').click()

            // assigned list reload on first page + articles elk aggregations for filters
            cy.wait(['@fetch-pricing-strategy-products', '@get-pricing-strategy-product-ids', '@articles-aggregations'])

            // Verify column headers on the assigned table
            cy.get('[data-context=unassign-table]').as('table')
            cy.get('@table').find('thead th').as('headers')
            ;['', 'Produit', 'Catégorie', 'Prix', 'Stock'].forEach((header, index) => {
                cy.get('@headers').eq(index).should('contain', header)
            })

            // Verify column headers on the search table
            cy.get('[data-context=assign-table]').as('table')
            cy.get('@table').find('thead th').as('headers')
            ;['', 'Produit', 'Catégorie', 'Prix', 'Stock', 'Scrap'].forEach((header, index) => {
                cy.get('@headers').eq(index).should('contain', header)
            })

            // Search button should be disabled
            cy.get('[data-context=search-btn]').should('be.disabled')

            // Unassign button should be disabled
            cy.get('[data-context=unassign-modal-btn]').should('be.disabled')

            // Assign button should be disabled
            cy.get('[data-context=assign-btn]').should('be.disabled')

            // Unassign product
            cy.get('[data-context=unassign-table] [data-context=toggle-all-checkbox]').click()
            cy.get('[data-context=unassign-modal-btn]').should('not.be.disabled')
            cy.get('[data-context=unassign-modal-btn]').click()

            // Verify API call was made
            cy.wait('@unassign-products')

            // Verify table is refreshed
            cy.wait(['@fetch-pricing-strategy-products', '@get-pricing-strategy-product-ids'])

            // Unassign button should be disabled again
            cy.get('[data-context=unassign-modal-btn]').should('be.disabled')

            // Select products
            cy.get('[data-context=brands]').erpMultiselect('', 'KEF')
            cy.get('[data-context=categories]').erpMultiselect('', 'Enceintes sans fil')
            cy.get('[data-context=search-btn]').click()

            cy.wait('@post_magic_search').then((xhr) => {
                expect(xhr.request.body).to.deep.equals({
                    search_terms: '',
                    context: 'articles',
                    size: 50,
                    from: 0,
                    excludes: [{ terms: { article_id: [123456] } }],
                    filters: [
                        { terms: { status: ['oui', 'last'] } },
                        { terms: { type: ['article'] } },
                        { terms: { 'brand.id': ['77'] } },
                        { terms: { 'category.id': ['138'] } },
                    ],
                })
            })

            // Assign product
            cy.get('[data-context=assign-table] [data-context=toggle-all-checkbox]').click()
            cy.get('[data-context=assign-btn]').should('not.be.disabled')
            cy.get('[data-context=assign-btn]').click()

            // Verify API call was made
            cy.wait('@assign-products')

            // Assign button should be disabled again
            cy.get('[data-context=assign-btn]').should('be.disabled')

            // Verify table is refreshed
            cy.wait(['@fetch-pricing-strategy-products', '@post_magic_search'])
        })
    })
})
