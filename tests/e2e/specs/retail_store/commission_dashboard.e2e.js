import { CALL_CENTER_COMMISSIONS_READ, RET<PERSON><PERSON>_STORE_COMMISSIONS_READ } from '../../../../src/apps/erp/permissions'

const PAGE_RETAIL_STORE = '/retail-store/commission-dashboard'
const PAGE_CALL_CENTER = '/call-center/commission-dashboard'
const WARNING_CLASS = 'text-yellow-400'
const ERROR_CLASS = 'text-red-600'
const LONG_DURATION_WARRANTY_ATTACHMENT_RATE_INDEX = 37

describe('Retail Store commission dashboard', function () {
    describe('Without permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()
        })

        it('display error page', () => {
            cy.checkUnauthorizedAccessFor(PAGE_RETAIL_STORE)
        })
    })

    describe('With permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([RETAIL_STORE_COMMISSIONS_READ, CALL_CENTER_COMMISSIONS_READ])

            cy.mockDate(new Date(2022, 6, 1, 15, 44, 0, 0))

            cy.intercept('GET', '**/api/erp/v1/seller-commission/commissions?*', {
                fixture: 'erp/seller-commission/commissions/seller-commissions.json',
            }).as('fetch_commissions')
        })

        it('has expected content for retail store', () => {
            cy.visit(PAGE_RETAIL_STORE)
            cy.toggleMenu()
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.get('at')).to.eq('2022-07')
                expect(params.get('type')).to.eq('retail_store')
            })

            cy.get('[data-context=page-header]').should('contain', 'Performances vendeurs retail')
            cy.get('[data-context=month-picker]')
                .should('be.visible')
                .find('.multiselect__element')
                .should('have.length', 13)
                .as('options')
            cy.get('@options').eq(0).should('contain', 'juillet 2022')
            cy.get('@options').eq(12).should('contain', 'juillet 2021')

            cy.get('[data-context=commissions-table]').should('be.visible').as('table')

            //
            // Header line
            //

            // Dynamic datas
            cy.get('[data-context=commissions-table] thead th')
                .should('contain', 'VENTES PRO / B2B (MINI 23%)')
                .should('contain', 'UNIVERS HIFI (MINI 36%)')
                .should('contain', 'UNIVERS IMAGE (MINI 20%)')
                .should('contain', 'EQUIPE')
                .should('contain', 'MANAGER')

            //
            // Seller line
            //

            let location_index = 0

            // Seller data
            cy.get('@table').selectCell(1, location_index++).should('contain', 'Mattéo RIVOLI')
            cy.get('@table').selectCell(1, location_index++).should('contain', 'R - 1').should('contain', 'sur 3 mois')

            // Primes
            cy.get('@table').selectCell(1, location_index++).should('contain', '691 €').should('contain', '12 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '0 €').should('contain', '-')
            cy.get('@table').selectCell(1, location_index++).should('contain', '102 €').should('contain', '78 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '0 €').should('contain', '87 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '123 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '907 €').should('contain', '44 €')
            cy.get('@table')
                .selectCell(1, location_index++)
                .should('contain', '1 877 €')
                .should('contain.text', 'dont rattrapage177 €')

            // Global
            location_index++
            cy.get('@table')
                .selectCell(1, location_index++)
                .should('contain', '73 748 €')
                .should('contain', '124 956 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '26 585 €').should('contain', '39 009 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '36,20 %').should('contain', '31,22 %')

            // Objectif du magasin
            location_index++
            cy.get('@table').selectCell(1, location_index++).should('contain', '-')
            cy.get('@table').selectCell(1, location_index++).should('contain', '-')

            // B2C
            location_index++
            cy.get('@table').selectCell(1, location_index++).should('contain', '60 195 €').should('contain', '84 454 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '123 €').should('contain', '234 230 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '23 032 €').should('contain', '28 921 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '422 €').should('contain', '73 454 €')
            cy.get('@table')
                .selectCell(1, location_index++)
                .should('contain', '343,09 %')
                .should('contain', '31,36 %')
                .find('span')
                .as('values')
            cy.get('@values').eq(0).should('not.have.class', ERROR_CLASS)
            cy.get('@values').eq(1).should('have.class', ERROR_CLASS)

            // B2C Image et informatique
            cy.get('@table').selectCell(1, location_index++).should('contain', '1 905 €').should('contain', '15 670 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '1 111 €').should('contain', '4 444 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '297 €').should('contain', '3 505 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '222 €').should('contain', '3 333 €')
            cy.get('@table')
                .selectCell(1, location_index++)
                .should('contain', '19,98 %')
                .should('contain', '75,00 %')
                .find('span')
                .as('values')
            cy.get('@values').eq(0).should('have.class', ERROR_CLASS)
            cy.get('@values').eq(1).should('not.have.class', ERROR_CLASS)

            // B2B
            location_index++
            cy.get('@table').selectCell(1, location_index++).should('contain', '11 787 €').should('contain', '24 832 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '1 333 €').should('contain', '88 888 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '3 395 €').should('contain', '6 582 €')
            cy.get('@table').selectCell(1, location_index++).should('contain', '4 444 €').should('contain', '66 666 €')
            cy.get('@table')
                .selectCell(1, location_index++)
                .should('contain', '333,38 %')
                .should('contain', '75,00 %')
                .find('span')
                .as('values')
            cy.get('@values').eq(0).should('not.have.class', ERROR_CLASS)
            cy.get('@values').eq(1).should('not.have.class', ERROR_CLASS)

            // GLD
            location_index++
            cy.get('@table').selectCell(1, location_index++).should('contain', '1/5').should('contain', '30/60')
            cy.get('@table').selectCell(1, location_index++).should('contain', '0/0').should('contain', '23/58')
            cy.get('@table').selectCell(1, location_index++).should('contain', '0 €').should('contain', '26 €')
            cy.get('@table')
                .selectCell(1, LONG_DURATION_WARRANTY_ATTACHMENT_RATE_INDEX)
                .should('contain', '0,00 %')
                .should('contain', '39,66 %')
                .find('span')
                .as('values')
            cy.get('@values').eq(0).should('have.class', ERROR_CLASS)
            cy.get('@values').eq(1).should('not.have.class', ERROR_CLASS)

            //
            // Retail line
            //
            cy.get('@table').selectCell(0, 0).should('contain', 'Antibes')
            cy.get('@table').selectCell(0, 1).should('contain', '-').should('contain', 'sur 3 mois')

            // global
            cy.get('@table').selectCell(0, 10).should('contain', '12 345 €').should('contain', '616 642 €')
            cy.get('@table').selectCell(0, 11).should('contain', '6 789 €').should('contain', '203 623 €')
            cy.get('@table').selectCell(0, 12).should('contain', '54,99 %').should('contain', '33,02 %')

            // objectif magasin
            cy.get('@table').selectCell(0, 14).should('contain', '98 340 €').should('contain', '445 761 €')
            cy.get('@table')
                .selectCell(0, 15)
                .should('contain', '98,00 %')
                .should('contain', '138,33 %')
                .find('span')
                .as('values')
            cy.get('@values').eq(0).should('have.class', ERROR_CLASS)
            cy.get('@values').eq(1).should('not.have.class', ERROR_CLASS)

            // B2C HIFI
            cy.get('@table').selectCell(0, 21).should('contain', '18,96 %').should('contain', '50,00 %')

            //
            // Check ordering
            //
            cy.get('@table').selectCell(0, 0).should('contain', 'Antibes')
            cy.get('@table').selectCell(12, 0).should('contain', 'Yannick')
            cy.get('@table').selectCell(13, 0).should('contain', 'Alain')
            cy.get('@table').selectCell(15, 0).should('contain', 'Salah')

            //
            // Specials cases
            //

            cy.get('@table').selectCell(15, 1).should('contain', 'V - 3')

            // Primes
            cy.get('@table').selectCell(11, 6).should('contain', '907 €')
            cy.get('@table').selectCell(11, 7).should('contain', '-')

            // Objectif du magasin
            cy.get('@table')
                .selectCell(2, 15)
                .should('contain', '103,00 %')
                .find('span')
                .eq(0)
                .should('not.have.class', ERROR_CLASS)

            // B2C
            cy.get('@table')
                .selectCell(7, 21)
                .should('contain', '3,38 %')
                .find('span')
                .eq(0)
                .should('have.class', ERROR_CLASS)

            // B2C Image et informatique
            cy.get('@table')
                .selectCell(7, 26)
                .should('contain', '52,49 %')
                .find('span')
                .eq(0)
                .should('not.have.class', ERROR_CLASS)

            // B2B
            cy.get('@table')
                .selectCell(3, 32)
                .should('contain', '3,35 %')
                .find('span')
                .eq(0)
                .should('have.class', ERROR_CLASS)

            // GLD
            cy.get('@table').selectCell(15, 34).should('contain', '1/3').should('contain', '0/58')
            cy.get('@table').selectCell(15, 35).should('contain', '1/3').should('contain', '0/58')
            cy.get('@table').selectCell(15, 36).should('contain', '100 €')
            cy.get('@table')
                .selectCell(15, LONG_DURATION_WARRANTY_ATTACHMENT_RATE_INDEX)
                .should('contain', '33,33 %')
                .find('span')
                .eq(0)
                .should('not.have.class', ERROR_CLASS)

            // GLD reach the intermediate threshold
            cy.get('@table')
                .selectCell(3, LONG_DURATION_WARRANTY_ATTACHMENT_RATE_INDEX)
                .should('contain', '13,33 %')
                .should('contain', '13,79 %')
                .find('span')
                .as('values')
            cy.get('@values').eq(0).should('have.class', WARNING_CLASS)
            cy.get('@values').eq(1).should('have.class', WARNING_CLASS)
        })

        it('has expected content for call center', () => {
            // it's the same as the store but with fewer data
            cy.visit(PAGE_CALL_CENTER)
            cy.toggleMenu()
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.get('at')).to.eq('2022-07')
                expect(params.get('type')).to.eq('call_center')
            })

            cy.get('[data-context=page-header]').should('contain', 'Performances vendeurs call center')
            cy.get('[data-context=month-picker]')
                .should('be.visible')
                .find('.multiselect__element')
                .should('have.length', 13)
                .eq(0)
                .should('contain', 'juillet 2022')

            cy.get('[data-context=commissions-table]').should('be.visible').as('table')

            //
            // Header line
            //

            // Dynamic datas
            cy.get('[data-context=commissions-table] thead th')
                .should('contain', 'VENTES PRO / B2B (MINI 23%)')
                .should('contain', 'UNIVERS HIFI')
                .should('contain', 'UNIVERS IMAGE')
                .should('not.contain', 'EQUIPE')
                .should('not.contain', 'MANAGER')

            //
            // Seller line
            //

            // Seller data
            cy.get('@table').selectCell(1, 0).should('contain', 'Mattéo RIVOLI')
            cy.get('@table').selectCell(1, 1).should('contain', 'R - 1').should('contain', 'sur 3 mois')

            // Primes
            cy.get('@table').selectCell(1, 2).should('contain', '691 €').should('contain', '12 €')
            cy.get('@table').selectCell(1, 3).should('contain', '0 €').should('contain', '-')
            cy.get('@table').selectCell(1, 4).should('contain', '102 €').should('contain', '78 €')
            cy.get('@table').selectCell(1, 5).should('contain', '0 €').should('contain', '87 €')
            cy.get('@table')
                .selectCell(1, 6)
                .should('contain', '1 877 €')
                .should('contain.text', 'dont rattrapage177 €')
        })

        it('should display red threshold only when rounded value is below the threshold', function () {
            cy.fixture('erp/seller-commission/commissions/seller-commissions.json').then((payload) => {
                const c = payload.data.commissions[0]

                // juuust a little below the thresholds
                c.goals.turnover_tax_excluded_rate = 99.999
                c.b2c_hifi.margin_tax_excluded_commissionable =
                    (c.b2c_hifi.turnover_tax_excluded_commissionable * (c.b2c_hifi.margin_rate_threshold - 0.001)) / 100
                c.b2c_image.margin_tax_excluded_commissionable =
                    (c.b2c_image.turnover_tax_excluded_commissionable * (c.b2c_image.margin_rate_threshold - 0.001)) /
                    100
                c.b2b.margin_tax_excluded_commissionable =
                    (c.b2b.turnover_tax_excluded_commissionable * (c.b2b.margin_rate_threshold - 0.001)) / 100
                c.long_duration_warranty.quantity_warranty_sold_commissionable = 14999
                c.long_duration_warranty.quantity_eligible_products_commissionable = 100000

                cy.intercept('GET', '**/api/erp/v1/seller-commission/commissions?*', {
                    statusCode: 200,
                    body: payload,
                }).as('fetch_commissions')

                cy.visit(PAGE_RETAIL_STORE)

                cy.get('[data-context=commissions-table]').should('be.visible').as('table')
                cy.get('@table').selectCell(5, 0).should('contain', 'Anthony RIVOLI')
                cy.get('@table').selectCell(4, 15).should('contain', '100,00 %').should('not.have.class', ERROR_CLASS)
                cy.get('@table').selectCell(5, 21).should('contain', '36,00 %').should('not.have.class', ERROR_CLASS)
                cy.get('@table').selectCell(5, 26).should('contain', '20,00 %').should('not.have.class', ERROR_CLASS)
                cy.get('@table').selectCell(5, 32).should('contain', '23,00 %').should('not.have.class', ERROR_CLASS)
                cy.get('@table')
                    .selectCell(5, LONG_DURATION_WARRANTY_ATTACHMENT_RATE_INDEX)
                    .should('contain', '15,00 %')
                    .should('not.have.class', ERROR_CLASS)
            })
        })

        it('should not display yellow font when intermediate threshold is null', function () {
            cy.fixture('erp/seller-commission/commissions/seller-commissions.json').then((payload) => {
                const c = payload.data.commissions[0]

                // juuust a little below the thresholds
                c.long_duration_warranty.quantity_warranty_sold = 0
                c.long_duration_warranty.quantity_eligible_products = 0
                c.long_duration_warranty.attachment_rate_intermediate_threshold = null

                cy.intercept('GET', '**/api/erp/v1/seller-commission/commissions?*', {
                    statusCode: 200,
                    body: payload,
                }).as('fetch_commissions')

                cy.visit(PAGE_RETAIL_STORE)

                cy.get('[data-context=commissions-table]').should('be.visible').as('table')
                cy.get('@table').selectCell(5, 0).should('contain', 'Anthony RIVOLI')
                cy.get('@table')
                    .selectCell(5, LONG_DURATION_WARRANTY_ATTACHMENT_RATE_INDEX)
                    .find('span')
                    .eq(0)
                    .should('contain', '0,00 %')
                    .should('not.have.class', WARNING_CLASS)
            })
        })

        it('should not display any error color font when thresholds are null', function () {
            cy.fixture('erp/seller-commission/commissions/seller-commissions.json').then((payload) => {
                const c = payload.data.commissions[0]

                c.long_duration_warranty.quantity_warranty_sold = 0
                c.long_duration_warranty.quantity_eligible_products = 0
                c.long_duration_warranty.attachment_rate_threshold = null
                c.long_duration_warranty.attachment_rate_intermediate_threshold = null

                cy.intercept('GET', '**/api/erp/v1/seller-commission/commissions?*', {
                    statusCode: 200,
                    body: payload,
                }).as('fetch_commissions')

                cy.visit(PAGE_RETAIL_STORE)

                cy.get('[data-context=commissions-table]').should('be.visible').as('table')
                cy.get('@table').selectCell(5, 0).should('contain', 'Anthony RIVOLI')
                cy.get('@table')
                    .selectCell(5, LONG_DURATION_WARRANTY_ATTACHMENT_RATE_INDEX)
                    .find('span')
                    .eq(0)
                    .should('contain', '0,00 %')
                    .should('not.have.class', WARNING_CLASS)
                    .should('not.have.class', ERROR_CLASS)
            })
        })

        it('should not display any computed rate when values are null', function () {
            cy.fixture('erp/seller-commission/commissions/seller-commissions.json').then((payload) => {
                const c = payload.data.commissions[0]

                c.long_duration_warranty.quantity_warranty_sold_commissionable = null
                c.long_duration_warranty.quantity_eligible_products_commissionable = null
                c.catch_up_three_months_long_duration_warranty.quantity_warranty_sold_commissionable = null
                c.catch_up_three_months_long_duration_warranty.quantity_eligible_products_commissionable = null

                cy.intercept('GET', '**/api/erp/v1/seller-commission/commissions?*', {
                    statusCode: 200,
                    body: payload,
                }).as('fetch_commissions')

                cy.visit(PAGE_RETAIL_STORE)

                cy.get('[data-context=commissions-table]').should('be.visible').as('table')
                cy.get('@table').selectCell(5, 0).should('contain', 'Anthony RIVOLI')
                cy.get('@table').selectCell(5, LONG_DURATION_WARRANTY_ATTACHMENT_RATE_INDEX).find('span').as('values')
                cy.get('@values')
                    .eq(0)
                    .should('not.contain', '0,00 %')
                    .should('contain', '-')
                    .should('not.have.class', WARNING_CLASS)
                    .should('not.have.class', ERROR_CLASS)
                cy.get('@values')
                    .eq(1)
                    .should('not.contain', '0,00 %')
                    .should('contain', '-')
                    .should('not.have.class', WARNING_CLASS)
                    .should('not.have.class', ERROR_CLASS)
            })
        })

        it('reload data on month selection and update search parameter', () => {
            cy.visit(PAGE_RETAIL_STORE)
            cy.toggleMenu()
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.get('at')).to.eq('2022-07')
                expect(params.get('type')).to.eq('retail_store')
            })
            cy.get('[data-context="month-picker"]').multiselect('', 'avril 2022', true)
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.has('at')).to.eq(true)
                expect(params.get('at')).to.eq('2022-04')
            })

            cy.location().should((loc) => {
                expect(loc.search).to.eq('?at=2022-04')
            })
        })

        it('load data on month selected in the url', () => {
            cy.visit(`${PAGE_RETAIL_STORE}?at=2022-03`)
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.has('at')).to.eq(true)
                expect(params.get('at')).to.eq('2022-03')
            })
            cy.get('[data-context=month-picker] .multiselect__single').should('contain', 'mars 2022')
        })

        it('display an failure error', () => {
            cy.intercept('GET', '**/api/erp/v1/seller-commission/commissions?*', {
                statusCode: 500,
                body: {
                    code: 500,
                    message: 'Un message custom de l’API',
                    data: {},
                },
            }).as('fetch_commissions')

            cy.visit(PAGE_RETAIL_STORE)

            cy.toast(`Une erreur est survenue`, 'danger')
        })

        it('display an alert when empty data', () => {
            cy.intercept('GET', '**/api/erp/v1/seller-commission/commissions?*', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        commissions: [],
                    },
                },
            }).as('fetch_commissions')

            cy.visit(PAGE_RETAIL_STORE)

            cy.get('[data-context=alert-info]').should('be.visible').should('contain', 'Aucune donnée à afficher.')
        })
    })
})
