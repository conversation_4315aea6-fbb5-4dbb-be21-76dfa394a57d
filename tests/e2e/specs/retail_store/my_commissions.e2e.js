const PAGE = '/retail-store/my-commissions'
const COMMISSIONS_API = '**/api/erp/v1/seller-commission/my-commissions?*'
const FIXTURE_FILE = 'erp/seller-commission/my-commissions/my_commissions.json'

const WARNING_CLASS = 'bg-yellow-50'
const ERROR_CLASS = 'bg-red-50'

const testTable = (table) => {
    // content from fixture file => has data
    cy.get(`[data-context=${table.data_context}]`)
        .should('be.visible')
        .should('not.have.class', ERROR_CLASS)
        .as('block')

    table?.contexts?.forEach((context) => {
        cy.get('@block').find(`[data-context=${context.data_context}]`).should('contain', context.value)
    })

    table.data.forEach((cell_data, line_number) => {
        if (cell_data.label) {
            cy.get('@block')
                .find(`[data-context=line-${line_number}]`)
                .should('contain', cell_data.label)
                .should('contain', cell_data.value)
        }
    })
}

describe('My commissions page', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.mockDate(new Date(2022, 6, 1, 15, 44, 0, 0))

        cy.intercept('GET', COMMISSIONS_API, { fixture: FIXTURE_FILE }).as('fetch_commissions')

        cy.visit(PAGE)
        cy.toggleMenu()
        cy.wait('@fetch_commissions').then((xhr) => {
            const params = new URLSearchParams(new URL(xhr.request.url).search)
            expect(params.get('at')).to.eq('2022-07')
        })
    })

    it('should reload data on month selection and update search parameter', () => {
        cy.get('[data-context="month-picker"]').multiselect('', 'avril 2022', true)
        cy.wait('@fetch_commissions').then((xhr) => {
            const url = new URL(xhr.request.url)
            const params = new URLSearchParams(url.search)
            expect(params.has('at')).to.eq(true)
            expect(params.get('at')).to.eq('2022-04')
        })

        cy.location().should((loc) => {
            expect(loc.search).to.eq('?at=2022-04')
        })
    })

    it('should load data on month selected in the url', () => {
        cy.visit(`${PAGE}?at=2022-03`)
        cy.wait('@fetch_commissions').then((xhr) => {
            const url = new URL(xhr.request.url)
            const params = new URLSearchParams(url.search)
            expect(params.has('at')).to.eq(true)
            expect(params.get('at')).to.eq('2022-03')
        })
        cy.get('[data-context=month-picker] .multiselect__single').should('contain', 'mars 2022')
        cy.get('[data-context=show-quote-btn]').should('contain', 'Mes devis en cours')
    })

    it('should display an error on failure', () => {
        cy.intercept('GET', COMMISSIONS_API, {
            statusCode: 500,
            body: {
                code: 500,
                message: 'Un message custom de l’API',
                data: {},
            },
        }).as('fetch_commissions')

        cy.visit(PAGE)

        cy.toast(`Une erreur est survenue`, 'danger')
    })

    it('should display an alert when empty data', () => {
        cy.intercept('GET', COMMISSIONS_API, {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    commissions: [],
                },
            },
        }).as('fetch_commissions')

        cy.visit(PAGE)

        cy.get('[data-context=alert-info]')
            .should('be.visible')
            .should('contain', 'Aucune donnée à afficher pour le mois sélectionné.')
    })

    describe('Bonus block by bonus block', function () {
        it('should display b2c_image data', function () {
            // default store worker
            let table = {
                data_context: 'b2c_image_bonus',
                contexts: [
                    { data_context: 'title', value: 'Prime B2C univers image' },
                    { data_context: 'bonus', value: '23 €' },
                ],
                data: [
                    { label: 'CA HT sans remise (+dsk et pcult)', value: '1 461 €' },
                    { label: 'Indice marge sans remise', value: '235 €' },
                    { label: 'Tx de marque sans remise', value: '16,07 %' },
                    {},
                    { label: 'CA HT avec remise', value: '444 €' },
                    { label: 'Indice marge avec remise', value: '555 €' },
                    { label: 'Tx de marque', value: '125,00 %' },
                    { label: 'Objectif mini', value: '20,00 %' },
                    { label: 'Rattrapage sur 3 mois :', value: '68 €' },
                    { label: 'CA HT sans remise (+dsk et pcult)', value: '9 004 €' },
                    { label: 'Indice marge sans remise', value: '-4 272 €' },
                    { label: 'Tx de marque sans remise', value: '-47,44 %' },
                    {},
                    { label: 'CA HT avec remise', value: '6 666 €' },
                    { label: 'Indice marge avec remise', value: '7 777 €' },
                    { label: 'Tx de marque', value: '116,67 %' },
                    { label: 'Objectif mini', value: '20,00 %' },
                ],
            }

            testTable(table)

            // has not the bonus, neither the catch-up
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].b2c_image.margin_tax_excluded_commissionable = 1
                payload.data.commissions[0].catch_up_three_months_bonuses_overview.b2c_image = null

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2c_image_bonus]')
                    .should('have.class', ERROR_CLASS)
                    .find('[data-context=line-8]')
                    .should('contain', 'Rattrapage sur 3 mois :')
                    .should('contain', 'Aucun')
            })

            // threshold not ok
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.b2c_image = 0
                payload.data.commissions[0].b2c_image.margin_rate_threshold = 500000

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2c_image_bonus]').should('have.class', ERROR_CLASS)
            })

            // juuuust below the threshold should be ok when rounded
            cy.fixture(FIXTURE_FILE).then((payload) => {
                const c = payload.data.commissions[0]
                c.b2c_image.margin_tax_excluded_commissionable =
                    (c.b2c_image.turnover_tax_excluded_commissionable * (c.b2c_image.margin_rate_threshold - 0.001)) /
                    100

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2c_image_bonus] [data-context=line-7]').should('contain', '20,00 %')
                cy.get('[data-context=b2c_image_bonus]').should('not.have.class', ERROR_CLASS)
            })

            // non relevant data
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.b2c_image = null
                payload.data.commissions[0].b2c_image.turnover_tax_excluded = null
                payload.data.commissions[0].b2c_image.turnover_tax_excluded_commissionable = null
                payload.data.commissions[0].b2c_image.margin_tax_excluded = null
                payload.data.commissions[0].b2c_image.margin_tax_excluded_commissionable = null
                payload.data.commissions[0].b2c_image.margin_rate_threshold = null
                payload.data.commissions[0].catch_up_three_months_bonuses_overview.b2c_image = null
                payload.data.commissions[0].catch_up_three_months_b2c_image.turnover_tax_excluded = null
                payload.data.commissions[0].catch_up_three_months_b2c_image.turnover_tax_excluded_commissionable = null
                payload.data.commissions[0].catch_up_three_months_b2c_image.margin_tax_excluded = null
                payload.data.commissions[0].catch_up_three_months_b2c_image.margin_tax_excluded_commissionable = null
                payload.data.commissions[0].catch_up_three_months_b2c_image.margin_rate_threshold = null

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                table = {
                    data_context: 'b2c_image_bonus',

                    data: [
                        { label: 'CA HT sans remise (+dsk et pcult)', value: '-' },
                        { label: 'Indice marge sans remise', value: '-' },
                        { label: 'Tx de marque sans remise', value: '-' },
                        {},
                        { label: 'CA HT avec remise', value: '-' },
                        { label: 'Indice marge avec remise', value: '-' },
                        { label: 'Tx de marque', value: '-' },
                        { label: 'Objectif mini', value: '-' },
                        { label: 'Rattrapage sur 3 mois :', value: 'Aucun' },
                        { label: 'CA HT sans remise (+dsk et pcult)', value: '-' },
                        { label: 'Indice marge sans remise', value: '-' },
                        { label: 'Tx de marque sans remise', value: '-' },
                        {},
                        { label: 'CA HT avec remise', value: '-' },
                        { label: 'Indice marge avec remise', value: '-' },
                        { label: 'Tx de marque', value: '-' },
                        { label: 'Objectif mini', value: '-' },
                    ],
                }

                testTable(table)
            })

            // call center worker
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].warehouse_role = 'CALL_CENTER_SELLER'

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2c_image_bonus]').should('exist')
            })
        })

        it('should display b2c_hifi data', function () {
            // default store worker
            let table = {
                data_context: 'b2c_hifi_bonus',
                contexts: [
                    { data_context: 'title', value: 'Prime B2C univers hifi' },
                    { data_context: 'bonus', value: '849 €' },
                ],
                data: [
                    { label: 'CA HT sans remise (+dsk et pcult)', value: '59 973 €' },
                    { label: 'Indice marge sans remise', value: '27 987 €' },
                    { label: 'Tx de marque sans remise', value: '46,67 %' },
                    {},
                    { label: 'CA HT avec remise', value: '222 €' },
                    { label: 'Indice marge avec remise', value: '333 €' },
                    { label: 'Tx de marque', value: '150,00 %' },
                    { label: 'Objectif mini', value: '36,00 %' },
                    { label: 'Rattrapage sur 3 mois :', value: '12 €' },
                    { label: 'CA HT sans remise (+dsk et pcult)', value: '75 566 €' },
                    { label: 'Indice marge sans remise', value: '28 922 €' },
                    { label: 'Tx de marque sans remise', value: '38,27 %' },
                    {},
                    { label: 'CA HT avec remise', value: '8 888 €' },
                    { label: 'Indice marge avec remise', value: '9 999 €' },
                    { label: 'Tx de marque', value: '112,50 %' },
                    { label: 'Objectif mini', value: '36,00 %' },
                ],
            }

            cy.get('[data-context=b2c_hifi_bonus]')
                .should('be.visible')
                .should('not.have.class', ERROR_CLASS)
                .as('block')

            testTable(table)

            // has not the bonus
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].b2c_hifi.margin_tax_excluded_commissionable = 1

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2c_hifi_bonus]').should('have.class', ERROR_CLASS)
            })

            // threshold not ok
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.b2c_hifi = 0
                payload.data.commissions[0].b2c_hifi.margin_rate_threshold = 50000

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2c_hifi_bonus]').should('have.class', ERROR_CLASS)
            })

            // juuuust below the threshold should be ok when rounded
            cy.fixture(FIXTURE_FILE).then((payload) => {
                const c = payload.data.commissions[0]
                c.b2c_hifi.margin_tax_excluded_commissionable =
                    (c.b2c_hifi.turnover_tax_excluded_commissionable * (c.b2c_hifi.margin_rate_threshold - 0.001)) / 100

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2c_hifi_bonus] [data-context=line-7]').should('contain', '36,00 %')
                cy.get('[data-context=b2c_hifi_bonus]').should('not.have.class', ERROR_CLASS)
            })

            // non relevant data
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.b2c_hifi = null
                payload.data.commissions[0].b2c_hifi.turnover_tax_excluded = null
                payload.data.commissions[0].b2c_hifi.turnover_tax_excluded_commissionable = null
                payload.data.commissions[0].b2c_hifi.margin_tax_excluded = null
                payload.data.commissions[0].b2c_hifi.margin_tax_excluded_commissionable = null
                payload.data.commissions[0].b2c_hifi.margin_rate_threshold = null
                payload.data.commissions[0].catch_up_three_months_bonuses_overview.b2c_hifi = null
                payload.data.commissions[0].catch_up_three_months_b2c_hifi.turnover_tax_excluded = null
                payload.data.commissions[0].catch_up_three_months_b2c_hifi.turnover_tax_excluded_commissionable = null
                payload.data.commissions[0].catch_up_three_months_b2c_hifi.margin_tax_excluded = null
                payload.data.commissions[0].catch_up_three_months_b2c_hifi.margin_tax_excluded_commissionable = null
                payload.data.commissions[0].catch_up_three_months_b2c_hifi.margin_rate_threshold = null

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                table = {
                    data_context: 'b2c_hifi_bonus',
                    data: [
                        { label: 'CA HT sans remise (+dsk et pcult)', value: '-' },
                        { label: 'Indice marge sans remise', value: '-' },
                        { label: 'Tx de marque sans remise', value: '-' },
                        {},
                        { label: 'CA HT avec remise', value: '-' },
                        { label: 'Indice marge avec remise', value: '-' },
                        { label: 'Tx de marque', value: '-' },
                        { label: 'Objectif mini', value: '-' },
                        { label: 'Rattrapage sur 3 mois :', value: 'Aucun' },
                        { label: 'CA HT sans remise (+dsk et pcult)', value: '-' },
                        { label: 'Indice marge sans remise', value: '-' },
                        { label: 'Tx de marque sans remise', value: '-' },
                        {},
                        { label: 'CA HT avec remise', value: '-' },
                        { label: 'Indice marge avec remise', value: '-' },
                        { label: 'Tx de marque', value: '-' },
                        { label: 'Objectif mini', value: '-' },
                    ],
                }

                cy.get('[data-context=b2c_hifi_bonus]').should('not.have.class', ERROR_CLASS)

                testTable(table)
            })

            // call center worker
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].warehouse_role = 'CALL_CENTER_SELLER'

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2c_hifi_bonus]').should('exist')
            })
        })

        it('should display b2b data', function () {
            // default store worker
            let table = {
                data_context: 'b2b_bonus',
                contexts: [
                    { data_context: 'title', value: 'Prime B2B' },
                    { data_context: 'bonus', value: '102 €' },
                ],
                data: [
                    { label: 'CA HT', value: '11 787 €' },
                    { label: 'CA HT hors dsk', value: '666 €' },
                    { label: 'Indice marge', value: '3 395 €' },
                    { label: 'Indice marge hors dsk', value: '777 €' },
                    { label: 'Tx de marque', value: '116,67 %' },
                    { label: 'Objectif mini', value: '23,00 %' },
                    { label: 'Rattrapage sur 3 mois :', value: '78 €' },
                    { label: 'CA HT', value: '24 832 €' },
                    { label: 'CA HT hors dsk', value: '4 444 €' },
                    { label: 'Indice marge', value: '6 582 €' },
                    { label: 'Indice marge hors dsk', value: '5 555 €' },
                    { label: 'Tx de marque', value: '125,00 %' },
                    { label: 'Objectif mini', value: '23,00 %' },
                ],
            }

            testTable(table)

            // has not the bonus
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].b2b.margin_tax_excluded_commissionable = 1

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2b_bonus]').should('have.class', ERROR_CLASS)
            })

            // threshold not ok
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.b2b = 0
                payload.data.commissions[0].b2b.margin_rate_threshold = 50000

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2b_bonus]').should('have.class', ERROR_CLASS)
            })

            // juuuust below the threshold should be ok when rounded
            cy.fixture(FIXTURE_FILE).then((payload) => {
                const c = payload.data.commissions[0]
                c.b2b.margin_tax_excluded_commissionable =
                    (c.b2b.turnover_tax_excluded_commissionable * (c.b2b.margin_rate_threshold - 0.001)) / 100

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2b_bonus] [data-context=line-5]').should('contain', '23,00 %')
                cy.get('[data-context=b2b_bonus]').should('not.have.class', ERROR_CLASS)
            })

            // non relevant data
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.b2b = null
                payload.data.commissions[0].b2b.turnover_tax_excluded = null
                payload.data.commissions[0].b2b.turnover_tax_excluded_commissionable = null
                payload.data.commissions[0].b2b.margin_tax_excluded = null
                payload.data.commissions[0].b2b.margin_tax_excluded_commissionable = null
                payload.data.commissions[0].b2b.margin_rate_threshold = null
                payload.data.commissions[0].catch_up_three_months_bonuses_overview.b2b = null
                payload.data.commissions[0].catch_up_three_months_b2b.turnover_tax_excluded = null
                payload.data.commissions[0].catch_up_three_months_b2b.turnover_tax_excluded_commissionable = null
                payload.data.commissions[0].catch_up_three_months_b2b.margin_tax_excluded = null
                payload.data.commissions[0].catch_up_three_months_b2b.margin_tax_excluded_commissionable = null
                payload.data.commissions[0].catch_up_three_months_b2b.margin_rate_threshold = null

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                table = {
                    data_context: 'b2b_bonus',
                    data: [
                        { label: 'CA HT', value: '-' },
                        { label: 'CA HT hors dsk', value: '-' },
                        { label: 'Indice marge', value: '-' },
                        { label: 'Indice marge hors dsk', value: '-' },
                        { label: 'Tx de marque', value: '-' },
                        { label: 'Objectif mini', value: '-' },
                        { label: 'Rattrapage sur 3 mois :', value: 'Aucun' },
                        { label: 'CA HT', value: '-' },
                        { label: 'CA HT hors dsk', value: '-' },
                        { label: 'Indice marge', value: '-' },
                        { label: 'Indice marge hors dsk', value: '-' },
                        { label: 'Tx de marque', value: '-' },
                        { label: 'Objectif mini', value: '-' },
                    ],
                }

                cy.get('[data-context=b2b_bonus]').should('not.have.class', ERROR_CLASS).as('block')

                testTable(table)
            })

            // call center worker
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].warehouse_role = 'CALL_CENTER_SELLER'

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=b2b_bonus]').should('exist')
            })
        })

        it('should display ldw data', function () {
            // default store worker
            let table = {
                data_context: 'long_duration_warranty_bonus',
                contexts: [
                    { data_context: 'title', value: 'Prime Garanties Longue Durée' },
                    { data_context: 'bonus', value: '29 €' },
                ],
                data: [
                    { label: 'CA HT', value: '987 €' },
                    { label: 'Quantité vendues', value: '26' },
                    { label: 'Quantité vendues souscat exclues', value: '20 / 30' },
                    { label: 'Tx d’attachement', value: '66,67 %' },
                    { label: 'Objectif mini', value: '15,00 %' },
                    { label: 'Rattrapage sur 3 mois :', value: '87 €' },
                    { label: 'CA HT', value: '266 €' },
                    { label: 'Quantité vendues', value: '23' },
                    { label: 'Quantité vendues souscat exclues', value: '20 / 50' },
                    { label: 'Tx d’attachement', value: '40,00 %' },
                    { label: 'Objectif mini', value: '15,00 %' },
                ],
            }

            testTable(table)

            // has not the bonus
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].long_duration_warranty.quantity_warranty_sold_commissionable = 1

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=long_duration_warranty_bonus]').should('have.class', ERROR_CLASS)
            })

            // threshold not ok
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.long_duration_warranty = 0
                payload.data.commissions[0].long_duration_warranty.quantity_warranty_sold_commissionable = 3
                payload.data.commissions[0].long_duration_warranty.quantity_eligible_products_commissionable = 300

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=long_duration_warranty_bonus]').should('have.class', ERROR_CLASS)
            })

            // intermediate threshold ok
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].long_duration_warranty.quantity_warranty_sold_commissionable = 5
                payload.data.commissions[0].long_duration_warranty.quantity_eligible_products_commissionable = 49

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=long_duration_warranty_bonus]').should('have.class', WARNING_CLASS)
            })

            // juuuust below the threshold should be ok when rounded
            cy.fixture(FIXTURE_FILE).then((payload) => {
                const c = payload.data.commissions[0]
                c.long_duration_warranty.quantity_warranty_sold_commissionable = 14999
                c.long_duration_warranty.quantity_eligible_products_commissionable = 100000

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=long_duration_warranty_bonus] [data-context=line-3]').should('contain', '15,00 %')
                cy.get('[data-context=long_duration_warranty_bonus]').should('not.have.class', ERROR_CLASS)
            })

            // non relevant data
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.long_duration_warranty = 0
                payload.data.commissions[0].long_duration_warranty.quantity_warranty_sold = 0
                payload.data.commissions[0].long_duration_warranty.quantity_warranty_sold_commissionable = 0
                payload.data.commissions[0].long_duration_warranty.quantity_eligible_products = 0
                payload.data.commissions[0].long_duration_warranty.quantity_eligible_products_commissionable = 0
                payload.data.commissions[0].long_duration_warranty.turnover_tax_excluded = 0
                payload.data.commissions[0].catch_up_three_months_bonuses_overview.long_duration_warranty = 0
                payload.data.commissions[0].catch_up_three_months_long_duration_warranty.quantity_warranty_sold = 0
                payload.data.commissions[0].catch_up_three_months_long_duration_warranty.quantity_warranty_sold_commissionable = 0
                payload.data.commissions[0].catch_up_three_months_long_duration_warranty.quantity_eligible_products = 0
                payload.data.commissions[0].catch_up_three_months_long_duration_warranty.quantity_eligible_products_commissionable = 0
                payload.data.commissions[0].catch_up_three_months_long_duration_warranty.turnover_tax_excluded = 0

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                table = {
                    data_context: 'long_duration_warranty_bonus',

                    data: [
                        { label: 'CA HT', value: '0 €' },
                        { label: 'Quantité vendues', value: '0' },
                        { label: 'Quantité vendues souscat exclues', value: '0 / 0' },
                        { label: 'Tx d’attachement', value: '0,00 %' },
                        { label: 'Objectif mini', value: '15,00 %' },
                        { label: 'Rattrapage sur 3 mois :', value: '0 €' },
                        { label: 'CA HT', value: '0 €' },
                        { label: 'Quantité vendues', value: '0' },
                        { label: 'Quantité vendues souscat exclues', value: '0 / 0' },
                        { label: 'Tx d’attachement', value: '0,00 %' },
                        { label: 'Objectif mini', value: '15,00 %' },
                    ],
                }

                cy.get('[data-context=long_duration_warranty_bonus]').should('not.have.class', ERROR_CLASS)

                testTable(table)
            })

            // call center worker
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].warehouse_role = 'CALL_CENTER_SELLER'

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=long_duration_warranty_bonus]').should('exist')
            })
        })

        it('should display team data', function () {
            // default store worker
            const table = {
                data_context: 'team_bonus',
                contexts: [
                    { data_context: 'title', value: 'Prime manager (Paris 8)' },
                    { data_context: 'bonus', value: '907 €' },
                ],
                data: [
                    { label: 'Niveau', value: 'R - 2' },
                    { label: 'CA HT Magasin', value: '101 979 €' },
                    { label: 'Tx d’atteinte', value: '103,70 %' },
                    { label: 'Objectif mini', value: '98 340 €' },
                    { label: 'TX DE MARQUE HIFI', value: '18,96 %' },
                    { label: 'Rattrapage sur 3 mois :', value: '33 €' },
                    { label: 'CA HT Magasin', value: '61 642 €' },
                    { label: 'Tx d’atteinte', value: '138,33 %' },
                    { label: 'Objectif mini', value: '445 761 €' },
                    { label: 'TX DE MARQUE HIFI', value: '50,00 %' },
                ],
            }

            testTable(table)

            // has not the bonus
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].goals.turnover_tax_excluded_rate = 1

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=team_bonus]').should('have.class', ERROR_CLASS)
            })

            // threshold not ok
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.manager = 0
                payload.data.commissions[0].goals.turnover_tax_excluded_rate = 89

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=team_bonus]').should('have.class', ERROR_CLASS)
            })

            // juuuust below the threshold should be ok when rounded
            cy.fixture(FIXTURE_FILE).then((payload) => {
                const c = payload.data.commissions[0]
                c.goals.turnover_tax_excluded_rate = 99.999

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=team_bonus] [data-context=line-2]').should('contain', '100,00 %')
                cy.get('[data-context=team_bonus]').should('not.have.class', ERROR_CLASS)
            })

            // non relevant data
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].bonuses_overview.manager = null
                payload.data.commissions[0].goals.turnover_tax_excluded_rate = 89

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=team_bonus]').should('have.class', ERROR_CLASS)
            })

            // title and bonus dynamic with warehouse_role
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].warehouse_role = 'RETAIL_STORE_SELLER'
                payload.data.commissions[0].warehouse_role_level = 3
                payload.data.commissions[0].bonuses_overview.team = 150
                payload.data.commissions[0].catch_up_three_months_bonuses_overview.team = 250

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=team_bonus]').as('block')
                cy.get('@block').find('[data-context=title]').should('contain', 'Prime équipe (Paris 8)')
                cy.get('@block').find('[data-context=bonus]').should('contain', '150 €')
                cy.get('@block').find('[data-context=line-0]').should('contain', 'V - 3')
                cy.get('@block')
                    .find('[data-context=line-5]')
                    .should('contain', 'Rattrapage sur 3 mois :')
                    .should('contain', '250 €')
            })

            // call center worker
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].warehouse_role = 'CALL_CENTER_SELLER'

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=team_bonus]').should('not.exist')
            })
        })

        it('should display total/global data', function () {
            // default store worker
            const table = {
                data_context: 'total_bonus',
                contexts: [
                    { data_context: 'title', value: 'Total' },
                    { data_context: 'bonus', value: '1 877 €' },
                    { data_context: 'catch-up-total', value: '177 €' },
                ],
                data: [
                    { label: 'CA HT', value: '73 748 €' },
                    { label: 'Indice marge', value: '26 585 €' },
                    { label: 'Tx de marque', value: '36,20 %' },
                    { label: '', value: 'Sur 3 mois' },
                    { label: 'CA HT', value: '124 956 €' },
                    { label: 'Indice marge', value: '39 009 €' },
                    { label: 'Tx de marque', value: '31,22 %' },
                ],
            }

            testTable(table)

            // call center worker
            cy.fixture(FIXTURE_FILE).then((payload) => {
                payload.data.commissions[0].warehouse_role = 'CALL_CENTER_SELLER'

                cy.intercept('GET', COMMISSIONS_API, {
                    statusCode: 200,
                    status: 'success',
                    body: payload,
                }).as('fetch_commissions')
                cy.visit(PAGE)

                cy.get('[data-context=total_bonus]').should('not.exist')
            })
        })
    })
})
