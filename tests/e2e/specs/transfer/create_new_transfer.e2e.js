import { PRODUCT_STOCK_READ, TRANSFER_CREATE } from '../../../../src/apps/erp/permissions.js'

describe('ERP create transfer from article page', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([PRODUCT_STOCK_READ, TRANSFER_CREATE])

        cy.intercept('GET', /\/api\/erp\/v2\/article\/(SONOSONENR|120390)$/, {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__SONOSONENR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', '**/api/erp/v2/wms/warehouses', { fixture: 'erp/wms/warehouses/v2/warehouses.json' }).as(
            'warehouses',
        )

        cy.visit('/articles/SONOSONENR/general', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })
        cy.wait('@get_article_by_id_or_sku_v2')

        cy.toggleMenu()
        cy.closeAllToasts()
    })

    it('can create a transfer from article page', function () {
        cy.intercept('GET', '**/api/erp/v1/product/120390/stock', { fixture: 'erp/product/product_stock' }).as(
            'product_stocks',
        )

        // open slideout
        cy.get('[data-context=show-more-trigger]').click()
        cy.get('[data-context=create-transfer]').click()

        // check slide out
        cy.wait('@warehouses')
        cy.wait('@product_stocks')
        cy.get('[data-context=slide-out-container]')
            .find('[data-context=page-header]')
            .should('contain', "Création d'un nouveau transfert")
        cy.get('[data-context=new-transfer-warehouse-source-label]').should('contain', 'Dépôt de départ')
        cy.get('[data-context=new-transfer-warehouse-source-input]')
            .find('[name=warehouse_autocomplete]')
            .should('have.attr', 'placeholder', 'Sélectionner un dépôt')
            .parent()
            .siblings()
            .find('.multiselect__element')
            .as('source_multiselect_tag')
            .should('have.length', 12)
        cy.get('@source_multiselect_tag').eq(0).should('contain', 'Champigny')
        cy.get('@source_multiselect_tag').eq(1).should('contain', 'Avignon')

        cy.get('[data-context=new-transfer-warehouse-destination-label]').should('contain', "Dépôt d'arrivée")
        cy.get('[data-context=new-transfer-warehouse-destination-input]')
            .find('[name=warehouse_autocomplete]')
            .should('have.attr', 'placeholder', 'Sélectionner un dépôt')
            .parent()
            .siblings()
            .find('.multiselect__element')
            .as('destination_multiselect_tag')
            .should('have.length', 16)
        cy.get('@destination_multiselect_tag').eq(0).should('contain', 'Champigny')
        cy.get('@destination_multiselect_tag').eq(1).should('contain', 'Havre')

        cy.get('[data-context=new-transfer-quantity-label]').should('contain', 'Quantité')
        cy.get('[data-context=new-transfer-quantity-input]').should('have.value', '1')

        cy.get('[data-context=create-transfer-form-submit]')
            .should('be.visible')
            .find('[data-context=submit-btn]')
            .should('have.attr', 'disabled', 'disabled')

        // Check validation form
        cy.get('[data-context=new-transfer-warehouse-source-input]').multiselect('Champigny', 'Champigny')
        cy.get('[data-context=create-transfer-form-submit]')
            .find('[data-context=submit-btn]')
            .should('have.attr', 'disabled', 'disabled')
        cy.get('@destination_multiselect_tag').eq(0).should('contain', 'Havre')
        cy.get('[data-context=new-transfer-warehouse-destination-input]').multiselect('Havre', 'Havre')
        cy.get('[data-context=create-transfer-form-submit]')
            .find('[data-context=submit-btn]')
            .should('not.have.attr', 'disabled')
        cy.get('[data-context=new-transfer-quantity-input]').type('3')
        cy.get('[data-context=create-transfer-form-submit]')
            .find('[data-context=submit-btn]')
            .should('not.have.attr', 'disabled')

        // check params sent to create transfer request
        // check request error
        cy.intercept('POST', '**/api/erp/v1/transfer', {
            statusCode: 500,
            body: {},
        }).as('create_transfer_error')

        cy.get('[data-context=create-transfer-form-submit]')
            .should('be.visible')
            .find('[data-context=submit-btn]')
            .click()
        cy.wait('@create_transfer_error')
        cy.get('[data-context=alert-danger]').should(
            'contain',
            'Une erreur est survenue lors de la création, veuillez réessayer.',
        )

        // check success
        cy.intercept('POST', '**/api/erp/v1/transfer', {
            statusCode: 200,
            body: { data: { transfer_id: 123 } },
        }).as('create_transfer_success')
        cy.get('[data-context=create-transfer-form-submit]')
            .should('be.visible')
            .find('[data-context=submit-btn]')
            .click()
        cy.wait('@create_transfer_success').then((xhr) => {
            expect(xhr.request.body.warehouse_from).to.eq(1)
            expect(xhr.request.body.warehouse_to).to.eq(3)
            expect(xhr.request.body.products).to.have.length(1)
            expect(xhr.request.body.products[0].product_id).to.eq('120390')
            expect(xhr.request.body.products[0].quantity).to.eq(13)
        })

        cy.get('[data-context=slide-out-container]').should('not.exist')

        // check open new tab with new quote legacy page
        cy.get('@windowOpen').should('be.calledWith', '/legacy/stock/bonTransfertEdit?id=123', '_blank')
    })

    it('show a custom message when no product in stock', function () {
        cy.intercept('GET', '**/api/erp/v1/product/120390/stock', { fixture: 'erp/product/no_product_stock' }).as(
            'product_stocks',
        )

        // open slideout
        cy.get('[data-context=show-more-trigger]').click()
        cy.get('[data-context=create-transfer]').click()

        // check content
        cy.get('[data-context=slide-out-container] svg[data-name="Void picture"]').should('be.visible')
        cy.get('[data-context=slide-out-container] strong').should(
            'contain',
            'Aucun stock pour ce produit, dans aucun dépôt',
        )
    })
})
