const selectRow = (idx) => {
    return cy.get('@table').find('tbody tr').eq(idx).as('row')
}

const selectCell = (idx) => {
    return cy.get('@row').find('td').eq(idx).as('cell')
}

const visit = () => {
    cy.mockErpUser()
    cy.visit('transfers/ongoing')
    cy.wait('@main_menu')
    cy.wait('@transfers_request')
    cy.wait('@printer')
    cy.get('table td > [data-context=skeleton]').should('not.exist')
    cy.get('[data-context=transfers-ongoing]').as('table')
}

describe('Transfers - Ongoing transfers', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/transfer-reports', { fixture: 'erp/transfers/ongoing_transfers.json' }).as(
            'transfers_request',
        )

        cy.intercept('POST', '**/api/erp/v1/expeditions/validate', {
            statusCode: 200,
            body: { data: { message: 'Explanation from the api' } },
        }).as('validate_transfers')

        cy.intercept('POST', '**/api/erp/v1/printers**', { fixture: 'erp/printer/printers_102x152' }).as('printer')
    })

    describe('List ongoing transfers', function () {
        beforeEach(() => {
            visit()
        })

        it('provides page with the ongoing transfers', function () {
            // Check table header
            cy.get('@table').find('thead tr th').as('headers')
            cy.get('@headers').should('have.length', 9)
            ;[
                'Depuis',
                '',
                'Vers',
                ['Numéro', 'de suivi'],
                ['Nombre', 'de colis'],
                ['Poids', 'Total'],
                'Transferts',
                'Poids',
                'Imprimer',
            ].forEach((header, index) => {
                if (Array.isArray(header)) {
                    header.forEach((h) => cy.get('@headers').eq(index).should('contain', h))

                    return
                }

                cy.get('@headers').eq(index).should('contain', header)
            })

            cy.get('[data-context=printer]')
                .eq(0)
                .find('[name=printer_autocomplete]')
                .should('have.attr', 'placeholder', 'Sélectionner')
                .parent()
                .siblings()
                .find('.multiselect__element')
                .as('source_multiselect_tag')
                .should('have.length', 2)
            cy.get('@source_multiselect_tag').eq(0).should('contain', 'Zebra_prod1')
            cy.get('@source_multiselect_tag').eq(1).should('contain', 'Zebra_prod2')

            cy.get('[data-context=validate-transfers]').should('not.be', 'disable')
        })

        it('checks a row with multiple tranfers', function () {
            selectRow(3)
            selectCell(0).should('contain', 'Strasbourg')
            selectCell(2).should('contain', 'Champigny 2')
            selectCell(3).should('contain', '90023149')
            selectCell(4).should('contain', '1')
            selectCell(5).should('contain', '239,70 Kg')
            selectCell(6).find('div').eq(0).should('contain', '205320')
            selectCell(6).find('div').eq(1).should('contain', '212312')
            selectCell(6).find('div').eq(2).should('contain', '212369')
            selectCell(7).find('div').eq(0).should('contain', '75,15 Kg')
            selectCell(7).find('div').eq(1).should('contain', '100,55 Kg')
            selectCell(7).find('div').eq(2).should('contain', '64,00 Kg')
            selectCell(8).find('[data-context=print-sticker-btn]').should('not.be', 'disable')
        })
    })
})
