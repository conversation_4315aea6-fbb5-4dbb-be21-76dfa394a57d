const selectRow = (idx) => {
    return cy.get('@table').find('tbody tr').eq(idx).as('row')
}

const selectCell = (idx) => {
    return cy.get('@row').find('td').eq(idx).as('cell')
}

const visit = () => {
    cy.mockErpUser()
    cy.visit('transfers/passed')
    cy.wait('@main_menu')
    cy.wait('@transfers_request')
    cy.get('table td > [data-context=skeleton]').should('not.exist')
    cy.get('[data-context=dispatch_notes]').as('table')
}

describe('Transfers - Passed transfers', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/dispatch-notes', { fixture: 'erp/transfers/passed_transfers.json' }).as(
            'transfers_request',
        )
    })

    describe('List passed transfers', function () {
        beforeEach(() => {
            visit()
        })

        it('provides page with the passed transfers', function () {
            // Check table header
            cy.get('@table').find('thead tr th').as('headers')
            cy.get('@headers').should('have.length', 2)
            ;['Date du rapport', 'Actions'].forEach((header, index) => {
                cy.get('@headers').eq(index).should('contain', header)
            })
        })

        it('can download transfer dispatch note', function () {
            cy.intercept('GET', '**/api/erp/v1/dispatch-note/7612', {
                delay: 500,
                body: {
                    content: '',
                },
            }).as('dispatch_note_request')

            selectRow(2)
            selectCell(1).find('[data-context=transfer-download]').as('download-button')
            cy.get('@download-button').parent().tooltip("Télécharger le rapport d'expédition")
            cy.get('@download-button').click()
            cy.get('@download-button').should('be.disabled')
            cy.wait('@dispatch_note_request')
        })
    })
})
