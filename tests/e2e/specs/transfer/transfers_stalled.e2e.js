const selectRow = (idx) => {
    return cy.get('@table').find('tbody tr').eq(idx).as('row')
}

const selectCell = (idx) => {
    return cy.get('@row').find('td').eq(idx).as('cell')
}

const visit = () => {
    cy.mockErpUser()
    cy.visit('transfers/stalled')
    cy.wait('@main_menu')
    cy.wait('@transfers_request')
    cy.wait('@printer')
    cy.get('table td > [data-context=skeleton]').should('not.exist')
    cy.get('[data-context=transfers-stalled]').as('table')
}

describe('Transfers - Stalled transfers', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/stalled-transfers', { fixture: 'erp/transfers/stalled_transfers.json' }).as(
            'transfers_request',
        )

        cy.intercept('POST', '**/api/erp/v1/printers**', { fixture: 'erp/printer/printers_102x152' }).as('printer')
    })

    describe('List stalled transfers', function () {
        beforeEach(() => {
            visit()
        })

        it('provides page with the stalled transfers', function () {
            // Check table header
            cy.get('@table').find('thead tr th').as('headers')
            cy.get('@headers').should('have.length', 9)
            ;[
                'Depuis',
                '',
                'Vers',
                ['Produit', 'Transporteur'],
                ['Nombre', 'Transferts'],
                ['Poids', 'Total'],
                'Transferts',
                'Poids',
                'Imprimer',
            ].forEach((header, index) => {
                if (Array.isArray(header)) {
                    header.forEach((h) => cy.get('@headers').eq(index).should('contain', h))

                    return
                }

                cy.get('@headers').eq(index).should('contain', header)
            })
        })

        it('checks the first row with one transfer', function () {
            selectRow(0)
            selectCell(0).should('contain', 'Antibes')
            selectCell(2).should('contain', 'Champigny 2')
            selectCell(3).should('contain', 'Express')
            selectCell(4).should('contain', '1')
            selectCell(5).should('contain', '109,07 Kg')
            selectCell(6).should('contain', '212426')
            selectCell(7).should('contain', '109,07 Kg')
            selectCell(8).find('[data-context=print-sticker-form]').as('print_form')

            cy.get('[data-context=printer]')
                .eq(0)
                .find('[name=printer_autocomplete]')
                .should('have.attr', 'placeholder', 'Sélectionner')
                .parent()
                .siblings()
                .find('.multiselect__element')
                .as('source_multiselect_tag')
                .should('have.length', 2)
            cy.get('@source_multiselect_tag').eq(0).should('contain', 'Zebra_prod1')
            cy.get('@source_multiselect_tag').eq(1).should('contain', 'Zebra_prod2')
            cy.get('@print_form').find('[data-context=sticker]').should('have.value', '0')
        })

        it('checks a row with 3 tranfers', function () {
            selectRow(7)
            selectCell(0).should('contain', 'Strasbourg')
            selectCell(2).should('contain', 'Champigny 2')
            selectCell(3).should('contain', 'Express')
            selectCell(4).should('contain', '3')
            selectCell(5).should('contain', '239,70 Kg')
            selectCell(6).find('div').eq(0).should('contain', '205320')
            selectCell(6).find('div').eq(1).should('contain', '212312')
            selectCell(6).find('div').eq(2).should('contain', '212369')
            selectCell(7).find('div').eq(0).should('contain', '75,15 Kg')
            selectCell(7).find('div').eq(1).should('contain', '100,55 Kg')
            selectCell(7).find('div').eq(2).should('contain', '64,00 Kg')
            selectCell(8).find('[data-context=print-sticker-form]').as('print_form')
            cy.get('@print_form').find('[data-context=sticker]').should('have.value', '0')
        })
    })
})
