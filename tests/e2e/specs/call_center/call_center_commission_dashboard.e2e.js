import { CALL_CENTER_COMMISSIONS_READ } from '../../../../src/apps/erp/permissions'

const PAGE = '/call-center/commission-dashboard'

describe('Call Center commission dashboard', function () {
    describe('Without permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()
        })

        it('display error page', () => {
            cy.checkUnauthorizedAccessFor(PAGE)
        })
    })

    describe('With permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([CALL_CENTER_COMMISSIONS_READ])

            cy.mockDate(new Date(2022, 6, 1, 15, 44, 0, 0))

            cy.intercept('GET', '**/api/erp/v1/seller-commission/commissions?*', {
                fixture: 'erp/seller-commission/commissions/seller-commissions.json',
            }).as('fetch_commissions')

            cy.visit(PAGE)
            cy.toggleMenu()
        })

        it('should load the retail dashboard with little changes', () => {
            cy.get('[data-context=commission-dashboard]').should('be.visible')
            cy.get('[data-context=page-header]').should('contain', 'Performances vendeurs call center')
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.get('at')).to.eq('2022-07')
                expect(params.get('type')).to.eq('call_center')
            })
        })
    })
})
