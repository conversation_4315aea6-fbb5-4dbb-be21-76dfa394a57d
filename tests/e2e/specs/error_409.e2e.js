describe('Automated error handling', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    it('pop in an alert when a 409 error occurs', function () {
        cy.intercept('http://_erp_proxy/api/hal/v1/me', {
            statusCode: 409,
            body: { data: { message: 'Explanation from the api' } },
        }).as('me_call')
        cy.visit('/')
        cy.wait('@me_call')

        cy.url().should('eq', Cypress.config().baseUrl + '/legacy/tache/index')

        cy.toast(`Explanation from the api`, 'danger')
    })

    it('does not pop in when the 409 error does not contain message', function () {
        cy.intercept('http://_erp_proxy/api/hal/v1/me', {
            statusCode: 409,
            body: { data: { thing: 'some not expecting thing' } },
        }).as('me_call')
        cy.visit('/')
        cy.wait('@me_call')

        cy.url().should('eq', Cypress.config().baseUrl + '/legacy/tache/index')

        cy.get('[data-context=toast-message]').should('not.exist')
    })
})
