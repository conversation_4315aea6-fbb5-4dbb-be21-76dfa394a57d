import { ARTICLE_BUYERS_WRITE, ARTICLE_GENERAL_INFORMATION_WRITE } from '../../../../src/apps/erp/permissions'

describe('Article v2 - Aside', function () {
    const PAGE = '/articles/SONOSONENR/general'
    const COMMON_FIXTURE = 'erp/article/get_article_by_id_or_sku_v2__SONOSONENR'

    const visit = (permissions) => {
        cy.authenticate()
        cy.mockErpUser([...(permissions ?? [])])

        cy.visit(PAGE)

        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.closeAllToasts()
    }

    beforeEach(() => {
        cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
            fixture: COMMON_FIXTURE,
        }).as('get_article_by_id_or_sku_v2')
    })

    context('Replaced by', () => {
        describe('With write permissions', function () {
            it('should show an add article button when the article has not been replaced yet', function () {
                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.replacements.replaced_by = null

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit([ARTICLE_GENERAL_INFORMATION_WRITE])

                cy.get('[data-context=replaced-by]').should('be.visible')
                cy.get('[data-context=replaced-by] [data-context=erp-article-item]').should('not.exist')
                cy.get(
                    '[data-context=replaced-by] [data-context=erp-button]:contains(Ajouter un article de remplacement)',
                )
                    .should('be.visible')
                    .should('not.be.disabled')

                // add an article
                cy.get(
                    '[data-context=replaced-by] [data-context=erp-button]:contains(Ajouter un article de remplacement)',
                ).click()

                cy.intercept('POST', '**/api/erp/v1/articles**', {
                    fixture: 'erp/article/cpost_articles_autocomplete__kef_q3',
                }).as('fetch_articles')

                cy.get('[data-context=slide-out-container]').within(() => {
                    cy.get('[data-context=erp-multiselect]').click()
                    cy.get('[data-context=erp-multiselect]').type('toto')

                    cy.wait('@fetch_articles')

                    cy.get('[data-context=suggestion]').eq(0).click()

                    // can have an error message from the server
                    const message = 'Annie aime les sucettes...'
                    cy.intercept('PUT', '**/erp/v1/article-replacement/SONOSONENR/replaced-by/KEFQ300BC', {
                        statusCode: 400,
                        body: {
                            code: 1000,
                            message,
                        },
                    }).as('put_replacement')

                    cy.get('button').click()

                    cy.wait('@put_replacement')

                    cy.get('[data-context=erp-input-helper]').should('contain', message)

                    // submit successfully

                    cy.intercept('PUT', '**/erp/v1/article-replacement/SONOSONENR/replaced-by/KEFQ300BC', {
                        statusCode: 204,
                    }).as('put_replacement')

                    cy.get('button').click()

                    // recharge article immediately afterward
                    cy.wait(['@put_replacement', '@get_article_by_id_or_sku_v2'])
                })
            })

            it('should show a replacement article that can be removed and not allow to add more article', function () {
                visit([ARTICLE_BUYERS_WRITE])

                cy.get('[data-context=replaced-by]').should('be.visible')

                // should not allow to be replaced by more than article
                cy.get(
                    '[data-context=replaced-by] [data-context=erp-button]:contains(Ajouter un article de remplacement)',
                ).should('not.exist')

                // check article
                cy.get('[data-context=replaced-by] [data-context=erp-article-item]').should('have.length', 1)
                cy.get('[data-context=replaced-by] [data-context=erp-article-item] [data-context=sku]').should(
                    'have.attr',
                    'href',
                    '/articles/SONOSERA100NR/',
                )

                // should send the correct params when deleting
                cy.intercept('DELETE', '**/erp/v1/article-replacement/SONOSONENR', {
                    statusCode: 204,
                }).as('delete_replacement')

                cy.get(
                    '[data-context=replaced-by] [data-context=erp-article-item] [data-context=remove-button]',
                ).click()

                // recharge article immediately afterward
                cy.wait(['@delete_replacement', '@get_article_by_id_or_sku_v2'])
            })

            it('Should not display for destocks', function () {
                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.replacements.replaced_by = structuredClone(payload.data.replacements.replaced_by)
                    payload.data.is_destock = true

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit()

                cy.get('[data-context=replaced-by]').should('not.exist')
            })
        })

        describe('Without write permissions', () => {
            it('should not be able to edit anything', () => {
                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.replacements.replaced_by = structuredClone(payload.data.replacements.replaced_by)

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit()

                cy.get('[data-context=replaced-by]').should('be.visible')
                cy.get(
                    '[data-context=replaced-by] [data-context=erp-button]:contains(Ajouter un article de remplacement)',
                ).should('not.exist')

                cy.get('[data-context=replaced-by] [data-context=erp-article-item]').should('be.visible')
                cy.get(
                    '[data-context=replaced-by] [data-context=erp-article-item] [data-context=remove-button]',
                ).should('not.exist')

                // even when there is no replacement yet

                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.replacements.replaced_by = null

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit()

                cy.get('[data-context=replaced-by]').should('be.visible')
                cy.get(
                    '[data-context=replaced-by] [data-context=erp-button]:contains(Ajouter un article de remplacement)',
                )
                    .should('be.visible')
                    .should('be.disabled')
                cy.get('[data-context=replaced-by] [data-context=erp-article-item]').should('not.exist')
            })
        })
    })

    context('Replaces', () => {
        describe('With write permissions', function () {
            it('should allow to add or more article to replace and allow to remove them', function () {
                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.replacements.replaces = [structuredClone(payload.data.replacements.replaced_by)]

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit([ARTICLE_BUYERS_WRITE, ARTICLE_GENERAL_INFORMATION_WRITE])

                cy.get('[data-context=replaces]').should('be.visible')

                // allow current article to replace as much article as it want
                cy.get('[data-context=replaces] [data-context=erp-button]:contains(Ajouter un article à remplacer)')
                    .should('be.visible')
                    .should('not.be.disabled')

                // check currently replaced article
                cy.get('[data-context=replaces] [data-context=erp-article-item]').should('have.length', 1)
                cy.get('[data-context=replaces] [data-context=erp-article-item] [data-context=sku]').should(
                    'have.attr',
                    'href',
                    '/articles/SONOSERA100NR/',
                )

                // should send the correct params when deleting
                cy.intercept('DELETE', '**/erp/v1/article-replacement/SONOSERA100NR', {
                    statusCode: 204,
                }).as('delete_replacement')

                cy.get('[data-context=replaces] [data-context=erp-article-item] [data-context=remove-button]').click()

                // recharge article immediately afterward
                cy.wait(['@delete_replacement', '@get_article_by_id_or_sku_v2'])

                // add another one
                cy.get(
                    '[data-context=replaces] [data-context=erp-button]:contains(Ajouter un article à remplacer)',
                ).click()

                cy.intercept('POST', '**/api/erp/v1/articles**', {
                    fixture: 'erp/article/cpost_articles_autocomplete__kef_q3',
                }).as('fetch_articles')

                cy.get('[data-context=slide-out-container]').within(() => {
                    cy.get('[data-context=erp-multiselect]').click()
                    cy.get('[data-context=erp-multiselect]').type('toto')

                    cy.wait('@fetch_articles')

                    cy.get('[data-context=suggestion]').eq(0).click()

                    // can have an error message from the server
                    // notice the parameters inverted compared to replaced-by
                    const message = 'Annie aime les sucettes...'
                    cy.intercept('PUT', '**/erp/v1/article-replacement/KEFQ300BC/replaced-by/SONOSONENR', {
                        statusCode: 400,
                        body: {
                            code: 1000,
                            message,
                        },
                    }).as('put_replacement')

                    cy.get('button').click()

                    cy.wait('@put_replacement')

                    cy.get('[data-context=erp-input-helper]').should('contain', message)

                    // submit successfully

                    cy.intercept('PUT', '**/erp/v1/article-replacement/KEFQ300BC/replaced-by/SONOSONENR', {
                        statusCode: 204,
                    }).as('put_replacement')

                    cy.get('button').click()

                    // recharge article immediately afterward
                    cy.wait(['@put_replacement', '@get_article_by_id_or_sku_v2'])
                })
            })

            it('Should not display for destocks', function () {
                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.replacements.replaces = [structuredClone(payload.data.replacements.replaced_by)]
                    payload.data.is_destock = true

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit()

                cy.get('[data-context=replaces]').should('not.exist')
            })
        })
        describe('Without write permissions', function () {
            it('should not be able to edit anything', () => {
                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.replacements.replaces = [structuredClone(payload.data.replacements.replaced_by)]

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit()

                cy.get('[data-context=replaces]').should('be.visible')
                cy.get('[data-context=replaces] [data-context=erp-button]:contains(Ajouter un article à remplacer)')
                    .should('be.visible')
                    .should('be.disabled')

                cy.get('[data-context=replaces] [data-context=erp-article-item]').should('be.visible')
                cy.get('[data-context=replaces] [data-context=erp-article-item] [data-context=remove-button]').should(
                    'not.exist',
                )

                // even when there is no replacement yet

                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.replacements.replaces = []

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit()

                cy.get('[data-context=replaces]').should('be.visible')
                cy.get('[data-context=replaces] [data-context=erp-button]:contains(Ajouter un article à remplacer)')
                    .should('be.visible')
                    .should('be.disabled')
                cy.get('[data-context=replaces] [data-context=erp-article-item]').should('not.exist')
            })
        })
    })
})
