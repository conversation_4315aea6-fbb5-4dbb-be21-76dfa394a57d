import { testPermissionInContext } from '../../../utils/permission-utils'
import { PRODUCT_STOCK_READ } from '../../../../../src/apps/erp/permissions'

describe('Article v2 - Logistique - Permissions', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([PRODUCT_STOCK_READ])

        cy.intercept('GET', '**/api/erp/v1/wms/product/143214/locations', {
            fixture: 'erp/product/locations-id-143214',
        })
            .as('locations-id-143214')
            .as('first_location_request')

        cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-***********', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-***********',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/warehouses', { fixture: 'erp/warehouse/warehouses' }).as(
            'warehouses_request',
        )

        cy.intercept('POST', '**/api/erp/v1/countries', { fixture: 'erp/country/cpost_countries_fr.json' }).as(
            'cpost_countries',
        )
    })

    const visitPage = () => {
        cy.visit('/articles/DESTOCK-***********/logistic')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.wait('@warehouses_request')

        cy.closeAllToasts()

        cy.get('[data-context=article-v2-logistic-information').as('form')
    }

    testPermissionInContext('[data-context=erp-account-permission-in-context-toolbar] button', visitPage, {
        _or: [
            {
                permission_id: {
                    _ilike: 'ARTICLE_LOGISTIC%',
                },
            },
            {
                permission_id: {
                    _ilike: 'PRODUCT_LOCATION_UPDATE',
                },
            },
            {
                permission_id: {
                    _ilike: 'PRODUCT_LOCATION_UPDATE_WITH_SUPPLIER_ORDER',
                },
            },
            {
                permission_id: {
                    _ilike: 'PRODUCT_LOCATION_UNLINK_DELIVERY',
                },
            },
        ],
    })
})
