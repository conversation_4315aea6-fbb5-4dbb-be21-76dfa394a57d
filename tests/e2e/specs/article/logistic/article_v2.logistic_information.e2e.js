import { ARTICLE_LOGISTIC_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Logistique - information', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/wms/product/143214/locations', {
            fixture: 'erp/product/locations-id-143214',
        })
            .as('locations-id-143214')
            .as('first_location_request')

        cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/warehouses', { fixture: 'erp/warehouse/warehouses' }).as(
            'warehouses_request',
        )

        cy.intercept('POST', '**/api/erp/v1/countries', { fixture: 'erp/country/cpost_countries.json' }).as(
            'cpost_countries',
        )
    })

    const visitPage = () => {
        cy.visit('/articles/DESTOCK-20191120013/logistic')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.wait('@warehouses_request')

        cy.get('[data-context=article-v2-logistic-information').as('form')
    }

    describe('View information related to the product logistic', function () {
        beforeEach(() => {
            cy.mockErpUser()
        })

        it('should show a form for each field and initialize them correctly without values', function () {
            cy.fixture('erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013').then((payload) => {
                payload.data.logistic = {
                    weight: null,
                    code_128: null,
                    packages: null,
                    rotations: {
                        '7_days': 0,
                        '30_days': 0,
                        '90_days': 0,
                    },
                    weight_tmp: 'N',
                    is_packaged: 'N',
                    customs_code: null,
                    package_unit: null,
                    source_country_id: null,
                    number_of_packages: null,
                    customs_code_origin: 'article',
                    source_country_name: null,
                    ecotax_code: null,
                    ecotax_code_origin: 'article',
                }

                cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visitPage()

            cy.get('@form').should('be.visible')

            const BLOCKS = [
                {
                    block: 'package_unit',
                    label: 'Colisage',
                    assert: 'empty',
                },
                {
                    block: 'is_packaged',
                    label: 'Conditionnement par lot',
                    assert: 'not.checked',
                },
                {
                    block: 'number_of_packages',
                    label: 'Nombre de colis',
                    assert: 'empty',
                },
                {
                    block: 'weight',
                    label: 'Poids',
                    assert: 'empty',
                },
                {
                    block: 'weight_tmp',
                    label: 'Poids Temporaire',
                    assert: 'not.checked',
                },
                {
                    block: 'source_country_id',
                    label: "Pays d'origine",
                    assert: 'empty',
                },
                {
                    block: 'customs_code',
                    label: 'Code douanier',
                    assert: 'empty',
                },
                {
                    block: 'ecotax-code',
                    label: 'Code écotaxe',
                    assert: 'empty',
                },
            ]

            BLOCKS.forEach((block) => {
                cy.get('@form')
                    .find(`[data-context=${block.block}] [data-context=erp-form-label]`)
                    .should('contain', block.label)

                if (block?.assert === 'empty') {
                    cy.get('@form').find(`[data-context=${block.block}] input`).should('be.empty')
                }
                if (block?.assert === 'not.checked') {
                    cy.get('@form').find(`[data-context=${block.block}] button`).should('not.have.attr', 'aria-checked')
                }
            })
            cy.get('@form').find('[data-context=erp-button]').as('button')
            cy.get('@button').should('be.disabled')
            cy.get('@button').parent().tooltip('Vous ne disposez pas des droits nécessaires')
        })

        it('should show values in each form field when they exists', function () {
            cy.fixture('erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013').then((payload) => {
                payload.data.logistic = {
                    weight: 0.1,
                    code_128: '12140143214',
                    packages: 1,
                    rotations: {
                        '7_days': 0,
                        '30_days': 0,
                        '90_days': 0,
                    },
                    weight_tmp: 'Y',
                    is_packaged: 'Y',
                    customs_code: '85271900',
                    package_unit: 1,
                    source_country_id: 67,
                    number_of_packages: 1,
                    customs_code_origin: 'article',
                    source_country_name: null,
                    ecotax_code: '13579',
                    ecotax_code_origin: 'article',
                }

                cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visitPage()

            cy.get('@form').should('be.visible')

            const BLOCKS = [
                {
                    block: 'package_unit',
                    label: 'Colisage',
                    have_value: '1',
                },
                {
                    block: 'is_packaged',
                    label: 'Conditionnement par lot',
                    assert: 'checked',
                },
                {
                    block: 'number_of_packages',
                    label: 'Nombre de colis',
                    have_value: '1',
                },
                {
                    block: 'weight',
                    label: 'Poids',
                    have_value: '0.1',
                },
                {
                    block: 'weight_tmp',
                    label: 'Poids Temporaire',
                    assert: 'checked',
                },
                {
                    block: 'source_country_id',
                    label: "Pays d'origine",
                    single_value: 'FRANCE',
                },
                {
                    block: 'customs_code',
                    label: 'Code douanier',
                    have_value: '85271900',
                },
                {
                    block: 'ecotax-code',
                    label: 'Code écotaxe',
                    have_value: '13579',
                },
            ]

            BLOCKS.forEach((block) => {
                cy.get('@form')
                    .find(`[data-context=${block.block}] [data-context=erp-form-label]`)
                    .should('contain', block.label)

                if (block.hasOwnProperty('have_value')) {
                    cy.get('@form').find(`[data-context=${block.block}] input`).should('have.value', block.have_value)
                }
                if (block.hasOwnProperty('single_value')) {
                    cy.get('@form')
                        .find(`[data-context=${block.block}] [data-context=single-value]`)
                        .should('contain', block.single_value)
                }

                if (block?.assert === 'checked') {
                    cy.get('@form')
                        .find(`[data-context=${block.block}] button`)
                        .should('have.attr', 'aria-checked', 'true')
                }
            })
            cy.get('@form').find('[data-context=erp-button]').should('be.disabled')
        })

        describe('Custom code behaviour', function () {
            it('should not show a warning when custom code is sourced from article', function () {
                cy.fixture('erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013').then((payload) => {
                    payload.data.logistic.customs_code_origin = 'article'

                    cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visitPage()

                cy.get('@form')
                    .find('[data-context=customs_code]  [data-context=erp-form-label]')
                    .should('contain', 'Code douanier')
                cy.get('@form').find('[data-context=customs_code] input').should('have.value', '85271900')
                cy.get('@form').find('[data-context=customs_code] [data-context=leading]').should('not.exist')
            })

            it('should show a warning when custom code is sourced from subcategory', function () {
                cy.fixture('erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013').then((payload) => {
                    payload.data.logistic.customs_code_origin = 'subcategory'

                    cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visitPage()

                cy.get('@form')
                    .find('[data-context=customs_code]  [data-context=erp-form-label]')
                    .should('contain', 'Code douanier')
                cy.get('@form').find('[data-context=customs_code] input').should('have.value', '85271900')
                cy.get('@form')
                    .find('[data-context=customs_code] [data-context=leading]')
                    .should('be.visible')
                    .tooltip('Code douanier de la sous-catégorie')
            })

            it('should show a warning when custom code is sourced from category', function () {
                visitPage()

                cy.get('@form')
                    .find('[data-context=customs_code]  [data-context=erp-form-label]')
                    .should('contain', 'Code douanier')
                cy.get('@form').find('[data-context=customs_code] input').should('have.value', '85271900')
                cy.get('@form')
                    .find('[data-context=customs_code] [data-context=leading]')
                    .should('be.visible')
                    .tooltip('Code douanier de la catégorie')
            })
        })
        describe('Ecotax code behaviour', function () {
            it('should not show a warning when ecotax code is sourced from article', function () {
                cy.fixture('erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013').then((payload) => {
                    payload.data.logistic.ecotax_code_origin = 'article'

                    cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visitPage()

                cy.get('@form')
                    .find('[data-context=ecotax-code]  [data-context=erp-form-label]')
                    .should('contain', 'Code écotaxe')
                cy.get('@form').find('[data-context=ecotax-code] input').should('have.value', '54321')
                cy.get('@form').find('[data-context=ecotax-code] [data-context=leading]').should('not.exist')
            })

            it('should show a warning when ecotax code is sourced from subcategory', function () {
                visitPage()

                cy.get('@form')
                    .find('[data-context=ecotax-code]  [data-context=erp-form-label]')
                    .should('contain', 'Code écotaxe')
                cy.get('@form').find('[data-context=ecotax-code] input').should('have.value', '54321')
                cy.get('@form')
                    .find('[data-context=ecotax-code] [data-context=leading]')
                    .should('be.visible')
                    .tooltip('Code écotaxe de la sous-catégorie')
            })
        })
    })

    describe('Update information related to the product logistic', function () {
        beforeEach(() => {
            cy.mockErpUser([ARTICLE_LOGISTIC_WRITE])

            cy.intercept('PUT', '**/api/erp/v1/article/143214', {
                body: { status: 'success', data: { updated: 1 } },
            }).as('put_article')
        })

        it('Should update an article successfully', () => {
            visitPage()
            cy.get('@form').should('be.visible')
            ;[
                { block: 'package_unit', input: '5' },
                { block: 'is_packaged', toggle: true },
                { block: 'number_of_packages', input: '2' },
                { block: 'weight', input: '0.2' },
                { block: 'weight_tmp', toggle: true },
                { block: 'source_country_id', multiselect: 'BELGIQUE' },
                { block: 'customs_code', input: '*********' },
                { block: 'ecotax-code', input: '12345' },
            ].forEach((block) => {
                if (block.input) {
                    cy.get('@form').find(`[data-context=${block.block}] input`).as('input')
                    cy.get('@input').clear()
                    cy.get('@input').type(block.input)
                }

                if (block.toggle) {
                    cy.get('@form').find(`[data-context=${block.block}] [role=switch]`).click()
                }

                if (block.multiselect) {
                    cy.get('@form')
                        .find(`[data-context=${block.block}]  [data-context=erp-multiselect]`)
                        .erpMultiselect(block.multiselect.slice(3).toLowerCase(), block.multiselect)
                }
            })

            cy.get('@form').find('[data-context=erp-button]').should('not.be.disabled').click()

            cy.wait('@put_article').then((xhr) => {
                expect(xhr.request.body.scope).to.equal('logistic_information')
                expect(xhr.request.body.data).to.deep.equals({
                    weight: 0.2,
                    weight_tmp: 'Y',
                    is_packaged: 'Y',
                    customs_code: '*********',
                    package_unit: 5,
                    source_country_id: 19,
                    number_of_packages: 2,
                    ecotax_code: '12345',
                    // ignored fields
                    customs_code_origin: 'article',
                    rotations: {
                        '7_days': 0,
                        '30_days': 0,
                        '90_days': 0,
                    },
                    code_128: '12140143214',
                    source_country_name: null,
                    ecotax_code_origin: 'article',
                })
            })
            cy.wait('@get_article_by_id_or_sku_v2')
        })

        describe('Custom code behaviour', function () {
            it('Should not set the customs code when a category code is used', () => {
                visitPage()
                cy.get('@form').should('be.visible')

                cy.get('@form').find('[data-context=customs_code] input').as('input')
                // sets a new customs code
                cy.get('@input').clear()
                cy.get('@input').type('*********')
                cy.get('@form').find('[data-context=customs_code] [data-context=leading]').should('not.exist')

                // set the original category's code back
                cy.get('@input').clear()
                cy.get('@input').type('85271900')

                cy.get('@form')
                    .find('[data-context=customs_code] [data-context=leading]')
                    .should('be.visible')
                    .tooltip('Code douanier de la catégorie')

                cy.get('@form').find('[data-context=erp-button]').click()

                cy.wait('@put_article').then((xhr) => {
                    expect(xhr.request.body.data.customs_code).to.equal(null)
                })
                cy.wait('@get_article_by_id_or_sku_v2')
            })

            it('Should set the customs code to null for an empty code', () => {
                cy.fixture('erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013').then((payload) => {
                    payload.data.logistic.customs_code_origin = 'article'

                    cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visitPage()
                cy.get('@form').should('be.visible')

                cy.get('@form').find('[data-context=customs_code] input').as('input')
                cy.get('@input').clear()
                cy.get('@form')
                    .find('[data-context=customs_code] [data-context=leading]')
                    .should('be.visible')
                    .tooltip('Le code douanier de la sous-catégorie ou la catégorie sera utilisé')

                cy.get('@form').find('[data-context=erp-button]').click()

                cy.wait('@put_article').then((xhr) => {
                    expect(xhr.request.body.data.customs_code).to.equal(null)
                })
                cy.wait('@get_article_by_id_or_sku_v2')
            })
        })

        describe('Ecotax code behaviour', function () {
            it('Should set the ecotax code to null for an empty code', () => {
                cy.fixture('erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013').then((payload) => {
                    payload.data.logistic.ecotax_code_origin = 'article'

                    cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visitPage()
                cy.get('@form').should('be.visible')

                cy.get('@form').find('[data-context=ecotax-code] input').as('input')
                cy.get('@input').clear()
                cy.get('@form')
                    .find('[data-context=ecotax-code] [data-context=leading]')
                    .should('be.visible')
                    .tooltip('Le code écotaxe de la sous-catégorie sera utilisé')

                cy.get('@form').find('[data-context=erp-button]').click()

                cy.wait('@put_article').then((xhr) => {
                    expect(xhr.request.body.data.ecotax_code).to.equal(null)
                })
                cy.wait('@get_article_by_id_or_sku_v2')
            })
        })

        it('should display validation errors', function () {
            cy.mockErpUser([ARTICLE_LOGISTIC_WRITE])
            visitPage()
            cy.get('@form').should('be.visible')

            cy.intercept('PUT', '**/api/erp/v1/article/143214', {
                statusCode: 400,
                body: {
                    status: 'error',
                    message: 'Invalid parameters',
                    code: 400,
                    data: {
                        validation_errors: {
                            weight: '[key:unauthorized_no_weight] you took this too lightly',
                            customs_code: '[key:customs_code_format_error] what were you thinking',
                            package_unit: 'This value should be either positive or zero.',
                            number_of_packages: 'This value should be either positive or zero.',
                        },
                    },
                },
            }).as('put_article')

            cy.get('@form').find('[data-context=erp-button]').click()

            cy.wait('@put_article')
            ;[
                { context: 'weight', message: 'Le poids ne peut pas être 0 pour le statut de cet article' },
                { context: 'customs_code', message: 'Le code douanier doit être un nombre de 0 à 12 chiffres' },
                { context: 'package_unit', message: 'Cette valeur doit être positive ou zéro' },
                { context: 'number_of_packages', message: 'Cette valeur doit être positive ou zéro' },
            ].forEach((o) => {
                cy.get('@form')
                    .find(`[data-context=${o.context}] [data-context=erp-input-helper]`)
                    .should('have.class', 'text-red-600')
                    .should('contain', o.message)
            })
        })
    })
})
