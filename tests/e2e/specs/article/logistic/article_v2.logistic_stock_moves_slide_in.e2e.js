import { PRODUCT_STOCK_READ, STOCK_MOVE_BUY_PRICE_UPDATE } from '../../../../../src/apps/erp/permissions.js'
import { ARTICLE_LOGISTIC_STOCK_ACTIVE_PARENTS_KEY } from '../../../../../src/apps/erp/components/Article/constants'

describe('Show the stock move list', function () {
    beforeEach(function () {
        cy.window().its('localStorage').invoke('removeItem', ARTICLE_LOGISTIC_STOCK_ACTIVE_PARENTS_KEY)

        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v1/wms/product/143214/locations', {
            fixture: 'erp/product/locations-id-143214',
        })
            .as('locations-id-143214')
            .as('first_location_request')

        cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/warehouses', { fixture: 'erp/warehouse/warehouses' }).as(
            'warehouses_request',
        )

        cy.intercept('POST', '**/api/erp/v1/stock-moves', { fixture: 'erp/stock-move/list.json' }).as(
            'fetch-stock-moves',
        )
        cy.intercept('POST', '**/api/erp/v2/wms/warehouses', { fixture: 'erp/wms/warehouses/v2/warehouses.json' }).as(
            'fetch-warehouses',
        )
    })

    const DEFAULT_PERMISSIONS = [PRODUCT_STOCK_READ]

    const visitPageAndOpenSlideIn = (permissions = DEFAULT_PERMISSIONS, frozenDate = undefined) => {
        cy.mockErpUser([...permissions])
        cy.visit('/articles/DESTOCK-20191120013/logistic')

        // freeze clock if necessary
        // Must be placed directly after visit() otherwise it will not work as you expect
        if (frozenDate) {
            cy.mockDate(frozenDate)
        }

        // Initial calls on the page
        cy.wait(['@get_article_by_id_or_sku_v2', '@warehouses_request'])
        cy.toggleMenu()
        cy.closeAllToasts()

        cy.get('[data-context=stock-move-filtered-slide-out]').should('not.exist')
        cy.get('[data-context=stock-move-btn]').first().click()
        cy.get('[data-context=stock-move-filtered-slide-out]').as('slide-out')
        cy.get('[data-context=stock-move-filtered-slide-out] [data-context=erp-table]').as('table')

        cy.wait('@fetch-stock-moves')
    }

    it('display the table', function () {
        visitPageAndOpenSlideIn()

        // Can't use be.visible due to the scroll feature
        cy.get('@table').should('exist')
        cy.get('@table').find('thead th').as('headers')

        const expected_headers = [
            'Date',
            'Type',
            'Dépôt',
            'Emplacement',
            'Utilisateur',
            'QTE',
            "Prix d'achat",
            'Prix de vente',
            'BL / TRF',
            'Origine',
            'Commentaire',
        ]
        expected_headers.forEach((item, i) => cy.get('@headers').eq(i).should('contain', item))
    })

    it('show the stock moves in the table', function () {
        visitPageAndOpenSlideIn()

        // test the values in the table
        cy.get('@table').find('tbody tr').as('rows')
        cy.get('@rows').first().find('td').as('cells')

        const expected_values = [
            ['16/02/2022 16:23:37'],
            ['interne'],
            ['Grenoble'],
            ['Emplacement par défaut (Grenoble)'],
            ['admin'],
            ['1'],
            ['50,00 €'],
            ['150,42 €'],
            [
                {
                    id: 4920091,
                    trigram: 'BL',
                    link: '/legacy/v1/commandes/edition_commande.php?id_commande=4301839',
                },
                { id: 171319, trigram: 'TRF', link: '/legacy/stock/bonTransfertEdit?id=171319' },
            ],
            [
                { id: 27030, trigram: 'AVR', link: '/legacy/v1/commandes/affichage_facture.php?id_facture=27030' },
                {
                    id: 104743,
                    trigram: 'FRN',
                    link: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=104743',
                },
                { id: 76696, trigram: 'RTR', link: '/v1/retours/bon_retour_pdf.php?id_bon_retour=76696' },
                { id: 76661, trigram: 'KDO', link: '/legacy/v1/cartes_cadeau/tableau-bord.php?id_carte=76661' },
            ],
            ['test filtre depot Grenoble'],
        ]
        expected_values.forEach((values, i) => {
            if (values.length > 1) {
                // test the badges
                values.forEach((value, j) => {
                    // link
                    cy.get('@cells').eq(i).find('a').eq(j).attribute('href').should('contain', value.link)
                    // trigram
                    cy.get('@cells')
                        .eq(i)
                        .find('[data-context="scope"]')
                        .find('span.uppercase')
                        .eq(j)
                        .should('contain', value.trigram)
                    // id displayed
                    cy.get('@cells').eq(i).find('[data-context="content"]').should('contain', value.id)
                })
            } else {
                // test the string value of the cell
                cy.get('@cells').eq(i).should('contain', values[0])
            }
        })
    })

    it(`doesn't display buy and sell price without values`, function () {
        visitPageAndOpenSlideIn()

        cy.get('@table').find('tbody tr').eq(1).as('row')
        const price_columns = [6, 7]
        price_columns.forEach((column_index) => {
            cy.get('@row')
                .find('td')
                .eq(column_index)
                .should((elt) => expect(elt.text().trim()).equal(''))
        })
    })

    it('filters the table when selecting a date range', function () {
        // freeze clock - Today is 09/03/2023
        cy.mockDate(new Date(2023, 2, 9, 20, 1, 0, 0))

        visitPageAndOpenSlideIn()

        cy.get('@slide-out')
            .find('[data-context=warehouse-autocomplete] .multiselect__tag')
            .should('contain', 'Champigny')

        // select 01/03/2023 as start date
        cy.get('[data-context="start-date"]').click().find('[data-context=day]').eq(2).click()
        cy.get('[data-context="start-date"] [data-context="day"]').should('not.exist')

        cy.get('[data-context="filters-submit-btn"]').as('rechercher').click()
        cy.get('@fetch-stock-moves').should(({ request }) => {
            expect(request.body.where._and[1].created_at._gt).is.equal('2023-03-01 00:00:00')
            expect(request.body.where._and.length).is.equal(4)
        })

        // select 2023/03/31 as end date
        cy.get('[data-context="end-date"]').click().find('[data-context=day]').eq(32).click()
        cy.get('[data-context="end-date"] [data-context="day"]').should('not.exist')

        // submit the search
        cy.get('@rechercher').click()

        cy.wait('@fetch-stock-moves')
        cy.get('@fetch-stock-moves').should(({ request }) => {
            expect(request.body.where._and[1].created_at._gt).is.equal('2023-03-01 00:00:00')
            expect(request.body.where._and[2].created_at._lt).is.equal('2023-03-31 23:59:59')
            expect(request.body.where._and.length).is.equal(5)
        })
    })

    it('filters the table when selecting a warehouse name', function () {
        visitPageAndOpenSlideIn()

        // remove default warehouse
        cy.get('[data-context="warehouse-autocomplete"]').multiselectRemoveTag('Champigny')
        // search a new warehouse
        cy.get('[data-context="warehouse-autocomplete"]').multiselect('grenoble')
        cy.wait('@fetch-warehouses')
        cy.get('[data-context="filters-submit-btn"]').click()

        cy.wait('@fetch-stock-moves')
        cy.get('@fetch-stock-moves').should(({ request }) => {
            expect(request.body.where._and[1]._or[0].warehouse_id._eq).is.equal(10)
        })
    })

    it('filters the table when changing the type multiselect', function () {
        visitPageAndOpenSlideIn()

        cy.get('@slide-out')
            .find('[data-context=warehouse-autocomplete] .multiselect__tag')
            .should('contain', 'Champigny')

        // test 3 tags by default
        const expect_3_tags = ['sortie', 'entree', 'correction']
        cy.get('@fetch-stock-moves').should(({ request }) => {
            expect_3_tags.forEach((tag) => {
                expect(request.body.where._and[2]._or).to.deep.include({ type: { _eq: tag } })
            })
            expect(request.body.where._and[2]._or.length).is.equal(3)
        })

        // test with 1
        cy.get('[data-context=styled-multiselect]').as('multiselect')
        cy.get('@multiselect').multiselectRemoveTag('sortie').multiselectRemoveTag('entree')
        cy.get('[data-context="filters-submit-btn"]').as('rechercher')
        cy.get('@rechercher').click()

        cy.wait('@fetch-stock-moves')
        let expect_1_tag = ['correction']
        cy.get('@fetch-stock-moves').should(({ request }) => {
            expect_1_tag.forEach((tag) => {
                expect(request.body.where._and[2]._or).to.deep.include({ type: { _eq: tag } })
            })
            expect(request.body.where._and[2]._or.length).is.equal(1)
        })

        // test with 0
        cy.get('@multiselect').multiselectRemoveTag('correction')
        cy.get('@rechercher').click()

        cy.wait('@fetch-stock-moves').then(({ request }) => {
            expect(request.body.where._and.length).is.equal(2)
        })

        // test with all
        cy.get('@multiselect')
            .multiselect(null, 'correction')
            .multiselect(null, 'sortie')
            .multiselect(null, 'entree')
            .multiselect(null, 'interne')
        cy.get('@rechercher').click()

        const expect_4_tags = ['interne', 'sortie', 'entree', 'correction']
        cy.wait('@fetch-stock-moves').then(({ request }) => {
            expect_4_tags.forEach((tag) => {
                expect(request.body.where._and[2]._or).to.deep.include({ type: { _eq: tag } })
            })
            expect(request.body.where._and[2]._or.length).is.equal(4)
        })
    })

    it('filters the table when searching for a BL number', function () {
        visitPageAndOpenSlideIn()

        cy.get('[data-context="filter-bl"]').type('4913057')
        cy.get('[data-context="filters-submit-btn"]').click()

        const expected_filters = [
            { comment: { _like: `%4913057%` } },
            { delivery_note_customer_id: { _like: `4913057%` } },
        ]
        cy.wait('@fetch-stock-moves').then(({ request }) => {
            expected_filters.forEach((filter) => {
                expect(request.body.where._and[1]._or).to.deep.include(filter)
            })
        })
    })

    it('filters the table when searching for a transfer number', function () {
        visitPageAndOpenSlideIn()

        cy.get('[data-context="filter-transfert"]').type('1713319')
        cy.get('[data-context="filters-submit-btn"]').click()

        cy.wait('@fetch-stock-moves').then(({ request }) => {
            expect(request.body.where._and[1]).to.deep.include({ transfer_id: { _like: `1713319%` } })
        })
    })

    it('filters the table when searching for a supplier order id', function () {
        visitPageAndOpenSlideIn()

        cy.get('[data-context="filter-supplier"]').type('104743')
        cy.get('[data-context="filters-submit-btn"]').click()

        cy.wait('@fetch-stock-moves').then(({ request }) => {
            expect(request.body.where._and[1]).to.deep.include({ supplier_order_id: { _like: `104743%` } })
        })
    })

    it('sends an ajax when updating the buy price', function () {
        cy.intercept('PUT', '**/api/erp/v1/stock-move/9430100', {
            body: {},
        }).as('successful-put-buy-price')

        visitPageAndOpenSlideIn([PRODUCT_STOCK_READ, STOCK_MOVE_BUY_PRICE_UPDATE])

        cy.get('[data-context=stock-move-filtered-list-buy-price]').eq(1).as('buy-price-form')
        cy.get('@buy-price-form').find('input').clear().type('42')
        cy.get('@buy-price-form').find('button[type=submit]').click()

        cy.wait('@successful-put-buy-price').then(({ request }) => {
            expect(request.body.buy_price).to.deep.include('42')
        })
    })

    it('show the original buy price when put request fail', function () {
        cy.intercept('PUT', '**/api/erp/v1/stock-move/9430100', {
            body: {},
            statusCode: 400,
        }).as('failed-put-buy-price')

        visitPageAndOpenSlideIn([PRODUCT_STOCK_READ, STOCK_MOVE_BUY_PRICE_UPDATE])

        cy.get('[data-context=stock-move-filtered-list-buy-price]').eq(1).as('buy-price-form')
        cy.get('@buy-price-form').find('input').as('buy-price-input')
        cy.get('@buy-price-input').should('have.value', 86.16)
        cy.get('@buy-price-input').clear().type('42')
        cy.get('@buy-price-form').find('button[type=submit]').click()

        cy.wait('@failed-put-buy-price')

        cy.toast('Une erreur est survenue, veuillez réessayer.', 'danger')
        cy.get('@buy-price-input').should('have.value', 86.16)
    })

    it(`does not allow price change if the user don't have the permission`, function () {
        visitPageAndOpenSlideIn()

        cy.get('[data-context=stock-move-filtered-slide-out] [data-context=stock-move-filtered-list-buy-price]').should(
            'not.exist',
        )
    })

    it(`does not allow price change if the format isn't valid`, function () {
        visitPageAndOpenSlideIn([PRODUCT_STOCK_READ, STOCK_MOVE_BUY_PRICE_UPDATE])

        cy.get('[data-context=stock-move-filtered-list-buy-price]').eq(1).as('buy-price-form')
        cy.get('@buy-price-form').find('input').as('buy-price-input')

        cy.get('@buy-price-input').clear().type('42.609')
        cy.get('@buy-price-form').find('button[type=submit]').click()

        cy.get('@buy-price-form').find('input:invalid').should('have.length', 1)

        cy.get('@buy-price-input').clear().type('0')
        cy.get('@buy-price-form').find('button[type=submit]').click()
        cy.get('@buy-price-form').find('input:invalid').should('have.length', 1)
    })

    it('display a toast when the price update is successful', function () {
        cy.intercept('PUT', '**/api/erp/v1/stock-move/9430100', {
            body: { status: 'success', data: { new_weighted_cost: 86.16 } },
        }).as('successful-put-buy-price')

        visitPageAndOpenSlideIn([PRODUCT_STOCK_READ, STOCK_MOVE_BUY_PRICE_UPDATE])

        cy.get('[data-context=stock-move-filtered-list-buy-price]').eq(1).as('buy-price-form')
        cy.get('@buy-price-form').find('input').clear().type('42')
        cy.get('@buy-price-form').find('button[type=submit]').click()

        cy.wait('@successful-put-buy-price').then(({ request }) => {
            expect(request.body.buy_price).to.deep.include('42')
        })

        cy.toast("Le prix d'achat a bien été mis à jour. Nouveau prix d'achat pondéré : 86.16€", 'success')
    })

    it('filters the table when changing the warehouse multiselect', function () {
        visitPageAndOpenSlideIn()

        cy.get('@slide-out')
            .find('[data-context=warehouse-autocomplete] .multiselect__tag')
            .should('contain', 'Champigny')

        // test with 1 warehouse
        cy.get('[data-context=warehouse-autocomplete]').as('multiselect')
        cy.get('[data-context="filters-submit-btn"]').as('rechercher')
        cy.get('@rechercher').click()
        cy.get('@fetch-stock-moves').should(({ request }) => {
            expect(request.body.where._and[1]._or[0]).to.deep.include({ warehouse_id: { _eq: 1 } })
            expect(request.body.where._and[1]._or.length).is.equal(1)
        })

        // test with 2 warehouses
        cy.get('@multiselect').multiselect('havre')
        cy.get('@rechercher').click()
        const expected_warehouses_1 = [1, 3]
        cy.get('@fetch-stock-moves').should(({ request }) => {
            expected_warehouses_1.forEach((warehouse_id, index) => {
                expect(request.body.where._and[1]._or[index]).to.deep.include({ warehouse_id: { _eq: warehouse_id } })
            })
        })

        // test with 3 warehouses
        cy.get('@multiselect').multiselect('nantes')
        cy.get('@rechercher').click()
        const expected_warehouses_2 = [1, 3, 5]
        cy.get('@fetch-stock-moves').should(({ request }) => {
            expected_warehouses_2.forEach((warehouse_id, index) => {
                expect(request.body.where._and[1]._or[index]).to.deep.include({ warehouse_id: { _eq: warehouse_id } })
            })
        })
        cy.get('@multiselect').contains('3 dépôts sélectionnés')
    })

    it('saves filters when reloading the slideout', function () {
        visitPageAndOpenSlideIn(DEFAULT_PERMISSIONS, new Date(2022, 2, 9, 20, 1, 0, 0))
        cy.get('[data-context="filters-submit-btn"]').as('rechercher')

        // select 01/03/2022 as start date
        cy.get('[data-context=start-date]').click().find('[data-context=day]:contains(1)').eq(0).click()
        cy.get('[data-context=start-date] [data-context="day"]').should('not.exist')

        // select 31/03/2022 as end date
        cy.get('[data-context=end-date]').click().find('[data-context=day]:contains(31)').click()
        cy.get('[data-context=end-date] [data-context="day"]').should('not.exist')

        cy.get('[data-context=styled-multiselect]').as('multiselect')
        cy.get('@multiselect').multiselectRemoveTag('sortie').multiselectRemoveTag('entree')

        cy.get('[data-context=filter-bl]').type('a')
        cy.get('[data-context=filter-transfert]').type('b')
        cy.get('[data-context=filter-supplier]').type('c')

        cy.get('@rechercher').click()

        // reopen the slide out
        cy.get('.slide-out-overlay').click({ force: true })
        cy.get('[data-context=stock-move-btn]').first().click()

        // check values
        cy.get('[data-context=start-date]').find('input').should('have.value', '01 mars 2022 à 00:00')
        cy.get('[data-context=end-date]').find('input').should('have.value', '31 mars 2022 à 23:59')
        // no check on warehouse, it's override by the opening on a specific one
        cy.get('[data-context=filter-type]').find('.multiselect__tag').should('contain', 'correction')
        cy.get('[data-context=filter-bl]').find('input').should('have.value', 'a')
        cy.get('[data-context=filter-transfert]').find('input').should('have.value', 'b')
        cy.get('[data-context=filter-supplier]').find('input').should('have.value', 'c')
    })

    it("doesn't saves filters when reloading the page", function () {
        visitPageAndOpenSlideIn(DEFAULT_PERMISSIONS, new Date(2044, 2, 9, 20, 1, 0, 0))

        cy.get('[data-context="filters-submit-btn"]').as('submit')

        // select 01/03/2044 as start date
        cy.get('[data-context=start-date]').click().find('[data-context=day]:contains(1)').eq(0).click()
        cy.get('[data-context=start-date] [data-context="day"]').should('not.exist')

        // select 31/03/2044 as end date
        cy.get('[data-context=end-date]').click().find('[data-context=day]:contains(31)').click()
        cy.get('[data-context=end-date] [data-context="day"]').should('not.exist')

        cy.get('[data-context=styled-multiselect]').multiselectRemoveTag('sortie').multiselectRemoveTag('entree')

        cy.get('[data-context=filter-bl]').type('a')
        cy.get('[data-context=filter-transfert]').type('b')
        cy.get('[data-context=filter-supplier]').type('c')

        cy.get('@submit').click()

        // reload the page and check
        visitPageAndOpenSlideIn()

        cy.get('[data-context=start-date]').find('input').should('have.value', '')
        cy.get('[data-context=end-date]').find('input').should('have.value', '')
        cy.get('[data-context=filter-type]').find('.multiselect__tag').should('contain', 'correction')
        cy.get('[data-context=filter-type]').find('.multiselect__tag').should('contain', 'sortie')
        cy.get('[data-context=filter-type]').find('.multiselect__tag').should('contain', 'entree')
        cy.get('[data-context=filter-bl]').find('input').should('have.value', '')
        cy.get('[data-context=filter-transfert]').find('input').should('have.value', '')
        cy.get('[data-context=filter-supplier]').find('input').should('have.value', '')
    })
})
