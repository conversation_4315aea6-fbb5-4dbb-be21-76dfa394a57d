import {
    PRODUCT_LOCATION_UNLINK_DELIVERY,
    PRODUCT_LOCATION_UPDATE,
    PRODUCT_STOCK_READ,
} from '../../../../../src/apps/erp/permissions'

import { NON_BREAKING_SPACE } from '../../../utils/text-utils'
import { ARTICLE_LOGISTIC_STOCK_ACTIVE_PARENTS_KEY } from '../../../../../src/apps/erp/components/Article/constants'

const openProductLocationRow = () => {
    cy.get('[data-context=stock-overview]').selectRow(1)

    cy.get('[data-context=stock-overview]').find('tbody tr').should('have.length', 5)

    cy.selectRow(2)
    cy.selectCell(0).should('contain', 'Champigny')
    cy.selectCell(0).find('svg').click()

    cy.get('[data-context=stock-overview]').find('tbody tr').should('have.length', 12)

    cy.selectRow(3)
    cy.selectCell(0).should('contain', '03.01.A$01.00.03')
    cy.selectCell(3).find('[data-context=update-product-location-btn]').click()
}

describe('ERP WMS - Stock dashboard overview', function () {
    beforeEach(() => {
        cy.window().its('localStorage').invoke('removeItem', ARTICLE_LOGISTIC_STOCK_ACTIVE_PARENTS_KEY)

        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/wms/product/143214/locations', {
            fixture: 'erp/product/locations-id-143214',
        })
            .as('locations-id-143214')
            .as('first_location_request')

        cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/warehouses', { fixture: 'erp/warehouse/warehouses' }).as(
            'warehouses_request',
        )
    })

    const visitPage = () => {
        cy.visit('/articles/DESTOCK-20191120013/logistic')
        cy.closeAllToasts()

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.wait('@warehouses_request')
    }

    describe('List stock per warehouse and stores', function () {
        beforeEach(() => {
            cy.mockErpUser([PRODUCT_STOCK_READ])
        })

        it('provides page with the WMS move missions for an authorized user', function () {
            visitPage()

            // Check table header
            cy.get('[data-context=stock-overview]').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 4)

            cy.get('@header').eq(0).should('contain', 'Dépot')
            cy.get('@header').eq(1).should('contain', 'Quantité')
            cy.get('@header').eq(2).should('contain', 'Infos')
            cy.get('@header').eq(3).should('contain', 'Actions')
        })

        it('check quantity & infos', function () {
            visitPage()

            // Check total row
            cy.get('[data-context=stock-overview]').selectRow(0)
            cy.selectCell(0).should('contain', 'Total')
            cy.selectCell(1).should('contain', '1040')

            // Check warehouses row
            cy.selectRow(1)
            cy.selectCell(0).should('contain', 'Entrepôts')
            cy.selectCell(1).should('contain', '1040')

            cy.selectRow(2)
            cy.selectCell(0).should('contain', 'Champigny')
            cy.selectCell(1).should('contain', '1040')

            // Check product location row
            cy.selectCell(0).click()

            cy.selectRow(3)
            cy.selectCell(0).should('contain', '03.01.A$01.00.03')
            cy.selectCell(1).should('contain', '10')
            cy.selectCell(2).should('contain', 'Stock')

            cy.selectRow(6)
            cy.selectCell(0).should('contain', '03.01.D$04.05.03')
            cy.selectCell(1).should('contain', '5')
            cy.selectCell(2).should('contain', 'MM 13')

            cy.selectRow(7)
            cy.selectCell(0).should('contain', '03.01.D$04.05.03')
            cy.selectCell(1).should('contain', '2')
            cy.selectCell(2).should('contain', 'BL 4294623')

            // Check stores row
            cy.selectRow(11)
            cy.selectCell(0).should('contain', 'Magasins')
            cy.selectCell(1).should('contain', '0')
        })
    })

    describe('Update product location', function () {
        beforeEach(() => {
            cy.mockErpUser([PRODUCT_STOCK_READ, PRODUCT_LOCATION_UPDATE])
        })

        it('check validation messages', function () {
            visitPage()

            // Check table header
            cy.get('[data-context=stock-overview]').as('stock_table')
            cy.get('@stock_table').find('thead tr th').as('header')

            // Select first production location
            openProductLocationRow()

            cy.get('[data-context=slide-out-container]').as('slide_out_container')
            cy.get('@slide_out_container').find('[data-context=submit-btn]').click()

            cy.get('@slide_out_container')
                .find('[data-context=error-message]')
                .should(
                    'contain',
                    `Une ou plusieurs erreurs empêchent la mise à jour des informations, veuillez les corriger.`,
                )

            cy.get('@slide_out_container')
                .find('[data-context=quantity] [data-context=validation-message]')
                .should('contain', `La quantité est obligatoire`)

            cy.get('@slide_out_container')
                .find('[data-context=comment] [data-context=validation-message]')
                .should('contain', `Le commentaire est obligatoire`)

            cy.get('@slide_out_container').find('[data-context=quantity] input').type('-1')

            cy.get('@slide_out_container').find('[data-context=comment] textarea').type(`Not enough`)

            cy.get('@slide_out_container')
                .find('[data-context=quantity] [data-context=validation-message]')
                .should('contain', `Vous ne pouvez pas saisir de quantité négative`)

            cy.get('@slide_out_container')
                .find('[data-context=comment] [data-context=validation-message]')
                .should('contain', `Le commentaire doit comporter au minimum 15 caractères.`)
        })

        it('update stock quantity successfully', function () {
            visitPage()

            // Check table header
            cy.get('[data-context=stock-overview]').as('stock_table')
            cy.get('@stock_table').find('thead tr th').as('header')

            // Select first production location
            openProductLocationRow()

            cy.get('[data-context=slide-out-container]').as('slide_out_container')

            cy.get('@slide_out_container').find('[data-context=quantity] input').type('666')

            cy.get('@slide_out_container').find('[data-context=comment] textarea').type(`This is not a lucky number`)

            cy.intercept('PUT', '**/api/erp/v1/wms/product/143214/location/7460', {
                statusCode: 200,
                body: true,
            }).as('successful_request')

            cy.get('@slide_out_container').find('[data-context=submit-btn]').click()

            cy.wait('@successful_request')

            cy.toast(`L'emplacement a été mis à jour.`, 'success')

            cy.get('@slide_out_container').should('not.exist')
        })

        it('unlink delivery note', function () {
            visitPage()

            // Select row with a delivery note
            cy.get('[data-context=stock-overview]').selectRow(2)
            cy.selectCell(0).click()
            cy.selectRow(7)
            cy.selectCell(2).should('contain', 'BL 4294623')

            // btn not displayed without proper right
            cy.selectCell(3).find('[data-context=unlink-delivery-btn]').should('be.disabled')
            cy.addPermissions([PRODUCT_LOCATION_UNLINK_DELIVERY])
            cy.selectCell(3).find('[data-context=unlink-delivery-btn]').should('exist').as('unlink_btn')

            // open confirmation slide-out
            cy.get('@unlink_btn').click()
            cy.get('[data-context=unlink-delivery-confirmation]').as('confirmation').should('be.visible')

            // test confirmation content
            cy.get('@confirmation').find('[data-context=page-header]').should('contain', 'Désassocier du BL 4294623')
            cy.get('@confirmation')
                .should(
                    'contain',
                    `En poursuivant, la quantité associée au BL 4294623 sera réassignée comme du stock libre dans l’emplacement « 03.01.D$04.05.03 ».`,
                )
                .should('contain', `Êtes vous sûr${NON_BREAKING_SPACE}?`)
            cy.get('@confirmation')
                .find('[data-context=submit-btn]')
                .as('submit_btn')
                .should('contain', 'Oui, désassocier')

            // test error display
            cy.intercept('POST', '**/api/erp/v1/wms/product/143214/location/8000/unlink', {
                statusCode: 500,
                body: {},
            }).as('error_request')
            cy.get('@submit_btn').click()

            cy.wait('@error_request')

            cy.toast(`Une erreur est survenue, veuillez réessayer.`, 'danger')

            // test submission success
            cy.intercept('POST', '**/api/erp/v1/wms/product/143214/location/8000/unlink', {
                statusCode: 200,
                body: true,
            }).as('successful_request')
            cy.get('@submit_btn').click()

            cy.wait('@successful_request').then((xhr) => {
                expect(xhr.request.body.delivery_ticket_id).to.eq(4294623)
            })
            cy.wait('@warehouses_request')

            cy.toast(`L'emplacement a été mis à jour.`, 'success')

            cy.get('[data-context=unlink-delivery-confirmation]', { timeout: 20000 }).should('not.exist')
        })

        it('display proper error messages', function () {
            visitPage()

            // Check table header
            cy.get('[data-context=stock-overview]').as('stock_table')

            // Select first production location
            openProductLocationRow()

            cy.get('[data-context=slide-out-container]').as('slide_out_container')

            cy.get('@slide_out_container').find('[data-context=quantity] input').type('666')

            cy.get('@slide_out_container').find('[data-context=comment] textarea').type(`This is not a lucky number`)

            // error code < 1000 => display default message
            cy.intercept('PUT', '**/api/erp/v1/wms/product/143214/location/7460', {
                statusCode: 500,
                body: {
                    code: 500,
                    message: 'Un message custom de l’API',
                    data: {},
                },
            }).as('request')
            cy.get('@slide_out_container').find('[data-context=submit-btn]').click()
            cy.wait('@request')

            cy.get('@slide_out_container')
                .find('[data-context=error-message]')
                .should('contain', `Une erreur est survenue lors du chargement, veuillez réessayer.`)

            // error code 1000 => display message from API
            cy.intercept('PUT', '**/api/erp/v1/wms/product/143214/location/7460', {
                statusCode: 500,
                body: {
                    code: 1000,
                    message: 'Un message custom de l’API',
                    data: {},
                },
            }).as('request')
            cy.get('@slide_out_container').find('[data-context=submit-btn]').click()
            cy.wait('@request')

            cy.get('@slide_out_container')
                .find('[data-context=error-message]')
                .should('contain', `Un message custom de l’API`)

            // error code 1001 => display translated message
            cy.intercept('PUT', '**/api/erp/v1/wms/product/143214/location/7460', {
                statusCode: 500,
                body: {
                    code: 1001,
                    message: 'Un message custom de l’API',
                    data: {},
                },
            }).as('request')
            cy.get('@slide_out_container').find('[data-context=submit-btn]').click()
            cy.wait('@request')

            cy.get('@slide_out_container')
                .find('[data-context=error-message]')
                .should('contain', `Un produit stock B ne peut pas avoir plus de 1 comme quantité en stock`)
        })
    })
})
