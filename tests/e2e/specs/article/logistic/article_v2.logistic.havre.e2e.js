import { ARTICLE_LOGISTIC_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Logistique - Havre', function () {
    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')
    })

    const visitPage = () => {
        cy.visit('/articles/KEFQ350NR/logistic')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.closeAllToasts()
        cy.toggleMenu()
        cy.closeAllToasts()
    }

    it('should display non-editable form', function () {
        cy.mockErpUser()
        visitPage()

        cy.get('[data-context=article-logistic-havre]').within(($form) => {
            cy.get('[data-context=erp-form-label]:contains(SKU)')
                .siblings('[data-context=erp-input]')
                .should('be.disabled')
            cy.get('[data-context=erp-form-label]:contains(Stock actif)')
                .closest('[data-context=erp-toggle]')
                .should('have.attr', 'data-disabled')
            cy.get('[data-context=erp-form-label]:contains(Colisage)')
                .siblings('[data-context=erp-input-number]')
                .should('be.disabled')
            cy.get('[data-context=erp-button]:contains(Sauvegarder)').should('be.disabled')
        })
    })

    it(`should let us edit the article information`, function () {
        cy.mockErpUser([ARTICLE_LOGISTIC_WRITE])
        visitPage()

        cy.get('[data-context=article-logistic-havre]').within(($form) => {
            // No form element are disabled
            cy.get('[data-context=erp-form-label]:contains(SKU)')
                .siblings('[data-context=erp-input]')
                .should('not.be.disabled')
            cy.get('[data-context=erp-form-label]:contains(Stock actif)')
                .closest('[data-context=erp-toggle]')
                .should('not.have.attr', 'data-disabled')
            cy.get('[data-context=erp-form-label]:contains(Colisage)')
                .siblings('[data-context=erp-input-number]')
                .should('not.be.disabled')
            cy.get('[data-context=erp-button]:contains(Sauvegarder)').should('not.be.disabled')

            // edit
            cy.get('[data-context=erp-form-label]:contains(SKU)').siblings('[data-context=erp-input]').type('toto')
            cy.get('[data-context=erp-form-label]:contains(Stock actif)').siblings('button').click()
            cy.get('[data-context=erp-form-label]:contains(Colisage)')
                .siblings('[data-context=erp-input-number]')
                .clear()
            cy.get('[data-context=erp-form-label]:contains(Colisage)')
                .siblings('[data-context=erp-input-number]')
                .type(2)

            cy.intercept('PUT', '**/api/erp/v1/article/117735', { statusCode: 201, body: {} }).as('put_request')

            cy.get('[data-context=erp-button]').click()

            cy.wait('@put_request').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    scope: 'havre',
                    data: {
                        sku_havre: 'toto',
                        is_active_havre: true,
                        package_unit_havre: 2,
                    },
                })
            })
        })
    })

    it('Logistic havre form is not display for destocks', function () {
        cy.mockErpUser()
        cy.fixture('erp/article/get_article_by_id_or_sku_v2__KEFQ350NR').then((payload) => {
            payload.data.is_destock = true
            cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
                statusCode: 200,
                body: payload,
            }).as('get_article_by_id_or_sku_v2')
        })

        visitPage()

        cy.get('[data-context=article-logistic-havre]').should('not.exist')
    })
})
