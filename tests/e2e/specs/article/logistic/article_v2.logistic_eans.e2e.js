import { ARTICLE_LOGISTIC_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Logistique - Eans', function () {
    // list of product eans, most recent first
    const EANS = ['0859451001605', '3250110015310', '0500369132492']

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', /\/api\/erp\/v2\/article\/(KEFQ350NR|117735)$/, {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')
    })

    const visitPage = () => {
        cy.visit('/articles/KEFQ350NR/logistic')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.closeAllToasts()

        cy.get('[data-context=ean-form]').as('ean-form')
        cy.get('@ean-form').find('[data-context=ean-list]').as('ean-list')
        cy.get('@ean-form').find('[data-context=ean-add]').as('ean-add')

        cy.get('@ean-add').find('input').as('add-input')
        cy.get('@ean-add').find('button').as('add-button')
    }

    describe('Without permission', function () {
        beforeEach(() => {
            cy.mockErpUser()
        })

        it('should show a list of ean and a disabled buttons', function () {
            visitPage()

            cy.get('@ean-add').find('button').as('add-button')
            cy.get('@add-button').should('be.disabled')
            cy.get('@add-button').parent().tooltip('Vous ne disposez pas des droits nécessaires')

            cy.get('@ean-list').should('have.length', 3)
            EANS.forEach((ean, index) => {
                cy.get('@ean-list').eq(index).should('contain', ean)
            })
            cy.get('@ean-list').eq(0).find('button').as('delete-button')
            cy.get('@delete-button').should('be.disabled')
            cy.get('@delete-button').parent().tooltip('Vous ne disposez pas des droits nécessaires')

            cy.get('@ean-form').find('[data-context=submit-button]').as('submit-button')
            cy.get('@submit-button').should('be.disabled')
            cy.get('@submit-button').parent().tooltip('Vous ne disposez pas des droits nécessaires')
        })
    })

    describe('With permission', function () {
        beforeEach(() => {
            cy.mockErpUser([ARTICLE_LOGISTIC_WRITE])

            visitPage()
        })

        const testAddEanValidation = (text, helper_should) => {
            cy.get('@add-input').clear()
            cy.get('@add-input').type(text)
            cy.get('@ean-add')
                .find('[data-context=erp-input-helper]')
                .should(...helper_should)
        }

        it('should open the slide-out form', function () {
            cy.get('@ean-list').should('have.length', 3)
            EANS.forEach((ean, index) => {
                cy.get('@ean-list').eq(index).should('contain', ean)
            })
        })

        it('can delete an EAN', function () {
            cy.get('@ean-list').eq(1).find('button').click()
            cy.get('@ean-list').should('have.length', 2)

            cy.get('@ean-list').eq(0).should('contain', EANS[0])
            cy.get('@ean-list').eq(1).should('contain', EANS[2])
        })

        it('shows a validation error when adding a bad ean', function () {
            cy.get('@add-input').should('have.value', '')

            testAddEanValidation('BAD_EAN_BAD{enter}', ['contain', "Cet EAN n'a pas le bon format"])
            testAddEanValidation('000123456789', ['not.exist'])
            testAddEanValidation('0000123456789', ['not.exist'])
            testAddEanValidation('1234{enter}', ['be.visible'])
            testAddEanValidation('00000001234{enter}', ['be.visible'])
            testAddEanValidation('12345678901234{enter}', ['be.visible'])
        })

        it('can add EANs', function () {
            cy.get('@add-input').should('have.value', '')

            testAddEanValidation('001234567890{enter}', ['not.exist'])
            cy.get('@add-input').should('have.value', '')
            cy.get('@ean-list').should('have.length', 4)
            cy.get('@ean-list').eq(0).should('contain', '0001234567890')

            testAddEanValidation('0009876543210', ['not.exist'])
            cy.get('@add-button').click()
            cy.get('@ean-add').find('[data-context=erp-input-helper]').should('not.exist')
            cy.get('@add-input').should('have.value', '')
            cy.get('@ean-list').should('have.length', 5)
            cy.get('@ean-list').eq(0).should('contain', '0009876543210')
        })

        it('display submit errors', function () {
            cy.intercept('PUT', '**/api/erp/v1/article/117735/eans', {
                statusCode: 400,
                body: {
                    status: 'error',
                    data: {
                        validation_errors: {
                            'eans[1]': '[key:ean_invalid] Thou shalt not submit invalid garbage',
                            'eans[2]': '[key:ean_duplicate] Thou shalt not steal an ean',
                        },
                    },
                },
            }).as('put_article_eans')

            cy.get('@ean-form').find('[data-context=submit-button]').click()

            cy.wait('@put_article_eans')

            cy.get('@ean-list')
                .eq(1)
                .find('[data-context=erp-input-helper]')
                .should('contain', "Cet EAN n'est pas valide")
            cy.get('@ean-list')
                .eq(2)
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Cet EAN est déjà utilisé')

            cy.get('@ean-list').eq(1).find('button').click()

            cy.get('@ean-list')
                .eq(1)
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Cet EAN est déjà utilisé')
        })

        it('can submit successfully', function () {
            cy.intercept('PUT', '**/api/erp/v1/article/117735/eans', {
                delay: 200,
                body: { status: 'success', data: { updated: 2 } },
            }).as('put_article_eans')

            cy.get('@ean-list').eq(1).find('button').click()
            cy.get('@ean-list').should('have.length', 2)

            cy.get('@ean-form').find('[data-context=submit-button]').as('submit-button')
            cy.get('@submit-button').click()
            cy.get('@submit-button').should('be.disabled')

            cy.wait('@put_article_eans').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({ eans: [EANS[0], EANS[2]] })
            })
            cy.wait('@get_article_by_id_or_sku_v2')
        })

        it('EAN form is not display for destocks', function () {
            cy.fixture('erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013').then((payload) => {
                payload.data.is_destock = true
                cy.intercept('GET', /\/api\/erp\/v2\/article\/(DESTOCK-20191120013|143214)$/, {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            cy.visit('/articles/DESTOCK-20191120013/logistic')

            cy.wait('@get_article_by_id_or_sku_v2')
            cy.toggleMenu()
            cy.closeAllToasts()

            cy.get('[data-context=ean-form]').should('not.exist')
        })
    })
})
