import { ARTICLE_V2_CONFIG_KEY } from '../../../../src/apps/erp/components/Article/constants'
import { aliasQuery, GRAPHQL_ENDPOINT, hasOperationName } from '../../utils/graphql-test-utils'

describe('Article - redirection', function () {
    const PAGE = '/articles/SONOSONENR'
    const FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE = 'fetchCmsArticleToDeduceType'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__SONOSONENR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, <PERSON><PERSON><PERSON>_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                req.reply({
                    data: {
                        published: [
                            {
                                sku: 'SONOSONENR',
                                article_id: 120390,
                                unbasketable_reason: null,
                                __typename: 'cms_article_article',
                            },
                        ],
                        draft: [],
                    },
                })
            }
        })
    })

    it('should redirect to last default page or last visited page', function () {
        // make sure that there is no last visited page yet
        cy.window().its('localStorage').invoke('removeItem', ARTICLE_V2_CONFIG_KEY)
        cy.visit(PAGE)
        cy.toggleMenu()

        // default redirect endpoint
        cy.url().should('not.eq', Cypress.config().baseUrl + PAGE + '/general')
        cy.url().should('eq', Cypress.config().baseUrl + PAGE)

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.get('[data-context=tabs] [data-context=item]:contains(Informations)').should('have.class', 'bg-white')
        cy.get('[data-context=tabs] [data-context=item]:contains(Statistiques)').should('not.have.class', 'bg-white')

        // default
        cy.url().should('eq', Cypress.config().baseUrl + PAGE + '/general')

        cy.get('[data-context=tabs] [data-context=item]:contains(Statistiques)').click()
        cy.get('[data-context=tabs] [data-context=item]:contains(Statistiques)').should('have.class', 'bg-white')
        cy.get('[data-context=tabs] [data-context=item]:contains(Informations)').should('not.have.class', 'bg-white')
        cy.url().should('eq', Cypress.config().baseUrl + PAGE + '/stats')

        // reload on default endpoint
        cy.visit(PAGE)

        cy.url().should('not.eq', Cypress.config().baseUrl + PAGE + '/stats')
        cy.url().should('eq', Cypress.config().baseUrl + PAGE)

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')

        // redirect to last visited page
        cy.get('[data-context=tabs] [data-context=item]:contains(Statistiques)', { timeout: 10000 }).should(
            'have.class',
            'bg-white',
        )
        cy.get('[data-context=tabs] [data-context=item]:contains(Informations)').should('not.have.class', 'bg-white')
        cy.url().should('eq', Cypress.config().baseUrl + PAGE + '/stats')
    })
})
