import { ARTICLE_PRICES_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Achats - Prix planifié', function () {
    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/article-stock/KEFQ350NR', {
            fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
        }).as('get_article_stock')

        cy.intercept('POST', '**/api/erp/v1/article/planned-prices', {
            fixture: 'erp/planned_prices/cpost_planned_prices',
        }).as('cpost_article_planned_prices')

        cy.intercept('POST', '**/api/erp/v1/sales-channel', {
            fixture: 'erp/sales-channel/cpost_sales_channels_light',
        }).as('cpost_sales_channels')
    })

    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page ?? '/articles/KEFQ350NR/commercial-operation')

        // Initial calls on the page
        cy.wait(['@get_article_by_id_or_sku_v2', '@cpost_sales_channels'])
        cy.closeAllToasts()

        cy.get('[data-context=article-planned-price]').as('article-planned-price').should('be.visible')
    }

    describe('View article planned price with permission', function () {
        it('Should show article planned prices', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-planned-price').find('[data-context=table-row]').should('have.length', 2).as('rows')

            cy.get('@rows')
                .eq(0)
                .within(() => {
                    cy.get('[data-context=cell-new-date-range] [data-context=start-date] input')
                        .should('have.value', '28 juin 2024 à 14:21')
                        .should('not.have.attr', 'data-disabled')
                    cy.get('[data-context=cell-new-date-range] [data-context=end-date] input')
                        .should('have.value', '30 juin 2035 à 18:10')
                        .should('not.have.attr', 'data-disabled')

                    cy.get('[data-context=cell-new-sales-channels]')
                        .find('[data-context=tag]')
                        .should('have.length', 2)
                        .should('contain', 'son-video.pro')
                        .should('contain', 'amazon.it')
                        .should('not.have.attr', 'data-disabled')

                    cy.get('[data-context=cell-new-exit-selling-price] input')
                        .should('have.value', '678.9')
                        .should('not.have.attr', 'data-disabled')

                    cy.get('[data-context=cell-new-selling-price] input')
                        .should('have.value', '589')
                        .should('not.have.attr', 'data-disabled')

                    cy.get('[data-context=cell-deleted-planned-price]').should('not.have.attr', 'data-disabled')
                })
            cy.get('@rows')
                .eq(1)
                .within(() => {
                    cy.get('[data-context=cell-new-date-range] [data-context=start-date] input')
                        .should('have.value', '')
                        .should('not.have.attr', 'data-disabled')
                    cy.get('[data-context=cell-new-date-range] [data-context=end-date] input')
                        .should('have.value', '')
                        .should('not.have.attr', 'data-disabled')

                    cy.get('[data-context=cell-new-sales-channels]')
                        .find('[data-context=tag]')
                        .should('have.length', 12)
                        .should('not.have.attr', 'data-disabled')

                    cy.get('[data-context=cell-new-exit-selling-price] input')
                        .should('have.value', '599')
                        .should('not.have.attr', 'data-disabled')

                    cy.get('[data-context=cell-new-selling-price] input')
                        .should('have.value', '')
                        .should('not.have.attr', 'data-disabled')

                    cy.get('[data-context=cell-deleted-planned-price] button').should('be.disabled')
                })
        })

        it('Should be impossible to create a new planned price without specifying all information', function () {
            cy.clock(new Date(2024, 5, 1, 11, 40, 0, 0))
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-planned-price')
                .find('[data-context=table-row]')
                .should('have.length', 2)
                .eq(1)
                .within(() => {
                    cy.get('[data-context=cell-new-selling-price] input').should('be.disabled')
                    cy.get('[data-context=cell-deleted-planned-price] button').should('be.disabled')

                    cy.get('[data-context=erp-date-range-picker]').scrollIntoView()
                    cy.get('[data-context=erp-date-range-picker]').erpDateRangePicker('8', '11')
                    cy.get('[data-context=cell-new-selling-price] input').should('not.be.disabled')

                    const deleteAllTags = () => {
                        cy.get('[data-context=cell-new-sales-channels] [data-context=erp-multiselect]').then(
                            (multiselect) => {
                                if (multiselect.find('[data-context=remove]').length > 0) {
                                    multiselect.find('[data-context=remove]').eq(0).click()
                                    deleteAllTags()
                                }
                            },
                        )
                    }
                    deleteAllTags()

                    cy.get('[data-context=cell-new-selling-price] input').should('be.disabled')
                })
        })

        it('Change the expected price of the item to a price with a negative margin', function () {
            visitPage([ARTICLE_PRICES_WRITE])
            cy.get('@article-planned-price')
                .find('[data-context=cell-new-selling-price] input')
                .eq(0)
                .as('input-selling-price')
                .clear()
            cy.get('@input-selling-price').type('123')

            cy.intercept('PUT', '**/api/erp/v1/article/117735/planned-price/26', {
                statusCode: 400,
                body: {
                    data: {
                        validation_errors: {
                            selling_price:
                                '[key:negative_margin] value "123": The planned selling price cannot be lower than the margin',
                            starts_at: '[key:starts_at_must_be_before_ends_at] pas content',
                            ends_at: '[key:starts_at_must_be_before_ends_at] pas content',
                        },
                    },
                },
            }).as('put-article-planned-price')

            cy.get('@article-planned-price').find('[data-context=cell-new-selling-price] button').eq(0).click()

            cy.wait('@put-article-planned-price').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    starts_at: '2024-06-28 14:21:00',
                    ends_at: '2035-06-30 18:10:00',
                    selling_price: 123,
                    exit_selling_price: 678.9,
                    article_planned_price_id: 26,
                    article_id: 117735,
                    sales_channel_ids: [2, 5],
                })
            })

            cy.toast('La marge ne peut être négative', 'danger')
            cy.toast('La date de début doit être antérieure à la date de fin.', 'danger')
        })

        it('Change article planned price details', function () {
            // freeze clock: we are allowed to change start date only if not in the past
            cy.clock(new Date(2024, 5, 1, 11, 40, 0, 0))

            visitPage([ARTICLE_PRICES_WRITE])

            cy.intercept('PUT', '**/api/erp/v1/article/117735/planned-price/26', {
                statusCode: 200,
                body: { data: { updated: 1 } },
            }).as('put-article-planned-price')

            cy.get('@article-planned-price')
                .find('[data-context=table-row]')
                .eq(0)
                .within(() => {
                    cy.get('[data-context=cell-new-selling-price] input').as('input').clear()
                    cy.get('@input').type('589.50')

                    cy.get('[data-context=cell-new-exit-selling-price] input').as('input').clear()
                    cy.get('@input').type('567.9')

                    cy.get('[data-context=erp-date-range-picker]').scrollIntoView()
                    cy.get('[data-context=erp-date-range-picker]').erpDateRangePicker('10', '15')

                    cy.get('[data-context=cell-new-sales-channels] [data-context=erp-multiselect]').erpMultiselect(
                        'cilo',
                        'cilo.dk',
                    )

                    cy.get('[data-context=cell-new-selling-price] button').click()
                })

            cy.wait('@put-article-planned-price').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    starts_at: '2024-06-10 00:00:00',
                    ends_at: '2035-06-15 00:00:00',
                    selling_price: 589.5,
                    exit_selling_price: 567.9,
                    article_planned_price_id: 26,
                    article_id: 117735,
                    sales_channel_ids: [2, 5, 11],
                })
            })

            cy.toast('La planification du prix de vente a été mise à jour. ', 'success')
        })

        it('Disable start date and sales channel edition for started planned price', function () {
            // freeze clock: we are allowed to change start date only if not in the past
            cy.clock(new Date(2025, 5, 1, 11, 40, 0, 0))

            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-planned-price')
                .find('[data-context=table-row]')
                .eq(0)
                .within(() => {
                    cy.get('[data-context=cell-new-date-range] [data-context=start-date] input').should('be.disabled')
                    cy.get('[data-context=cell-new-date-range] [data-context=end-date] input').should('not.be.disabled')
                    cy.get('[data-context=cell-new-sales-channels] input').should('be.disabled')
                    cy.get('[data-context=cell-new-exit-selling-price] input').should('not.be.disabled')
                    cy.get('[data-context=cell-new-selling-price] input').should('not.be.disabled')

                    cy.get('[data-context=cell-deleted-planned-price] button').should('not.be.disabled')
                })
        })

        it('Allow start date edition for unsaved planned price', function () {
            // freeze clock:
            cy.clock(new Date(2025, 5, 1, 11, 40, 0, 0))

            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-planned-price')
                .find('[data-context=table-row]')
                .eq(1)
                .find('[data-context=cell-new-date-range]')
                .within(() => {
                    cy.root().scrollIntoView()
                    cy.root().erpDateRangePicker(1, 2)
                    cy.get('[data-context=start-date] input')
                        .should('have.value', '01 juin 2025 à 00:00')
                        .should('not.be.disabled')
                })
        })

        it('Disallow setting a start date in the past', function () {
            // freeze clock just before start date
            cy.clock(new Date(2024, 5, 20, 11, 40, 0, 0))
            visitPage([ARTICLE_PRICES_WRITE])

            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-planned-price')
                .find('[data-context=table-row]')
                .eq(0)
                .find('[data-context=cell-new-date-range]')
                .within(() => {
                    cy.get('[data-context=start-date]').click()
                    cy.get('div[data-context=day]:contains(19)').should('not.have.attr', 'data-active')
                })
        })

        it('Create new article planned price', function () {
            // freeze clock
            cy.clock(new Date(2024, 5, 1, 11, 40, 0, 0))

            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-planned-price')
                .find('[data-context=table-row]')
                .eq(1)
                .within(() => {
                    cy.get('[data-context=erp-date-range-picker]').scrollIntoView()
                    cy.get('[data-context=erp-date-range-picker]').erpDateRangePicker('2', '8')

                    cy.get('[data-context=cell-new-selling-price] input').as('input-selling-price').clear()
                    cy.get('@input-selling-price').type('699.50')

                    cy.intercept('POST', '**/api/erp/v1/article/117735/planned-price', {
                        statusCode: 200,
                        body: { data: { article_planned_price_id: 2 } },
                    }).as('post-article-planned-price')

                    cy.get('button[type=submit]').click()

                    cy.wait('@post-article-planned-price').then((xhr) => {
                        expect(xhr.request.body).to.deep.eq({
                            starts_at: '2024-06-02 00:00:00',
                            ends_at: '2024-06-08 00:00:00',
                            selling_price: 699.5,
                            exit_selling_price: 599,
                            article_id: 117735,
                            sales_channel_ids: [1, 2, 3, 7, 6, 4, 5, 9, 11, 12, 10, 8],
                        })
                    })
                })
        })

        it('Delete article planned price', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.intercept('DELETE', '**/api/erp/v1/article/planned-price/26', {
                statusCode: 200,
                body: { data: { deleted: 1 } },
            }).as('delete-article-planned-price')

            cy.get('[data-context=planned-price-delete-confirm-slide-in]').should('not.exist')

            cy.get('@article-planned-price').find('[data-context=cell-deleted-planned-price] button').eq(0).click()

            cy.get('[data-context=planned-price-delete-confirm-slide-in]')
                .should('be.visible')
                .within(() => {
                    cy.get('[data-context=alert-warn]').should(
                        'contain',
                        'La planification de prix sera définitivement supprimée. Cette action est irréversible.',
                    )
                    cy.get('[data-context=confirm-btn]').click()
                    cy.root().should('not.be.visible')
                })

            cy.wait('@delete-article-planned-price')
        })
    })

    describe('View article planned price without permission', function () {
        it('Try to add/update article planned price', function () {
            visitPage()

            cy.get('@article-planned-price')
                .find('[data-context=table-row]')
                .eq(0)
                .within(() => {
                    cy.get('[data-context=cell-new-date-range] [data-context=start-date] input').should('be.disabled')
                    cy.get('[data-context=cell-new-date-range] [data-context=end-date] input').should('be.disabled')
                    cy.get('[data-context=cell-new-sales-channels] input').should('be.disabled')
                    cy.get('[data-context=cell-new-exit-selling-price] input').should('be.disabled')
                    cy.get('[data-context=cell-new-selling-price] input').should('be.disabled')

                    cy.get('[data-context=cell-deleted-planned-price] button').should('be.disabled')
                })
        })
    })
})
