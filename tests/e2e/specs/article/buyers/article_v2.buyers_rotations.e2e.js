describe('Article v2 - Buyers - Rotations', function () {
    beforeEach(() => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')
    })

    const visitPage = () => {
        cy.authenticate()
        cy.mockErpUser()

        cy.visit('/articles/KEFQ350NR/buyers')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        cy.closeAllToasts()
    }
    it('should show the product rotations', function () {
        cy.fixture('erp/article/get_article_by_id_or_sku_v2__KEFQ350NR').then((payload) => {
            payload.data.logistic.rotations = {
                '7_days': 12,
                '30_days': 23,
                '90_days': 34,
            }

            cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
                statusCode: 200,
                body: payload,
            }).as('get_article_by_id_or_sku_v2')
        })

        visitPage()

        // Check table header
        ;['7 jours', '30 jours', '90 jours'].forEach((text, idx) => {
            cy.get('[data-context=article-buyers-rotations] [data-context=erp-table] th')
                .eq(idx)
                .should('contain', text)
        })

        // Check content
        cy.get('[data-context=article-buyers-rotations] [data-context=erp-table]')
            .selectCell(0, 0)
            .should('contain', '12')
        cy.get('[data-context=article-buyers-rotations] [data-context=erp-table]')
            .selectCell(0, 1)
            .should('contain', '23')
        cy.get('[data-context=article-buyers-rotations] [data-context=erp-table]')
            .selectCell(0, 2)
            .should('contain', '34')
    })

    it('Rotations is not display for destocks', function () {
        cy.fixture('erp/article/get_article_by_id_or_sku_v2__KEFQ350NR').then((payload) => {
            payload.data.is_destock = true

            cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
                statusCode: 200,
                body: payload,
            }).as('get_article_by_id_or_sku_v2')
        })

        visitPage()

        cy.get('[data-context=article-buyers-rotations]').should('not.exist')
    })
})
