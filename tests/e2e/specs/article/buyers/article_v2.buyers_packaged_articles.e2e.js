import { ARTICLE_PRICES_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Buyers - Packaged articles', function () {
    const magic_search_articles = ['CHORDMOJONR', 'CHORDMOJOPOLYNR', 'CHORDMOJONRKITETUI'].map((sku, index) => ({
        image: '/images/dynamic/Streaming_et_reseau/articles/Chord_Electronics/CHORDMOJONR/Chord-Electronics-Mojo_P_300_square.jpg',
        type: 'article',
        article_url: 'https://www.son-video.com/article/dac-audio-portables/chord-electronics/mojo',
        related_products: {
            bundles: null,
            declinations: null,
            substitute_of: null,
        },
        article_id: 101624 + index,
        delay: null,
        unbasketable_reason: 'UNAVAILABLE',
        computed_name: 'Chord Electronics Mojo',
        name: 'Mojo',
        group_brand: 'AV Industry',
        sku: sku,
        subcategory: {
            name: 'Ind\u00e9finie',
            id: 184,
        },
        prices: {
            selling_price: 499,
            selling_price_generally_observed: 250,
            supplier_price_tariff: 1,
            supplier_price: 1,
            margin_rate: 0,
            ecotax: 0,
            intragroup: 1.24,
        },
        stock: 0,
        brand: {
            name: 'Chord Electronics',
            id: 0,
        },
        status: 'oui',
    }))

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', /\/api\/erp\/v2\/article\/(BOSESOUBAR900SPK700BM700NR|164551)$/, {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__BOSESOUBAR900SPK700BM700NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/package/164551/packaged-articles', {
            fixture: 'erp/article/get_packaged_articles__164551',
        }).as('get_packaged_articles')

        cy.intercept('GET', '**/api/erp/v1/magic-search**', {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    packageable_articles: {
                        results: magic_search_articles,
                        total: 3,
                    },
                },
            },
        }).as('get_magic_search')
    })

    const visitPage = (permissions) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit('/articles/BOSESOUBAR900SPK700BM700NR/buyers')

        // Initial calls on the page
        cy.wait(['@get_article_by_id_or_sku_v2', '@get_packaged_articles'])
        cy.toggleMenu()

        cy.get('[data-context=article-v2-buyers-packaged-articles]').as('panel').should('be.visible')
        cy.closeAllToasts()
    }

    describe('Without permissions', function () {
        it('should show the packaged articles', () => {
            visitPage()

            cy.get('[data-context=article-buyers-availability-button]').eq(0).should('be.visible').click()

            cy.get('[data-context=article-buyers-availability]').should('be.visible')

            cy.get(
                '[data-context=article-buyers-availability] [data-context=stock-move-filtered-list-safety-stock-threshold]',
            ).should('not.exist')

            cy.get('[data-context=slide-out-container-close-btn]').click()

            cy.get('@panel').find('[data-context=table-row]').as('rows')
            cy.get('@rows').should('have.length', 2)
            ;[
                {
                    image: 'https://image.son-video.com/images/ui/uiV3/graphics/no-img-300.png',
                    sku: 'ELIPSPRESTIGE2I',
                    name: 'Prestige 2i Calvados',
                    delay: ['OUI', '24-48 heures'],
                    quantity: 2,
                    stock: 26,
                    stock_available: 0,
                    unit_selling_price: '279,50 €',
                    selling_price: '559,00 €',
                },
                {
                    image: 'https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_300_square.jpg',
                    sku: 'ELIPSPRESTIGE_C_2I',
                    name: 'Prestige C2i Calvados',
                    delay: ['YAPU', '16 à 30 jours'],
                    quantity: 1,
                    stock: 15,
                    stock_available: 9,
                    unit_selling_price: '199,15 €',
                    selling_price: '199,15 €',
                },
            ].forEach((def, index) => {
                cy.get('@rows').eq(index).as('packaged-article')

                cy.get('@packaged-article').find('[data-context=erp-article-item]').as('article-item')
                cy.get('@article-item').find('[data-context=image]').should('have.attr', 'src', def.image)
                cy.get('@article-item').find('[data-context=sku]').should('contain', def.sku)
                cy.get('@article-item').find('[data-context=name]').should('contain', def.name)

                cy.get('@article-item').find('[data-context=status-delay-badge]').as('badge')
                def.delay.forEach((needle) => {
                    cy.get('@badge').should('contain', needle)
                })

                cy.get('@packaged-article').find('[data-context=cell-quantity]').as('quantity')
                cy.get('@quantity').find('input').should('have.value', def.quantity).should('be.disabled')
                cy.get('@quantity')
                    .find('[data-context=erp-button]')
                    .should('be.disabled')
                    .parent()
                    .tooltip('Vous ne disposez pas des droits nécessaires')
                cy.get('@packaged-article').find('[data-context=cell-stock]').should('contain', def.stock)
                cy.get('@packaged-article')
                    .find('[data-context=cell-unit-selling-price]')
                    .should('contain', def.unit_selling_price)
                cy.get('@packaged-article')
                    .find('[data-context=cell-selling-price]')
                    .should('contain', def.selling_price)
                cy.get('@packaged-article')
                    .find('[data-context=cell-actions] [data-context=erp-button]')
                    .should('be.disabled')
                    .parent()
                    .tooltip('Vous ne disposez pas des droits nécessaires')
            })
        })
    })

    describe('With permissions', function () {
        beforeEach(() => {
            visitPage([ARTICLE_PRICES_WRITE])
        })

        it('should add a packaged article', function () {
            cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('MOJO')
            cy.wait('@get_magic_search')

            cy.get('[data-context=add-product-or-section] [data-context=suggestions]').should('be.visible')
            cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                .should('be.visible')
                .should('have.length', 3)

            // add product
            cy.fixture('erp/article/cpost_packaged_article.json').then((payload) => {
                cy.intercept('POST', '**/api/erp/v1/package/164551/add-article', {
                    statusCode: 200,
                    body: { data: { quantity: 1, article_id: payload.data[2].article_id } },
                }).as('post_packaged_article')
            })

            cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                .eq(0)
                .click()

            cy.wait('@post_packaged_article').then((xhr) => {
                expect(xhr.response.body.data).to.deep.eq({ quantity: 1, article_id: 101624 })
            })
            cy.wait('@get_article_by_id_or_sku_v2')
        })

        it('should display error messages', function () {
            cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('MOJO')
            cy.wait('@get_magic_search')

            cy.intercept('POST', '**/api/erp/v1/package/164551/add-article', {
                statusCode: 400,
                body: {
                    data: {
                        validation_errors: {
                            packaged_article:
                                '[key:too_many_packaged_articles] value "160254" : A package cannot have more than 8 articles',
                        },
                    },
                },
            }).as('post_packaged_article')

            cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                .eq(0)
                .click()

            cy.wait('@post_packaged_article')

            cy.toast('Un composé ne peut avoir plus de 8 articles', 'danger')

            cy.get('[data-context=add-product-or-section] [data-context=search-input]').type('MOJO')
            cy.wait('@get_magic_search')

            cy.intercept('POST', '**/api/erp/v1/package/164551/add-article', {
                statusCode: 400,
                body: {
                    data: {
                        validation_errors: {
                            packaged_article:
                                '[key:packaged_article_already_in_package] value "160254" : Article(s) with id(s) 160254 already exists in package',
                        },
                    },
                },
            }).as('post_packaged_article')

            cy.get('[data-context=add-product-or-section] [data-context=suggestions] [data-context=articles]')
                .eq(0)
                .click()

            cy.wait('@post_packaged_article')
            cy.toast('Article déjà présent dans le composé', 'danger')
        })

        it('should delete a packaged article', function () {
            cy.get('[data-context=article-v2-buyers-packaged-articles] [data-context=table-row]').should(
                'have.length',
                2,
            )

            cy.intercept('delete', '**/api/erp/v1/package/164551/packaged-article/95145', {
                statusCode: 200,
                body: {},
            }).as('delete_packaged_article')

            cy.get('[data-context=delete-button] [data-context=erp-button]').eq(0).should('not.be.disabled').click()

            cy.wait(['@delete_packaged_article', '@get_packaged_articles', '@get_article_by_id_or_sku_v2'])
        })

        it('should update quantity of a packaged article', function () {
            cy.get('[data-context=article-v2-buyers-packaged-article-quantity]')
                .eq(0)
                .find('[data-context=erp-input-number]')
                .as('input')

            cy.get('@input').should('not.be.disabled').should('have.value', '2').clear()

            cy.get('@input').type(1)

            cy.intercept('put', '**/api/erp/v1/package/164551/packaged-article/95145', {
                statusCode: 200,
                body: { data: { quantity: 1 } },
            }).as('put_packaged_article')

            cy.get('[data-context=article-v2-buyers-packaged-article-quantity]').find('button').eq(0).click()
            cy.wait('@put_packaged_article').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({ quantity: 1 })
            })

            cy.wait(['@get_packaged_articles', '@get_article_by_id_or_sku_v2'])
        })
    })
})
