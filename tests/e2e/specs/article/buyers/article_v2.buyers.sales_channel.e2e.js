import { ARTICLE_BUYERS_WRITE, ARTICLE_SALES_CHANNEL_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Informations - Selling platforms', function () {
    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', '**/api/erp/v1/sales-channel', {
            fixture: 'erp/sales-channel/cpost_sales_channels_light',
        }).as('cpost_sales_channels_light')
        cy.intercept('POST', '**/api/erp/v1/article/unconditional-discounts', {
            fixture: 'erp/sales-channel/cpost_unconditional_discounts',
        }).as('cpost_unconditional_discounts')
        cy.intercept('GET', '**/api/erp/v1/article-stock/KEFQ350NR', {
            fixture: 'erp/article/get_article_stock_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_stock')
        cy.intercept('POST', '**/api/erp/v1/article/planned-prices', {
            body: { status: 'success', data: { article_planned_prices: [] } },
        }).as('cpost_planned_prices')
    })

    const visitPage = () => {
        cy.visit('/articles/KEFQ350NR/sales-channel')

        // Initial calls on the page
        cy.wait(
            [
                '@get_article_by_id_or_sku_v2',
                '@cpost_sales_channels_light',
                '@cpost_unconditional_discounts',
                '@get_article_stock',
                '@cpost_planned_prices',
            ],
            { timeout: 10000 },
        )
        cy.toggleMenu()
        cy.closeAllToasts()
    }
    describe('Without permission', () => {
        beforeEach(() => {
            cy.mockErpUser()
            visitPage()
        })
        const verifySalesChannel = (salesChannelRowSelector, expected) => {
            let i = 0
            cy.get(salesChannelRowSelector)
                .eq(i++)
                .should('contain', expected.name)
                .find('[data-context=status-indicator]')
                .should('have.class', expected.statusClass)
            cy.get(salesChannelRowSelector)
                .eq(i++)
                .find('[data-context=sales-channel-selling-price-input]')
                .should('have.value', expected.sellingPrice)
                .should('be.disabled')
                .parent()
                .tooltip("Vous n'avez pas la permission d'effectuer cette action.")
            cy.get(salesChannelRowSelector)
                .eq(i++)
                .should('contain', expected.commission)
                .find('[data-context=commission-rate]')
                .should('contain', expected.commissionRate)
            cy.get(salesChannelRowSelector).eq(i++).should('contain', expected.margin)
            cy.get(salesChannelRowSelector).eq(i++).should('contain', expected.marginRate)
            cy.get(salesChannelRowSelector)
                .eq(i++)
                .find('[data-context=delete-sales-channel]')
                .should(!expected.deleteButton ? 'be.enabled' : 'be.disabled')
        }

        it('should display non-editable selling platforms form', function () {
            cy.get('[data-context=comparator-toggle]').within(() => {
                cy.get('[data-context=erp-form-label]:contains(Comparateur)')
                    .siblings('[data-context=erp-multiselect]')
                    .find('input')
                    .should('be.disabled')
            })

            cy.get('[data-context=article-selling-platforms] [data-context=article-sales-channels-table]')
                .as('table')
                .should('be.visible')

            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 6)
            let i = 0
            cy.get('@header').eq(i++).should('contain', 'Canal de vente')
            cy.get('@header').eq(i++).should('contain', 'Prix de vente')
            cy.get('@header').eq(i++).should('contain', 'Commission.')

            cy.get('@table').find('tbody tr').as('rows')
            cy.get('@rows').should('have.length', 3)
            cy.get('@rows').eq(0).find('td').as('first_row')
            verifySalesChannel('@first_row', {
                name: 'son-video.com',
                statusClass: 'bg-green-500',
                sellingPrice: '599',
                sellingPriceSubtitle: '499,17 € HT',
                commission: '0 €',
                commissionRate: 'Pas de commission',
                margin: '223,73 € HT',
                marginRate: '44,90%',
                deleteButton: true,
            })

            cy.get('@rows').eq(1).find('td').as('second_row')

            verifySalesChannel('@second_row', {
                name: 'easylounge.com',
                statusClass: 'bg-green-500',
                sellingPrice: '599',
                sellingPriceSubtitle: '499,17 € HT',
                commission: '0 €',
                commissionRate: 'Pas de commission',
                margin: '223,73 € HT',
                marginRate: '44,90%',
                deleteButton: true,
            })

            cy.get('@rows').eq(2).find('td').as('last_row')
            verifySalesChannel('@last_row', {
                name: 'cultura',
                statusClass: 'bg-red-500',
                sellingPrice: '599',
                sellingPriceSubtitle: '499,17 € HT',
                commission: '53,91 €',
                commissionRate: '9,00 %',
                margin: '169,82 € HT',
                marginRate: '34,08%',
                deleteButton: true,
            })
        })
    })

    describe('With permission', () => {
        beforeEach(() => {
            cy.mockErpUser([ARTICLE_BUYERS_WRITE, ARTICLE_SALES_CHANNEL_WRITE])
        })

        it('should display stock information', function () {
            visitPage()
            cy.get('[data-context=article-buyers-availability]').as('stock')
            cy.get('@stock').get('[data-context=title]:contains(Disponibilité)')
            cy.get('@stock').get('[data-context=erp-table]').as('table')
            // ensure table has only 2 elements cf. global and not the detail view.
            cy.get('@stock').find('tbody tr').should('have.length', 2)
            cy.get('@stock')
                .find('tbody tr')
                .eq(1)
                .within(() => {
                    cy.get('td').eq(1).should('contain', '25')
                    cy.get('td').eq(2).should('contain', '1')
                    cy.get('td').eq(3).should('contain', '0')
                    cy.get('td').eq(4).should('contain', '-7')
                })
        })

        it('should edit comparator', function () {
            visitPage()
            cy.get('[data-context=comparator-toggle]').within(() => {
                // No form element are disabled
                cy.get('[data-context=erp-form-label]:contains(Comparateur)')
                    .siblings('[data-context=erp-multiselect]')
                    .find('input')
                    .should('not.be.disabled')

                // Verify & edit values
                cy.get('[data-context=erp-form-label]:contains(Comparateur)')
                    .siblings('[data-context=erp-multiselect]')
                    .as('comparator')

                cy.get('@comparator').should('contain', 'Oui').click()
                cy.get('@comparator').find('[data-context="suggestion"]').eq(1).click()
                cy.get('@comparator').should('contain', 'Non')

                cy.intercept('PUT', '**/api/erp/v1/article/117735', { statusCode: 201, body: {} }).as('put_request')

                cy.get('[data-context=erp-button]').click()

                cy.wait('@put_request').then((xhr) => {
                    expect(xhr.request.body).to.deep.eq({
                        scope: 'selling_platforms',
                        data: {
                            comparator: 'non',
                        },
                    })
                })
            })
        })

        it('should link to pricing strategy', function () {
            visitPage()
            cy.get('[data-context=link-to-pricing-strategy')
                .eq(0)
                .should('not.be.disabled')
                .should('have.attr', 'href', '/pricing-strategy/1/information')
                .tooltip('Stratégie de prix')
            cy.get('[data-context=link-to-pricing-strategy')
                .eq(1)
                .should('not.be.disabled')
                .should('have.attr', 'href', '/pricing-strategy/1/information')
            cy.get('[data-context=link-to-pricing-strategy]').eq(2).should('have.attr', 'data-status', 'disabled')
        })

        it('should display events', function () {
            visitPage()
            cy.intercept('POST', '**/erp/v1/system-events', {
                statusCode: 200,
                fixture: 'erp/article/events/article_sales_channel_svd_events.json',
            }).as('sales_channel_events')

            cy.get('[data-context=show-article-price-history-for-channel]').eq(0).should('not.be.disabled').click()

            const expected_request = {
                order_by: 'created_at',
                order_direction: 'desc',
                page: 1,
                limit: 50,
                where: {
                    main_id: {
                        _eq: 117735,
                    },
                },
                included_dependencies: {
                    sales_channel_id: {
                        _eq: 1,
                    },
                },
            }
            cy.wait('@sales_channel_events').then((xhr) => {
                expect(xhr.request.body).to.deep.eq(expected_request)
            })

            cy.get('[data-context=article-timeline-update]')
                .eq(0)
                .should('be.visible')
                .should('contain', 'a mis à jour le canal de vente son-video')

            cy.get('[data-context=slide-out-container] [data-context=reload]').click()
            cy.wait('@sales_channel_events').then((xhr) => {
                expect(xhr.request.body).to.deep.eq(expected_request)
            })
        })

        it('should soft delete sales channel', function () {
            visitPage()
            cy.intercept('PUT', '**/erp/v1/article/117735/sales-channel/11', {
                statusCode: 204,
            }).as('delete_article_sales_channel')

            cy.get('[data-context=article-selling-platforms] [data-context=article-sales-channels-table]')
                .as('table')
                .should('be.visible')

            cy.get('@table').find('tbody tr').as('rows')
            cy.get('@rows').should('have.length', 3)

            cy.get('@rows')
                .eq(2)

                .find('[data-context=delete-sales-channel]')
                .should('not.be.disabled')
                .click()

            cy.wait(['@get_article_by_id_or_sku_v2', '@delete_article_sales_channel'])

            cy.toast(`La marketplace a été désactivé pour l'article`, 'success')
        })

        it('should insert sales channel', function () {
            visitPage()
            cy.intercept('POST', '**/erp/v1/article/117735/sales-channel', {
                statusCode: 204,
            }).as('post_article_sales_channel')

            cy.get('[data-context=article-selling-platforms] [data-context=article-sales-channels-table]')
                .as('table')
                .should('be.visible')

            cy.get('@table').find('tfoot tr th').as('foot')
            cy.get('@foot').find('[data-context=erp-multiselect]').as('sales_channels_input')
            cy.get('@sales_channels_input').find('input').should('not.be.disabled')
            cy.get('@sales_channels_input').click()
            cy.get('@sales_channels_input').find('[data-context=suggestion]').should('have.length', 9)
            cy.get('@sales_channels_input').find('[data-context=suggestion]').eq(4).click()

            cy.wait('@post_article_sales_channel').then((xhr) => {
                expect(xhr.request.body.sales_channel_id).to.eq(5)
            })

            cy.wait(['@get_article_by_id_or_sku_v2'])

            cy.toast(`La marketplace a été ajouté pour l'article`, 'success')
        })

        it('should update sales channels selling price', function () {
            visitPage()
            cy.intercept('PUT', '**/erp/v1/article/117735/sales-channel/11', {
                statusCode: 204,
            }).as('post_article_sales_channel_cultura')

            cy.intercept('POST', '**/api/erp/v1/sales-channel/prices-and-margins-calculation', {
                body: {
                    status: 'success',
                    data: [
                        {
                            sales_channel_id: 11,
                            margin: 148.14,
                            margin_rate: 0.2765,
                            selling_price_tax_excluded: 123.44999999999999,
                            sales_channel_commission: 0,
                            markup_rate: 0.0,
                            errors: null,
                        },
                    ],
                },
            }).as('post_prices_and_margins_calculation')

            cy.get('[data-context=article-selling-platforms] [data-context=article-sales-channels-table]')
                .as('table')
                .should('be.visible')
                .scrollIntoView()

            cy.get('@table').find('[data-context=sales-channel-selling-price-input]').as('selling-price-input')

            cy.get('@selling-price-input').eq(2).should('have.value', '599')
            cy.get('@selling-price-input').eq(2).clear().type('570')

            cy.wait('@post_prices_and_margins_calculation').then((xhr) => {
                expect(xhr.request.body).to.deep.eq([
                    {
                        article_id: 117735,
                        sales_channel_id: 11,
                        selling_price: 570,
                        pvgc: 599,
                        sales_channel_commission_fee: 9,
                        ecotax: 1.1,
                        sorecop: 0,
                        vat_rate: 0.2,
                        purchase_price_tax_excluded: 274.52,
                        weighted_purchase_price_tax_excluded: 233.34,
                        stock: 10,
                        unconditional_discount: 0,
                        promo_budget_amount: 0,
                    },
                ])
            })
            cy.get('[data-context=cell-margin-after-commission]').eq(2).should('contain.text', '148,14 € HT')
            cy.get('[data-context=cell-margin-rate]').eq(2).should('contain.text', '27,65%')

            cy.get('[data-context=submit-sales-channel]').click()

            cy.wait('@post_article_sales_channel_cultura').then((xhr) => {
                expect(xhr.request.body.selling_price).to.eq(570)
            })

            cy.wait('@get_article_by_id_or_sku_v2') // back to fixture value
            cy.get('@selling-price-input').eq(2).should('have.value', '599')
        })

        it('should link to pricing strategy', function () {
            visitPage()
            cy.get('[data-context=article-selling-platforms] [data-context=article-sales-channels-table]')
                .find('[data-context=sales-channel-selling-price-input]')
                .eq(0)
                .should('be.disabled')
                .parent()
                .tooltip('Impossible de modifier le prix, une stratégie est en cours.')
            cy.get('[data-context=article-selling-platforms] [data-context=article-sales-channels-table]')
                .find('[data-context=sales-channel-selling-price-input]')
                .eq(1)
                .should('be.disabled')
                .parent()
                .tooltip('Impossible de modifier le prix, une stratégie est en cours.')

            cy.get('[data-context=link-to-pricing-strategy')
                .eq(0)
                .should('not.be.disabled')
                .should('have.attr', 'href', '/pricing-strategy/1/information')
                .tooltip('Stratégie de prix')
            cy.get('[data-context=link-to-pricing-strategy')
                .eq(1)
                .should('not.be.disabled')
                .should('have.attr', 'href', '/pricing-strategy/1/information')
            cy.get('[data-context=link-to-pricing-strategy]').eq(2).should('have.attr', 'data-status', 'disabled')
        })

        it('should disable edition of sales channels under planned price', function () {
            cy.intercept('POST', '**/api/erp/v1/article/planned-prices', {
                body: {
                    status: 'success',
                    data: {
                        article_planned_prices: [
                            {
                                article_planned_price_id: 26,
                                article_id: 117735,
                                selling_price: 589,
                                exit_selling_price: 678.9,
                                starts_at: '2024-06-28 14:21:00',
                                ends_at: '2035-06-30 18:10:00',
                                applied_at: '2024-06-28 14:22:00',
                                sales_channel_ids: [11], // cultura
                                created_by: 'Gérard MANVUSSA',
                            },
                        ],
                    },
                },
            }).as('cpost_planned_prices')
            visitPage()

            cy.get('[data-context=article-selling-platforms] [data-context=article-sales-channels-table]')
                .find('[data-context=sales-channel-selling-price-input]')
                .eq(2)
                .should('be.disabled')
                .parent()
                .tooltip('Impossible de modifier le prix, un prix planifié est en cours')
        })
    })
})
