import { ARTICLE_COMMENT_WRITE, PRODUCT_STOCK_READ } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Achats - Alerte', function () {
    beforeEach(() => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', '**/api/erp/v1/article-comments', {
            fixture: 'erp/article/cpost_article_comments__117735_alert',
        }).as('cpost_article_comments')
    })

    const visitPage = (permission) => {
        cy.authenticate()
        cy.mockErpUser([PRODUCT_STOCK_READ, ...(permission ?? [])])

        cy.visit('/articles/KEFQ350NR/buyers')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        cy.wait('@cpost_article_comments').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({
                where: {
                    article_id: { _eq: 117735 },
                    type: { _eq: 'alert' },
                },
            })
        })

        cy.closeAllToasts()
    }

    describe('Supplier order - article alerts', function () {
        beforeEach(() => {
            cy.intercept('POST', '**/api/erp/v1/suppliers', { fixture: 'erp/supplier/suppliers_240.json' }).as(
                'cpost_suppliers',
            )
        })

        describe('Without permission', function () {
            it('should show the add button with no other action if there is no article alert', function () {
                cy.intercept('POST', '**/api/erp/v1/article-comments', {
                    statusCode: 200,
                    body: [],
                }).as('cpost_article_comments')

                visitPage()

                cy.get('[data-context=add-article-alert]')
                    .should('be.disabled')
                    .parent()
                    .tooltip("Vous n'avez pas la permission d'effectuer cette action")
                cy.get('[data-context=article-alert-display-mode]').should('not.exist')
            })

            it('should show the article alerts', function () {
                visitPage()

                cy.get('[data-context=add-article-alert]').should('be.disabled')
                cy.get('[data-context=article-alert-display-mode] button').should('have.attr', 'aria-checked', 'true')

                // Check active items
                cy.get('[data-context=erp-timeline-article-comment-item]').as('alert-items')
                cy.get('@alert-items').should('have.length', 2)
                // Do not test what and how comments are displayed since the component is generic
                cy.get('@alert-items').eq(0).find('[data-context=erp-button]').as('button')
                cy.get('@button').should('be.disabled')
                cy.get('@button').parent().tooltip("Vous n'avez pas la permission d'effectuer cette action")

                // Toggle state
                cy.get('[data-context=article-alert-display-mode]').tooltip('Voir alerte(s) archivée(s)').click()

                // Check archived items
                cy.get('[data-context=erp-timeline-article-comment-item]').as('alert-items')
                cy.get('@alert-items').should('have.length', 1)
                // Do not test what and how comments are displayed since the component is generic
                cy.get('@alert-items').eq(0).find('[data-context=erp-button]').as('button')
                cy.get('@button').should('be.disabled')
                cy.get('@button').parent().tooltip("Vous n'avez pas la permission d'effectuer cette action")

                // Check the toggle state button
                cy.get('[data-context=article-alert-display-mode] button').should(
                    'not.have.attr',
                    'aria-checked',
                    'false',
                )
                cy.get('[data-context=article-alert-display-mode]').parent().tooltip('Voir alerte(s) active(s)')
            })
        })

        describe('With permission', function () {
            beforeEach(() => {
                visitPage([ARTICLE_COMMENT_WRITE])
            })

            it('should add alert', function () {
                cy.get('[data-context=add-article-alert]').should('not.be.disabled').click()
                cy.get('[data-context=article-v2-buyers-alert-form]').as('article-v2-buyers-alert-form')
                cy.get('@article-v2-buyers-alert-form').should('be.visible')

                cy.get('@article-v2-buyers-alert-form')
                    .find('[data-context=erp-form-block]:contains("Message")')
                    .find('[data-context=erp-textarea]')
                    .should('have.value', '')
                    .type('Je suis con-tent')

                cy.intercept('POST', '**/api/erp/v1/article/117735/comment', {
                    statusCode: 200,
                    body: { status: 'success', data: { article_comment_id: 1 } },
                }).as('post_article_comment')

                cy.get('@article-v2-buyers-alert-form').find('[data-context=erp-button]').click()

                cy.wait('@post_article_comment').then((xhr) => {
                    expect(xhr.request.body.data).to.deep.eq({ type: 'alert', message: 'Je suis con-tent' })
                })

                cy.wait('@cpost_article_comments')
                cy.get('@article-v2-buyers-alert-form').should('not.exist')
            })

            it('should displays errors when adding an alert', function () {
                cy.get('[data-context=add-article-alert]').should('not.be.disabled').click()
                cy.get('[data-context=article-v2-buyers-alert-form]').as('article-v2-buyers-alert-form')
                cy.get('@article-v2-buyers-alert-form').should('be.visible')

                cy.intercept('POST', '**/api/erp/v1/article/117735/comment', {
                    statusCode: 400,
                    body: {
                        status: 'error',
                        message: 'Invalid parameters',
                        code: 400,
                        data: {
                            validation_errors: {
                                message: 'This value should not be blank.',
                            },
                        },
                    },
                }).as('post_article_comment')

                cy.get('@article-v2-buyers-alert-form').find('[data-context=erp-button]').click()

                cy.wait('@post_article_comment')

                cy.get('@article-v2-buyers-alert-form')
                    .find('[data-context=erp-form-block]:contains("Message")')
                    .as('form-block')

                cy.get('@form-block').find('[data-context=erp-textarea]').should('have.class', 'ring-red-500')

                cy.get('@form-block')
                    .find('[data-context=erp-input-helper]')
                    .should('have.class', 'text-red-600')
                    .should('contain', 'Cette valeur ne doit pas être vide')
            })

            it('should toggle an article alert', function () {
                cy.log('=== Disable an active alert ===')
                checkAlertToggling({
                    article_comment_id: 17582,
                    tooltip: 'Désactiver',
                    new_is_active: false,
                })

                cy.get('[data-context=article-alert-display-mode]').click()

                cy.log('=== Enable an inactive alert ===')
                checkAlertToggling({
                    article_comment_id: 17583,
                    tooltip: 'Activer',
                    new_is_active: true,
                })
            })

            const checkAlertToggling = ({ article_comment_id, tooltip, new_is_active }) => {
                cy.get('[data-context=erp-timeline-article-comment-item]')
                    .eq(0)
                    .find('[data-context=erp-button]')
                    .as('button')
                cy.get('@button').should('not.be.disabled')
                cy.get('@button').parent().tooltip(tooltip)

                cy.intercept('PUT', `**/api/erp/v1/article/117735/comment/${article_comment_id}`, {
                    statusCode: 200,
                    body: {
                        status: 'success',
                        data: { updated: 1 },
                    },
                }).as('put_article_comment')

                cy.get('@button').click()

                cy.get('@put_article_comment').then((xhr) => {
                    expect(xhr.request.body.data).to.deep.eq({ is_active: new_is_active })
                })
                cy.wait('@cpost_article_comments')
            }
        })
    })
})
