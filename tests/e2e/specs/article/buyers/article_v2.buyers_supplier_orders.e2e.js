import { PRODUCT_STOCK_READ } from '../../../../../src/apps/erp/permissions.js'
describe('Article v2 - Achats - Commande fournisseur', function () {
    beforeEach(() => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')
    })

    const visitPage = (permission) => {
        cy.authenticate()
        cy.mockErpUser([PRODUCT_STOCK_READ, ...(permission ?? [])])

        cy.visit('/articles/KEFQ350NR/buyers')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.wait('@get_article_stock')

        cy.closeAllToasts()
    }

    describe('Supplier orders', function () {
        beforeEach(() => {
            cy.intercept('POST', '**/api/erp/v1/suppliers', { fixture: 'erp/supplier/suppliers_240.json' }).as(
                'cpost_suppliers',
            )
        })

        it('should not see the supplier orders block if there not any ongoing on the product', function () {
            cy.intercept('GET', '**/api/erp/v1/article-stock/KEFQ350NR', {
                fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
            }).as('get_article_stock')

            visitPage()

            cy.get('[data-context=article-buyers-supplier-orders]').should('not.exist')
        })

        it('should show the ongoing supplier orders', function () {
            // At Champigny
            cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
                fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR_with_supplier_orders',
            }).as('get_article_stock')

            visitPage()

            cy.get('[data-context=article-buyers-supplier-orders]')
                .should('be.visible')
                .find('[data-context=erp-table]')
                .as('supplier_order')

            const table_header_tests = [
                { should: 'contain', value: '# CMD.' },
                { should: 'contain', value: 'À livrer' },
                { should: 'contain', value: 'Date de livraison' },
            ]

            cy.get('@supplier_order').find('thead tr').checkRow(table_header_tests, { cell_selector: 'th' })

            const table_tests = [
                [
                    [
                        { should: 'contain', value: '100044' },
                        {
                            context: 'erp-link',
                            should: 'have.attr',
                            method: 'href',
                            value: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=100044',
                        },
                        { should: 'contain', value: 'standard' },
                    ],
                    [{ should: 'contain', value: '8' }],
                    [{ should: 'contain', value: '03/07/2023' }],
                ],
                [
                    [
                        { should: 'contain', value: '100044' },
                        {
                            context: 'erp-link',
                            should: 'have.attr',
                            method: 'href',
                            value: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=100044',
                        },
                        { should: 'contain', value: 'standard' },
                    ],
                    [{ should: 'contain', value: '2' }],
                    [{ should: 'contain', value: '04/08/2023' }],
                ],
                [
                    [
                        { should: 'contain', value: '100666' },
                        {
                            context: 'erp-link',
                            should: 'have.attr',
                            method: 'href',
                            value: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=100666',
                        },
                        { should: 'contain', value: 'standard' },
                    ],
                    [{ should: 'contain', value: '25' }],
                    [{ should: 'contain', value: '19/05/2023' }],
                ],
                [
                    [
                        { should: 'contain', value: '100666' },
                        {
                            context: 'erp-link',
                            should: 'have.attr',
                            method: 'href',
                            value: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=100666',
                        },
                        { should: 'contain', value: 'standard' },
                    ],
                    [{ should: 'contain', value: '25' }],
                    [{ should: 'contain', value: '01/01/2011' }],
                ],
                [
                    [
                        { should: 'contain', value: '100666' },
                        {
                            context: 'erp-link',
                            should: 'have.attr',
                            method: 'href',
                            value: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=100666',
                        },
                        { should: 'contain', value: 'standard' },
                    ],
                    [{ should: 'contain', value: '50' }],
                    [{ should: 'contain', value: 'Pas de délai' }],
                ],
                [
                    [
                        { should: 'contain', value: '123456' },
                        {
                            context: 'erp-link',
                            should: 'have.attr',
                            method: 'href',
                            value: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456',
                        },
                        { should: 'contain', value: 'standard' },
                    ],
                    [{ should: 'contain', value: '2' }],
                    [{ should: 'contain', value: '19/05/2023' }],
                ],
                [
                    [
                        { should: 'contain', value: '123457' },
                        {
                            context: 'erp-link',
                            should: 'have.attr',
                            method: 'href',
                            value: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=123457',
                        },
                        { should: 'contain', value: 'standard' },
                    ],
                    [{ should: 'contain', value: '2' }],
                    [{ should: 'contain', value: 'Pas de délai' }],
                ],
                [
                    [
                        { should: 'contain', value: '100669' },
                        {
                            context: 'erp-link',
                            should: 'have.attr',
                            method: 'href',
                            value: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=100669',
                        },
                        { should: 'contain', value: 'forecast' },
                    ],
                    [{ should: 'contain', value: '1' }],
                    [{ should: 'contain', value: 'Pas de délai' }],
                ],
                [
                    [{ should: 'contain', value: 'Total' }],
                    [{ should: 'contain', value: '115' }],
                    [{ should: 'contain', value: '' }],
                ],
            ]

            cy.get('@supplier_order').find('tbody tr').as('rows').checkRows(table_tests)
        })
    })
})
