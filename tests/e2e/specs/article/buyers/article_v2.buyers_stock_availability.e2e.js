import { ARTICLE_SAFETY_STOCK_THRESHOLD_WRITE } from '../../../../../src/apps/erp/permissions.js'

import { ARTICLE_BUYERS_STOCK_ACTIVE_PARENTS_KEY } from '../../../../../src/apps/erp/components/Article/constants'

describe('Article v2 - Buyers - Stock availability', function () {
    beforeEach(() => {
        localStorage.removeItem(ARTICLE_BUYERS_STOCK_ACTIVE_PARENTS_KEY)

        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
            fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
        }).as('get_article_stock')
    })

    const visitPage = (permissions) => {
        cy.authenticate()
        cy.mockErpUser([...(permissions ?? [])])

        cy.visit('/articles/KEFQ350NR/buyers')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.wait('@get_article_stock')

        cy.get('[data-context=article-buyers-availability]').as('panel').should('be.visible')
        cy.get('@panel').find('table').as('table').should('be.visible')
    }

    describe('Check table', function () {
        it('should have expected title and refresh button', function () {
            visitPage()

            cy.get('@panel').find('[data-context=title]').should('contain', 'Disponibilité')
        })

        const getHeaders = (type) => {
            return [
                { warehouse: 'Entrepôts', store: 'Magasins', global: 'Global' }[type],
                'Sécu',
                'Stock',
                'Réservé',
                'En attente',
                { warehouse: 'Expédiable', store: 'Dispo. Mag.', global: 'Expédiable' }[type],
                'Détails',
            ]
        }

        it('should have expected headers', function () {
            // load page with closed accordions
            localStorage.setItem(ARTICLE_BUYERS_STOCK_ACTIVE_PARENTS_KEY, '{ "warehouses": false, "stores": false }')
            visitPage()

            getHeaders('global').forEach((text, index) => {
                cy.get('@table').selectCell(0, index).should('contain', text)
            })

            getHeaders('warehouse').forEach((text, index) => {
                cy.get('@table').selectCell(2, index).should('contain', text)
            })
            cy.get('@table').selectCell(3, 0).should('contain', 'Total')

            getHeaders('store').forEach((text, index) => {
                cy.get('@table').selectCell(4, index).should('contain', text)
            })
            cy.get('@table').selectCell(5, 0).should('contain', 'Total')
        })
    })

    describe('Check expand/collapsed behavior', function () {
        it('should expand warehouses and collapse the stores by default', function () {
            visitPage()

            cy.get('@table').find('tbody tr').should('have.length', 8)

            // Check that it is the warehouses that are displayed
            ;['Global', '', 'Entrepôt', 'Total', 'Champigny 2', 'Havre', 'Magasins', 'Total'].forEach((text, idx) => {
                cy.get('@table').selectCell(idx, 0).should('contain', text)
            })

            cy.get('@table').should('not.contain', 'Nantes')
        })

        it("should show user's store when collapsed", function () {
            cy.fixture('erp/article/get_article_stock_by_sku__KEFQ350NR').then((payload) => {
                payload.data.current_user_warehouse_id = 17 // Montpellier

                cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_stock')
            })

            visitPage()

            cy.get('@table').find('tbody tr').should('have.length', 9)

            // Check that it is the warehouses that are displayed
            ;['Global', '', 'Entrepôt', 'Total', 'Champigny 2', 'Havre', 'Magasins', 'Total', 'Montpellier'].forEach(
                (text, idx) => {
                    cy.get('@table').selectCell(idx, 0).should('contain', text)
                },
            )
        })

        it("should not show user's warehouse when collapsed", function () {
            cy.fixture('erp/article/get_article_stock_by_sku__KEFQ350NR').then((payload) => {
                payload.data.current_user_warehouse_id = 21 // Champigny 2

                cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_stock')
            })

            visitPage()

            cy.get('@table').find('[data-context=expand-trigger]').eq(0).click()

            cy.get('@table').find('tbody tr').should('have.length', 6)

            // Check that it is the warehouses that are displayed
            ;['Global', '', 'Entrepôt', 'Total', 'Magasins', 'Total'].forEach((text, idx) => {
                cy.get('@table').selectCell(idx, 0).should('contain', text)
            })
        })

        it('should keep state of last expanded rows upon page reload', function () {
            visitPage()

            cy.get('@table').find('tbody tr').should('have.length', 8)

            cy.get('@table').should('contain', 'Champigny 2')
            cy.get('@table').should('not.contain', 'Nantes')

            cy.get('@table').find('[data-context=expand-trigger]').eq(0).click()
            cy.get('@table').find('[data-context=expand-trigger]').eq(1).click()

            cy.get('@table').should('not.contain', 'Champigny 2')
            cy.get('@table').should('contain', 'Nantes')

            visitPage()

            cy.get('@table').should('not.contain', 'Champigny 2')
            cy.get('@table').should('contain', 'Nantes')
        })
    })

    describe('Computed stock and dynamic badges', function () {
        const ROW_NB = {
            grand_total: 1,
            warehouse_total: 3,
            champigny: 4,
            havre: 5,
            store_total: 7,
            nantes: 16,
        }

        const checkRow = (row_nb, values, badges) => {
            values.forEach((value, index) => {
                cy.get('@table')
                    .selectCell(row_nb, index + 2)
                    .should('contain', value)
            })

            const badges_entries = Object.entries(badges)
            cy.get('@table').selectCell(row_nb, 6).as('details_cell')

            cy.get('@details_cell').find('[data-context=scope]').should('have.length', badges_entries.length)

            badges_entries.forEach(([name, badge_value]) => {
                cy.get('@details_cell').find(`[data-context=${name}] [data-context=content]`).as('content')
                if (Array.isArray(badge_value)) {
                    badge_value.forEach((value, index) => {
                        cy.get('@content').children().eq(index).should('have.text', value)
                    })
                    return
                }

                cy.get('@content').should('have.text', badge_value)
            })
        }

        const checkTransferLink = (context, query_strings) => {
            cy.get('@table').find(`[data-context=${context}]`).as('links')
            cy.get('@links').should('have.length', query_strings.length)

            query_strings.forEach((qs, index) => {
                cy.get(`@links`)
                    .eq(index)
                    .invoke('attr', 'href')
                    .then((url) => {
                        expect(url).to.match(new RegExp(`/legacy/stock/bonsTransfertShow\\?${qs}$`))
                    })
            })
        }

        it('should show appropriate customer orders badges and computed stock', function () {
            cy.fixture('erp/article/get_article_stock_by_sku__KEFQ350NR').then((payload) => {
                payload.data.stock[0].quantity.awaiting_preparation = 2
                payload.data.stock[10].quantity.in_stock = 3
                payload.data.stock[10].quantity.awaiting_preparation = 3

                cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_stock')
            })

            visitPage()

            cy.get('@table').find('[data-context=expand-trigger]').eq(1).click()
            cy.get('@table').find('[data-context=commands]').should('have.length', 5)

            checkRow(ROW_NB.nantes, ['3', '3', '0', '0'], { commands: '3' })
            checkRow(ROW_NB.store_total, ['3', '3', '0', '0'], { commands: '3' })
            checkRow(ROW_NB.champigny, ['10', '2', '0', '8'], { commands: '2' })
            checkRow(ROW_NB.warehouse_total, ['10', '2', '0', '8'], { commands: '2' })
            checkRow(ROW_NB.grand_total, ['13', '5', '0', '8'], { commands: '5' })
        })

        it('should show appropriate transfer badges and computed stock', function () {
            cy.fixture('erp/article/get_article_stock_by_sku__KEFQ350NR').then((payload) => {
                payload.data.stock[0].outgoing_transfers = [
                    {
                        to_id: 2,
                        from_id: 3,
                        quantity: 1,
                        created_at: '2050-08-08 00:00:00.000000',
                        transfer_id: 100,
                        delivery_note_id: null,
                        customer_order_id: null,
                    },
                    {
                        to_id: 1,
                        from_id: 3,
                        quantity: 5,
                        created_at: '2050-08-08 00:00:00.000000',
                        transfer_id: 104,
                        delivery_note_id: null,
                        customer_order_id: null,
                    },
                ]
                payload.data.stock[10].incoming_transfers = [
                    {
                        to_id: 5,
                        from_id: 1,
                        quantity: 3,
                        created_at: '2050-08-08 00:00:00.000000',
                        transfer_id: 103,
                        delivery_note_id: null,
                        customer_order_id: null,
                    },
                ]

                payload.data.stock[9].quantity.in_stock = 1
                payload.data.stock[10].quantity.in_stock = 1

                cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_stock')
            })

            visitPage()

            cy.get('@table').find('[data-context=expand-trigger]').eq(1).click()

            checkTransferLink('transfer-outgoing', [
                'sku=KEFQ350NR&process=1&statut=au%20depart', // global
                'sku=KEFQ350NR&process=1&statut=au%20depart', // warehouse total
                'sku=KEFQ350NR&process=1&statut=au%20depart&id_depot_depart=21', // champigny
            ])

            checkTransferLink('transfer-incoming', [
                'sku=KEFQ350NR&process=1&statut=expedie', // global
                'sku=KEFQ350NR&process=1&statut=expedie', // store total
                'sku=KEFQ350NR&process=1&statut=expedie&id_depot_arrivee=5', // nantes
            ])

            checkRow(ROW_NB.nantes, ['1', '0', '0', '4'], { 'transfer-incoming': '3' })
            checkRow(ROW_NB.store_total, ['2', '0', '0', '5'], { 'transfer-incoming': '3' })
            checkRow(ROW_NB.champigny, ['10', '6', '0', '4'], { 'transfer-outgoing': '6' })
            checkRow(ROW_NB.warehouse_total, ['10', '6', '0', '4'], { 'transfer-outgoing': '6' })
            checkRow(ROW_NB.grand_total, ['12', '6', '0', '4'], { 'transfer-outgoing': '6', 'transfer-incoming': '3' })
        })

        it('should show appropriate reserved badges and computed stock', function () {
            cy.fixture('erp/article/get_article_stock_by_sku__KEFQ350NR').then((payload) => {
                payload.data.stock[0].quantity.in_stock = 2
                payload.data.stock[0].quantity.is_prepared = 1
                payload.data.stock[0].quantity.awaiting_preparation = 3
                payload.data.stock[1].quantity.in_stock = 1
                payload.data.stock[9].quantity.in_stock = 1
                payload.data.stock[10].quantity.awaiting_preparation = 5

                cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_stock')
            })

            visitPage()

            cy.get('@table').find('[data-context=expand-trigger]').eq(1).click()
            cy.get('@table').find('[data-context=reserved-store]').should('have.length', 3)

            checkRow(ROW_NB.nantes, ['0', '0', '5', '0'], { commands: '5' })
            checkRow(ROW_NB.store_total, ['1', '0', '5', '1'], { commands: '5' })
            checkRow(ROW_NB.champigny, ['2', '2', '6', '0'], {
                'reserved-store': '4',
                commands: ['3', '1'],
            })
            checkRow(ROW_NB.havre, ['1', '0', '0', '0'], { 'reserved-store': '1' })
            checkRow(ROW_NB.warehouse_total, ['3', '2', '6', '0'], {
                'reserved-store': '5',
                commands: ['3', '1'],
            })
            checkRow(ROW_NB.grand_total, ['4', '2', '6', '0'], {
                commands: ['8', '1'],
            })
        })

        it('Update security stock without permission', function () {
            cy.fixture('erp/article/get_article_stock_by_sku__KEFQ350NR').then((payload) => {
                payload.data.stock[0].quantity.in_stock = 2
                payload.data.stock[0].quantity.security_threshold = 1

                cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_stock')
            })

            visitPage()

            cy.get('[data-context=stock-move-filtered-list-safety-stock-threshold]')
                .eq(0)
                .find('input')
                .as('safety_stock_threshold_input')
                .should('be.disabled')

            cy.get('[data-context=stock-move-filtered-list-safety-stock-threshold]')
                .eq(0)
                .find('button')
                .should('be.disabled')
        })

        it('Update security stock with permission', function () {
            cy.fixture('erp/article/get_article_stock_by_sku__KEFQ350NR').then((payload) => {
                payload.data.stock[0].quantity.in_stock = 2
                payload.data.stock[0].quantity.security_threshold = 1

                cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_stock')
            })
            visitPage([ARTICLE_SAFETY_STOCK_THRESHOLD_WRITE])

            cy.get('[data-context=stock-move-filtered-list-safety-stock-threshold]')
                .eq(0)
                .find('input')
                .as('safety_stock_threshold_input')
                .should('be.visible')
            cy.get('@safety_stock_threshold_input').type('{backspace}03')

            cy.intercept('PUT', '**/api/erp/v1/article/117735/warehouse/21', {
                statusCode: 204,
                body: {},
            }).as('put_article_safety_stock_threshold')

            cy.get('[data-context=stock-move-filtered-list-safety-stock-threshold]').eq(0).find('button').click()

            cy.wait('@put_article_safety_stock_threshold').then((xhr) => {
                expect(xhr.request.body.safety_stock_threshold).to.eq(3)
            })
        })

        it('Safety stock total', function () {
            visitPage([ARTICLE_SAFETY_STOCK_THRESHOLD_WRITE])

            cy.get('[data-context=stock-move-filtered-list-safety-stock-threshold]')
                .eq(0)
                .find('input')
                .should('have.value', 0)
            cy.get('[data-context=stock-move-filtered-list-safety-stock-threshold]')
                .eq(1)
                .find('input')
                .should('have.value', 2)

            cy.get('@table').selectRow(3)
            cy.selectCell(0).should('contain', 'Total')
            cy.selectCell(1).should('contain', 2)
        })
    })

    describe('Command slide-out', () => {
        const checkCommands = (index, commands, warehouse_name) => {
            cy.get('@commands-badges').should('be.visible')
            cy.get('@commands-badges').should('have.class', 'cursor-pointer')
            cy.get('@commands-badges').eq(index).click()
            cy.wait('@post_customer_orders').then((xhr) => {
                expect(xhr.request.body.where.customer_order_id._in).to.deep.eq(commands)
            })
            cy.get('[data-context=pre-filtered-customer-orders]').should('be.visible')
            cy.get('[data-context=warehouse]').as('badge')
            cy.get('@badge').find('[data-context=scope]').should('contain', 'DEPOT')
            cy.get('@badge').find('[data-context=content]').should('contain', warehouse_name)
            cy.get('[data-context=customer]').should('not.exist')
            cy.get('[data-context=slide-out-container-close-btn]').click()
        }

        it('shows the commands details', () => {
            cy.fixture('erp/article/get_article_stock_by_sku__KEFQ350NR').then((payload) => {
                payload.data.stock[0].quantity.awaiting_preparation = 2
                payload.data.stock[0].related_customer_order_ids = [123, 456]
                payload.data.stock[0].quantity.is_prepared = 2
                payload.data.stock[1].quantity.awaiting_preparation = 2
                payload.data.stock[1].related_customer_order_ids = [456, 789]
                payload.data.stock[2].quantity.is_prepared = 2

                cy.intercept('GET', '**/api/erp/v1/article-stock/*', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_stock')
            })
            visitPage()

            cy.get('[data-context=warehouse-command-detail]').should('not.exist')

            cy.intercept('POST', '**/api/erp/v1/customer-orders', {
                body: {},
            }).as('post_customer_orders')
            cy.get('[data-context=commands]').as('commands-badges')
            cy.get('@commands-badges').should('have.length', 5)

            checkCommands(0, [123, 456, 789], 'Global')
            checkCommands(1, [123, 456, 789], 'Total')
            checkCommands(2, [123, 456], 'Champigny 2')
            cy.get('@commands-badges').eq(4).should('have.class', 'cursor-pointer')
        })

        it('Should display logistic stock moves slide in', function () {
            visitPage()

            cy.intercept('POST', '**/api/erp/v1/stock-moves', { fixture: 'erp/stock-move/list.json' }).as(
                'fetch-stock-moves',
            )

            cy.get('[data-context=stock-move-filtered-slide-out]').should('not.exist')
            cy.get('[data-context=stock-move-btn]').first().click()

            cy.get('[data-context=stock-move-filtered-slide-out]').should('be.visible')

            cy.wait('@fetch-stock-moves')
        })
    })
})
