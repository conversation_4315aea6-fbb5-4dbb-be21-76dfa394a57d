import { ARTICLE_PRICES_WRITE } from '../../../../../src/apps/erp/permissions.js'

const PAGE_TIMEOUT = 6000
describe('Article v2 - Buyers - Prices', function () {
    beforeEach(() => {
        cy.mockDate(new Date(2023, 4, 25, 10, 44, 21, 0))

        cy.authenticate()
    })

    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page ?? '/articles/KEFQ350NR/buyers')
        cy.get('[data-context=tab]', { timeout: PAGE_TIMEOUT }).should('be.visible')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        cy.get('[data-context=article-v2-buyers-prices]').as('form').should('be.visible')
        cy.closeAllToasts()
    }

    context('With a regular article', () => {
        beforeEach(() => {
            cy.intercept('GET', /\/api\/erp\/v2\/article\/(KEFQ350NR|117735)$/, {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
            }).as('get_article_by_id_or_sku_v2')

            cy.intercept('GET', '**/api/erp/v1/article-stock/KEFQ350NR', {
                fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
            }).as('get_article_stock')
        })

        describe('View prices related to the product', function () {
            it('should show a form with all the inputs', function () {
                visitPage()

                const HIDDEN_BLOCKS = ['package-articles-total-selling-price', 'package-discount']
                HIDDEN_BLOCKS.forEach((block) => {
                    cy.get('@form').find(`[data-context=${block.context}]`).should('not.exist')
                })

                const PRICES_BLOCKS = [
                    {
                        context: 'tariff-tax-excluded',
                        value: 274.52,
                        label: 'PA Tarif',
                        suffix: '€ HT',
                        helper: {
                            content: '329,42',
                            suffix: '€ TTC',
                        },
                    },
                    {
                        context: 'purchase-tax-excluded',
                        value: 274.52,
                        label: 'PA Net',
                        suffix: '€ HT',
                    },
                    {
                        context: 'weighted_cost',
                        value: 233.34,
                        label: 'Prix pondéré',
                        suffix: '€ HT',
                        disabled: true,
                    },
                    {
                        context: 'selling-price',
                        value: 599,
                        label: 'Prix de vente',
                        suffix: '€ TTC',
                        helper: {
                            content: '499,17',
                            suffix: '€ HT',
                        },
                    },
                    {
                        context: 'pvgc',
                        value: 599,
                        label: 'PVGC',
                        suffix: '€ TTC',
                    },
                    {
                        context: 'margin',
                        value: 223.73,
                        label: 'Marge',
                        suffix: '€ HT',
                        disabled: true,
                        helper: {
                            content: '44,90',
                            suffix: '%',
                        },
                    },
                    {
                        context: 'initial-selling-price',
                        text: '55,55',
                        label: 'Lancé à',
                        suffix: '€ TTC',
                    },
                    {
                        context: 'ecotax',
                        value: 1.1,
                        label: 'Écotaxe',
                        suffix: '€ TTC',
                        helper: {
                            content: '0,92',
                            suffix: '€ HT',
                        },
                    },
                    {
                        context: 'sorecop',
                        value: 0,
                        label: 'Sorecop',
                        suffix: '€ TTC',
                        helper: {
                            content: '0,00',
                            suffix: '€ HT',
                        },
                    },
                    {
                        context: 'reseller-price',
                        value: 0,
                        label: 'Revendeur',
                        suffix: '€ HT',
                    },
                    {
                        context: 'intragroup',
                        value: 289.51,
                        label: 'Prix 3%',
                        suffix: '€ TTC',
                        disabled: true,
                    },
                ]

                PRICES_BLOCKS.forEach((block) => {
                    // Input
                    cy.get('@form').find(`[data-context=${block.context}]`).as('context')

                    cy.get('@context').find(`[data-context=erp-form-label]`).should('contain', block.label)
                    if (block.value) {
                        cy.get('@context').find(`input`).should('have.value', block.value)
                        cy.get('@context').find(`[data-context=trailing]`).should('contain', block.suffix)
                    }
                    if (block.text) {
                        cy.get('@context').should('contain', block.text)
                        cy.get('@context').should('contain', block.suffix)
                    }

                    if (block.disabled) {
                        cy.get('@context').find(`input`).should('be.disabled')
                    }

                    // Helper
                    if (block.helper) {
                        cy.get('@context')
                            .find(`[data-context=erp-input-helper]`)
                            .should('contain', block.helper.content)
                        cy.get('@context')
                            .find(`[data-context=erp-input-helper]`)
                            .should('contain', block.helper.suffix)
                    }
                })
                cy.get('@form').find(`[data-context=submit-button]`).should('be.disabled')
            })
        })
        describe('Update prices', function () {
            it('should be disabled for user without permission', function () {
                visitPage()

                cy.get('@form').find('[data-context=submit-button]').as('button')

                cy.get('@button').should('be.disabled')
                cy.get('@button').parent().tooltip('Vous ne disposez pas des droits nécessaires')
            })

            it('should be enabled for user with permission', function () {
                visitPage([ARTICLE_PRICES_WRITE])

                cy.get('@form').find('[data-context=submit-button]').as('button')
                cy.get('@button').should('not.be.disabled')

                cy.intercept('PUT', '**/api/erp/v1/article/117735', {
                    delay: 200,
                    body: { status: 'success', data: { updated: 1 } },
                }).as('put_article')
                ;['tariff-tax-excluded', 'pvgc', 'ecotax', 'sorecop', 'reseller-price'].forEach((field, index) => {
                    cy.get(`[data-context=${field}] input`).as('input')
                    cy.get('@input').clear()
                    cy.get('@input').type(`${100.02 + index}`)
                })

                cy.get('[data-context=initial-selling-price]').as('initial-selling-price')
                cy.get('@initial-selling-price').should('contain', '55,55').click()

                cy.get('@initial-selling-price').find('[data-context=dropdown]').as('dropdown')

                cy.get('@dropdown').find('[data-context=suggestion]').as('suggestions')
                // suggestions should be sorted by date
                cy.get('@suggestions').eq(0).should('contain', 'Aucun')
                cy.get('@suggestions').eq(1).should('contain', '52,20')
                cy.get('@suggestions').eq(2).should('contain', '55,55')
                cy.get('@suggestions').eq(3).should('contain', '44,44').should('contain', 'déclinaison').click()
                cy.get('@initial-selling-price').should('contain', '44,44')
                cy.get('@dropdown').should('not.exist')

                cy.get('@button').click()
                cy.get('@button').should('be.disabled')

                cy.wait('@put_article').then((xhr) => {
                    expect(xhr.request.body.scope).to.equal('prices')
                    expect(xhr.request.body.data.tariff_tax_excluded).to.equal(100.02)
                    expect(xhr.request.body.data.selling_price).to.equal(599)
                    expect(xhr.request.body.data.pvgc).to.equal(101.02)
                    expect(xhr.request.body.data.ecotax).to.equal(102.02)
                    expect(xhr.request.body.data.sorecop).to.equal(103.02)
                    expect(xhr.request.body.data.reseller_price).to.equal(104.02)
                    expect(xhr.request.body.data.initial_selling_price).to.equal(44.44)
                })
            })

            it('tariff tax incl. should be updated', function () {
                visitPage([ARTICLE_PRICES_WRITE])
                cy.get('[data-context=tariff-tax-excluded]').as('tariff')
                cy.get('@tariff').find('input').as('input')
                // initial price
                cy.get('@input').should('have.value', '274.52')
                cy.get('@tariff').find('[data-context=erp-input-helper]').should('contain', '329,42 € TTC')

                cy.intercept('POST', '**/api/erp/v1/sales-channel/prices-and-margins-calculation', {
                    body: {
                        status: 'success',
                        data: [
                            {
                                sales_channel_id: 1,
                                margin: 148.14,
                                margin_rate: 0.2765,
                                selling_price_tax_excluded: 123.44999999999999,
                                sales_channel_commission: 0,
                                markup_rate: 0.0,
                                errors: null,
                            },
                        ],
                    },
                })

                // changing price
                cy.get('@input').clear()
                cy.get('@input').type('123.45')

                cy.get('@tariff').find('[data-context=erp-input-helper]').should('contain', '148,14 € TTC')
                // after submit, fields should reset to server-side (mocked) values
                cy.intercept('PUT', '**/api/erp/v1/article/117735', {
                    body: { status: 'success', data: { updated: 1 } },
                }).as('put_article')
                cy.get('@form').find('[data-context=submit-button]').click()
                cy.wait(['@put_article', '@get_article_by_id_or_sku_v2'])

                cy.get('@input').should('have.value', '274.52')
                cy.get('@tariff').find('[data-context=erp-input-helper]').should('contain', '329,42 € TTC')
            })
        })

        describe('Weighted prices adjustments', function () {
            describe('The adjustment list', () => {
                it('displays adjustment slide-in without write permission', function () {
                    visitPage()

                    cy.get('@form').find('[data-context=weighted-costs-adjustments-button]').click()

                    cy.get('[data-context=weighted-costs-adjustments]').as('adjustments')
                    cy.get('@adjustments').should('be.visible')

                    cy.get('@adjustments').find('[data-context=adjustment-item]').as('items')
                    cy.get('[data-context=adjustment-item]').should('have.length', 2)

                    cy.get('@items').eq(0).as('item')
                    cy.get('@item')
                        .should('contain.text', 'Dévaluation comptable')
                        .should('contain.text', 'le 22/03/2023 à 16:56')
                        .should('contain.text', 'créé par Dr. Epanosaurus')
                        .should('not.contain.text', 'Dates (fourchette)')
                    cy.get('@item')
                        .find('[data-context=erp-table] tbody tr')
                        .checkRows([
                            ['Quantité', '9'],
                            ['Chrono', 'AZERTY'],
                            ['Commentaire', 'Tout se perd ma bonne dame'],
                            ['Nouveau prix', '123,45 €'],
                        ])
                    cy.get('@item').find('[data-context=edit-button]').should('not.exist')

                    cy.get('[data-context=adjustment-item]').eq(1).as('item2')
                    cy.get('@item2')
                        .should('contain.text', 'Avoir fournisseur')
                        .should('contain.text', 'le 23/05/2023 à 16:56')
                        .should('contain.text', 'créé par Kali MEROT')
                        .should('not.contain.text', 'Chrono')
                    cy.get('@item2')
                        .find('[data-context=erp-table] tbody tr')
                        .checkRows([
                            ['Quantité', '12'],
                            ['Dates (fourchette)', 'du 12/04/2012 au 13/06/2012'],
                            ['Commentaire', "je suis bien content qu'on me donne des sous"],
                            ['Montant total', '1 234,56 €'],
                        ])
                    cy.get('@item2').find('[data-context=edit-button]').as('edit-button')
                    cy.get('@edit-button').should('be.disabled')
                    cy.get('@edit-button').parent().tooltip('Vous ne disposez pas des droits nécessaires')

                    cy.get('@adjustments').find('[data-context=add-new-item]').as('add-new-item')
                    cy.get('@add-new-item').should('be.disabled')
                    cy.get('@add-new-item').parent().tooltip('Vous ne disposez pas des droits nécessaires')
                })

                it('displays adjustment slide-in with write permission', function () {
                    visitPage([ARTICLE_PRICES_WRITE])

                    cy.get('@form').find('[data-context=weighted-costs-adjustments-button]').as('button')
                    cy.get('@button').find('[data-icon=pencil]').should('be.visible')

                    cy.get('@button').click()

                    cy.get('[data-context=weighted-costs-adjustments]').as('adjustments')

                    cy.get('@adjustments').find('[data-context=adjustment-item]').as('items')
                    cy.get('@items').should('have.length', 2)
                    cy.get('@items').eq(0).find('[data-context=edit-button]').should('not.exist')
                    cy.get('@items').eq(1).find('[data-context=edit-button]').should('be.enabled')

                    cy.get('@adjustments').find('[data-context=add-new-item]').should('be.enabled')
                })
            })

            describe('The edit form', () => {
                beforeEach(() => {
                    visitPage([ARTICLE_PRICES_WRITE])

                    cy.get('@form').find('[data-context=weighted-costs-adjustments-button]').click()
                })

                describe('Adding a new entry', () => {
                    beforeEach(() => {
                        cy.get('[data-context=weighted-costs-adjustments] [data-context=add-new-item]').click()

                        cy.get('[data-context=weighted-costs-adjustment-form]').as('edit-form')
                        cy.get('@edit-form').find('[data-context=erp-form-block]').as('form-blocks')
                    })

                    it('displays an empty form', function () {
                        cy.get('@edit-form')
                            .find('[data-context=page-header]')
                            .should('contain', 'Ajouter un ajustement')

                        cy.get('@form-blocks')
                            .filter(':contains(Type)')
                            .find('[data-context=single-value]')
                            .should('contain', 'Avoir fournisseur')

                        cy.get('@form-blocks').filter(':contains(Nouveau prix)').should('not.exist')
                        cy.get('@form-blocks')
                            .filter(':contains(Montant total)')
                            .find('[data-context=erp-input-number]')
                            .should('have.value', '0')

                        cy.get('@form-blocks')
                            .filter(':contains(Chrono)')
                            .find('[data-context=erp-input]')
                            .should('be.visible')

                        cy.get('@form-blocks')
                            .filter(':contains(Quantité)')
                            .find('[data-context=erp-input-number]')
                            .should('have.attr', 'placeholder', '10')

                        cy.get('@form-blocks')
                            .filter(':contains(Dates)')
                            .find('[data-context=erp-date-range-picker]')
                            .as('date-range')
                        cy.get('@date-range').should('be.visible')
                        cy.get('@date-range').find('[data-context=start-date] input').should('have.value', '')
                        cy.get('@date-range').find('[data-context=end-date] input').should('have.value', '')

                        cy.get('@form-blocks')
                            .filter(':contains(Commentaire)')
                            .find('[data-context=erp-textarea]')
                            .should('be.visible')

                        cy.get('@edit-form').find('[data-context=submit-btn]').should('be.enabled')
                    })

                    it('can submit a new devaluation', function () {
                        cy.get('@form-blocks')
                            .filter(':contains(Type)')
                            .find('[data-context=erp-multiselect]')
                            .erpMultiselect('', 'Dévaluation')

                        cy.get('@form-blocks').filter(':contains(Montant total)').should('not.exist')
                        cy.get('@form-blocks')
                            .filter(':contains(Nouveau prix)')
                            .find('[data-context=erp-input-number]')
                            .type('123.26')

                        cy.get('@form-blocks')
                            .filter(':contains(Chrono)')
                            .find('[data-context=erp-input]')
                            .type('AZERTY')

                        cy.get('@form-blocks')
                            .filter(':contains(Quantité)')
                            .find('[data-context=erp-input-number]')
                            .as('input')
                        cy.get('@input').clear()
                        cy.get('@input').type('5')

                        cy.get('@form-blocks').filter(':contains(Dates)').should('not.exist')

                        cy.get('@form-blocks')
                            .filter(':contains(Commentaire)')
                            .find('[data-context=erp-textarea]')
                            .type('Je suis content')

                        cy.intercept('POST', '**/api/erp/v1/article/117735/weighted-cost-adjustment', {
                            statusCode: 200,
                            body: true,
                        }).as('post_weighted_cost_adjustment')

                        cy.get('@edit-form').find('[data-context=submit-btn]').click()

                        cy.get('[data-context=confirm-block]').should('be.visible')

                        cy.get('@form-blocks')
                            .filter(':contains(Nouveau prix)')
                            .find('[data-context=erp-input-number]')
                            .should('have.value', 123.26)
                            .should('be.disabled')

                        // test cancel button
                        cy.get('[data-context=confirm-block-cancel-btn]').click()
                        cy.get('@form-blocks')
                            .filter(':contains(Nouveau prix)')
                            .find('[data-context=erp-input-number]')
                            .should('not.be.disabled')

                        cy.get('@edit-form').find('[data-context=submit-btn]').click()
                        cy.get('[data-context=confirm-block]').should('be.visible')

                        cy.get('[data-context=confirm-block-validate-btn]').click()

                        cy.wait('@post_weighted_cost_adjustment').then((xhr) => {
                            expect(xhr.request.body).to.deep.eq({
                                type: 'devaluation',
                                amount: 123.26,
                                meta: {
                                    chrono: 'AZERTY',
                                    comment: 'Je suis content',
                                    quantity: 5,
                                },
                            })
                        })

                        cy.wait('@get_article_by_id_or_sku_v2')
                        cy.get('@edit-form').should('not.be.visible')
                        cy.get('[data-context=weighted-costs-adjustments]').should('be.visible')
                    })

                    it('can submit a new minimal credit note', function () {
                        cy.get('@form-blocks')
                            .filter(':contains(Type)')
                            .find('[data-context=erp-multiselect]')
                            .erpMultiselect('', 'Avoir fournisseur')

                        cy.get('@form-blocks').filter(':contains(Nouveau prix)').should('not.exist')
                        cy.get('@form-blocks')
                            .filter(':contains(Montant total)')
                            .find('[data-context=erp-input-number]')
                            .type('123.26')

                        cy.intercept('POST', '**/api/erp/v1/article/117735/weighted-cost-adjustment', {
                            statusCode: 200,
                            body: true,
                        }).as('post_weighted_cost_adjustment')

                        cy.get('@edit-form').find('[data-context=submit-btn]').click()

                        cy.get('[data-context=confirm-block]').should('be.visible')
                        cy.get('[data-context=confirm-block-validate-btn]').click()

                        cy.wait('@post_weighted_cost_adjustment').then((xhr) => {
                            expect(xhr.request.body).to.deep.eq({
                                type: 'supplier_credit_note',
                                amount: 123.26,
                                meta: {},
                            })
                        })

                        cy.wait('@get_article_by_id_or_sku_v2')
                        cy.get('@edit-form').should('not.be.visible')
                        cy.get('[data-context=weighted-costs-adjustments]').should('be.visible')
                    })

                    it('can submit a new complete credit note', function () {
                        cy.get('@form-blocks')
                            .filter(':contains(Type)')
                            .find('[data-context=erp-multiselect]')
                            .erpMultiselect('', 'Avoir fournisseur')

                        cy.get('@form-blocks').filter(':contains(Nouveau prix)').should('not.exist')
                        cy.get('@form-blocks')
                            .filter(':contains(Montant total)')
                            .find('[data-context=erp-input-number]')
                            .type('123.26')

                        cy.get('@form-blocks')
                            .filter(':contains(Chrono)')
                            .find('[data-context=erp-input]')
                            .type('AZERTY')

                        cy.get('@form-blocks')
                            .filter(':contains(Quantité)')
                            .find('[data-context=erp-input-number]')
                            .as('input')
                        cy.get('@input').clear()
                        cy.get('@input').type('5')

                        cy.get('@form-blocks')
                            .filter(':contains(Dates)')
                            .find('[data-context=erp-date-range-picker]')
                            .erpDateRangePicker(10, 20)

                        cy.get('@form-blocks')
                            .filter(':contains(Commentaire)')
                            .find('[data-context=erp-textarea]')
                            .type('Je suis content')

                        cy.intercept('POST', '**/api/erp/v1/article/117735/weighted-cost-adjustment', {
                            statusCode: 200,
                            body: true,
                        }).as('post_weighted_cost_adjustment')

                        cy.get('@edit-form').find('[data-context=submit-btn]').click()

                        cy.get('[data-context=confirm-block]').should('be.visible')
                        cy.get('[data-context=confirm-block-validate-btn]').click()

                        cy.wait('@post_weighted_cost_adjustment').then((xhr) => {
                            expect(xhr.request.body).to.deep.eq({
                                type: 'supplier_credit_note',
                                amount: 123.26,
                                meta: {
                                    chrono: 'AZERTY',
                                    comment: 'Je suis content',
                                    quantity: 5,
                                    date_range: { from: '2023-05-10', to: '2023-05-20' },
                                },
                            })
                        })

                        cy.wait('@get_article_by_id_or_sku_v2')
                        cy.get('@edit-form').should('not.be.visible')
                        cy.get('[data-context=weighted-costs-adjustments]').should('be.visible')
                    })
                })

                describe('Editing an entry', () => {
                    beforeEach(() => {
                        cy.get('[data-context=adjustment-item] [data-context=edit-button]').click()
                        cy.get('[data-context=weighted-costs-adjustment-form]').as('edit-form')
                        cy.get('@edit-form').find('[data-context=erp-form-block]').as('form-blocks')
                    })

                    it('fills the form', function () {
                        cy.get('@edit-form')
                            .find('[data-context=page-header]')
                            .should('contain', 'Modifier un ajustement')

                        cy.get('@form-blocks').filter(':contains(Type)').as('type')
                        cy.get('@type').find('[data-context=single-value]').should('contain', 'Avoir fournisseur')
                        cy.get('@type').find('input').should('be.disabled')

                        cy.get('@form-blocks').filter(':contains(Nouveau prix)').should('not.exist')
                        cy.get('@form-blocks')
                            .filter(':contains(Montant total)')
                            .find('[data-context=erp-input-number]')
                            .should('have.value', '1234.56')
                            .should('be.disabled')

                        cy.get('@form-blocks')
                            .filter(':contains(Chrono)')
                            .find('[data-context=erp-input]')
                            .should('have.value', '')

                        cy.get('@form-blocks')
                            .filter(':contains(Quantité)')
                            .find('[data-context=erp-input-number]')
                            .should('have.value', '12')

                        cy.get('@form-blocks')
                            .filter(':contains(Dates)')
                            .find('[data-context=erp-date-range-picker]')
                            .as('date-range')
                        cy.get('@date-range')
                            .find('[data-context=start-date] input')
                            .should('have.value', '12 avr. 2012')
                        cy.get('@date-range').find('[data-context=end-date] input').should('have.value', '13 juin 2012')

                        cy.get('@form-blocks')
                            .filter(':contains(Commentaire)')
                            .find('[data-context=erp-textarea]')
                            .should('have.value', "je suis bien content qu'on me donne des sous")

                        cy.get('@edit-form').find('[data-context=submit-btn]').should('be.enabled')
                    })

                    it('can edit and submit a credit note', function () {
                        cy.get('@form-blocks').filter(':contains(Chrono)').find('[data-context=erp-input]').type('UIOP')

                        cy.get('@form-blocks')
                            .filter(':contains(Quantité)')
                            .find('[data-context=erp-input-number]')
                            .as('input')
                        cy.get('@input').clear()
                        cy.get('@input').type('10')

                        cy.get('@form-blocks')
                            .filter(':contains(Dates)')
                            .find('[data-context=erp-date-range-picker]')
                            .erpDateRangePicker(5, 10)

                        cy.get('@form-blocks')
                            .filter(':contains(Commentaire)')
                            .find('[data-context=erp-textarea]')
                            .as('textarea')
                        cy.get('@textarea').clear()
                        cy.get('@textarea').type('Je suis heureux')

                        cy.intercept('PUT', '**/api/erp/v1/article/117735/weighted-cost-adjustment/2', {
                            statusCode: 200,
                            body: true,
                        }).as('put_weighted_cost_adjustment')

                        cy.get('@edit-form').find('[data-context=submit-btn]').click()
                        cy.wait('@put_weighted_cost_adjustment').then((xhr) => {
                            expect(xhr.request.body).to.deep.eq({
                                meta: {
                                    chrono: 'UIOP',
                                    comment: 'Je suis heureux',
                                    quantity: 10,
                                    date_range: { from: '2012-04-05', to: '2012-06-10' },
                                },
                            })
                        })

                        cy.wait('@get_article_by_id_or_sku_v2')
                        cy.get('@edit-form').should('not.be.visible')
                        cy.get('[data-context=weighted-costs-adjustments]').should('be.visible')
                    })
                })
            })
        })
    })

    context('With a package article', () => {
        beforeEach(() => {
            cy.intercept('GET', /\/api\/erp\/v2\/article\/(SONOSSUBBEAMERABL|164551)$/, {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__SONOSSUBBEAMERABL',
            }).as('get_article_by_id_or_sku_v2')

            cy.intercept('GET', '**/api/erp/v1/article-stock/SONOSSUBBEAMERABL', {
                body: { status: 'success', data: [] },
            }).as('get_article_stock')

            cy.intercept('GET', '**/api/erp/v1/package/175998/packaged-articles', {
                fixture: 'erp/article/get_packaged_articles__175998',
            }).as('get_packaged_articles')

            visitPage([ARTICLE_PRICES_WRITE], 'articles/SONOSSUBBEAMERABL/buyers')
            cy.wait('@get_packaged_articles')
        })

        describe('View prices related to the package', function () {
            it('should show a form with all the inputs', function () {
                const HIDDEN_BLOCKS = ['purchase-tax-excluded', 'initial-selling-price', 'reseller-price', 'intragroup']
                HIDDEN_BLOCKS.forEach((block) => {
                    cy.get('@form').find(`[data-context=${block.context}]`).should('not.exist')
                })

                cy.get('@form').find('[data-context=weighted-costs-adjustments-button]').should('not.exist')

                const PRICES_BLOCKS = [
                    {
                        context: 'tariff-tax-excluded',
                        value: 1204.94,
                        label: 'PA Tarif',
                        suffix: '€ HT',
                        helper: {
                            content: '1 445,93',
                            suffix: '€ TTC',
                        },
                        disabled: true,
                    },
                    {
                        context: 'selling-price',
                        value: '1599',
                        label: 'Prix de vente',
                        suffix: '€ TTC',
                        helper: {
                            content: '1 332,50',
                            suffix: '€ HT',
                        },
                    },
                    {
                        context: 'weighted_cost',
                        value: 1204.94,
                        label: 'Prix pondéré',
                        suffix: '€ HT',
                        disabled: true,
                    },
                    {
                        context: 'pvgc',
                        value: 2006,
                        label: 'PVGC',
                        suffix: '€ TTC',
                        disabled: true,
                    },
                    {
                        context: 'margin',
                        value: 123.84,
                        label: 'Marge',
                        suffix: '€ HT',
                        disabled: true,
                        helper: {
                            content: '9,3',
                            suffix: '%',
                        },
                    },
                    {
                        context: 'package-articles-total-selling-price',
                        value: '2006',
                        label: 'Prix vente articles',
                        suffix: '€ TTC',
                        disabled: true,
                        helper: {
                            content: '1 671,67',
                            suffix: '€ HT',
                        },
                    },
                    {
                        context: 'package-discount',
                        value: '-407',
                        label: 'Remise',
                        suffix: '€ TTC',
                        disabled: true,
                        helper: {
                            content: '-20,29',
                            suffix: '%',
                        },
                    },
                    {
                        context: 'ecotax',
                        value: 4.47,
                        label: 'Écotaxe',
                        suffix: '€ TTC',
                        disabled: true,
                        helper: {
                            content: '3,73',
                            suffix: '€ HT',
                        },
                    },
                    {
                        context: 'sorecop',
                        value: 0,
                        label: 'Sorecop',
                        suffix: '€ TTC',
                        disabled: true,
                        helper: {
                            content: '0,00',
                            suffix: '€ HT',
                        },
                    },
                ]

                PRICES_BLOCKS.forEach((block) => {
                    // Input
                    cy.get('@form').find(`[data-context=${block.context}]`).as('context')

                    cy.get('@context').find(`[data-context=erp-form-label]`).should('contain', block.label)
                    if (block.value) {
                        cy.get('@context').find(`input`).should('have.value', block.value)
                        cy.get('@context').find(`[data-context=trailing]`).should('contain', block.suffix)
                    }
                    if (block.text) {
                        cy.get('@context').should('contain', block.text)
                        cy.get('@context').should('contain', block.suffix)
                    }

                    if (block.disabled) {
                        cy.get('@context').find(`input`).should('be.disabled')
                    }

                    // Helper
                    if (block.helper) {
                        cy.get('@context')
                            .find(`[data-context=erp-input-helper]`)
                            .should('contain', block.helper.content)
                        cy.get('@context')
                            .find(`[data-context=erp-input-helper]`)
                            .should('contain', block.helper.suffix)
                    }
                })
            })
        })

        describe('Update prices', function () {
            const changeQuantity = () => {
                // add product
                cy.get('[data-context=article-v2-buyers-packaged-article-quantity]')
                    .eq(0)
                    .find('[data-context=erp-input-number]')
                    .as('quantity-input')

                cy.get('@quantity-input').clear()
                cy.get('@quantity-input').type(2)

                cy.intercept('put', '**/api/erp/v1/package/175998/packaged-article/129002', {
                    statusCode: 200,
                    body: { data: { quantity: 2 } },
                }).as('put_packaged_article')

                cy.fixture('erp/article/get_packaged_articles__175998').then((payload) => {
                    payload.data[0].quantity = 2
                    cy.intercept('GET', '**/api/erp/v1/package/175998/packaged-articles', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_packaged_articles')
                })

                cy.get('[data-context=article-v2-buyers-packaged-article-quantity]').find('button').eq(0).click()
                cy.wait(['@put_packaged_article', '@get_packaged_articles'])
            }

            it('packaged articles selling price should be updated', function () {
                cy.get('[data-context=package-articles-total-selling-price]').as('package-articles-price')
                cy.get('@package-articles-price').find('input').as('input')
                // initial
                cy.get('@input').should('have.value', '2006')
                cy.get('@package-articles-price')
                    .find('[data-context=erp-input-helper]')
                    .should('contain', '1 671,67 € HT')

                changeQuantity()

                cy.get('@input').should('have.value', '2905')
                cy.get('@package-articles-price')
                    .find('[data-context=erp-input-helper]')
                    .should('contain', '2 420,83 € HT')
            })

            it('package discount should be updated', function () {
                cy.get('[data-context=package-discount]').as('package-discount')
                // initial margin
                cy.get('@package-discount').find('input').should('have.value', '-407')
                cy.get('@package-discount').find('[data-context=erp-input-helper]').should('contain', '-20,29 %')

                changeQuantity()

                cy.get('@package-discount').find('input').should('have.value', '-1306')
                cy.get('@package-discount').find('[data-context=erp-input-helper]').should('contain', '-44,96 %')
            })
        })
    })
})
