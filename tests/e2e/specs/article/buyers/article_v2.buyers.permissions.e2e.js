import { testPermissionInContext } from '../../../utils/permission-utils'

describe('Article v2 - Achats - Permissions', function () {
    beforeEach(() => {
        cy.intercept('GET', '**/api/erp/v1/article-stock/KEFQ350NR', {
            fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
        }).as('get_article_stock')

        cy.intercept('GET', '**/api/erp/v1/brand/77/suppliers', {
            fixture: 'erp/brand/get_suppliers_by_brand_id',
        }).as('get_suppliers_by_brand_id')

        cy.intercept('GET', /\/api\/erp\/v2\/article\/(KEFQ350NR|117735)$/, {
            delay: 200,
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')
    })

    const visitPage = (permission) => {
        cy.authenticate()
        cy.mockErpUser([...(permission ?? [])])

        cy.visit('/articles/KEFQ350NR/buyers')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.wait('@get_article_stock')

        cy.closeAllToasts()
    }

    testPermissionInContext('[data-context=erp-account-permission-in-context-toolbar] button', visitPage, {
        _or: [
            {
                permission_id: {
                    _ilike: 'ARTICLE_BUYERS%',
                },
            },
            {
                permission_id: {
                    _ilike: 'ARTICLE_PRICES_WRITE',
                },
            },
            {
                permission_id: {
                    _ilike: 'ARTICLE_SAFETY_STOCK_THRESHOLD_WRITE',
                },
            },
            {
                permission_id: {
                    _ilike: 'ARTICLE_PACKAGE_WRITE',
                },
            },
            {
                permission_id: {
                    _ilike: 'ARTICLE_COMMENT_WRITE',
                },
            },
            {
                permission_id: {
                    _ilike: 'ARTICLE_SALES_CHANNEL_WRITE',
                },
            },
        ],
    })
})
