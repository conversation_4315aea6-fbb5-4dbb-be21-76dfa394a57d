import { ARTICLE_PRICES_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Achats - Soldes', function () {
    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/article-stock/KEFQ350NR', {
            fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
        }).as('get_article_stock')

        cy.intercept('POST', '**/api/erp/v1/sales-periods/articles', {
            fixture: 'erp/sales/cpost_article_sales_periods',
        }).as('get_article_sales_periods')

        cy.intercept('POST', '**/api/erp/v1/sales', {
            fixture: 'erp/sales/cpost_sales',
        }).as('get_article_sales')
    })

    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page ?? '/articles/KEFQ350NR/buyers')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        cy.get('[data-context=article-v2-buyers-prices]').as('form').should('be.visible')
        cy.get('[data-context=erp-page-sidebar-menu-item]:contains(Opération commerciale)')
            .tooltip('Opération commerciale')
            .click()

        cy.wait(['@get_article_sales', '@get_article_sales_periods', '@get_article_stock'])

        cy.get('[data-context=article-sales-periods]').as('article-sales-periods').should('be.visible')

        cy.closeAllToasts()
    }

    describe('View article sales products with permission', function () {
        it('Should show article sales products', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-sales-periods').find('[data-context=table-row]').should('have.length', 2)

            cy.get('@article-sales-periods')
                .find('[data-context=cell-sales-period]')
                .eq(0)
                .should('contain', ' 12/05/2024 au 08/07/2024 ')
            cy.get('@article-sales-periods')
                .find('[data-context=cell-selling-price]')
                .eq(0)
                .find('input')
                .should('have.value', '499')
                .should('not.have.attr', 'data-disabled')
            cy.get('@article-sales-periods')
                .find('[data-context=cell-initial-selling-price]')
                .eq(0)
                .should('contain', '599')
            cy.get('@article-sales-periods')
                .find('[data-context=cell-stock-30-d-quantity]')
                .eq(0)
                .should('contain', '10')
            cy.get('@article-sales-periods').find('[data-context=cell-order-quantity]').eq(0).should('contain', '0')
            cy.get('@article-sales-periods')
                .find('[data-context=erp-toggle]')
                .eq(0)
                .should('not.have.attr', 'data-disabled')
        })

        it('Should display an error when trying to create a sales period with no price', function () {
            cy.fixture('erp/sales/cpost_article_sales_periods').then((payload) => {
                payload.data.article_sales_periods.pop()
                cy.intercept('POST', '**/api/erp/v1/sales-periods/articles', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_sales_periods')
            })

            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-sales-periods')
                .find('[data-context=cell-selling-price]')
                .find('button')
                .eq(0)
                .as('button-selling-price')
                .click()

            cy.toast('Vous devez renseigner le prix soldé.', 'danger')
        })

        it('Change article sale selling price to a price that is too high', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-sales-periods')
                .find('[data-context=cell-selling-price]')
                .eq(1)
                .find('input')
                .as('input-selling-price')
                .clear()
            cy.get('@input-selling-price').type('999')

            cy.intercept('PUT', '**/api/erp/v1/article//117735/article-sales-period/1', {
                statusCode: 400,
                body: {
                    data: {
                        validation_errors: {
                            selling_price:
                                '[key:price_too_high_initial_selling_price] value "999" price is too high compared to initial selling price',
                        },
                    },
                },
            }).as('put_article-sales-period')

            cy.get('@article-sales-periods')
                .find('[data-context=cell-selling-price]')
                .find('button')
                .eq(1)
                .as('button-selling-price')
                .click()

            cy.wait('@put_article-sales-period').then((xhr) => {
                expect(xhr.request.body.is_active).to.eq(true)
                expect(xhr.request.body.selling_price).to.eq(999)
                expect(xhr.request.body.only_is_active).to.eq(null)
            })

            cy.toast('Le prix soldé ne peut être plus grand que le prix de vente initial.', 'danger')
        })

        it('Change article sale selling price', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-sales-periods')
                .find('[data-context=cell-selling-price]')
                .eq(1)
                .find('input')
                .as('input-selling-price')
                .clear()
            cy.get('@input-selling-price').type('499.5')

            cy.intercept('PUT', '**/api/erp/v1/article//117735/article-sales-period/1', {
                statusCode: 200,
                body: { data: { updated: 1 } },
            }).as('put_article-sales-period')

            cy.get('@article-sales-periods')
                .find('[data-context=cell-selling-price]')
                .find('button')
                .eq(1)
                .as('button-selling-price')
                .click()

            cy.wait('@put_article-sales-period').then((xhr) => {
                expect(xhr.request.body.is_active).to.eq(true)
                expect(xhr.request.body.sales_period_id).to.eq(42)
                expect(xhr.request.body.selling_price).to.eq(499.5)
                expect(xhr.request.body.only_is_active).to.eq(null)
            })

            cy.toast('La période de solde a été mise à jour.', 'success')
        })

        it('Change article sale status', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-sales-periods')
                .find('[data-context=cell-is-active]')
                .eq(1)
                .find('[data-context=erp-toggle]')
                .as('toggle')

            cy.get('@toggle').find('button').should('be.visible').should('have.attr', 'aria-checked')

            cy.intercept('PUT', '**/api/erp/v1/article//117735/article-sales-period/1', {
                statusCode: 200,
                body: { data: { updated: 1 } },
            }).as('put_article-sales-period')

            cy.get('@toggle').find('button').click()

            cy.wait('@put_article-sales-period').then((xhr) => {
                expect(xhr.request.body.is_active).to.eq(false)
                expect(xhr.request.body.sales_period_id).to.eq(42)
                expect(xhr.request.body.selling_price).to.eq(519)
                expect(xhr.request.body.only_is_active).to.eq(false)
            })

            cy.toast('La période de solde a été mise à jour.', 'success')
        })
    })

    describe('View article sales products without permission', function () {
        it('Should show article sales products', function () {
            visitPage()

            cy.get('@article-sales-periods')
                .find('[data-context=cell-selling-price]')
                .find('input')
                .should('be.disabled')

            cy.get('@article-sales-periods').find('[data-context=erp-toggle]').should('have.attr', 'data-disabled')
        })
    })
})
