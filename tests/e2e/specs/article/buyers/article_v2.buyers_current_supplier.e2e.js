import { ARTICLE_BUYERS_WRITE, PRODUCT_STOCK_READ } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Achats - Fournisseur', function () {
    beforeEach(() => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/brand/77/suppliers', {
            fixture: 'erp/brand/get_suppliers_by_brand_id',
        }).as('get_suppliers_by_brand_id')
    })

    const visitPage = (permission) => {
        cy.authenticate()
        cy.mockErpUser([PRODUCT_STOCK_READ, ...(permission ?? [])])

        cy.visit('/articles/KEFQ350NR/buyers')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        cy.wait('@get_suppliers_by_brand_id')
        cy.closeAllToasts()
    }

    describe('Supplier form whith permission', function () {
        it('should show values in each form field', function () {
            cy.intercept('POST', '**/api/erp/v1/suppliers', { fixture: 'erp/supplier/suppliers_240.json' }).as(
                'cpost_suppliers',
            )

            visitPage()

            cy.get('[data-context=supplier-form]').as('form').should('be.visible')

            const BLOCKS = [
                {
                    block: 'supplier-name',
                    label: 'Fournisseur',
                    single_value: 'GP ACOUSTICS',
                },
                {
                    block: 'supplier-reference',
                    label: 'Référence',
                    have_value: 'SP3959BA',
                },
                {
                    block: 'supplier-mininum-order-quantity',
                    label: 'Quantité minimale de commande',
                    have_value: 2,
                },
            ]

            BLOCKS.forEach((block) => {
                cy.get('@form')
                    .find(`[data-context=${block.block}] [data-context=erp-form-label]`)
                    .should('contain', block.label)

                if (block.hasOwnProperty('have_value')) {
                    cy.get('@form').find(`[data-context=${block.block}] input`).should('have.value', block.have_value)
                }
                if (block.hasOwnProperty('single_value')) {
                    cy.get('@form')
                        .find(`[data-context=${block.block}] [data-context=single-value]`)
                        .should('contain', block.single_value)
                }

                if (block?.assert === 'checked') {
                    cy.get('@form')
                        .find(`[data-context=${block.block}] button`)
                        .should('have.attr', 'aria-checked', 'true')
                }
            })
            cy.get('@form').find(`[data-context=erp-button]`).should('be.disabled')
        })

        it('change value in supplier fields with permission', function () {
            cy.intercept('POST', '**/api/erp/v1/suppliers', { fixture: 'erp/supplier/suppliers_240.json' }).as(
                'cpost_suppliers',
            )

            visitPage([ARTICLE_BUYERS_WRITE])

            cy.get('[data-context=supplier-form]').as('form')
            cy.get('@form').find('[data-context=erp-multiselect]').eq(0).as('supplier_input').click()

            cy.get('@supplier_input').find('[data-context=dropdown]').should('be.visible')
            cy.get('@supplier_input').find('[data-context=suggestion]').should('have.length', 6)
            cy.get('@supplier_input').find('[data-context=suggestion]').eq(0).click()
            cy.get('@supplier_input').find('[data-context=single-value]').should('contain', 'EUROSELL')

            cy.intercept('PUT', '**/api/erp/v1/article/117735', {
                statusCode: 200,
                body: {},
            }).as('put_article_supplier')

            cy.get('@form').find('[data-context=erp-button]').click()

            cy.wait('@put_article_supplier').then((xhr) => {
                expect(xhr.request.body.data.supplier_id).to.eq(424)
                expect(xhr.request.body.data.supplier_reference).to.eq(null)
                expect(xhr.request.body.data.mininum_order_quantity).to.eq(1)
            })
        })

        it('Remove value of supplier reference fields with permission', function () {
            cy.intercept('POST', '**/api/erp/v1/suppliers', { fixture: 'erp/supplier/suppliers_240.json' }).as(
                'cpost_suppliers',
            )

            cy.intercept('PUT', '**/api/erp/v1/article/117735', {
                statusCode: 200,
                body: {},
            }).as('put_article_supplier')

            visitPage([ARTICLE_BUYERS_WRITE])

            cy.get('[data-context=supplier-form]')
                .find('[data-context=supplier-reference] [data-context=erp-input]')
                .as('edit-supplier-reference')
            cy.get('@edit-supplier-reference').clear()
            cy.get('@edit-supplier-reference').type('{enter}')

            cy.wait('@put_article_supplier').then((xhr) => {
                expect(xhr.request.body.data.supplier_id).to.eq(240)
                expect(xhr.request.body.data.supplier_reference).to.eq(null)
                expect(xhr.request.body.data.mininum_order_quantity).to.eq(2)
            })
        })

        it('filter supplier search', function () {
            cy.intercept('POST', '**/api/erp/v1/suppliers', { fixture: 'erp/supplier/suppliers_240.json' }).as(
                'cpost_suppliers',
            )

            visitPage([ARTICLE_BUYERS_WRITE])

            cy.get('[data-context=supplier-form]').as('form')
            cy.get('@form').find('[data-context=erp-multiselect]').eq(0).as('supplier_input').click()

            cy.get('@supplier_input').find('[data-context=dropdown]').should('be.visible')
            cy.get('@supplier_input').find('[data-context=suggestion]').as('suggestion').should('have.length', 6)

            cy.get('@supplier_input').find('[data-context=input]').eq(0).as('supplier_search')

            cy.get('@supplier_search').type('E')
            cy.get('@suggestion').should('have.length', 2)
            cy.get('@supplier_search').type('urosel')
            cy.get('@suggestion').should('have.length', 1)
            cy.get('@supplier_search').type('{backspace}')
            cy.get('@suggestion').should('have.length', 2)

            cy.get('@supplier_search').clear()
            cy.get('@suggestion').should('have.length', 6)
        })

        it('errors should be displayed', function () {
            cy.intercept('GET', '**/api/erp/v1/brand/77/suppliers', {
                fixture: 'erp/supplier/suppliers_invalid.json',
            }).as('get_suppliers_by_brand_id')

            visitPage([ARTICLE_BUYERS_WRITE])

            cy.get('[data-context=supplier-form] [data-context=supplier-name]')
                .find(`[data-context=erp-input-helper]`)
                .should('have.class', 'text-red-600')
                .should('contain', "La marque n'est pas disponible chez ce fournisseur")

            cy.intercept('PUT', '**/api/erp/v1/article/117735', {
                statusCode: 400,
                body: {
                    status: 'error',
                    message: 'Invalid parameters',
                    code: 400,
                    data: {
                        validation_errors: {
                            supplier_id: '[key:ean_invalid] A random validation message',
                            supplier_reference: 'This value is not valid.',
                            mininum_order_quantity: 'This value should be positive.',
                        },
                    },
                },
            }).as('put_article')

            cy.get('[data-context=supplier-form] [data-context=erp-button]').click()

            cy.wait('@put_article')
            ;[
                {
                    context: 'supplier-name',
                    message: "Cet EAN n'est pas valide",
                },
                {
                    context: 'supplier-reference',
                    message: 'Veuillez entrer des caractères valides',
                },
                {
                    context: 'supplier-mininum-order-quantity',
                    message: 'Cette valeur doit être positive',
                },
            ].forEach((o) => {
                cy.get(`[data-context=supplier-form] [data-context=${o.context}]`)
                    .find(`[data-context=erp-input-helper]`)
                    .should('have.class', 'text-red-600')
                    .should('contain', o.message)
            })
        })

        it('link to supplier page', function () {
            cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
            }).as('get_article_by_id_or_sku_v2')

            cy.intercept('POST', '**/api/erp/v1/suppliers', { fixture: 'erp/supplier/suppliers_240.json' }).as(
                'cpost_suppliers',
            )

            visitPage([ARTICLE_BUYERS_WRITE])

            cy.get('[data-context=link-to-supplier]').should('have.attr', 'href', '/legacy/fournisseur/edit?id=240')
        })

        it('Current Supplier is not display for destocks', function () {
            cy.fixture('erp/article/get_article_by_id_or_sku_v2__KEFQ350NR').then((payload) => {
                payload.data.is_destock = true

                cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            cy.authenticate()
            cy.mockErpUser([PRODUCT_STOCK_READ])

            cy.visit('/articles/KEFQ350NR')

            cy.wait('@get_article_by_id_or_sku_v2')
            cy.toggleMenu()
            cy.get('[data-context=tabs] [data-context=item]:contains(Achats)').click()

            cy.get('[data-context=article-buyers-supplier]').should('not.exist')
        })
    })

    describe('Supplier form without permission', function () {
        it('change value in supplier fields', function () {
            cy.intercept('POST', '**/api/erp/v1/suppliers', { fixture: 'erp/supplier/suppliers_240.json' }).as(
                'cpost_suppliers',
            )

            visitPage()

            cy.get('[data-context=supplier-form]').as('form')
            cy.get('@form').find('[data-context=erp-button]').should('have.attr', 'disabled', 'disabled')
        })
    })
})
