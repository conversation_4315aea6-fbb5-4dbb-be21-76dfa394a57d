context('With a regular article', () => {
    beforeEach(() => {
        cy.intercept('GET', /\/api\/erp\/v2\/article\/(KEFQ350NR|117735)$/, {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/article-stock/KEFQ350NR', {
            fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
        }).as('get_article_stock')
    })

    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page ?? '/articles/KEFQ350NR/buyers')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        cy.get('[data-context=article-v2-buyers-prices]').as('form').should('be.visible')
        cy.closeAllToasts()
    }

    describe('View Article Buyers subtabs', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()
        })

        it('should have the expected subtabs for the tab buyers', function () {
            visitPage()

            const subtabs = [
                'Stock et fournisseur',
                'Concurrents',
                'Canaux de vente',
                'Plan de vente',
                'Pro',
                'Opération commerciale',
            ]
            cy.get('[data-context=erp-page-sidebar-menu-item]').as('subtabs')

            cy.get('@subtabs').should('have.length', subtabs.length)

            subtabs.forEach((subtab, index) => {
                cy.get('@subtabs').eq(index).should('contain', subtab)
            })
        })

        it('should not have the "concurrents" subtabs for a packaged article', function () {
            cy.intercept('GET', '/api/erp/v2/article/BOSESOUBAR900SPK700BM700NR', {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__BOSESOUBAR900SPK700BM700NR',
            }).as('get_article_by_id_or_sku_v2')

            visitPage([], '/articles/BOSESOUBAR900SPK700BM700NR/buyers')

            const subtabs = ['Stock et fournisseur', 'Canaux de vente', 'Plan de vente', 'Pro', 'Opération commerciale']
            cy.get('[data-context=erp-page-sidebar-menu-item]').as('subtabs')

            cy.get('@subtabs').should('have.length', subtabs.length)

            subtabs.forEach((subtab, index) => {
                cy.get('@subtabs').eq(index).should('contain', subtab)
            })
        })

        it('should have the prices bloc on all subtabs', function () {
            visitPage()

            const subtabs = ['Stock et fournisseur', 'Concurrents', 'Canaux de vente', 'Plan de vente', 'Pro']

            subtabs.forEach((subtab, index) => {
                cy.get('[data-context=erp-page-sidebar-menu-item]').eq(index).click()
                cy.get('[data-context=open-supplier-order-form-btn]').should('be.visible')
                cy.get('[data-context=article-v2-buyers-prices]').should('be.visible')
            })
        })
    })
})
