import { ARTICLE_PRICES_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Achats - Remise inconditionnelle', function () {
    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
            timeout: 10000,
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/article-stock/KEFQ350NR', {
            fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
            timeout: 10000,
        }).as('get_article_stock')

        cy.intercept('POST', '**/api/erp/v1/article/unconditional-discounts', {
            fixture: 'erp/article/cpost_article_unconditional_discounts',
            timeout: 10000,
        }).as('cpost_article_unconditional_discount')
    })

    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page ?? '/articles/KEFQ350NR/commercial-operation')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2', { timeout: 10000 })
        cy.closeAllToasts()

        cy.get('[data-context=unconditional-discount]').as('unconditional-discount').should('be.visible')
    }

    describe('View article unconditional discount with permission', function () {
        it('Should show article unconditional discount', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@unconditional-discount')
                .find('input')
                .should('have.value', '10.78')
                .should('not.have.attr', 'data-disabled')

            cy.get('@unconditional-discount').find('[data-context=erp-input-helper]').should('contain', '29.59 € HT')
        })

        it('Discount amount should be 0 on article with no discount', function () {
            cy.intercept('POST', '**/api/erp/v1/article/unconditional-discounts', {
                fixture: 'erp/article/cpost_article_unconditional_discounts_empty',
            }).as('cpost_article_unconditional_discount')
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@unconditional-discount')
                .find('input')
                .should('have.value', '0')
                .should('not.have.attr', 'data-disabled')

            cy.get('@unconditional-discount').find('[data-context=erp-input-helper]').should('contain', '0 €')
        })

        it('Should change unconditional discount', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.intercept('PUT', '**/api/erp/v1/article/117735/unconditional-discount', {
                statusCode: 204,
            }).as('put-article-unconditional-discount')

            cy.get('@unconditional-discount').find('input').as('amount')
            cy.get('@unconditional-discount').find('[data-context=erp-input-helper]').as('preview')
            cy.get('@unconditional-discount').find('[data-context=save-button]').as('save-button')

            cy.get('@amount').clear().clear().type('105')
            cy.get('@preview').should('have.attr', 'data-invalid')
            cy.get('@preview').should('contain', 'Le montant doit être compris entre 0 et 100.')
            cy.get('@save-button').should('be.disabled')

            cy.get('@amount').clear().clear().type('-10')
            cy.get('@preview').should('have.attr', 'data-invalid')
            cy.get('@preview').should('contain', 'Le montant doit être compris entre 0 et 100.')
            cy.get('@save-button').should('be.disabled')

            cy.get('@amount').clear().clear().type('12.45')
            cy.get('@preview').should('not.have.attr', 'data-invalid')
            cy.get('@preview').should('contain', '34.18 € HT')
            cy.get('@save-button').should('not.be.disabled')

            cy.get('@save-button').click()

            cy.wait('@put-article-unconditional-discount').then((xhr) => {
                expect(xhr.request.body.article_id).to.eq(117735)
                expect(xhr.request.body.amount).to.eq(12.45)
            })

            cy.toast('La remise inconditionnelle a été mise à jour.', 'success')
        })
    })
})
