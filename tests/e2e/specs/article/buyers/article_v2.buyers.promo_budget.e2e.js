import { ARTICLE_PRICES_WRITE } from '../../../../../src/apps/erp/permissions.js'

const PAGE_TIMEOUT = 30000
describe('Article v2 - Achats - Budgets promo', function () {
    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/article-stock/KEFQ350NR', {
            fixture: 'erp/article/get_article_stock_by_sku__KEFQ350NR',
        }).as('get_article_stock')

        cy.intercept('POST', '**/api/erp/v1/article/promo-budgets', {
            fixture: 'erp/article/promo-budget/cpost_article_promo_budgets',
        }).as('cpost_article_promo_budget')
    })

    const visitPage = (permissions, page) => {
        cy.mockErpUser([...(permissions ?? [])])
        cy.visit(page ?? '/articles/KEFQ350NR/commercial-operation')
        cy.get('[data-context=article-promo-budget]', { timeout: PAGE_TIMEOUT }).should('be.visible')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.closeAllToasts()

        cy.get('[data-context=article-promo-budget]').as('article-promo-budget').should('be.visible')
    }

    describe('View article promo budgets with permission', function () {
        it('Should show article promo budgets', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-promo-budget').find('[data-context=table-row]').should('have.length', 4)

            cy.get('@article-promo-budget')
                .find('[data-context=start-date] input')
                .eq(0)
                .should('have.value', '22 août 2024')
                .should('not.have.attr', 'data-disabled')

            cy.get('@article-promo-budget')
                .find('[data-context=end-date] input')
                .eq(0)
                .should('have.value', '24 août 2024')
                .should('not.have.attr', 'data-disabled')

            cy.get('@article-promo-budget')
                .find('[data-context=amount]')
                .eq(0)
                .should('have.value', '10')
                .should('not.have.attr', 'data-disabled')

            cy.get('@article-promo-budget')
                .find('[data-context=save-button]')
                .eq(0)
                .should('not.have.attr', 'data-disabled')

            cy.get('@article-promo-budget')
                .find('[data-context=remove-button]')
                .eq(0)
                .should('not.have.attr', 'data-disabled')
        })

        it('Should be impossible to create a new promo budget without specifying a date', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-promo-budget').find('[data-context=amount]').eq(3).should('be.disabled')

            cy.get('@article-promo-budget').find('[data-context=save-button]').eq(3).should('be.disabled')
        })

        it('Change a budget promo but period overlaps with another one', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-promo-budget').find('[data-context=end-date]').eq(0).erpDatePicker('1')

            cy.intercept('PUT', '**/api/erp/v1/article/117735/promo-budget/14', {
                statusCode: 400,
                body: {
                    data: {
                        validation_errors: {
                            start_at: '[key:period_overlap] : the budget promo period overlaps another one',
                        },
                    },
                },
            }).as('put-article-promo-budget')

            cy.get('@article-promo-budget').find('[data-context=save-button]').eq(0).click()

            cy.wait('@put-article-promo-budget').then((xhr) => {
                expect(xhr.request.body.article_id).to.eq(117735)
                expect(xhr.request.body.id).to.eq(14)
                expect(xhr.request.body.amount).to.eq(10)
                expect(xhr.request.body.start_at).to.eq('2024-08-22 00:00:00')
                expect(xhr.request.body.end_at).to.eq('2024-08-01 00:00:00')
            })

            cy.toast('Un budget promo est déjà présent sur cette période.', 'danger')
        })

        it('Change a budget promo successfully', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-promo-budget').find('[data-context=start-date]').eq(0).erpDatePicker('4')

            cy.intercept('PUT', '**/api/erp/v1/article/117735/promo-budget/14', {
                statusCode: 204,
            }).as('put-article-promo-budget')

            cy.get('@article-promo-budget').find('[data-context=save-button]').eq(0).click()

            cy.wait('@put-article-promo-budget').then((xhr) => {
                expect(xhr.request.body.article_id).to.eq(117735)
                expect(xhr.request.body.id).to.eq(14)
                expect(xhr.request.body.amount).to.eq(10)
                expect(xhr.request.body.start_at).to.eq('2024-08-04 00:00:00')
                expect(xhr.request.body.end_at).to.eq('2024-08-24 00:00:00')
            })

            cy.toast('Le budget promo a été mis à jour.', 'success')
        })

        it('Create new article promo budget', function () {
            // freeze clock
            cy.clock(new Date(2024, 5, 1, 11, 40, 0, 0))

            visitPage([ARTICLE_PRICES_WRITE])

            cy.get('@article-promo-budget').find('[data-context=start-date]').eq(3).erpDatePicker('1')
            cy.get('@article-promo-budget').find('[data-context=end-date]').eq(3).erpDatePicker('2')

            cy.get('@article-promo-budget').find('[data-context=amount]').eq(3).clear().type('20.45')

            cy.intercept('POST', '**/api/erp/v1/article/117735/promo-budget', {
                statusCode: 200,
                body: { data: { article_promo_budget_id: 2 } },
            }).as('post-article-promo-budget')

            cy.get('@article-promo-budget').find('[data-context=save-button]').eq(3).click()

            cy.wait('@post-article-promo-budget').then((xhr) => {
                expect(xhr.request.body.amount).to.eq(20.45)
                expect(xhr.request.body.start_at).to.eq('2024-06-01 00:00:00')
                expect(xhr.request.body.end_at).to.eq('2024-06-02 00:00:00')
            })

            cy.toast('Le budget promo a été ajouté.', 'success')
        })

        it('Delete article promo budget', function () {
            visitPage([ARTICLE_PRICES_WRITE])

            cy.intercept('DELETE', '**/api/erp/v1/article/117735/promo-budget/14', {
                statusCode: 204,
            }).as('delete-article-promo-budget')

            cy.get('@article-promo-budget').find('[data-context=remove-button]').eq(0).click()

            cy.wait('@delete-article-promo-budget')

            cy.toast('Le budget promo a été supprimé.', 'success')
        })
    })
})
