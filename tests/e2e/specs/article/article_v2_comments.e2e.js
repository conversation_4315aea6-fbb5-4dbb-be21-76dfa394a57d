import { ARTICLE_COMMENT_WRITE } from '../../../../src/apps/erp/permissions'

describe('Article v2 - Comments', function () {
    const PAGE = '/articles/SONOSONENR/general'

    const visit = (permissions) => {
        cy.authenticate()
        cy.mockErpUser([...(permissions ?? [])])

        cy.visit(PAGE)

        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        cy.get('[data-context=open-comment-button]').click()
        cy.get('[data-context=article-comments]').should('be.visible')

        cy.wait('@cpost_article_comments').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({
                where: {
                    article_id: { _eq: 120390 },
                    type: { _eq: 'general' },
                },
            })
        })

        cy.closeAllToasts()
    }

    beforeEach(() => {
        cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__SONOSONENR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', '**/api/erp/v1/article-comments', {
            fixture: 'erp/article/cpost_article_comments__120390_general',
        }).as('cpost_article_comments')
    })

    describe('Without permission', () => {
        beforeEach(() => {
            visit()
        })

        it('should display comments', () => {
            cy.get('[data-context=add-article-comment]')
                .should('be.disabled')
                .parent()
                .tooltip('Vous ne disposez pas des droits nécessaires')

            // Check active items
            cy.get('[data-context=erp-timeline-article-comment-item]').as('comment-item')
            cy.get('@comment-item').should('have.length', 2)
            // Do not test what and how comments are displayed since the component is generic
            cy.get('@comment-item').eq(0).find('[data-context=erp-button]').as('button')
            cy.get('@button').should('be.disabled')
            cy.get('@button').parent().tooltip("Vous n'avez pas la permission d'effectuer cette action")

            // Toggle state
            cy.get('[data-context=article-comment-display-mode]').within(() => {
                cy.get('label').should('contain', 'Voir les commentaire(s) archivé(s)')
                cy.get('button').should('have.attr', 'aria-checked', 'true').click()
            })

            // Check archived items
            cy.get('[data-context=erp-timeline-article-comment-item]').as('comment-item')
            cy.get('@comment-item').should('have.length', 1)
            // Do not test what and how comments are displayed since the component is generic
            cy.get('@comment-item').eq(0).find('[data-context=erp-button]').as('button')
            cy.get('@button').should('be.disabled')
            cy.get('@button').parent().tooltip("Vous n'avez pas la permission d'effectuer cette action")

            // Check the toggle state button
            cy.get('[data-context=article-comment-display-mode]').within(() => {
                cy.get('label').should('contain', 'Voir les commentaire(s) actif(s)')
                cy.get('button').should('not.have.attr', 'aria-checked', 'false').click()
            })
        })
    })
    describe('With permission', () => {
        beforeEach(() => {
            visit([ARTICLE_COMMENT_WRITE])
        })

        const checkCommentToggling = ({ article_comment_id, tooltip, new_is_active }) => {
            cy.get('[data-context=erp-timeline-article-comment-item]')
                .eq(0)
                .find('[data-context=erp-button]')
                .as('button')
            cy.get('@button').should('not.be.disabled')
            cy.get('@button').parent().tooltip(tooltip)

            cy.intercept('PUT', `**/api/erp/v1/article/120390/comment/${article_comment_id}`, {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: { updated: 1 },
                },
            }).as('put_article_comment')

            cy.get('@button').click()

            cy.get('@put_article_comment').then((xhr) => {
                expect(xhr.request.body.data).to.deep.eq({ is_active: new_is_active })
            })
            cy.wait(['@cpost_article_comments', '@get_article_by_id_or_sku_v2'])
        }

        it('should toggle comment', () => {
            cy.log('=== Disable an active comment ===')
            checkCommentToggling({
                article_comment_id: 17582,
                tooltip: 'Désactiver',
                new_is_active: false,
            })

            cy.get('[data-context=article-comment-display-mode] button').click()

            cy.log('=== Enable an inactive comment ===')
            checkCommentToggling({
                article_comment_id: 17583,
                tooltip: 'Activer',
                new_is_active: true,
            })
        })

        it('should add comment', () => {
            cy.get('[data-context=add-article-comment]').should('not.be.disabled').click()

            cy.get('[data-context=article-v2-comment-form]').within(() => {
                cy.get('[data-context=erp-form-block]:contains("Message")')
                    .find('[data-context=erp-textarea]')
                    .as('textarea')
                cy.get('@textarea').should('have.value', '')
                cy.get('@textarea').type("Je pense donc j'essuie")

                cy.intercept('POST', '**/api/erp/v1/article/120390/comment', {
                    statusCode: 200,
                    body: { status: 'success', data: { article_comment_id: 1 } },
                }).as('post_article_comment')

                cy.get('[data-context=erp-button]').click()

                cy.wait('@post_article_comment').then((xhr) => {
                    expect(xhr.request.body.data).to.deep.eq({ type: 'general', message: "Je pense donc j'essuie" })
                })

                cy.wait(['@cpost_article_comments', '@get_article_by_id_or_sku_v2'])

                cy.root().should('not.exist')
            })
        })

        it('should displays errors when adding a comment', function () {
            cy.get('[data-context=add-article-comment]').should('not.be.disabled').click()
            cy.intercept('POST', '**/api/erp/v1/article/120390/comment', {
                statusCode: 400,
                body: {
                    status: 'error',
                    message: 'Invalid parameters',
                    code: 400,
                    data: {
                        validation_errors: {
                            message: 'This value should not be blank.',
                        },
                    },
                },
            }).as('post_article_comment')

            cy.get('[data-context=article-v2-comment-form]').within(() => {
                cy.get('[data-context=erp-button]').click()

                cy.wait('@post_article_comment')

                cy.get('[data-context=erp-form-block]:contains("Message")').within(() => {
                    cy.get('[data-context=erp-textarea]').should('have.class', 'ring-red-500')

                    cy.get('[data-context=erp-input-helper]')
                        .should('have.class', 'text-red-600')
                        .should('contain', 'Cette valeur ne doit pas être vide')
                })
            })
        })
    })
})
