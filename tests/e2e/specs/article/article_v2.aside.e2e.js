import { ARTICLE_COMMENT_WRITE, ARTICLE_QR_CODE_VIEW } from '../../../../src/apps/erp/permissions'

describe('Article v2 - Aside', function () {
    const PAGE = '/articles/SONOSONENR/general'
    const COMMON_FIXTURE = 'erp/article/get_article_by_id_or_sku_v2__SONOSONENR'

    const visit = (permissions, page = PAGE) => {
        cy.authenticate()
        cy.mockErpUser([...(permissions ?? [])])

        cy.visit(page)

        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.closeAllToasts()
    }

    beforeEach(() => {
        cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
            fixture: COMMON_FIXTURE,
        }).as('get_article_by_id_or_sku_v2')
    })

    describe('QR Code helper', function () {
        it('should not have the qr code if the user does not have permission', function () {
            visit()

            cy.get('[data-context=qr-code]').should('not.exist')
        })

        it('should show the qr code EAN', function () {
            visit([ARTICLE_QR_CODE_VIEW])

            cy.get('[data-context=qr-code]').should('be.visible')
            cy.get('[data-context=qr-code] [data-context=hint]').should('contain', 'Basé sur le dernier EAN:')
            cy.get('[data-context=qr-code] [data-context=hint]').should('contain', '8717755775764')
        })

        it('should show the qr code Code 128 when there is no EAN', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.eans = []

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit([ARTICLE_QR_CODE_VIEW])

            cy.get('[data-context=qr-code]').should('be.visible')
            cy.get('[data-context=qr-code] [data-context=hint]').should('contain', 'Basé sur le code 128:')
            cy.get('[data-context=qr-code] [data-context=hint]').should('contain', '13900120390')
        })

        it('should not show the qr code when there are no Code 128 nor EAN', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.eans = []
                payload.data.logistic.code_128 = null

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit([ARTICLE_QR_CODE_VIEW])
            cy.get('[data-context=qr-code]').should('not.exist')
        })

        it('should not show the qr code for package', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.is_package = true

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit([ARTICLE_QR_CODE_VIEW])
            cy.get('[data-context=qr-code]').should('not.exist')
        })
    })

    describe('Article image', function () {
        it('should not show image section if none are set yet', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.image = null

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit()

            cy.get('[data-context=main-image]').should('not.exist')
        })

        it('should the article main image of the article', function () {
            visit()

            cy.get('[data-context=main-image]').should('be.visible')
            cy.get('[data-context=main-image] img')
                .should('have.attr', 'src')
                .and(
                    'match',
                    /https?:\/\/.*\/images\/article\/sonos\/SONOSONENR\/one-noir_5ac33e1957e09_300_square.jpg/,
                )
        })
    })

    describe('Last comment', function () {
        describe('Without write permission', () => {
            it('should only show the disabled comment button if there are no last comment', function () {
                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.last_comment = null

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit()

                cy.get('[data-context=last-comment]').should('not.exist')
                cy.get('[data-context=open-comment-button]')
                    .should('be.visible')
                    .should('be.disabled')
                    .should('contain', 'Ajouter un commentaire')
                    .parent()
                    .tooltip('Vous ne disposez pas des droits nécessaires')
            })

            it('should show the last comment on the article if it exists and open comments slide-in', function () {
                visit()

                cy.get('[data-context=last-comment]').should('be.visible')
                cy.get('[data-context=last-comment] [data-context=comment-owner]').should('contain', 'Cyril RENAUD')
                cy.get('[data-context=last-comment] [data-context=description]').should(
                    'contain',
                    'le 25/11/2022 à 22:35',
                )
                cy.get('[data-context=last-comment] [data-context=message]').should('contain', 'Bf cobra')

                cy.get('[data-context=open-comment-button]')
                    .should('be.visible')
                    .find('[data-icon=eye]')
                    .tooltip('Commentaires')
                    .click()

                cy.get('[data-context=article-comments]').should('be.visible')
            })
        })

        describe('With write permission', () => {
            it('should only show the comment button if there are no last comment and open the slide-in', function () {
                cy.fixture(COMMON_FIXTURE).then((payload) => {
                    payload.data.last_comment = null

                    cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                        statusCode: 200,
                        body: payload,
                    }).as('get_article_by_id_or_sku_v2')
                })

                visit([ARTICLE_COMMENT_WRITE])

                cy.get('[data-context=last-comment]').should('not.exist')
                cy.get('[data-context=open-comment-button]')
                    .should('be.visible')
                    .should('contain', 'Ajouter un commentaire')
                    .click()

                cy.get('[data-context=article-comments]').should('be.visible')
            })

            it('should open the comment detail slide-in with write permission for an article', function () {
                visit([ARTICLE_COMMENT_WRITE])

                cy.get('[data-context=open-comment-button]')
                    .find('[data-icon=pencil]')
                    .should('be.visible')
                    .tooltip('Commentaires')
                    .click()

                cy.get('[data-context=article-comments]').should('be.visible')
            })

            it('should open the comment detail slide-in with write permission for a package', function () {
                cy.intercept('GET', '**/api/erp/v2/article/BOSESOUBAR900SPK700BM700NR', {
                    fixture: 'erp/article/get_article_by_id_or_sku_v2__BOSESOUBAR900SPK700BM700NR',
                }).as('get_article_by_id_or_sku_v2')

                visit([ARTICLE_COMMENT_WRITE], '/articles/BOSESOUBAR900SPK700BM700NR/general')

                cy.get('[data-context=last-comment]').should('not.exist')

                cy.get('[data-context=last-comment]').should('not.exist')
                cy.get('[data-context=open-comment-button]')
                    .should('be.visible')
                    .should('contain', 'Ajouter un commentaire')
                    .click()

                cy.get('[data-context=article-comments]').should('be.visible')
            })
        })
    })

    describe('Declinations', function () {
        it('should show an empty declinations section if there are none', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.declinations = []

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit()

            cy.get('[data-context=declinations]').should('be.visible')
            cy.get('[data-context=declinations] [data-context=erp-article-item]').should('not.exist')
        })

        it('should show the article declinations', function () {
            visit()

            cy.get('[data-context=declinations]').should('be.visible')
            cy.get('[data-context=declinations]').should('contain', 'Déclinaisons')
            cy.get('[data-context=declinations]').should('contain', '1')

            cy.get('[data-context=declinations] [data-context=erp-article-item]').should('have.length', 1)
            cy.get('[data-context=declinations] [data-context=erp-article-item] [data-context=sku]').should(
                'have.attr',
                'href',
                '/articles/SONOSONEBC/',
            )
        })
    })

    describe('Initial Article', function () {
        it('should not show declinations section if there are none', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.initial_article = null

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit()

            cy.get('[data-context=initial-article]').should('not.exist')
        })

        it('should show related article reference section', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.initial_article = {
                    article_id: 128416,
                    delay: 0,
                    image: '/images/ui/uiV3/graphics/no-img-300.png',
                    short_description: 'Arcam BW CCM',
                    sku: 'BWCCM74',
                    status: 'tmp',
                }

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit()

            cy.get('[data-context=initial-article]').should('be.visible')

            cy.get('[data-context=initial-article] [data-context=erp-article-item]').should('have.length', 1)

            cy.get('[data-context=initial-article] [data-context=erp-article-item] [data-context=sku]')
                .eq(0)
                .should('have.attr', 'href', '/articles/BWCCM74/')
        })
    })

    describe('Destock related', function () {
        it('should show an empty delination section if article is a destock', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.is_destock = true

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit()

            cy.get('[data-context=article-aside-collapsible-block]').should('not.exist')
        })

        it('should show an empty destocks section if there are none', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.destocks = []

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit()

            cy.get('[data-context=destocks]').should('be.visible')
            cy.get('[data-context=destocks] [data-context=erp-article-item]').should('not.exist')
        })

        it('should show related destock section', function () {
            visit()

            cy.get('[data-context=destocks]').should('be.visible')
            cy.get('[data-context=destocks]').should('contain', 'Destocks')
            cy.get('[data-context=destocks]').should('contain', '8')

            cy.get('[data-context=destocks] [data-context=erp-article-item]').should('have.length', 5)
            cy.get('[data-context=destocks] [data-context=erp-article-item] [data-context=sku]')
                .eq(0)
                .should('have.attr', 'href', '/articles/DESTOCK-20180405003/')
        })
    })

    describe('Related package', function () {
        it('should show an empty related package section if there is none', function () {
            cy.fixture(COMMON_FIXTURE).then((payload) => {
                payload.data.related_packages = []

                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visit()

            cy.get('[data-context=related-packages]').should('be.visible')
            cy.get('[data-context=related-packages] [data-context=erp-article-item]').should('not.exist')
        })

        it('should show related package section', function () {
            visit()

            cy.get('[data-context=related-packages]').should('be.visible')
            cy.get('[data-context=related-packages] [data-context=erp-article-item]').should('have.length', 2)
            cy.get('[data-context=related-packages] [data-context=erp-article-item] [data-context=sku]')
                .eq(0)
                .should('have.attr', 'href', '/articles/SONOSARCNR/')
        })
    })

    describe('Code EANS', function () {
        it('should not show ean section', function () {
            // regression test
            visit()

            cy.get('[data-context=eans]').should('not.exist')
        })
    })

    describe('Print sticker', function () {
        it('should show the section', function () {
            visit()

            cy.get('[data-context=sticker]').should('be.visible')
            cy.get('[data-context=sticker] [data-context=slide_in_print-btn]').should('have.length', 1)
        })
    })
})
