import {
    ARTICLE_DESTOCK_WRITE,
    ARTICLE_STATUS_WRITE,
    PRODUCT_STOCK_READ,
    TRANSFER_CREATE,
} from '../../../../src/apps/erp/permissions'
import { aliasQuery, GRAPHQL_ENDPOINT, hasOperationName } from '../../utils/graphql-test-utils'
import { testPermissionInContext } from '../../utils/permission-utils'

describe('Article v2 - Header', function () {
    const PAGE = '/articles/SONOSONENR/general'
    const PAGE_DESTOCK = '/articles/DESTOCK-20191120013'
    const PAGE_PACKAGE = '/articles/LGOLED77C2S80QR'
    const FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE = 'fetchCmsArticleToDeduceType'

    const visit = (permissions, page = PAGE) => {
        cy.mockErpUser([...(permissions ?? [])])

        cy.visit(page)

        if (page === PAGE_PACKAGE) {
            cy.wait('@get_article_with_package_by_id_or_sku_v2')
        }
        if (page === PAGE_DESTOCK) {
            cy.wait(['@get_article_destock_by_id_or_sku_v2'])
        }
        if (page === PAGE) {
            cy.wait('@get_article_by_id_or_sku_v2')
        }
        cy.get('[data-context=erp-skeleton] [data-context=loaders]').should('not.exist')

        cy.toggleMenu()
        cy.closeAllToasts()
    }

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', /\/api\/erp\/v2\/article\/(SONOSONENR|120390)$/, {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__SONOSONENR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', /\/api\/erp\/v2\/article\/(LGOLED77C2S80QR|168898)$/, {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__LGOLED77C2S80QR',
        }).as('get_article_with_package_by_id_or_sku_v2')

        cy.intercept('GET', /\/api\/erp\/v2\/article\/(DESTOCK-20191120013|143214)$/, {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013',
        }).as('get_article_destock_by_id_or_sku_v2')
    })

    describe('CMS link', function () {
        function visitForCmsLink() {
            visit()

            cy.get('[data-context=article-v2-header] [data-context=cms-link]')
                .should('have.attr', 'href')
                .and('match', /https?:\/\/.*\/article\/edit\/SONOSONENR/)

            cy.closeAllToasts()
        }

        it('should show the a link to a regular CMS article', function () {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                    aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                    req.reply({
                        data: {
                            published: [
                                {
                                    sku: 'SONOSONENR',
                                    article_id: 120390,
                                    unbasketable_reason: null,
                                    __typename: 'cms_article_article',
                                },
                            ],
                            draft: [],
                        },
                    })
                }
            })

            visitForCmsLink()

            cy.get('[data-context=article-v2-header] [data-context=cms-link]')
                .should('not.be.disabled')
                .should('contain', 'CMS')
                .should('not.contain', 'DRAFT')
                .should('not.contain', 'NON')
        })

        it('should show the a link to a draft CMS article', function () {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                    aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                    req.reply({
                        data: {
                            published: [],
                            draft: [
                                {
                                    sku: 'SONOSONENR',
                                    article_id: 120390,
                                    unbasketable_reason: null,
                                    __typename: 'cms_article_article',
                                },
                            ],
                        },
                    })
                }
            })

            visitForCmsLink()

            cy.get('[data-context=article-v2-header] [data-context=cms-link]')
                .should('contain', 'DRAFT')
                .should('not.contain', 'NON')
        })

        it('should show the link to a non-basketable CMS article', function () {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                    aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                    req.reply({
                        data: {
                            published: [
                                {
                                    sku: 'SONOSONENR',
                                    article_id: 120390,
                                    unbasketable_reason: 'UNAVAILABLE',
                                    __typename: 'cms_article_article',
                                },
                            ],
                            draft: [],
                        },
                    })
                }
            })
            cy.fixture('erp/article/get_article_by_id_or_sku_v2__SONOSONENR').then((payload) => {
                payload.data.unbasketable_reason = 'UNAVAILABLE'
                cy.intercept('GET', /\/api\/erp\/v2\/article\/(SONOSONENR|120390)$/, {
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visitForCmsLink()
            cy.get('[data-context=article-v2-header] [data-context=cms-link] svg.fa-shopping-cart').should('exist')
            cy.get('[data-context=article-v2-header] [data-context=cms-link]').tooltip('Non vendable sur son-video.com')
        })

        it('should show a non-clickable link to a CMS article', function () {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                    aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                    req.reply({
                        data: {
                            published: [],
                            draft: [],
                        },
                    })
                }
            })

            visitForCmsLink()

            cy.get('[data-context=article-v2-header] [data-context=cms-link]')
                .should('have.attr', 'data-status', 'disabled')
                .should('not.contain', 'DRAFT')
                .should('not.contain', 'NON')

            cy.get('[data-context=article-v2-header] [data-context=cms-link]')
                .parent()
                .tooltip('Article non présent dans le CMS')
        })
    })

    it('should have the expected informations', function () {
        visit()

        // Title bar
        cy.get('[data-context=article-v2-header] [data-context=page-title]').should('contain', 'SONOS ONE Noir')
        cy.get('[data-context=article-v2-header] [data-context=availability]').should('contain', '24-48 heures')

        cy.get('[data-context=article-v2-header] [data-context=site-link]')
            .should('contain', 'son-video.com')
            .should('have.attr', 'href')
            .and(
                'match',
                /https?:\/\/.*\/article\/haute-fidelite-systemes-multiroom-enceintes-multiroom\/sonos\/one-noir/,
            )

        cy.get('[data-context=tabs] [data-context=item]')
            .should('have.length', 5)
            .then(($elements) => {
                const expectedTexts = ['Informations', 'Achats', 'Logistique', 'Statistiques', 'Historique']

                $elements.map((index, element) => {
                    cy.wrap(element).contains(expectedTexts[index])
                })
            })

        // Subtitle
        const EXPECTED_INFOS = [
            'SONOSONENR',
            '120390',
            'Crée le 05/10/2017',
            'Modifié le 29/03/2024',
            'Enceintes connectées',
            'SONOS',
            '2,40 kg',
            '229,00 €',
            '44,00 %',
            '123,45 €',
        ]

        EXPECTED_INFOS.forEach((info, index) => {
            cy.get('[data-context=article-v2-header] [data-context=sub-title] [data-context=item]')
                .eq(index)
                .should('contain', info)
        })

        // Copy values to clipboard
        const COPIABLE_INFOS = ['SONOSONENR', '120390']

        cy.window().then((win) => {
            cy.stub(win, 'prompt') // for copy-to-clipboard which opens a prompt when running cypress...
        })

        COPIABLE_INFOS.forEach((info, index) => {
            cy.get('[data-context=article-v2-header] [data-context=sub-title] [data-context=item]').eq(index).as('item')

            cy.get('@item').tooltip('Copier dans le presse papier')
            cy.get('@item').click()
            cy.toast(`"${info}" copié dans le presse-papier`, 'default')
        })

        const INDEX_SUBCATEGORY = 4

        cy.get('[data-context=article-v2-header] [data-context=sub-title] [data-context=item]')
            .eq(INDEX_SUBCATEGORY)
            .find('[data-context=erp-link]')
            .should('have.attr', 'href', '/category/subcategory/234/edit')

        cy.get('[data-context=icon-package]').should('not.exist')
        cy.get('[data-context=icon-destock]').should('not.exist')

        const INDEX_BRAND = 5

        cy.get('[data-context=article-v2-header] [data-context=sub-title] [data-context=item]')
            .eq(INDEX_BRAND)
            .find('[data-context=erp-link]')
            .should('have.attr', 'href', '/legacy/marque/editionMarque?id=585')

        const INDEX_MARGING = 8
        cy.get('[data-context=article-v2-header] [data-context=sub-title] [data-context=item]')
            .eq(INDEX_MARGING)
            .tooltip('123,98 € HT')

        const INDEX_WARRANTY = 9
        cy.get('[data-context=article-v2-header] [data-context=sub-title] [data-context=item]')
            .eq(INDEX_WARRANTY)
            .tooltip('GLD TTC')
    })

    it('should have a link to easylounge backoffice', function () {
        visit()

        cy.get('[data-context=dropdown-menu-item]').should('not.exist')

        cy.get('[data-context=show-more-trigger]').click()
        cy.get('[data-context=dropdown-menu-item] a:contains("CAP Caisse")')
            .should('be.visible')
            .should('have.attr', 'href')
            .and('match', /https?:\/\/.*capcaisse.*\/produits\/bysku\/SONOSONENR/)
    })

    context('Status', () => {
        it('should be disabled for user without permission', function () {
            visit()

            cy.get('[data-context=article-v2-header] [data-context=article-status]').as('article-status')
            cy.get('@article-status').parent().tooltip('Vous ne disposez pas des droits nécessaires')

            cy.get('@article-status').should('have.class', 'cursor-not-allowed')
            cy.get('@article-status').click()
            cy.get('@article-status').find('[data-context=dropdown]').should('not.exist')
        })

        it('should have the expected status', function () {
            visit([ARTICLE_STATUS_WRITE])

            cy.get('[data-context=article-v2-header] [data-context=article-status]').as('article-status')
            cy.get('@article-status').trigger('mouseenter')
            cy.get('[role=tooltip]').should('not.exist')

            cy.get('@article-status').should('contain', 'OUI').click()
            cy.get('@article-status').find('[data-context=dropdown] [data-context=suggestion]').as('suggestions')
            ;['A VOIR', 'TODO', 'NON', 'OUI', 'LAST', 'TMP', 'YAPU'].forEach((status, index) => {
                cy.get('@suggestions').eq(index).should('contain', status)
            })
            cy.get('@suggestions').eq(3).find('[data-context=checked-icon]').should('be.visible')
        })

        it('should change the status', function () {
            visit([ARTICLE_STATUS_WRITE])

            cy.get('[data-context=article-v2-header] [data-context=article-status]').as('article-status')
            cy.get('@article-status').trigger('mouseenter')
            cy.get('[role=tooltip]').should('not.exist')

            cy.get('@article-status').should('contain', 'OUI').click()

            cy.intercept('PUT', '**/api/erp/v1/article/120390', {
                delay: 400,
                body: { status: 'success', data: { updated: 1 } },
            }).as('put_article')

            cy.get('@article-status').find('[data-context=dropdown]').as('dropdown')
            cy.get('@dropdown').find('[data-context=suggestion]').eq(5).click()

            cy.get('@dropdown').should('not.exist')
            cy.get('@article-status').should('have.class', 'cursor-not-allowed').hasIcon('spinner-third', 'fad')

            cy.wait('@put_article').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({ scope: 'status', data: { status: 'tmp' } })
            })
        })

        it('should change the status destock with ARTICLE_DESTOCK_WRITE permission', function () {
            visit([ARTICLE_DESTOCK_WRITE], '/articles/DESTOCK-20191120013/general')

            cy.get('[data-context=article-v2-header] [data-context=article-status]').as('article-status')
            cy.get('@article-status').trigger('mouseenter')
            cy.get('[role=tooltip]').should('not.exist')

            cy.get('@article-status').should('contain', 'YAPU').click()

            cy.intercept('PUT', '**/api/erp/v1/article/143214', {
                delay: 400,
                body: { status: 'success', data: { updated: 1 } },
            }).as('put_article')

            cy.get('@article-status').find('[data-context=dropdown]').as('dropdown')
            cy.get('@dropdown').find('[data-context=suggestion]').eq(5).click()

            cy.get('@dropdown').should('not.exist')

            cy.wait('@put_article').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({ scope: 'status', data: { status: 'tmp' } })
            })
        })

        it('should handle api error', function () {
            visit([ARTICLE_STATUS_WRITE])

            cy.get('[data-context=article-v2-header] [data-context=article-status]').as('article-status')
            cy.get('@article-status').trigger('mouseenter')
            cy.get('[role=tooltip]').should('not.exist')

            cy.get('@article-status').should('contain', 'OUI').click()

            cy.intercept('PUT', '**/api/erp/v1/article/120390', {
                delay: 400,
                statusCode: 400,
                body: {
                    status: 'error',
                    data: {
                        validation_errors: {
                            status: '[key:unauthorized_for_destock] value "todo" : unauthorized for a destock',
                        },
                    },
                },
            }).as('put_article')

            cy.get('@article-status').find('[data-context=dropdown]').as('dropdown')
            cy.get('@dropdown').find('[data-context=suggestion]').eq(5).click()

            cy.get('@dropdown').should('not.exist')
            cy.get('@article-status').should('have.class', 'cursor-not-allowed').hasIcon('spinner-third', 'fad')

            cy.wait('@put_article')
            cy.toast('Statut incompatible avec un article destocké', 'danger')
            cy.get('@article-status').should('not.have.class', 'cursor-not-allowed')
            cy.get('@article-status').should('contain', 'OUI')
        })

        it('should have expected actions', function () {
            visit([TRANSFER_CREATE, PRODUCT_STOCK_READ])

            cy.get('[data-context=show-more-trigger]').click()
            cy.get('[data-context=dropdown-menu] [data-context=menu]').should('be.visible')
            cy.get('[data-context=menu] [data-context=dropdown-menu-item]').should('have.length', 4)
            cy.get('[data-context=create-transfer]').click()

            cy.get('[data-context=slide-out-container]').should('be.visible')
            cy.get('[data-context=slide-out-container]').find('h1').contains("Création d'un nouveau transfert")
        })
    })

    it('only displays package information', function () {
        // test the redirection on a route incompatible with the package
        localStorage.setItem('article_v2.config', 'article_stats')
        visit(null, '/articles/LGOLED77C2S80QR/general')
        cy.get('[data-context=article-v2-general-information]').should('be.visible')

        cy.get('[data-context=article-tab-information]').find('nav').should('have.length', 1)

        cy.get('[data-context=icon-package]').should('be.visible')

        cy.get('[data-context=tabs] [data-context=item]')
            .should('have.length', 4)
            .then(($elements) => {
                const expectedTexts = ['Informations', 'Achats', 'Statistiques', 'Historique']

                $elements.map((index, element) => {
                    cy.wrap(element).contains(expectedTexts[index])
                })
            })

        cy.get('[data-context=show-more-trigger]').click()
        cy.get('[data-context=menu] [data-context=dropdown-menu-item]').should('have.length', 1)
        cy.get('[data-context=old-item-sheet] [data-context=erp-tooltip]').should('contain', 'Ancienne fiche article')
    })

    it('only displays destock information', function () {
        localStorage.setItem('article_v2.config', 'article_stats')
        visit(null, '/articles/DESTOCK-20191120013/general')

        cy.get('[data-context=article-v2-general-information]').should('not.exist')
        cy.get('[data-context=article-v2-destock-information]').should('be.visible')

        cy.get('[data-context=icon-destock]').should('be.visible')

        cy.get('[data-context=tabs] [data-context=item]')
            .should('have.length', 4)
            .then(($elements) => {
                const expectedTexts = ['Informations', 'Achats', 'Logistique', 'Historique']

                $elements.map((index, element) => {
                    cy.wrap(element).contains(expectedTexts[index])
                })
            })

        cy.get('[data-context=show-more-trigger]').click()
        cy.get('[data-context=menu] [data-context=dropdown-menu-item]')
            .should('have.length', 2)
            .then(($elements) => {
                const expectedTexts = ['Créer Transfert', 'Ancienne fiche article']

                $elements.map((index, element) => {
                    cy.wrap(element).contains(expectedTexts[index])
                })
            })
    })

    testPermissionInContext('[data-context=permissions-in-header]', visit, {
        _or: [
            {
                permission_id: {
                    _ilike: 'PRODUCT_STOCK_READ',
                },
            },
            {
                permission_id: {
                    _ilike: 'TRANSFER_CREATE',
                },
            },
            {
                permission_id: {
                    _ilike: 'ARTICLE_STATUS_WRITE',
                },
            },
            {
                permission_id: {
                    _ilike: 'ARTICLE_QR_CODE_VIEW',
                },
            },
        ],
    })
})
