describe('Article v2 - Tabs', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    const checkTabs = (tabs) => {
        cy.get('[data-context=tabs] [data-context=item]').as('tabs')

        cy.get('@tabs').should('have.length', tabs.length)

        tabs.forEach((tab, index) => {
            cy.get('@tabs').eq(index).should('contain', tab)
        })
    }

    const checkSubtabs = (subtabs) => {
        cy.get('[data-context=erp-page-sidebar-menu-item]').as('subtabs')

        cy.get('@subtabs').should('have.length', subtabs.length)

        subtabs.forEach((subtab, index) => {
            cy.get('@subtabs').eq(index).should('contain', subtab)
        })
    }

    it('should have the expected tabs and subtabs for an article', function () {
        cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__SONOSONENR',
        }).as('get_article_by_id_or_sku_v2')

        cy.visit('/articles/SONOSONENR/general')
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        checkTabs(['Informations', 'Achats', 'Logistique', 'Statistiques', 'Historique'])
        checkSubtabs(['General', 'EAV', 'Visuels', 'Documents'])
    })

    it('should have the expected tabs and subtabs for a package', function () {
        cy.intercept('GET', '**/api/erp/v2/article/BOSESOUBAR900SPK700BM700NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__BOSESOUBAR900SPK700BM700NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.visit('/articles/BOSESOUBAR900SPK700BM700NR/general')
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        checkTabs(['Informations', 'Achats', 'Statistiques', 'Historique'])
        checkSubtabs(['General', 'EAV', 'Visuels', 'Documents'])
    })
})
