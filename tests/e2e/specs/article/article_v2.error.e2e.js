import {} from '../../../../src/apps/erp/permissions'

describe('Article v2 - Common tests', function () {
    const PAGE = '/articles/DUMMY'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v2/article/DUMMY', {
            statusCode: 404,
            body: false,
        }).as('get_article_by_id_or_sku_v2')
    })

    it('should redirect to 404 page if no article has been found', function () {
        cy.visit(PAGE)

        cy.get('[data-context=error-code]').should('contain', '404')
        cy.get('[data-context=error-message-general]').should('contain', 'Oops... Une erreur est survenue')
        cy.get('[data-context=error-message-code]').should('contain', `L'article "DUMMY" n'existe pas`)
        cy.get('[data-context=back-home-link]').should('contain', "Retour sur la page d'accueil")
    })
})
