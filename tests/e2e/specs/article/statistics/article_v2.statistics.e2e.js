describe('Article statistics page', function () {
    const visitPageWithSku = (sku) => {
        cy.intercept('GET', '**/api/erp/v2/article/' + sku, {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__' + sku,
        }).as('get_article_by_id_or_sku_v2')

        cy.visit('/articles/' + sku + '/stats')

        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/metabase/embed-dashboard-url', {
            statusCode: 200,
            body: {
                status: 'success',
                data: { url: 'https:metabase.lxc' },
            },
        }).as('post_metabase_embed_dashboard_url')
    })

    it('Display article statistics dashboard from metabase', function () {
        const sku = 'SONOSONENR'
        visitPageWithSku(sku)

        cy.wait('@post_metabase_embed_dashboard_url').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({
                dashboard: 16,
                params: { sku: sku },
            })
        })

        cy.get('[data-context=metabase-wrapper]').should('be.visible')
    })

    it('Display package statistics dashboard from metabase', function () {
        const sku = 'LGOLED77C2S80QR'
        visitPageWithSku(sku)

        cy.wait('@post_metabase_embed_dashboard_url').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({
                dashboard: 19,
                params: { sku: sku },
            })
        })

        cy.get('[data-context=metabase-wrapper]').should('be.visible')
    })
})
