import { ARTICLE_GENERAL_INFORMATION_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - General - basic informations', function () {
    beforeEach(() => {
        cy.intercept('POST', '**/api/erp/v1/brands', { fixture: 'erp/brand/cpost_brands' }).as('cpost_brands')

        cy.intercept('POST', '**/api/erp/v1/subcategories', { fixture: 'erp/subcategory/cpost_subcategories' }).as(
            'cpost_subcategories',
        )

        cy.intercept('POST', '**/api/erp/v1/article-colors', {
            fixture: 'erp/article/cpost_article_colors',
        }).as('cpost_article_colors')
    })
    const visitPage = (page) => {
        cy.visit(page)

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2', { timeout: 6000 })
        cy.toggleMenu()

        cy.get('[data-context=tabs] [data-context=item]:contains(Informations)').click()
        cy.wait('@cpost_article_colors')

        cy.get('[data-context=article-v2-general-information]').as('form').should('be.visible')
        cy.closeAllToasts()
    }

    context('With an article', function () {
        beforeEach(() => {
            cy.authenticate()

            cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
            }).as('get_article_by_id_or_sku_v2')
        })

        it('should display general information but disabled', function () {
            cy.mockErpUser()
            visitPage('/articles/KEFQ350NR/general')

            cy.get('label:contains(Marque)').closest('[data-context=brand-name]').should('contain', 'KEF')
            cy.get('label:contains(Marque)').closest('[data-context=brand-name]').find('input').should('be.disabled')

            cy.get('label:contains(Modèle)')
                .closest('[data-context=model]')
                .find('input')
                .as('input_model')
                .should('have.value', 'Q350 Noir')
            cy.get('@input_model').should('be.disabled')

            cy.get('label:contains(Sous-catégorie)')
                .closest('[data-context=subcategory]')
                .should('contain', 'Enceintes bibliothèque')
            cy.get('label:contains(Sous-catégorie)')
                .closest('[data-context=subcategory]')
                .find('input')
                .should('be.disabled')

            cy.get('label:contains(Sous-catégorie)')
                .closest('[data-context=subcategory]')
                .find('input')
                .should('be.disabled')

            cy.get('label:contains(Description courte)')
                .closest('[data-context=short-description]')
                .find('textarea')
                .as('input_short_description')
                .should('have.value', 'KEF Q350 Noir (la paire)')
            cy.get('@input_short_description').should('be.disabled')

            cy.get('label:contains(Description panier)')
                .closest('[data-context=basket-description]')
                .find('textarea')
                .as('input_basket_description')
                .should('have.value', "Paire d'enceintes KEF Q350 Noir")
            cy.get('@input_basket_description').should('be.disabled')

            cy.get('label:contains(Description marketplace)')
                .closest('[data-context=marketplace-description]')
                .find('textarea')
                .as('input_marketplace_description')
                .should('have.value', 'Je suis une description pour les marketplaces')
            cy.get('@input_marketplace_description').should('be.disabled')

            cy.get('label:contains(Vendu par lot)')
                .closest('[data-context=packages]')
                .find('input')
                .should('have.value', '2')

            cy.get("label:contains(Date d'embargo)")
                .closest('[data-context=embargo-date]')
                .find('input')
                .should('have.value', '15 janv. 2024')

            cy.get('label:contains(Garantie constructeur)')
                .closest('[data-context=manufacturer-warranty-years]')
                .within(() => {
                    cy.get('input').should('be.disabled').should('have.value', '3')
                    cy.get('[data-context=trailing]').should('contain', 'an(s)')
                })

            cy.get('label:contains(Extension de garantie 5 ans)')
                .closest('[data-context=has-warranty-extension-5-years]')
                .should('contain', 'OUI')

            cy.get("label:contains(Prix de l'extension de garantie 5 ans)")
                .closest('[data-context=warranty-extension-5-years-price]')
                .should('contain', '123,45 € TTC')

            cy.get('label:contains(Couleur)')
                .closest('[data-context=color]')
                .should('contain', 'Anthracite')
                .within(() => {
                    cy.get('img:visible').should(
                        'have.attr',
                        'src',
                        'http://www.son-video.com/images/static/Coloris/Anthracite.jpg',
                    )
                    cy.get('input').should('be.disabled')
                })
        })

        it('should display general information', function () {
            cy.mockErpUser([ARTICLE_GENERAL_INFORMATION_WRITE])
            visitPage('/articles/KEFQ350NR/general')

            cy.get('label:contains(Marque)').closest('[data-context=brand-name]').should('contain', 'KEF')
            cy.get('label:contains(Marque)')
                .closest('[data-context=brand-name]')
                .find('input')
                .should('not.be.disabled')

            cy.get('label:contains(Modèle)')
                .closest('[data-context=model]')
                .find('input')
                .as('input_model')
                .should('have.value', 'Q350 Noir')
            cy.get('@input_model').should('not.be.disabled')

            cy.get('label:contains(Vendu par lot)')
                .closest('[data-context=packages]')
                .find('input')
                .should('have.value', '2')
                .should('not.be.disabled')

            cy.get('label:contains(Sous-catégorie)')
                .closest('[data-context=subcategory]')
                .should('contain', 'Enceintes bibliothèque')
            cy.get('label:contains(Sous-catégorie)')
                .closest('[data-context=subcategory]')
                .find('input')
                .should('not.be.disabled')

            cy.get('label:contains(Description courte)')
                .closest('[data-context=short-description]')
                .find('textarea')
                .as('input_short_description')
                .should('have.value', 'KEF Q350 Noir (la paire)')
            cy.get('@input_short_description').should('not.be.disabled')

            cy.get('label:contains(Description marketplace)')
                .closest('[data-context=marketplace-description]')
                .find('textarea')
                .as('input_marketplace_description')
                .should('have.value', 'Je suis une description pour les marketplaces')
            cy.get('@input_marketplace_description').should('not.be.disabled')

            cy.get('label:contains(Description panier)')
                .closest('[data-context=basket-description]')
                .find('textarea')
                .as('input_basket_description')
                .should('have.value', "Paire d'enceintes KEF Q350 Noir")
            cy.get('@input_basket_description').should('not.be.disabled')

            cy.get('label:contains(Couleur)')
                .closest('[data-context=color]')
                .should('contain', 'Anthracite')
                .within(() => {
                    cy.get('img:visible').should(
                        'have.attr',
                        'src',
                        'http://www.son-video.com/images/static/Coloris/Anthracite.jpg',
                    )
                    cy.get('input').should('not.be.disabled')
                })

            cy.get('label:contains(Garantie constructeur)')
                .closest('[data-context=manufacturer-warranty-years]')
                .find('input')
                .should('not.be.disabled')
                .should('have.value', '3')
        })

        it('can update general information', function () {
            cy.mockErpUser([ARTICLE_GENERAL_INFORMATION_WRITE])
            visitPage('/articles/KEFQ350NR/general')

            cy.get('label:contains(Marque)')
                .closest('[data-context=brand-name]')
                .should('contain', 'KEF')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('Ac', 'Accuphase')
            cy.wait('@cpost_brands')

            cy.get('label:contains(Modèle)')
                .closest('[data-context=model]')
                .find('input')
                .should('have.value', 'Q350 Noir')
                .type(' foncé')

            cy.get('label:contains(Sous-catégorie)')
                .closest('[data-context=subcategory]')
                .should('contain', 'Enceintes bibliothèque')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('enc', 'Accessoires enceintes')
            cy.wait('@cpost_subcategories')

            cy.get('label:contains(Couleur)')
                .closest('[data-context=color]')
                .within(() => {
                    cy.get('[data-context=erp-multiselect]').erpMultiselect('indef', 'indéfinie')
                    cy.get('img:visible').should(
                        'have.attr',
                        'src',
                        'https://image.son-video.com/images/ui/uiV3/graphics/no-img-300.png',
                    )
                    cy.get('[data-context=erp-multiselect]').erpMultiselect('baf', 'Bambou foncé')
                    cy.get('img:visible').should(
                        'have.attr',
                        'src',
                        'https://www.son-video.com/images/static/Coloris/BambouFonce.gif',
                    )
                })

            cy.get('label:contains(Description courte)')
                .closest('[data-context=short-description]')
                .find('textarea')
                .as('input_short_description')
                .should('have.value', 'KEF Q350 Noir (la paire)')
            cy.get('@input_short_description').clear()
            cy.get('@input_short_description').type('KEF Q350 Blanc (la paire)')

            cy.get('label:contains(Description panier)')
                .closest('[data-context=basket-description]')
                .find('textarea')
                .as('input_basket_description')
                .should('have.value', "Paire d'enceintes KEF Q350 Noir")
                .type(' (couleur non garantie)')

            cy.get('label:contains(Description marketplace)')
                .closest('[data-context=marketplace-description]')
                .find('textarea')
                .as('input_marketplace_description')
                .should('have.value', 'Je suis une description pour les marketplaces')
            cy.get('@input_marketplace_description').clear()
            cy.get('@input_marketplace_description').type('Soy una descripción de producto para Marketplaces españoles')

            cy.get('label:contains(Vendu par lot)').closest('[data-context=packages]').find('input').as('input')
            cy.get('@input').clear()
            cy.get('@input').type('10')

            cy.get("label:contains(Date d'embargo)")
                .closest('[data-context=embargo-date]')
                .within(() => {
                    cy.get('input').should('have.value', '15 janv. 2024')
                    cy.get('[data-context=erp-date-picker]').erpDatePicker('16')
                })

            cy.get('label:contains(Garantie constructeur)')
                .closest('[data-context=manufacturer-warranty-years]')
                .find('input')
                .within(() => {
                    cy.root().clear()
                    cy.root().type(5)
                })

            cy.intercept('PUT', '**/api/erp/v1/article/117735', {
                delay: 200,
            }).as('put_article')

            cy.get('@form').find('[data-context=erp-button]').as('button')

            cy.get('@button').click()
            cy.get('@button').should('be.disabled')

            cy.wait('@put_article').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    scope: 'general_information',
                    data: {
                        name: 'Q350 Noir foncé',
                        embargo_date: '2024-01-16',
                        brand_id: 261,
                        color_id: 256,
                        basket_description: 'Paire d\u0027enceintes KEF Q350 Noir (couleur non garantie)',
                        short_description: 'KEF Q350 Blanc (la paire)',
                        marketplace_description: 'Soy una descripción de producto para Marketplaces españoles',
                        packages: 10,
                        subcategory_id: 144,
                        manufacturer_warranty_years: 5,
                    },
                })
            })

            cy.wait('@get_article_by_id_or_sku_v2')
        })

        it('displays errors', function () {
            cy.mockErpUser([ARTICLE_GENERAL_INFORMATION_WRITE])
            visitPage('/articles/KEFQ350NR/general')

            cy.intercept('PUT', '**/api/erp/v1/article/117735', {
                statusCode: 400,
                body: {
                    status: 'error',
                    message: 'Invalid parameters',
                    code: 400,
                    data: {
                        validation_errors: {
                            manufacturer_warranty_years: '[key:value_out_of_range] value 1000 : Out of range',
                        },
                    },
                },
            }).as('put_article')

            cy.get('@form').find('[data-context=erp-button]').click()

            cy.wait('@put_article')

            cy.get('label:contains(Garantie constructeur)')
                .closest('[data-context=manufacturer-warranty-years]')
                .find('[data-context=erp-input-helper]')
                .should('have.class', 'text-red-600')
                .should('contain', 'Cette valeur est hors-limite')
        })
    })
    context('With a package', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_GENERAL_INFORMATION_WRITE])
        })

        it('should not display warranties', function () {
            cy.intercept('GET', '**/api/erp/v2/article/LGOLED77C2S80QR', {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__LGOLED77C2S80QR',
            }).as('get_article_by_id_or_sku_v2')

            visitPage('/articles/LGOLED77C2S80QR/general')
            ;[
                'Garantie constructeur',
                'Extension de garantie 2 ans',
                'Extension de garantie 5 ans',
                "Prix de l'extension de garantie 5 ans",
            ].forEach((label) => {
                cy.get(`label:contains(${label})`).should('not.exist')
            })
        })

        it('should not change manufacturer warranty', function () {
            cy.fixture('erp/article/get_article_by_id_or_sku_v2__LGOLED77C2S80QR').then((payload) => {
                payload.data.manufacturer_warranty_years = 3
                cy.intercept('GET', '**/api/erp/v2/article/LGOLED77C2S80QR', {
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })

            visitPage('/articles/LGOLED77C2S80QR/general')

            cy.intercept('PUT', '**/api/erp/v1/article/168898', {
                delay: 200,
            }).as('put_article')

            cy.get('@form').find('[data-context=erp-button]').click()

            cy.wait('@put_article').then((xhr) => {
                expect(xhr.request.body.data.manufacturer_warranty_years).to.equal(3)
            })

            cy.wait(['@get_article_by_id_or_sku_v2'])
        })
    })

    context('With a package', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_GENERAL_INFORMATION_WRITE])
        })

        it('should not display warranties', function () {
            cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013',
            }).as('get_article_by_id_or_sku_v2')

            cy.visit('/articles/DESTOCK-20191120013/general')

            cy.wait('@get_article_by_id_or_sku_v2')
            cy.toggleMenu()

            cy.get('[data-context=article-v2-general-information]').should('not.exist')
        })
    })
})
