import {
    ATTRIBUTE_READ,
    ATTRIBUTE_VALUE_CREATE,
    ATTRIBUTE_VALUE_DELETE,
} from '../../../../../src/apps/erp/permissions.js'
import { getWhereProductAttributesVariables } from '../../../../../src/apps/erp/components/Article/Eav/where'
import {
    aliasMutation,
    aliasQuery,
    getMutationAlias,
    getQueryAlias,
    GRAPHQL_ENDPOINT,
    hasOperationName,
} from '../../../utils/graphql-test-utils'

describe('Article v2 - Informations - EAV', function () {
    const PAGE = '/articles/KEFQ350NR/general'

    const commonFixtures = () => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'fetchAttributesAndValuesForProduct')) {
                aliasQuery(req, 'fetchAttributesAndValuesForProduct')

                req.reply({
                    fixture: 'graphql/eav/product/fetch_product_values__KEFQ350NR',
                })
            }
        })
    }
    const navigateToEAVSection = () => {
        cy.visit(PAGE)
        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')

        cy.toggleMenu()

        cy.get('[data-context=erp-page-sidebar-menu-item]:contains(EAV)').tooltip('EAV').click()

        cy.wait(getQueryAlias('fetchAttributesAndValuesForProduct')).then((xhr) => {
            expect(xhr.request.body.operationName).to.eq('fetchAttributesAndValuesForProduct')
            expect(xhr.request.body.variables).to.deep.eq(getWhereProductAttributesVariables('KEFQ350NR', 94))
        })

        cy.closeAllToasts()
    }

    context('With no permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            commonFixtures()
        })

        it('should not show the tab if the user does have the permission to access it', function () {
            cy.visit(PAGE)
            // Initial calls on the page
            cy.wait('@get_article_by_id_or_sku_v2')
            cy.toggleMenu()

            cy.get('[data-context=erp-page-sidebar-menu-item]:contains(EAV)').tooltip('EAV').click()

            cy.get('[data-context=article-v2-block-eav-manager] [data-context=alert-warn]').should(
                'contain',
                "Vous n'avez pas la permission de consulter cette page",
            )
        })
    })

    context('With read only permissions', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ATTRIBUTE_READ])

            commonFixtures()
        })

        it('should provide a page listing all attributes and their values associated to an article', function () {
            navigateToEAVSection()

            const section_titles = [
                'Attribut(s) associé(s) à la sous-catégorie',
                'Attribut(s) non associé(s) à la sous-catégorie',
            ]

            const headers = ['Attribut', 'Libellé public', 'Valeurs']

            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel]').should('have.length', 2)

            section_titles.forEach((title, index) => {
                // Section title
                cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel]').eq(index).as('panel')
                cy.get('@panel').find('[data-context=title]').should('contain', title)

                // Table headers
                cy.get('@panel').find('[data-context=erp-table] thead tr th').should('have.length', 3)
                headers.forEach((th, idx) => {
                    cy.get('@panel').find('[data-context=erp-table] thead tr th').eq(idx).should('contain', th)
                })
            })

            // ===
            // Check non editable rows
            // ===
            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel] [data-context=erp-table]')
                .eq(0)
                .as('table')

            cy.get('@table').find('tbody tr').should('have.length', 16)

            // classic content
            cy.get('@table').selectRow(1)
            cy.selectCell(0).should('contain', 'Coloris enceinte')
            cy.selectCell(1).should('contain', 'Couleur')
            cy.selectCell(2).should('contain', 'Noir')

            // with suffix
            cy.get('@table').selectRow(2)
            cy.selectCell(0).should('contain', "Hauteur de l'enceinte")
            cy.selectCell(1).should('contain', "Hauteur de l'enceinte")
            cy.selectCell(2).should('contain', '362 mm')

            // with prefix
            cy.get('@table').selectRow(9)
            cy.selectCell(0).should('contain', 'Puissance continu enceinte')
            cy.selectCell(1).should('contain', 'Puissance continu (ou admissible)')
            cy.selectCell(2).should('contain', 'RMS PMPO 120 Watts')

            // empty value is mixed among other category attributes
            cy.get('@table').selectRow(10)
            cy.selectCell(0).should('contain', 'Puissance crête enceinte')
            cy.selectCell(1).should('contain', 'Puissance en crête')
            cy.selectCell(2).should('contain', 'Aucune valeur')

            // multiple values, reordered
            cy.get('@table').selectRow(15)
            cy.selectCell(0).should('contain', 'Usage enceinte')
            cy.selectCell(1).should('contain', 'Usage')
            cy.selectCell(2).find('[data-context="tag"]').eq(0).should('contain', 'Home-cinéma')
            cy.selectCell(2).find('[data-context="tag"]').eq(1).should('contain', 'Haute-fidélité')

            // ===
            // Check non editable rows
            // ===
            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel] [data-context=erp-table]')
                .eq(1)
                .as('table')

            cy.get('@table').find('tbody tr').should('have.length', 2)

            // type color
            cy.get('@table').selectRow(0)
            cy.selectCell(0).should('contain', 'SVD_Couleur')
            cy.selectCell(1).should('contain', 'Couleur')
            cy.selectCell(2).should('contain', 'Noir')

            cy.get('@table').selectRow(1)
            cy.selectCell(0).should('contain', 'SVD_Couleur détaillée')
            cy.selectCell(2).should('contain', 'Noir mat')

            // No remove button anywhere on the panel
            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel] [data-context=remove]').should(
                'not.exist',
            )
        })

        it('check order on attributes', function () {
            navigateToEAVSection()

            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel] [data-context=erp-table]')
                .eq(0)
                .as('table')

            cy.get('@table').selectRow(15)
            cy.selectCell(0).should('contain', 'Usage enceinte')
            cy.selectCell(1).should('contain', 'Usage')
            cy.selectCell(2).should('contain', 'Home-cinéma')

            cy.get('@table').selectRow(14)
            cy.selectCell(0).should('contain', 'Sensibilité')
            cy.selectCell(1).should('contain', 'Sensibilité')
            cy.selectCell(2).should('contain', '87 dB')

            cy.get('@table').selectRow(13)
            cy.selectCell(0).should('contain', ' Réponse fréquence mini ')
            cy.selectCell(1).should('contain', ' Réponse en fréquence Min ')
            cy.selectCell(2).should('contain', '63 Hz')

            cy.get('@table').selectRow(12)
            cy.selectCell(0).should('contain', 'Type de charge enceinte')
            cy.selectCell(1).should('contain', 'Type de charge')
            cy.selectCell(2).should('contain', 'Bass-Reflex')
        })
    })

    context('With write permissions', function () {
        beforeEach(function () {
            cy.authenticate()
            cy.mockErpUser([ATTRIBUTE_READ, ATTRIBUTE_VALUE_CREATE, ATTRIBUTE_VALUE_DELETE])

            commonFixtures()
        })

        it('edit an attribute of type text', function () {
            navigateToEAVSection()

            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel] [data-context=erp-table]')
                .eq(0)
                .as('table')

            cy.get('@table').selectRow(0)
            cy.selectCell(2)

            cy.get('@cell').find('[data-context="tag"]').as('tag')

            cy.get('@tag').should('have.length', 1)
            cy.get('@tag').eq(0).should('contain', 'Non (bornier classique)')

            cy.get('@cell').find('[data-context="erp-multiselect"]').click().type('{downarrow}')
            cy.get('@cell').find('[data-context="suggestion"]').as('suggestions')

            cy.get('@suggestions').should('have.length', 4)

            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'upsertAttributeValuesAndProductValues')) {
                    aliasMutation(req, 'upsertAttributeValuesAndProductValues')

                    req.reply({
                        fixture: 'graphql/eav/product/upsert_attribute_values_and_product_values__KEFQ350NR_1.json',
                    })

                    expect(req.body.variables).to.deep.eq({
                        attribute_value: [
                            {
                                attribute_id: 608,
                                value: 'Non (bornier sertissage)',
                                meta: {},
                                product_values: { data: { sku: 'KEFQ350NR' } },
                            },
                            {
                                attribute_id: 608,
                                value: 'Oui (Bi-câblage possible)',
                                meta: {},
                                product_values: { data: { sku: 'KEFQ350NR' } },
                            },
                        ],
                        where_delete: { attribute_value_id: { _in: 0 }, sku: { _eq: 'KEFQ350NR' } },
                        where_sku: { sku: { _eq: 'KEFQ350NR' } },
                        product_subcategory: { sku: 'KEFQ350NR', subcategory_id: 94 },
                    })
                }

                // ===
                // test deletion
                // ===
                if (hasOperationName(req, 'deleteProductValue')) {
                    aliasMutation(req, 'deleteProductValue')

                    req.reply({
                        fixture: 'graphql/eav/product/delete_eav_product_value__KEFQ350NR_11381.json',
                    })

                    expect(req.body.variables).to.deep.eq({
                        where: { attribute_value_id: { _in: [11381] }, sku: { _eq: 'KEFQ350NR' } },
                    })
                }
            })

            cy.get('@suggestions').eq(1).click()
            cy.get('@suggestions').eq(2).click()
            cy.get('@cell').find('[data-context="erp-multiselect"]').type('{esc}')

            cy.wait(getMutationAlias('upsertAttributeValuesAndProductValues'))

            cy.get('@tag').should('have.length', 2)

            cy.get('@tag').eq(0).should('contain', 'Non (bornier sertissage)')
            cy.get('@tag').eq(1).should('contain', 'Oui (Bi-câblage possible)')

            cy.get('@tag').eq(1).find('[data-context="remove"]').click()
            cy.wait(getMutationAlias('deleteProductValue'))

            cy.get('@tag').should('have.length', 1)
        })

        it('edit attributes of type numeric', function () {
            navigateToEAVSection()

            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel] [data-context=erp-table]')
                .eq(0)
                .as('table')

            // ===
            // Edit with existing value and keyboard
            // ===
            cy.get('@table').selectRow(2)
            cy.selectCell(2).as('cell')

            cy.get('@cell').should('contain', '362 mm')

            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'upsertAttributeValuesAndProductValues')) {
                    aliasMutation(req, 'upsertAttributeValuesAndProductValues')

                    req.reply({
                        fixture: 'graphql/eav/product/upsert_attribute_values_and_product_values__KEFQ350NR_2.json',
                    })

                    expect(req.body.variables).to.deep.eq({
                        attribute_value: [
                            {
                                attribute_id: 241,
                                value: '12.2',
                                meta: {},
                                product_values: { data: { sku: 'KEFQ350NR' } },
                            },
                        ],
                        where_delete: { attribute_value_id: { _in: [18694] }, sku: { _eq: 'KEFQ350NR' } },
                        where_sku: { sku: { _eq: 'KEFQ350NR' } },
                        product_subcategory: { sku: 'KEFQ350NR', subcategory_id: 94 },
                    })
                }
            })
            cy.get('@cell').find('[data-context="erp-multiselect"]').click().type('{downarrow}{enter}{esc}')

            cy.wait(getMutationAlias('upsertAttributeValuesAndProductValues'))

            cy.get('@cell').should('contain', '12.2 mm')

            // ===
            // Create a new value, a number containing a comma
            // ===
            cy.get('@table').selectRow(7)
            cy.selectCell(2).should('contain', '7.6 Kg')

            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'upsertAttributeValuesAndProductValues')) {
                    aliasMutation(req, 'upsertAttributeValuesAndProductValues')

                    req.reply({
                        fixture: 'graphql/eav/product/upsert_attribute_values_and_product_values__KEFQ350NR_3.json',
                    })

                    expect(req.body.variables).to.deep.eq({
                        attribute_value: [
                            {
                                attribute_id: 244,
                                value: '6.666',
                                meta: {},
                                product_values: { data: { sku: 'KEFQ350NR' } },
                            },
                        ],
                        where_delete: { attribute_value_id: { _in: [15657] }, sku: { _eq: 'KEFQ350NR' } },
                        where_sku: { sku: { _eq: 'KEFQ350NR' } },
                        product_subcategory: { sku: 'KEFQ350NR', subcategory_id: 94 },
                    })
                }
            })

            cy.get('@cell').find('[data-context="erp-multiselect"]').click().type('6,666{enter}')

            cy.wait(getMutationAlias('upsertAttributeValuesAndProductValues'))

            cy.selectCell(2).should('contain', '6.666 Kg')

            // ===
            // Check error message
            // ===

            cy.get('@cell').find('[data-context="erp-multiselect"]').click().type('text{enter}')

            cy.get('@cell').find('[data-context="hint"]').should('contain', 'La valeur doit être numérique')

            // ===
            // test deletion
            // ===
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'deleteProductValue')) {
                    aliasMutation(req, 'deleteProductValue')

                    req.reply({
                        fixture: 'graphql/eav/product/delete_eav_product_value__KEFQ350NR_26390.json',
                    })

                    expect(req.body.variables).to.deep.eq({
                        where: { attribute_value_id: { _in: [26390] }, sku: { _eq: 'KEFQ350NR' } },
                    })
                }
            })

            cy.get('@cell').find('[data-context="remove"]').click()

            cy.wait(getMutationAlias('deleteProductValue'))

            cy.selectCell(2).should('contain', 'Aucune valeur')
        })

        it('edit attributes of type color', function () {
            navigateToEAVSection()

            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel] [data-context=erp-table]')
                .eq(1)
                .as('table')

            // ===
            // "couleur détaillée" values depends on selected Couleur
            // ===
            cy.get('@table').selectRow(1)
            cy.selectCell(2).find('[data-context="erp-multiselect"]').click().type('{downarrow}')

            // list filtered
            cy.get('@cell')
                .find('[data-context="suggestion"]')
                .should('have.length', 34)
                .should('not.contain', 'Bois de Rose')
                .should('contain', 'Anthracite')

            // has no error on "couleur détaillée"
            cy.get('@cell').find('[data-context=eav-product-value-error]').should('not.exist')

            cy.selectCell(2).find('[data-context="erp-multiselect"]').type('{esc}')

            // ===
            // Edit with existing value and keyboard
            // ===
            cy.get('@table').selectRow(0)
            cy.selectCell(2)
            cy.get('@cell').should('contain', 'Noir')

            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'upsertAttributeValuesAndProductValues')) {
                    aliasMutation(req, 'upsertAttributeValuesAndProductValues')

                    req.reply({
                        fixture: 'graphql/eav/product/upsert_attribute_values_and_product_values__KEFQ350NR_25941.json',
                    })

                    expect(req.body.variables).to.deep.eq({
                        attribute_value: [
                            {
                                attribute_id: 952,
                                value: 'Bois foncé',
                                meta: {},
                                product_values: { data: { sku: 'KEFQ350NR' } },
                            },
                        ],
                        where_delete: { attribute_value_id: { _in: [25941] }, sku: { _eq: 'KEFQ350NR' } },
                        where_sku: { sku: { _eq: 'KEFQ350NR' } },
                        product_subcategory: { sku: 'KEFQ350NR', subcategory_id: 94 },
                    })
                }
            })

            cy.get('@cell').find('[data-context="erp-multiselect"]').click().type('bois fonce{downarrow}{enter}')

            cy.wait(getMutationAlias('upsertAttributeValuesAndProductValues'))

            cy.get('@table').selectRow(0)
            cy.selectCell(2).should('not.contain', 'Noir')
            cy.selectCell(2).should('contain', 'Bois foncé')

            // now has error on "couleur détaillée"
            // = "Noir mat" is not linked to "Bois foncé"
            cy.get('@table').selectRow(1)
            cy.selectCell(2).find('[data-context=eav-product-value-error]').should('be.visible')

            // available values also changed to children of "Bois foncé"
            cy.get('@cell').find('[data-context="erp-multiselect"]').as('field').click().type('{downarrow}')
            cy.get('@cell')
                .find('[data-context="suggestion"]')
                .should('have.length', 51)
                .should('contain', 'Bois de Rose')
                .should('not.contain', 'Anthracite')
            cy.get('@field').type('{esc}')

            // ===
            // test deletion
            // ===
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'deleteProductValue')) {
                    aliasMutation(req, 'deleteProductValue')

                    req.reply({
                        fixture: 'graphql/eav/product/delete_eav_product_value__KEFQ350NR_25934.json',
                    })

                    expect(req.body.variables).to.deep.eq({
                        where: { attribute_value_id: { _in: [25934] }, sku: { _eq: 'KEFQ350NR' } },
                    })
                }
            })

            cy.get('@table').selectRow(0)
            cy.selectCell(2)
            cy.get('@cell').find('[data-context="remove"]').click()

            cy.wait(getMutationAlias('deleteProductValue'))

            cy.get('@table').selectRow(0)
            cy.selectCell(2).should('contain', 'Aucune valeur')

            // child field show some useful state
            // - error message
            // - no autocomplete to select value
            // - error on current value
            cy.get('@table').selectRow(1)
            cy.selectCell(2).should('contain', 'Il faut d’abord choisir une valeur pour SVD_Couleur')
            cy.get('@cell').find('[data-context=erp-multiselect]').should('not.exist')
            cy.get('@cell').find('[data-context=eav-product-value-error]').should('not.exist')
        })

        it('clone product values successfully', function () {
            navigateToEAVSection()

            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel] [data-context=erp-table]')
                .eq(0)
                .as('table')

            // Check values for current product
            // classic content
            cy.get('@table').selectRow(1)
            cy.selectCell(2).should('contain', 'Noir')

            // with prefix
            cy.get('@table').selectRow(9)
            cy.selectCell(2).should('contain', 'RMS PMPO 120 Watts')

            cy.intercept('GET', '**/api/erp/v1/common-content/KEFQ350NR', {
                fixture: 'erp/common_content___KEFQ350NR',
            }).as('fetch_common_content')

            cy.get('[data-context="open-eav-cloner-btn"]').click()
            cy.get('[data-context="page-header"]').should('contain', `Cloner les valeurs d'attributs`)

            // SKU from common contents
            cy.wait('@fetch_common_content')

            cy.get('[data-context=choices]').as('table')
            cy.get('@table').selectRow(0)
            cy.selectCell(0).get('input').should('not.be.checked')
            cy.selectCell(1).should('contain', 'KEFQ350BC')
            cy.selectCell(2).should('contain', 'KEF Q350 Blanc (la paire)')

            cy.intercept('POST', '**/api/erp/v1/articles**', {
                fixture: 'erp/article/cpost_articles_autocomplete__kef_q3',
            }).as('fetch_articles')

            // Search by autocomplete
            cy.get('[data-context=form] input').type('  kef q3 {enter}')

            cy.wait('@fetch_articles').then((xhr) => {
                expect(xhr.request.body.where).to.deep.eq({
                    _and: [
                        {
                            _or: [
                                {
                                    sku: {
                                        _like: '%kefq3%',
                                    },
                                },
                                {
                                    short_description: {
                                        _like: '%kef q3%',
                                    },
                                },
                            ],
                        },
                    ],
                    subcategory_id: {
                        _eq: 94,
                    },
                    sku: {
                        _nin: ['KEFQ350NR'],
                    },
                })
                expect(xhr.request.body.limit).to.eq(10)
                expect(xhr.request.body.order_by).to.eq('is_destock ASC, sku ASC')
            })

            cy.get('[data-context=choices]').as('table')
            cy.get('@table').selectRow(6)
            cy.selectCell(0).get('input').should('not.be.checked')
            cy.selectCell(1).should('contain', 'KEFQ350BC')
            cy.selectCell(2).should('contain', 'KEF Q350 Blanc (la paire)')

            cy.get('[data-context="clone-btn"]').should('not.exist')
            cy.get('[data-context="attributes-quick-preview"]').should('not.exist')

            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'fetchAttributesAndValuesForProduct')) {
                    aliasQuery(req, 'fetchAttributesAndValuesForProduct')

                    req.reply({
                        fixture: 'graphql/eav/product/fetch_product_values__KEFQ350BC.json',
                    })

                    expect(req.body.variables).to.deep.eq(getWhereProductAttributesVariables('KEFQ350BC', 94))
                }
            })

            cy.get('@table').selectCell(0).click().get('input').should('be.checked')

            cy.get('[data-context="clone-btn"]').should('be.visible')

            cy.get('[data-context="quick-preview"]').as('table')

            // Check values for declination
            // classic content
            cy.get('@table').selectRow(1)
            cy.selectCell(1).should('contain', 'Blanc')

            // with prefix
            cy.get('@table').selectRow(9)
            cy.selectCell(1).should('contain', '120 Watts')

            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'cloneProductValues')) {
                    aliasMutation(req, 'cloneProductValues')

                    req.reply({
                        fixture: 'graphql/eav/product/clone_product_values__KEFQ350NR.json',
                    })

                    expect(req.body.variables.new_product_values).to.deep.eq([
                        { attribute_value_id: 11539, sku: 'KEFQ350NR' },
                        { attribute_value_id: 11382, sku: 'KEFQ350NR' },
                        { attribute_value_id: 11380, sku: 'KEFQ350NR' },
                        { attribute_value_id: 15657, sku: 'KEFQ350NR' },
                        { attribute_value_id: 24129, sku: 'KEFQ350NR' },
                        { attribute_value_id: 18694, sku: 'KEFQ350NR' },
                        { attribute_value_id: 8714, sku: 'KEFQ350NR' },
                        { attribute_value_id: 11399, sku: 'KEFQ350NR' },
                        { attribute_value_id: 4634, sku: 'KEFQ350NR' },
                        { attribute_value_id: 11584, sku: 'KEFQ350NR' },
                        { attribute_value_id: 14178, sku: 'KEFQ350NR' },
                        { attribute_value_id: 11536, sku: 'KEFQ350NR' },
                        { attribute_value_id: 4746, sku: 'KEFQ350NR' },
                        { attribute_value_id: 11385, sku: 'KEFQ350NR' },
                        { attribute_value_id: 11384, sku: 'KEFQ350NR' },
                        { attribute_value_id: 4337, sku: 'KEFQ350NR' },
                        { attribute_value_id: 25937, sku: 'KEFQ350NR' },
                    ])

                    expect(req.body.variables.where_old_delete).to.deep.eq({ sku: { _eq: 'KEFQ350NR' } })

                    expect(req.body.variables.product_subcategory).to.deep.eq({ sku: 'KEFQ350NR', subcategory_id: 94 })
                }
            })

            cy.get('[data-context="clone-btn"]').click()
            cy.wait(getMutationAlias('cloneProductValues'))

            // Check updated values for current product
            cy.get('[data-context="slide-out-container"]').should('not.exist')

            cy.get('[data-context=article-v2-block-eav-manager] [data-context=erp-panel] [data-context=erp-table]')
                .eq(0)
                .as('table')

            // classic content
            cy.get('@table').selectRow(1)
            cy.selectCell(2).should('contain', 'Blanc')

            // with prefix
            cy.get('@table').selectRow(9)
            cy.selectCell(2).should('contain', '120 Watts')
        })
    })
})
