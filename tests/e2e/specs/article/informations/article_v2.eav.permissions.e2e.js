import {
    ATTRIBUTE_READ,
    ATTRIBUTE_VALUE_CREATE,
    ATTRIBUTE_VALUE_DELETE,
} from '../../../../../src/apps/erp/permissions.js'
import { getWhereProductAttributesVariables } from '../../../../../src/apps/erp/components/Article/Eav/where'
import { aliasQuery, getQueryAlias, GRAPHQL_ENDPOINT, hasOperationName } from '../../../utils/graphql-test-utils'
import { testPermissionInContext } from '../../../utils/permission-utils'

describe('Article v2 - Informations - EAV - Permissions', function () {
    const PAGE = '/articles/KEFQ350NR/general'

    const commonFixtures = () => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, 'fetchAttributesAndValuesForProduct')) {
                aliasQuery(req, 'fetchAttributesAndValuesForProduct')

                req.reply({
                    fixture: 'graphql/eav/product/fetch_product_values__KEFQ350NR',
                })
            }
        })
    }
    const navigateToEAVSection = () => {
        cy.visit(PAGE)
        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')

        cy.toggleMenu()

        cy.get('[data-context=erp-page-sidebar-menu-item]:contains(EAV)').tooltip('EAV').click()

        cy.wait(getQueryAlias('fetchAttributesAndValuesForProduct')).then((xhr) => {
            expect(xhr.request.body.operationName).to.eq('fetchAttributesAndValuesForProduct')
            expect(xhr.request.body.variables).to.deep.eq(getWhereProductAttributesVariables('KEFQ350NR', 94))
        })

        cy.closeAllToasts()
    }

    context('With write permissions', function () {
        beforeEach(function () {
            cy.authenticate()
            cy.mockErpUser([ATTRIBUTE_READ, ATTRIBUTE_VALUE_CREATE, ATTRIBUTE_VALUE_DELETE])

            commonFixtures()
        })

        testPermissionInContext(
            '[data-context=erp-account-permission-in-context-toolbar] button',
            navigateToEAVSection,
            {
                _or: [
                    {
                        permission_id: {
                            _ilike: 'ATTRIBUTE_%',
                        },
                    },
                    {
                        permission_id: {
                            _ilike: 'SUBCATEGORY_READ',
                        },
                    },
                ],
            },
        )
    })
})
