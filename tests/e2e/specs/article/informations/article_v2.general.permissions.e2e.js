import { testPermissionInContext } from '../../../utils/permission-utils'

describe('Article v2 - - General - Permissions', function () {
    beforeEach(() => {
        cy.mockErpUser()
        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')
    })

    const visitPage = () => {
        cy.visit('/articles/KEFQ350NR/general')

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()

        cy.get('[data-context=article-v2-general-information]').as('form').should('be.visible')
        cy.closeAllToasts()
    }

    testPermissionInContext('[data-context=erp-account-permission-in-context-toolbar] button', visitPage, {
        _and: [
            {
                permission_id: {
                    _ilike: 'ARTICLE_DESTOCK_WRITE',
                },
            },
        ],
    })
})
