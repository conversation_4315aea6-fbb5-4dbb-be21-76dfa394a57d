import { ARTICLE_MEDIA_ADMINISTRATE } from '../../../../../src/apps/erp/permissions.js'
import { aliasQuery, getQuery<PERSON>lias, GRAPHQL_ENDPOINT, hasOperationName } from '../../../utils/graphql-test-utils'

describe('Article v2 - Informations - Documents', function () {
    const PAGE = '/articles/KEFQ350NR/documents'
    const FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE = 'fetchCmsArticleToDeduceType'
    const FETCH_QUERY = 'fetchCmsArticleMedia'
    const CREATE_QUERY = 'createCmsArticleMedias'
    const DELETE_QUERY = 'deleteCmsArticleMedia'

    const commonFixtures = () => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                req.reply({
                    data: {
                        published: [
                            {
                                sku: 'KEFQ350NR',
                                article_id: 117735,
                                unbasketable_reason: 'UNAVAILABLE',
                                __typename: 'cms_article_article',
                            },
                        ],
                        draft: [],
                    },
                })
            }

            if (hasOperationName(req, FETCH_QUERY)) {
                aliasQuery(req, FETCH_QUERY)

                req.reply({
                    fixture: 'graphql/cms/article_media/fetchCmsArticleMedia__KEFQ350NR',
                })
            }
        })
    }

    const navigateToMediaSection = () => {
        cy.visit(PAGE, {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })

        // Initial calls on the page
        cy.wait(['@get_article_by_id_or_sku_v2', getQueryAlias(FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)])
        cy.toggleMenu()

        cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
            expect(xhr.request.body.operationName).to.eq(FETCH_QUERY)
            expect(xhr.request.body.variables).to.deep.eq({
                where: {
                    sku: { _eq: 'KEFQ350NR' },
                },
                where_media_i18n: {
                    supported_culture_id: { _eq: 'fr' },
                },
            })
        })

        cy.get('[data-context=article-v2-documents]').as('documents')
    }

    describe('Read only', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            commonFixtures()
        })

        it('should show a message if the article is not available in the CMS', function () {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                    aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                    req.reply({
                        data: {
                            published: [],
                            draft: [],
                        },
                    })
                }
            })

            cy.visit(PAGE)

            // Initial calls on the page - doesn't use the shared method as some request never happen
            // if the article does not exist in the CMS
            cy.wait('@get_article_by_id_or_sku_v2')
            cy.toggleMenu()

            cy.get('[data-context=article-v2-documents]').should(
                'contain',
                "Non disponible: Le produit n'existe pas dans le CMS",
            )
        })

        it('should show an empty message if no media are attached to the article yet', function () {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_QUERY)) {
                    aliasQuery(req, FETCH_QUERY)

                    req.reply({
                        body: {
                            data: {
                                cms_article_article: [
                                    {
                                        article_id: 117735,
                                        sku: 'KEFQ350NR',
                                        unbasketable_reason: 'UNAVAILABLE',
                                        medias: [],
                                        __typename: 'cms_article_article',
                                    },
                                ],
                            },
                        },
                    })
                }
            })

            navigateToMediaSection()

            cy.get('@documents').should('contain', "Aucun document n'est associé à cet article")
        })

        it('should show the documents attached to the article', function () {
            navigateToMediaSection()

            cy.get('[data-context=upload-btn]').should('be.disabled')

            // After documents have been loaded
            cy.get('@documents').find('[data-context=erp-stacked-list-item]').should('have.length', 3)

            // Check one item
            cy.get('@documents').find('[data-context=erp-stacked-list-item]').eq(1).as('item')

            // not editable, so grab handle for drag & drop reorder
            cy.get('@item').find('[data-context=grab]').should('not.exist')

            cy.get('@item').find('[data-context=erp-input]').should('be.disabled')
            cy.get('@item').find('[data-context=open-document]').should('not.be.disabled')
            cy.get('@item').find('[data-context=remove]').should('be.disabled')
        })
    })

    describe('With write permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_MEDIA_ADMINISTRATE])

            commonFixtures()
        })

        it('should allow edition of the document description', () => {
            navigateToMediaSection()
            cy.get('@documents').find('[data-context=erp-stacked-list-item]').eq(2).as('item')

            cy.get('@documents').find('[data-context=save]').should('contain', 'Sauvegarder').should('be.disabled')

            cy.get('@item').find('[data-context=erp-input]').should('have.value', 'Texte pas ouf')
            cy.get('@item').find('[data-context=erp-input]').clear()

            // open helpers
            cy.get('@item').find('[data-context=prefilled-choices]').should('not.exist')
            cy.get('@documents').find('[data-context=toggle-prefilled-choices] button').click()
            cy.get('@item').find('[data-context=prefilled-choices]').should('be.visible')

            // invalid description should not be saved
            cy.get('@documents').find('[data-context=save]').should('contain', 'Sauvegarder').should('be.disabled')
            // then
            cy.get('@item').find('[data-context=prefilled-choices] button:contains(Notice)').click()
            cy.get('@item').find('[data-context=erp-input]').should('have.value', 'Notice')
            cy.get('@documents')
                .find('[data-context=save]')
                .should('contain', 'Sauvegarder 1 document(s)')
                .should('not.be.disabled')

            // Do another one without clearing the input
            cy.get('@documents').find('[data-context=erp-stacked-list-item]').eq(1).as('item')
            cy.get('@item').find('[data-context=erp-input]').should('have.value', 'Indice de réparabilité')
            cy.get('@item').find('[data-context=prefilled-choices] button:contains(KEF Q350 Noir)').click()
            cy.get('@item')
                .find('[data-context=erp-input]')
                .should('have.value', 'Indice de réparabilité KEF Q350 Noir')
            cy.get('@documents')
                .find('[data-context=save]')
                .should('contain', 'Sauvegarder 2 document(s)')
                .should('not.be.disabled')
        })

        it('should have working actions buttons', () => {
            navigateToMediaSection()
            cy.get('@documents').find('[data-context=erp-stacked-list-item]').eq(2).as('item')

            // preview document in a new tab
            cy.get('@item').find('[data-context=open-document]').click()
            cy.get('@windowOpen').should(
                'be.calledWithMatch',
                /.*\/images\/documents\/article\/KEF\/KEFQ350NR\/document-65ef40bfca9ed5.08163714.pdf/,
                '_blank',
            )

            // delete a document
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, DELETE_QUERY)) {
                    aliasQuery(req, DELETE_QUERY)

                    req.reply({
                        data: {
                            delete_cms_article_article_media: {
                                affected_rows: 1,
                                __typename: 'cms_article_media',
                            },
                        },
                    })
                }
            })

            cy.intercept('DELETE', '**/api/erp/v1/article/media/images/documents/article/KEF/KEFQ350NR/**', {
                statusCode: 204,
                body: true,
            }).as('delete_request')

            cy.get('@item').find('[data-context=remove]').click()

            cy.wait(['@delete_request'])

            // check what's sent to graphql
            cy.wait(getQueryAlias(DELETE_QUERY)).then((xhr) => {
                expect(xhr.request.body.variables).to.deep.eq({
                    where: {
                        media_id: {
                            _in: ['fad1444d-9acb-4fd7-befe-7663ada0ec94'],
                        },
                    },
                })
            })

            // re-fetch + don't check for further states as everything is mocked
            cy.wait(getQueryAlias(FETCH_QUERY))
        })

        context('Document upload', () => {
            it('should upload the documents and persist them in the CMS successfully', () => {
                // empty message
                navigateToMediaSection()

                cy.get('[data-context=upload-btn]').should('not.be.disabled').click()
                cy.get('[data-context=article-v2-media-uploader]').should('be.visible')

                cy.get('[data-context=article-v2-media-uploader]').should(
                    'contain',
                    'Ajoutez un ou plusieurs documents',
                )
                cy.get('[data-context=article-v2-media-uploader]').should(
                    'contain',
                    'Seuls les fichiers de type PDF sont acceptés',
                )

                cy.get('[data-context=file-to-upload]').should('not.exist')
                cy.get('[data-context=rejected-files]').should('not.exist')

                cy.get('[data-context=article-v2-media-uploader] input[type=file]').selectFile(
                    [
                        'tests/e2e/fixtures/files/doc.pdf',
                        'tests/e2e/fixtures/files/image-3.jpg',
                        'tests/e2e/fixtures/files/spectral-product-portfolio-2018-de-en.pdf',
                    ],
                    { force: true },
                )

                cy.get('[data-context=rejected-files]').should('be.visible')
                cy.get('[data-context=rejected-files] li').should('have.length', 2)

                // ignore files that are not PDFs
                cy.get('[data-context=rejected-files]').should('contain', `Le fichier n'est pas un PDF`)
                // ignore files that exceeds the size limit
                cy.get('[data-context=rejected-files]').should(
                    'contain',
                    `La taille du fichier (45.50 MB) excède le poids maximum de 25.00 MB autorisé`,
                )

                // upload remaining file
                cy.intercept('POST', '**/api/erp/v1/article/KEFQ350NR/media/upload', {
                    statusCode: 200,
                    body: {
                        data: {
                            file: {
                                url: '/images/images/documents/article/KEF/KEFQ350NR/doc.pdf',
                            },
                        },
                    },
                }).as('upload_document')

                cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                    if (hasOperationName(req, CREATE_QUERY)) {
                        aliasQuery(req, CREATE_QUERY)

                        req.reply({
                            fixture: 'graphql/cms/article_media/createCmsArticleMedias__KEFQ350NR',
                        })
                    }
                })

                cy.get('[data-context=file-to-upload]').should('have.length', 1)
                cy.get('[data-context=article-v2-media-uploader] [data-context=upload-btn]').click()

                // should
                // - upload
                // - create the document via graphql
                // - re-fetch data afterward
                // no need to check the display again as all calls are mocked, and, we don't want to test mocks
                cy.wait(['@upload_document', getQueryAlias(CREATE_QUERY), getQueryAlias(FETCH_QUERY)])
            })
        })
    })
})
