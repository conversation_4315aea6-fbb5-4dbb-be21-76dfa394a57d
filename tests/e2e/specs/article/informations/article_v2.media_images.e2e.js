import { ARTICLE_DESTOCK_WRITE, ARTICLE_MEDIA_ADMINISTRATE } from '../../../../../src/apps/erp/permissions.js'
import { aliasQuery, getQuery<PERSON>lias, GRAPHQL_ENDPOINT, hasOperationName } from '../../../utils/graphql-test-utils'

describe('Article v2 - Informations - Visuals', function () {
    const PAGE = '/articles/KEFQ350NR/images'
    const PAGE_DESTOCK = '/articles/DESTOCK-20191120013'
    const FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE = 'fetchCmsArticleToDeduceType'
    const FETCH_QUERY = 'fetchCmsArticleMedia'
    const CREATE_QUERY = 'createCmsArticleMedias'
    const UPSERT_QUERY = 'upsertCmsArticleMediaI18n'
    const DELETE_QUERY = 'deleteCmsArticleMedia'

    const commonFixtures = (page = PAGE) => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013',
        }).as('get_article_destock_by_id_or_sku_v2')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)
                if (page === PAGE) {
                    req.reply({
                        data: {
                            published: [
                                {
                                    sku: 'KEFQ350NR',
                                    article_id: 117735,
                                    unbasketable_reason: 'UNAVAILABLE',
                                    __typename: 'cms_article_article',
                                },
                            ],
                            draft: [],
                        },
                    })
                }
                if (page === PAGE_DESTOCK) {
                    req.reply({
                        data: {
                            published: [
                                {
                                    sku: 'DESTOCK-20191120013',
                                    article_id: 143214,
                                    unbasketable_reason: null,
                                    __typename: 'cms_article_article',
                                },
                            ],
                            draft: [],
                        },
                    })
                }
            }

            if (hasOperationName(req, FETCH_QUERY)) {
                aliasQuery(req, FETCH_QUERY)

                req.reply({
                    fixture: 'graphql/cms/article_media/fetchCmsArticleMedia__KEFQ350NR',
                })
            }
        })
    }

    const fetchWithAnInactiveItem = () => {
        cy.fixture('graphql/cms/article_media/fetchCmsArticleMedia__KEFQ350NR').then((payload) => {
            // new uploaded media don't have a display_order
            payload.data.cms_article_article[0].medias.push({
                media_id: 'be86767f-c91e-4338-ae18-e6d22f2eea6f',
                media_variation: {
                    image: {
                        largest: '180',
                        referential: {
                            95: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_P_95.jpg',
                            140: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_P_140.jpg',
                            180: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_P_180.jpg',
                        },
                    },
                },
                i18n: [
                    {
                        __typename: 'cms_article_article_media_i18n',
                        media_id: 'be86767f-c91e-4338-ae18-e6d22f2eea6f',
                        supported_culture_id: 'fr',
                        display_order: null,
                        meta: {
                            view: null,
                        },
                        type: 'IMAGE',
                    },
                ],
                __typename: 'cms_article_article_media',
            })

            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_QUERY)) {
                    aliasQuery(req, FETCH_QUERY)

                    req.reply({
                        data: payload.data,
                    })
                }
            })
        })
    }

    const navigateToMediaSection = () => {
        cy.visit(PAGE)
        // Initial calls on the page
        cy.wait(['@get_article_by_id_or_sku_v2', getQueryAlias(FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)])
        cy.toggleMenu()

        cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
            expect(xhr.request.body.operationName).to.eq(FETCH_QUERY)
            expect(xhr.request.body.variables).to.deep.eq({
                where: {
                    sku: { _eq: 'KEFQ350NR' },
                },
                where_media_i18n: {
                    supported_culture_id: { _eq: 'fr' },
                },
            })
        })

        cy.get('[data-context=article-v2-images] [data-context=article-v2-images-active]').as('active-images')
    }

    const navigateToMediaSectionDestock = () => {
        cy.visit(PAGE_DESTOCK + '/images')
        // Initial calls on the page
        cy.wait(['@get_article_destock_by_id_or_sku_v2', getQueryAlias(FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)])
        cy.toggleMenu()

        cy.get('[data-context=article-v2-images] [data-context=article-v2-images-active]').as('active-images')
    }

    describe('Read only', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            commonFixtures()
        })

        it('should show a message if the article is not available in the CMS', function () {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                    aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                    req.reply({
                        data: {
                            published: [],
                            draft: [],
                        },
                    })
                }
            })

            cy.visit(PAGE)

            // Initial calls on the page - doesn't use the shared method as some request never happen
            // if the article does not exist in the CMS
            cy.wait('@get_article_by_id_or_sku_v2')
            cy.toggleMenu()

            cy.get('[data-context=article-v2-images]').should(
                'contain',
                "Non disponible: Le produit n'existe pas dans le CMS",
            )
        })

        it('should show an empty message if no media are attached to the article yet', function () {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_QUERY)) {
                    aliasQuery(req, FETCH_QUERY)

                    req.reply({
                        body: {
                            data: {
                                cms_article_article: [
                                    {
                                        article_id: 117735,
                                        sku: 'KEFQ350NR',
                                        medias: [],
                                        __typename: 'cms_article_article',
                                    },
                                ],
                            },
                        },
                    })
                }
            })

            navigateToMediaSection()

            cy.get('@active-images').should('contain', "Aucun visuel n'est associé à cet article")
        })

        it('should show the media attached to the article', function () {
            navigateToMediaSection()

            cy.get('[data-context=upload-btn]').should('be.disabled')

            // After images have been loaded
            cy.get('@active-images').find('[data-context=erp-stacked-list-item]').should('have.length', 6)

            // Check one item
            cy.get('@active-images').find('[data-context=erp-stacked-list-item]').eq(1).as('item')

            // not editable, so no checkbox for bulk deletion nor grab handle for drag & drop reorder
            cy.get('@item').find('[data-context=select-input]').should('not.exist')
            cy.get('@item').find('[data-context=grab]').should('not.exist')

            cy.get('@item')
                .find('[data-context=thumbnail] img')
                .should('be.visible')
                .should('have.attr', 'src')
                .and(
                    'match',
                    /https?:\/\/.*\/images\/dynamic\/Enceintes\/articles\/KEF\/KEFQ350NR\/KEF-Q350-Noir_Vd1_1200.jpg/,
                )

            cy.get('@item').find('[data-context=view-selector]').should('contain', 'Q350 Noir Vue de détail 1')
            cy.get('@item').find('[data-context=view-selector] input').should('be.disabled')
            cy.get('@item').find('[data-context=normal-variations] [data-context=erp-button]').should('have.length', 12)

            // squared variations if they do not exist
            cy.get('@item').find('[data-context=squared-variations] [data-context=erp-button]').should('not.exist')

            // Fist item have the squared variations
            cy.get('@active-images')
                .find('[data-context=erp-stacked-list-item]')
                .eq(0)
                .find('[data-context=squared-variations] [data-context=erp-button]')
                .should('have.length', 1)
        })

        it('should show a bigger preview on an image media', function () {
            navigateToMediaSection()

            // After images have been loaded
            cy.get('@active-images').find('[data-context=erp-stacked-list-item]').should('have.length', 6)

            cy.get('@active-images').find('[data-context=erp-stacked-list-item]').eq(0).as('item')
            cy.get('@item').find('[data-context=thumbnail]').should('have.class', 'w-28')
            cy.get('[data-context=larger-thumbnail]').click()
            cy.get('@item').find('[data-context=thumbnail]').should('have.class', 'w-96')
        })

        it('should copy an image variation link to clipboard', function () {
            navigateToMediaSection()

            // After images have been loaded
            cy.get('@active-images').find('[data-context=erp-stacked-list-item]').should('have.length', 6)
            cy.get('@active-images').find('[data-context=erp-stacked-list-item]').eq(0).as('item')

            cy.window().then((win) => {
                cy.stub(win, 'prompt') // for copy-to-clipboard which opens a prompt when running cypress...
            })

            cy.get('@item').find('[data-context=normal-variations] [data-context=erp-button]').eq(2).click()
            cy.toast(`Le lien de l'image à été copié dans le presse-papier`, 'default')
        })

        it('should show the inactive images (just uploaded)', function () {
            fetchWithAnInactiveItem()
            navigateToMediaSection()

            cy.get('@active-images').find('[data-context=erp-stacked-list-item]').should('have.length', 6)

            cy.get('[data-context=article-v2-images] [data-context=article-v2-images-inactive]').as('inactive-images')
            cy.get('@inactive-images').find('[data-context=erp-stacked-list-item]').should('have.length', 1)
            cy.get('@inactive-images').should('contain', 'Visuel(s) inactif(s)')
            cy.get('@inactive-images')
                .find('[data-context=activate-btn]')
                .should('contain', 'Activer')
                .should('be.disabled')

            cy.get('@inactive-images').find('[data-context=erp-stacked-list-item]').eq(0).as('item')

            cy.get('@item').find('[data-context=view-selector] input').should('be.disabled')
            cy.get('@item').find('[data-context=view-selector]').should('contain', '...')
            cy.get('@item').find('[data-context=normal-variations] [data-context=erp-button]').should('have.length', 3)
        })
    })

    describe('With write permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_MEDIA_ADMINISTRATE])

            commonFixtures()
        })

        describe('Active item change view', function () {
            it('should save the modified items in a bulk query', function () {
                cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                    if (hasOperationName(req, UPSERT_QUERY)) {
                        aliasQuery(req, UPSERT_QUERY)

                        req.reply({
                            data: {
                                insert_cms_article_article_media_i18n: {
                                    affected_rows: 2,
                                    __typename: 'cms_article_media_i18n',
                                },
                            },
                        })
                    }
                })

                navigateToMediaSection()

                cy.get('@active-images').get('[data-context=save-btn]').should('be.disabled')
                cy.get('@active-images').find('[data-context=erp-stacked-list-item]').eq(1).as('item')

                // Select a view
                cy.get('@item').find('[data-context=view-selector]').should('contain', 'Vue de détail').click()
                cy.get('@item')
                    .find('[data-context=view-selector] [data-context=suggestion]:contains(Application)')
                    .click()

                // check save button
                cy.get('@active-images')
                    .get('[data-context=save-btn]')
                    .should('not.be.disabled')
                    .should('contain', 'Sauvegarder 1 visuel(s)')

                // Select a view for another item
                cy.get('@active-images').find('[data-context=erp-stacked-list-item]').eq(2).as('item')

                cy.get('@item').find('[data-context=view-selector]').should('contain', 'Vue 3/4').click()
                cy.get('@item')
                    .find('[data-context=view-selector] [data-context=suggestion]:contains(Mise en situation)')
                    .click()

                // save
                cy.get('@active-images')
                    .get('[data-context=save-btn]')
                    .should('not.be.disabled')
                    .should('contain', 'Sauvegarder 2 visuel(s)')
                    .click()

                // check what's sent to graphql
                cy.wait(getQueryAlias(UPSERT_QUERY)).then((xhr) => {
                    expect(xhr.request.body.variables).to.deep.eq({
                        media_i18n: [
                            {
                                article_id: 117735,
                                media_id: 'da563780-beaa-462e-968d-854b573fae72',
                                supported_culture_id: 'fr',
                                meta: {
                                    view: 'Application',
                                },
                                display_order: 2,
                                type: 'IMAGE',
                            },
                            {
                                article_id: 117735,
                                media_id: '20eed0df-4794-4f0f-895d-3d30172f69e9',
                                supported_culture_id: 'fr',
                                meta: {
                                    view: 'Mise en situation',
                                },
                                display_order: 3,
                                type: 'IMAGE',
                            },
                        ],
                    })
                })

                // re-fetch + don't check for further states as everything is mocked
                cy.wait(getQueryAlias(FETCH_QUERY))
            })
        })

        describe('Active item reorder', function () {
            it('should update the reordered items', function () {
                cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                    if (hasOperationName(req, UPSERT_QUERY)) {
                        aliasQuery(req, UPSERT_QUERY)

                        req.reply({
                            data: {
                                insert_cms_article_article_media_i18n: {
                                    affected_rows: 4,
                                    __typename: 'cms_article_media_i18n',
                                },
                            },
                        })
                    }
                })

                navigateToMediaSection()
                cy.get('@active-images').get('[data-context=save-btn]').should('be.disabled')

                cy.get('[data-context=erp-stacked-list-item]:nth-child(2)').drag(
                    '[data-context=erp-stacked-list-item]:nth-child(5)',
                )

                // save
                cy.get('@active-images')
                    .get('[data-context=save-btn]')
                    .should('not.be.disabled')
                    .should('contain', 'Sauvegarder 4 visuel(s)')
                    .click()

                // check what's sent to graphql
                cy.wait(getQueryAlias(UPSERT_QUERY)).then((xhr) => {
                    expect(xhr.request.body.variables).to.deep.eq({
                        media_i18n: [
                            {
                                article_id: 117735,
                                media_id: '20eed0df-4794-4f0f-895d-3d30172f69e9',
                                supported_culture_id: 'fr',
                                meta: {
                                    view: 'Q350 Noir Vue 3/4 droite',
                                },
                                display_order: 2,
                                type: 'IMAGE',
                            },
                            {
                                article_id: 117735,
                                media_id: '8fbcf91e-f2a6-4758-8ab8-7c2daa360157',
                                supported_culture_id: 'fr',
                                meta: {
                                    view: 'Q350 Noir Vue technologie 1',
                                },
                                display_order: 3,
                                type: 'IMAGE',
                            },
                            {
                                article_id: 117735,
                                media_id: '6ef76fad-b84c-4e82-9974-00bbcb734d20',
                                supported_culture_id: 'fr',
                                meta: {
                                    view: 'Q350 Noir Vue technologie 2',
                                },
                                display_order: 4,
                                type: 'IMAGE',
                            },
                            {
                                article_id: 117735,
                                media_id: 'da563780-beaa-462e-968d-854b573fae72',
                                supported_culture_id: 'fr',
                                meta: {
                                    view: 'Q350 Noir Vue de détail 1',
                                },
                                display_order: 5,
                                type: 'IMAGE',
                            },
                        ],
                    })
                })

                // re-fetch + don't check for further states as everything is mocked
                cy.wait(getQueryAlias(FETCH_QUERY))
            })
        })

        describe('Image deletion', function () {
            it('should delete all the selected items', function () {
                cy.fixture('graphql/cms/article_media/fetchCmsArticleMedia__KEFQ350NR').then((payload) => {
                    // All supported image formats
                    payload.data.cms_article_article[0].medias[1].media_variation.image.referential = {
                        95: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_3QD_95.jpg',
                        140: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_3QD_140.JPG',
                        180: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_3QD_180.png',
                        225: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_3QD_225.PNG',
                        666: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_3QD_225.jpg?p=something',
                        1200: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_3QD_1200.jpg',
                    }

                    cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                        if (hasOperationName(req, FETCH_QUERY)) {
                            aliasQuery(req, FETCH_QUERY)

                            req.reply({
                                data: payload.data,
                            })
                        }
                    })
                })

                cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                    if (hasOperationName(req, DELETE_QUERY)) {
                        aliasQuery(req, DELETE_QUERY)

                        req.reply({
                            data: {
                                delete_cms_article_article_media: {
                                    affected_rows: 2,
                                    __typename: 'cms_article_media',
                                },
                            },
                        })
                    }
                })

                cy.intercept(
                    'DELETE',
                    '**/api/erp/v1/article/media/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/**',
                    {
                        statusCode: 204,
                        body: true,
                    },
                ).as('delete_request')

                navigateToMediaSection()

                // select an item for deletion
                cy.get('@active-images').get('[data-context=article-v2-media-actions').eq(0).as('more-actions')
                cy.get('@more-actions').should('be.disabled')

                cy.get('@active-images')
                    .find('[data-context=erp-stacked-list-item]')
                    .eq(2)
                    .find('[data-context=select-input]')
                    .click()

                // Check button
                cy.get('@more-actions').should('not.be.disabled').click()
                cy.get('[data-context=dropdown-menu-item]').eq(1).should('contain', 'Supprimer 1 visuel(s)')

                // select another item for deletion
                cy.get('@active-images')
                    .find('[data-context=erp-stacked-list-item]')
                    .eq(4)
                    .find('[data-context=select-input]')
                    .click()

                // Check button & delete
                cy.get('@more-actions').should('not.be.disabled').click()
                cy.get('[data-context=dropdown-menu-item]').eq(1).should('contain', 'Supprimer 2 visuel(s)').click()

                // Confirm prompt
                cy.get('[data-context=slide-out-container]').should(
                    'contain',
                    'Confirmez vous la suppression de 2 visuel(s) ?',
                )
                cy.get('[data-context=confirm-deletion-btn]').click()

                // delete the all images variations for both items
                cy.wait([...Array(18)].map((_) => '@delete_request'))

                // check what's sent to graphql
                cy.wait(getQueryAlias(DELETE_QUERY)).then((xhr) => {
                    expect(xhr.request.body.variables).to.deep.eq({
                        where: {
                            media_id: {
                                _in: ['20eed0df-4794-4f0f-895d-3d30172f69e9', '6ef76fad-b84c-4e82-9974-00bbcb734d20'],
                            },
                        },
                    })
                })

                // re-fetch + don't check for further states as everything is mocked
                cy.wait(getQueryAlias(FETCH_QUERY))
            })
        })

        describe('Image bulk rename', function () {
            it('should rename all the selected items', function () {
                cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                    if (hasOperationName(req, UPSERT_QUERY)) {
                        aliasQuery(req, UPSERT_QUERY)

                        req.reply({
                            data: {
                                insert_cms_article_article_media_i18n: {
                                    affected_rows: 2,
                                    __typename: 'cms_article_media_i18n',
                                },
                            },
                        })
                    }
                })

                navigateToMediaSection()

                // select an item for renaming
                cy.get('@active-images').get('[data-context=article-v2-media-actions').eq(0).as('more-actions')
                cy.get('@more-actions').should('be.disabled')

                cy.get('@active-images')
                    .find('[data-context=erp-stacked-list-item]')
                    .eq(2)
                    .find('[data-context=select-input]')
                    .click()

                // Check button
                cy.get('@more-actions').should('not.be.disabled').click()
                cy.get('[data-context=dropdown-menu-item]').eq(0).should('contain', 'Renommer 1 visuel(s)')

                // select another item for renaming
                cy.get('@active-images')
                    .find('[data-context=erp-stacked-list-item]')
                    .eq(4)
                    .find('[data-context=select-input]')
                    .click()

                // Check button & rename
                cy.get('@more-actions').should('not.be.disabled').click()
                cy.get('[data-context=dropdown-menu-item]').eq(0).should('contain', 'Renommer 2 visuel(s)').click()

                // Confirm prompt
                cy.get('[data-context=slide-out-container] [data-context=view-selector]').click()
                cy.get(
                    '[data-context=slide-out-container] [data-context=view-selector] [data-context=suggestion]:contains(Connectique)',
                ).click()
                cy.get('[data-context=confirm-bulk-rename-btn]').click()

                // check what's sent to graphql
                cy.wait(getQueryAlias(UPSERT_QUERY)).then((xhr) => {
                    expect(xhr.request.body.variables).to.deep.eq({
                        media_i18n: [
                            {
                                article_id: 117735,
                                media_id: '20eed0df-4794-4f0f-895d-3d30172f69e9',
                                supported_culture_id: 'fr',
                                meta: {
                                    view: 'Connectique',
                                },
                                display_order: 3,
                                type: 'IMAGE',
                            },
                            {
                                article_id: 117735,
                                media_id: '6ef76fad-b84c-4e82-9974-00bbcb734d20',
                                supported_culture_id: 'fr',
                                meta: {
                                    view: 'Connectique',
                                },
                                display_order: 5,
                                type: 'IMAGE',
                            },
                        ],
                    })
                })

                // re-fetch + don't check for further states as everything is mocked
                cy.wait(getQueryAlias(FETCH_QUERY))
            })
        })

        describe('Image upload', function () {
            it('should show an empty message', function () {
                navigateToMediaSection()

                cy.get('[data-context=upload-btn]').should('not.be.disabled').click()
                cy.get('[data-context=article-v2-media-uploader]').should('be.visible')

                cy.get('[data-context=article-v2-media-uploader]').should('contain', 'Ajoutez un ou plusieurs visuels')
                cy.get('[data-context=article-v2-media-uploader]').should(
                    'contain',
                    'Seules les images de type JPG sont acceptées',
                )
            })

            it('should ignore files that are not JPGs', function () {
                navigateToMediaSection()

                cy.get('[data-context=upload-btn]').should('not.be.disabled').click()
                cy.get('[data-context=article-v2-media-uploader]').should('be.visible')

                cy.get('[data-context=file-to-upload]').should('not.exist')
                cy.get('[data-context=rejected-files]').should('not.exist')

                cy.get('[data-context=article-v2-media-uploader] input[type=file]').selectFile(
                    [
                        'tests/e2e/fixtures/files/image-1.jpg',
                        'tests/e2e/fixtures/files/image-2.png',
                        'tests/e2e/fixtures/files/image-3.jpg',
                    ],
                    { force: true },
                )

                cy.get('[data-context=rejected-files]').should('be.visible')
                cy.get('[data-context=rejected-files] li').should('have.length', 1)
                cy.get('[data-context=rejected-files]').should('contain', `Le fichier n'est pas un JPG`)

                cy.get('[data-context=rejected-files]').find('[data-context=alert-dismiss-btn]').click()
                cy.get('[data-context=rejected-files]').should('not.exist')

                cy.get('[data-context=file-to-upload]').should('have.length', 2)
                cy.get('[data-context=file-to-upload]').eq(0).should('contain', 'image-1.jpg')
                cy.get('[data-context=file-to-upload]').eq(1).should('contain', 'image-3.jpg')
            })

            it('should ignore files that exceeds the size limit', function () {
                navigateToMediaSection()

                cy.get('[data-context=upload-btn]').should('not.be.disabled').click()
                cy.get('[data-context=article-v2-media-uploader]').should('be.visible')

                cy.get('[data-context=file-to-upload]').should('not.exist')
                cy.get('[data-context=rejected-files]').should('not.exist')

                cy.get('[data-context=article-v2-media-uploader] input[type=file]').selectFile(
                    ['tests/e2e/fixtures/files/image-3.jpg', 'tests/e2e/fixtures/files/image-4.jpg'],
                    { force: true },
                )

                cy.get('[data-context=rejected-files]').should('be.visible')
                cy.get('[data-context=rejected-files] li').should('have.length', 1)
                cy.get('[data-context=rejected-files]').should(
                    'contain',
                    'La taille du fichier (3.90 MB) excède le poids maximum de 2.00 MB autorisé',
                )

                cy.get('[data-context=rejected-files]').find('[data-context=alert-dismiss-btn]').click()
                cy.get('[data-context=rejected-files]').should('not.exist')

                cy.get('[data-context=file-to-upload]').should('have.length', 1)
                cy.get('[data-context=file-to-upload]').eq(0).should('contain', 'image-3.jpg')
            })

            it('should allow to remove an image from the pool to upload', function () {
                navigateToMediaSection()

                cy.get('[data-context=upload-btn]').should('not.be.disabled').click()
                cy.get('[data-context=article-v2-media-uploader]').should('be.visible')

                cy.get('[data-context=file-to-upload]').should('not.exist')

                cy.get('[data-context=article-v2-media-uploader] input[type=file]').selectFile(
                    ['tests/e2e/fixtures/files/image-1.jpg', 'tests/e2e/fixtures/files/image-3.jpg'],
                    { force: true },
                )

                cy.get('[data-context=file-to-upload]').should('have.length', 2)
                cy.get('[data-context=file-to-upload]').eq(0).find('[data-context=remove-btn]').click()

                cy.get('[data-context=file-to-upload]').should('have.length', 1)
                cy.get('[data-context=file-to-upload]').eq(0).should('contain', 'image-3.jpg')
            })

            it('should allow to add another image', function () {
                navigateToMediaSection()

                cy.get('[data-context=upload-btn]').should('not.be.disabled').click()
                cy.get('[data-context=article-v2-media-uploader]').should('be.visible')

                cy.get('[data-context=file-to-upload]').should('not.exist')

                cy.get('[data-context=article-v2-media-uploader] input[type=file]').selectFile(
                    ['tests/e2e/fixtures/files/image-3.jpg'],
                    { force: true },
                )

                cy.get('[data-context=file-to-upload]').should('have.length', 1)

                cy.get('[data-context=article-v2-media-uploader] input[type=file]').selectFile(
                    ['tests/e2e/fixtures/files/image-1.jpg'],
                    { force: true },
                )

                cy.get('[data-context=file-to-upload]').should('have.length', 2)
                cy.get('[data-context=file-to-upload]').eq(0).should('contain', 'image-3.jpg')
                cy.get('[data-context=file-to-upload]').eq(1).should('contain', 'image-1.jpg')
            })

            it('should upload the images and persist them in the CMS successfully', function () {
                cy.intercept('POST', '**/api/erp/v1/article/KEFQ350NR/media/upload', {
                    statusCode: 200,
                    body: {
                        data: {
                            file: {
                                url: '/images/dynamic/Enceintes/articles/KEF/KEFQ350NR/KEF-Q350-Noir_P_500.jpg',
                                width: 1200,
                                height: 1024,
                            },
                        },
                    },
                }).as('upload_image')

                cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                    if (hasOperationName(req, CREATE_QUERY)) {
                        aliasQuery(req, CREATE_QUERY)

                        req.reply({
                            fixture: 'graphql/cms/article_media/createCmsArticleMedias__KEFQ350NR',
                        })
                    }
                })

                navigateToMediaSection()

                cy.get('[data-context=upload-btn]').should('not.be.disabled').click()
                cy.get('[data-context=article-v2-media-uploader]').should('be.visible')

                cy.get('[data-context=article-v2-media-uploader] input[type=file]').selectFile(
                    ['tests/e2e/fixtures/files/image-1.jpg', 'tests/e2e/fixtures/files/image-3.jpg'],
                    { force: true },
                )

                cy.get('[data-context=article-v2-media-uploader] [data-context=upload-btn]').click()

                // should make
                // - two separate uploads
                // - one call to create the images in bulk via graphql
                // - one call to re-fetch data afterward
                // no need to check the display again as all calls are mocked, and, we don't want to test mocks
                cy.wait(['@upload_image', '@upload_image', getQueryAlias(CREATE_QUERY), getQueryAlias(FETCH_QUERY)])
            })
        })

        describe('New image', function () {
            it('should activate a new media', function () {
                cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                    if (hasOperationName(req, UPSERT_QUERY)) {
                        aliasQuery(req, UPSERT_QUERY)

                        req.reply({
                            data: {
                                insert_cms_article_article_media_i18n: {
                                    affected_rows: 1,
                                    __typename: 'cms_article_media_i18n',
                                },
                            },
                        })
                    }
                })

                fetchWithAnInactiveItem()
                navigateToMediaSection()

                cy.get('@active-images').find('[data-context=erp-stacked-list-item]').should('have.length', 6)

                cy.get('[data-context=article-v2-images] [data-context=article-v2-images-inactive]').as(
                    'inactive-images',
                )
                cy.get('@inactive-images').find('[data-context=erp-stacked-list-item]').should('have.length', 1)
                cy.get('@inactive-images')
                    .find('[data-context=activate-btn]')
                    .should('contain', 'Activer')
                    .should('be.disabled')

                cy.get('@inactive-images').find('[data-context=erp-stacked-list-item]').eq(0).as('item')

                // set the view
                cy.get('@item').find('[data-context=view-selector]').should('contain', '...').click()
                cy.get('@item')
                    .find('[data-context=view-selector] [data-context=suggestion]:contains(Application)')
                    .click()

                // activate
                cy.get('@inactive-images')
                    .find('[data-context=activate-btn]')
                    .should('not.be.disabled')
                    .should('contain', 'Activer 1 visuel(s)')
                    .click()

                // check what's sent to graphql
                cy.wait(getQueryAlias(UPSERT_QUERY)).then((xhr) => {
                    expect(xhr.request.body.variables).to.deep.eq({
                        media_i18n: [
                            {
                                article_id: 117735,
                                media_id: 'be86767f-c91e-4338-ae18-e6d22f2eea6f',
                                supported_culture_id: 'fr',
                                meta: {
                                    view: 'Application',
                                },
                                display_order: 7,
                                type: 'IMAGE',
                            },
                        ],
                    })
                })

                // re-fetch + don't check for further states as everything is mocked
                cy.wait(getQueryAlias(FETCH_QUERY))
            })
        })
    })
    describe('With write permission destock', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_DESTOCK_WRITE])

            commonFixtures(PAGE_DESTOCK)
        })

        it('allow to add another image', function () {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, UPSERT_QUERY)) {
                    aliasQuery(req, UPSERT_QUERY)

                    req.reply({
                        data: {
                            insert_cms_article_article_media_i18n: {
                                affected_rows: 2,
                                __typename: 'cms_article_media_i18n',
                            },
                        },
                    })
                }
            })

            navigateToMediaSectionDestock()

            cy.get('@active-images').get('[data-context=save-btn]').should('be.disabled')
            cy.get('@active-images').find('[data-context=erp-stacked-list-item]').eq(1).as('item')

            // Select a view
            cy.get('@item').find('[data-context=view-selector]').should('contain', 'Vue de détail').click()
            cy.get('@item').find('[data-context=view-selector] [data-context=suggestion]:contains(Application)').click()

            // check save button
            cy.get('@active-images')
                .get('[data-context=save-btn]')
                .should('not.be.disabled')
                .should('contain', 'Sauvegarder 1 visuel(s)')

            cy.get('[data-context=upload-btn]').should('not.be.disabled').click()
            cy.get('[data-context=article-v2-media-uploader]').should('be.visible')

            cy.get('[data-context=file-to-upload]').should('not.exist')

            cy.get('[data-context=article-v2-media-uploader] input[type=file]').selectFile(
                ['tests/e2e/fixtures/files/image-3.jpg'],
                { force: true },
            )

            cy.get('[data-context=file-to-upload]').should('have.length', 1)

            cy.get('[data-context=article-v2-media-uploader] input[type=file]').selectFile(
                ['tests/e2e/fixtures/files/image-1.jpg'],
                { force: true },
            )

            cy.get('[data-context=file-to-upload]').should('have.length', 2)
            cy.get('[data-context=file-to-upload]').eq(0).should('contain', 'image-3.jpg')
            cy.get('[data-context=file-to-upload]').eq(1).should('contain', 'image-1.jpg')
        })
    })
})
