import { ARTICLE_DESTOCK_WRITE } from '../../../../../src/apps/erp/permissions.js'

describe('Article - General - destock condition', function () {
    context('Without permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()
        })
        context('with an article', () => {
            beforeEach(() => {
                cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
                    fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
                }).as('get_article_by_id_or_sku_v2')

                cy.visit('/articles/KEFQ350NR/general')

                // Initial calls on the page
                cy.wait('@get_article_by_id_or_sku_v2')
                cy.toggleMenu()

                cy.get('[data-context=article-v2-destock-information]').as('form').should('be.visible')
                cy.closeAllToasts()
            })

            it('should not display destock condition', function () {
                cy.get('[data-context=article-v2-destock-information]').should('not.exist')
            })
        })

        context('with an destock', () => {
            beforeEach(() => {
                cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                    fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013',
                }).as('get_article_by_id_or_sku_v2')

                cy.visit('/articles/DESTOCK-20191120013/general')

                // Initial calls on the page
                cy.wait('@get_article_by_id_or_sku_v2')
                cy.toggleMenu()

                cy.get('[data-context=article-v2-destock-information]').as('form').should('be.visible')
                cy.closeAllToasts()
            })

            it('should display destock condition', function () {
                cy.get('[data-context=article-v2-destock-information]').within(() => {
                    cy.get('label:contains(État)')
                        .siblings('[data-context=erp-multiselect]')
                        .should('contain', 'État acceptable')
                        .find('input')
                        .should('be.disabled')

                    cy.get('label:contains(Origine)')
                        .siblings('[data-context=erp-multiselect]')
                        .should('contain', 'Reconditionné (retour SAV)')
                        .find('input')
                        .should('be.disabled')

                    cy.get('label:contains(Assignation)')
                        .siblings('[data-context=erp-multiselect]')
                        .should('contain', 'Destock')
                        .find('input')
                        .should('be.disabled')

                    cy.get('label:contains(Défauts)')
                        .siblings('[data-context=erp-multiselect]')
                        .should('contain', " Léger(s) choc(s)  Traces d'utilisation ")
                        .find('input')
                        .should('be.disabled')

                    cy.get('label:contains(Commentaire interne)')
                        .siblings('textarea')
                        .should('have.value', 'Complètement cassé')
                        .should('be.disabled')

                    cy.get('label:contains(Commentaire publique)')
                        .siblings('textarea')
                        .should('have.value', 'En parfait état')
                        .should('be.disabled')

                    cy.get('button:contains(Sauvegarder)')
                        .parent()
                        .tooltip('Vous ne disposez pas des droits nécessaires')
                    cy.get('button:contains(Sauvegarder)').should('be.disabled')
                })
            })
        })
    })

    context('With permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_DESTOCK_WRITE])

            cy.intercept('GET', '**/api/erp/v2/article/DESTOCK-20191120013', {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013',
            }).as('get_article_by_id_or_sku_v2')

            cy.visit('/articles/DESTOCK-20191120013/general')

            // Initial calls on the page
            cy.wait('@get_article_by_id_or_sku_v2')
            cy.toggleMenu()

            cy.get('[data-context=article-v2-destock-information]').as('form').should('be.visible')
            cy.closeAllToasts()
        })

        it('can update destock condition', function () {
            cy.get('[data-context=article-v2-destock-information]').within(() => {
                cy.get('label:contains(État)')
                    .siblings('[data-context=erp-multiselect]')
                    .then((multiselect) => {
                        cy.get(multiselect).should('contain', 'État acceptable').find('input').should('not.be.disabled')

                        cy.get(multiselect).trigger('click')

                        cy.get(multiselect).find('[data-context=suggestion]').should('have.length', 3)
                        cy.get(multiselect).find('[data-context=suggestion]').eq(1).trigger('click') // 'GOOD'
                    })

                cy.get('label:contains(Origine)')
                    .siblings('[data-context=erp-multiselect]')
                    .then((multiselect) => {
                        cy.get(multiselect)
                            .should('contain', 'Reconditionné (retour SAV)')
                            .find('input')
                            .should('not.be.disabled')

                        cy.get(multiselect).trigger('click')

                        cy.get(multiselect).find('[data-context=suggestion]').should('have.length', 4)
                        cy.get(multiselect).find('[data-context=suggestion]').eq(0).trigger('click') // 'EXHIBITION_MODEL'
                    })

                cy.get('label:contains(Assignation)')
                    .siblings('[data-context=erp-multiselect]')
                    .then((multiselect) => {
                        cy.get(multiselect).should('contain', 'Destock').find('input').should('not.be.disabled')

                        cy.get(multiselect).trigger('click')

                        cy.get(multiselect).find('[data-context=suggestion]').should('have.length', 4)
                        cy.get(multiselect).find('[data-context=suggestion]').eq(1).trigger('click') // 'EXPO'
                    })

                cy.get('label:contains(Défauts)')
                    .siblings('[data-context=erp-multiselect]')
                    .then((multiselect) => {
                        cy.get(multiselect)
                            .should('contain', " Léger(s) choc(s)  Traces d'utilisation ")
                            .find('input')
                            .should('not.be.disabled')

                        cy.get(multiselect).trigger('click')

                        cy.get(multiselect).find('[data-context=suggestion]').should('have.length', 8)
                        cy.get(multiselect).find('[data-context=suggestion]').eq(5).trigger('click') // remove 'SLIGHT_SHOCK'
                        cy.get(multiselect).find('[data-context=suggestion]').eq(6).trigger('click') // remove 'TRACE_OF_USE'
                        cy.get(multiselect).find('[data-context=suggestion]').eq(0).trigger('click') // add 'ALTERED_PACKAGING'
                    })
                cy.get('label:contains(Défauts)').click()

                cy.get('label:contains(Commentaire interne)')
                    .siblings('textarea')
                    .within(() => {
                        cy.root().clear()
                        cy.root().type('Réparé')
                    })

                cy.get('label:contains(Commentaire publique)')
                    .siblings('textarea')
                    .within(() => {
                        cy.root().clear()
                        cy.root().type('Mieux que neuf')
                    })

                cy.intercept('PUT', '**/api/erp/v1/article/143214', {
                    delay: 400,
                }).as('put_article')

                cy.get('button:contains(Sauvegarder)').within(() => {
                    cy.root().click()
                    cy.root().should('be.disabled')
                })

                cy.wait('@put_article').then((xhr) => {
                    expect(xhr.request.body.scope).to.equal('general_destock_information')
                    expect(xhr.request.body.data.internal_comment).to.equal('Réparé')
                    expect(xhr.request.body.data.public_description).to.equal('Mieux que neuf')
                    expect(xhr.request.body.data.destock_state).to.equal('GOOD')
                    expect(xhr.request.body.data.destock_origin).to.equal('EXHIBITION_MODEL')
                    expect(xhr.request.body.data.destock_assignment).to.equal('EXPO')
                    expect(xhr.request.body.data.destock_defects[0]).to.equal('ALTERED_PACKAGING')
                })
            })
        })
    })
})
