import { ARTICLE_MEDIA_ADMINISTRATE } from '../../../../../src/apps/erp/permissions.js'
import { aliasQuery, getQueryAlias, GRAPHQL_ENDPOINT, hasOperationName } from '../../../utils/graphql-test-utils'
import { testPermissionInContext } from '../../../utils/permission-utils'

describe('Article v2 - Informations - Visuals', function () {
    const PAGE = '/articles/KEFQ350NR/images'
    const FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE = 'fetchCmsArticleToDeduceType'
    const FETCH_QUERY = 'fetchCmsArticleMedia'

    const commonFixtures = () => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)) {
                aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                req.reply({
                    data: {
                        published: [
                            {
                                sku: 'KEFQ350NR',
                                article_id: 117735,
                                unbasketable_reason: null,
                                __typename: 'cms_article_article',
                            },
                        ],
                        draft: [],
                    },
                })
            }

            if (hasOperationName(req, FETCH_QUERY)) {
                aliasQuery(req, FETCH_QUERY)

                req.reply({
                    fixture: 'graphql/cms/article_media/fetchCmsArticleMedia__KEFQ350NR',
                })
            }
        })
    }

    const navigateToMediaSection = () => {
        cy.visit(PAGE)
        // Initial calls on the page
        cy.wait(['@get_article_by_id_or_sku_v2', getQueryAlias(FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)])
        cy.toggleMenu()

        cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
            expect(xhr.request.body.operationName).to.eq(FETCH_QUERY)
            expect(xhr.request.body.variables).to.deep.eq({
                where: {
                    sku: { _eq: 'KEFQ350NR' },
                },
                where_media_i18n: {
                    supported_culture_id: { _eq: 'fr' },
                },
            })
        })

        cy.get('[data-context=article-v2-images] [data-context=article-v2-images-active]').as('active-images')
    }

    describe('With write permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_MEDIA_ADMINISTRATE])

            commonFixtures()
        })

        testPermissionInContext(
            '[data-context=erp-account-permission-in-context-toolbar] button',
            navigateToMediaSection,
            {
                _and: [
                    {
                        permission_id: {
                            _ilike: 'ARTICLE_MEDIA_ADMINISTRATE',
                        },
                    },
                ],
            },
        )
    })
})
