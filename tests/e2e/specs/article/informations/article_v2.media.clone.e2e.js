import { aliasQ<PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../../../utils/graphql-test-utils'
import { ARTICLE_MEDIA_ADMINISTRATE } from '../../../../../src/apps/erp/permissions.js'

describe('Article v2 - Informations - clone media', () => {
    const PAGE = '/articles/KEFQ350NR/documents'
    const IMAGE_SECTION_PAGE = '/articles/KEFQ350NR/images'
    const FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE = 'fetchCmsArticleToDeduceType'
    const FETCH_QUERY = 'fetchCmsArticleMedia'

    const commonFixtures = () => {
        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (
                hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE) &&
                req.body.variables.where_draft.sku._eq === 'KEFQ350NR'
            ) {
                aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                req.reply({
                    data: {
                        published: [
                            {
                                sku: 'KEFQ350NR',
                                article_id: 117735,
                                unbasketable_reason: 'UNAVAILABLE',
                                __typename: 'cms_article_article',
                            },
                        ],
                        draft: [],
                    },
                })
            }

            if (hasOperationName(req, FETCH_QUERY) && req.body.variables.where.sku._eq === 'KEFQ350NR') {
                aliasQuery(req, FETCH_QUERY)

                req.reply({
                    fixture: 'graphql/cms/article_media/fetchCmsArticleMedia__KEFQ350NR',
                })
            }
        })
    }

    const navigateToMediaSection = () => {
        cy.visit(PAGE)

        // Initial calls on the page
        cy.wait(['@get_article_by_id_or_sku_v2', getQueryAlias(FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)])
        cy.toggleMenu()

        cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
            expect(xhr.request.body.operationName).to.eq(FETCH_QUERY)
            expect(xhr.request.body.variables).to.deep.eq({
                where: {
                    sku: { _eq: 'KEFQ350NR' },
                },
                where_media_i18n: {
                    supported_culture_id: { _eq: 'fr' },
                },
            })
        })

        cy.get('[data-context=article-v2-documents]').as('documents')
    }

    context('With write permissions', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_MEDIA_ADMINISTRATE])

            commonFixtures()
        })

        it('should show the clone button in the image section as well', () => {
            cy.visit(IMAGE_SECTION_PAGE)

            // Initial calls on the page
            cy.wait(['@get_article_by_id_or_sku_v2', getQueryAlias(FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)])
            cy.toggleMenu()

            cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
                expect(xhr.request.body.operationName).to.eq(FETCH_QUERY)
                expect(xhr.request.body.variables).to.deep.eq({
                    where: {
                        sku: { _eq: 'KEFQ350NR' },
                    },
                    where_media_i18n: {
                        supported_culture_id: { _eq: 'fr' },
                    },
                })
            })

            // no need to do more test, it's the same component in both sub-tabs
            cy.get('[data-context=open-media-clone-btn]:contains(Cloner)').should('be.visible')
        })

        it('should allow the user to clone the specified media', () => {
            navigateToMediaSection()

            cy.get('[data-context=open-media-clone-btn]:contains(Cloner)').should('be.visible').click()

            // mock auto complete
            cy.intercept('POST', '**/api/erp/v1/articles**', {
                fixture: 'erp/article/cpost_articles_autocomplete__kef_q3',
            }).as('fetch_articles')

            // mock media for selected article
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (
                    hasOperationName(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE) &&
                    req.body.variables.where_draft.sku._eq === 'KEFQ300BC'
                ) {
                    aliasQuery(req, FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)

                    req.reply({
                        data: {
                            published: [
                                {
                                    sku: 'KEFQ300BC',
                                    article_id: 82537,
                                    unbasketable_reason: 'UNAVAILABLE',
                                    __typename: 'cms_article_article',
                                },
                            ],
                            draft: [],
                        },
                    })
                }

                if (hasOperationName(req, FETCH_QUERY) && req.body.variables.where.sku._eq === 'KEFQ300BC') {
                    aliasQuery(req, FETCH_QUERY)

                    req.reply({
                        fixture: 'graphql/cms/article_media/fetchCmsArticleMedia__KEFQ350NR',
                    })
                }
            })

            // search an article
            cy.get('[data-context=search-article-form]')
                .should('be.visible')
                .within(() => {
                    cy.get('[data-context=erp-multiselect]').click()
                    cy.get('[data-context=erp-multiselect]').type('toto')

                    cy.wait('@fetch_articles')

                    cy.get('[data-context=suggestion]').eq(0).click()
                })

            cy.wait(getQueryAlias(FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)).then((xhr) => {
                expect(xhr.request.body.operationName).to.eq(FETCH_CMS_ARTICLE_TO_DEDUCE_TYPE)
                expect(xhr.request.body.variables).to.deep.eq({
                    where_published: {
                        sku: {
                            _eq: 'KEFQ300BC',
                        },
                    },
                    where_draft: {
                        sku: {
                            _eq: 'KEFQ300BC',
                        },
                    },
                })
            })
            cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
                expect(xhr.request.body.operationName).to.eq(FETCH_QUERY)
                expect(xhr.request.body.variables).to.deep.eq({
                    where: {
                        sku: { _eq: 'KEFQ300BC' },
                    },
                    where_media_i18n: {
                        supported_culture_id: { _eq: 'fr' },
                    },
                })
            })

            // no media selected
            cy.get('[data-context=clone-btn]').should('be.disabled').should('contain', 'Cloner')

            // select some images
            cy.get('[data-context=clone-media-choices]').within(() => {
                cy.get('[data-context=article-v2-images-list] [data-context=erp-stacked-list-item] img').eq(2).click()
                cy.get('[data-context=article-v2-images-list] [data-context=erp-stacked-list-item] img').eq(4).click()
            })

            cy.get('[data-context=clone-btn]').should('not.be.disabled').should('contain', 'Cloner 2 média(s)')

            // add some documents
            cy.get('[data-context=clone-media-choices]').within(() => {
                cy.get('[data-context=documents] [data-context=erp-stacked-list-item]').eq(0).click({ force: true })
                cy.get('[data-context=documents] [data-context=erp-stacked-list-item]').eq(1).click({ force: true })
            })

            cy.get('[data-context=clone-btn]').should('not.be.disabled').should('contain', 'Cloner 4 média(s)')

            // unselect a media
            cy.get('[data-context=clone-media-choices]').within(() => {
                cy.get('[data-context=documents] [data-context=erp-stacked-list-item]').eq(1).click({ force: true })
            })

            cy.get('[data-context=clone-btn]').should('not.be.disabled').should('contain', 'Cloner 3 média(s)')

            // submit and reload
            cy.intercept('POST', '**/api/erp/v1/article/KEFQ300BC/media/clone-to/KEFQ350NR', {
                statusCode: 204,
                body: {
                    data: [],
                },
            }).as('clone_request')

            cy.get('[data-context=clone-btn]').should('not.be.disabled').click()

            cy.wait('@clone_request').then((xhr) => {
                expect(xhr.request.body.selected_media_ids).to.deep.eq([
                    '20eed0df-4794-4f0f-895d-3d30172f69e9',
                    '6ef76fad-b84c-4e82-9974-00bbcb734d20',
                    'fad1444d-9acb-4fd7-befe-7663ada0ec92',
                ])
            })

            // reload the media
            // no visual control in test, we just want to make sure that the request happened
            // not that the mocked data are correct
            cy.wait(getQueryAlias(FETCH_QUERY))
        })
    })
})
