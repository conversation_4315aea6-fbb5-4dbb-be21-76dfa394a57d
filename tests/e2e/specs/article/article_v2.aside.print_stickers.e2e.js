import { ARTICLE_QR_CODE_VIEW } from '../../../../src/apps/erp/permissions'

describe('ERP print from article page', function () {
    const PAGE = '/articles/SONOSONENR/general'

    const visit = () => {
        cy.visit(PAGE)

        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
    }

    context('with an article', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_QR_CODE_VIEW])

            cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__SONOSONENR',
            }).as('get_article_by_id_or_sku_v2')

            cy.intercept('POST', '**/api/erp/v1/printers**', { fixture: 'erp/printer/printers_32x25' }).as('printer')
        })
        describe('Print 128 sticker', function () {
            it('can print 128 code from article page', function () {
                visit()

                // check button
                cy.get('[data-context=slide_in_print-btn]').should('be.visible').tooltip('Impression etiquette').click()

                // check slide out
                cy.wait('@printer')
                cy.get('[data-context=slide-out-container]')
                    .find('[data-context=page-header]')
                    .should('contain', "Impression d'étiquette")
                cy.get('[data-context=type-choice]').should('contain', "Type d'etiquette")
                cy.get('[data-context=type-choice]').find('input').should('have.attr', 'disabled', 'disabled')
                cy.get('[data-context=type-choice] [data-context=single-value]').should('contain', 'Code 128')
                cy.get('[data-context=print-source-label]').should('contain', 'Imprimante')
                cy.get('[data-context=print-source-input]')
                    .find('[name=printer_autocomplete]')
                    .should('have.attr', 'placeholder', 'Sélectionner une imprimante')
                    .parent()
                    .siblings()
                    .find('.multiselect__element')
                    .as('source_multiselect_tag')
                    .should('have.length', 3)
                cy.get('@source_multiselect_tag').eq(0).should('contain', 'Zebra_128_1')
                cy.get('@source_multiselect_tag').eq(1).should('contain', 'Zebra_dev_128')
                cy.get('[data-context=print-qty-label]').should('contain', 'Quantité')
                cy.get('[data-context=print-qty-input]').should('have.value', '0')
                cy.get('[data-context=print-btn]').should('be.visible').should('have.attr', 'disabled', 'disabled')

                // Check validation form
                cy.get('[data-context=print-source-input]').multiselect('Zebra_dev_128', 'Zebra_dev_128')
                cy.get('[data-context=print-btn]').should('have.attr', 'disabled', 'disabled')
                cy.get('[data-context=print-qty-input]').focus().type('3')
                cy.get('[data-context=print-btn]').should('not.have.attr', 'disabled')

                // check params sent to print request
                // check request error
                cy.intercept('POST', '**/v1/article/120390/sticker/code-128/print', {
                    statusCode: 500,
                    body: {},
                }).as('print128_error')

                cy.get('[data-context=print-btn]').click()
                cy.wait('@print128_error')
                cy.get('[data-context=alert-danger]').should(
                    'contain',
                    "Une erreur est survenue lors de l'impression, veuillez réessayer.",
                )

                // check success
                cy.intercept('POST', '**/v1/article/120390/sticker/code-128/print', {
                    statusCode: 200,
                    body: { status: 'success', data: null },
                }).as('print128_success')
                cy.get('[data-context=print-btn]').click()
                cy.wait('@print128_success').then((xhr) => {
                    expect(xhr.request.body.quantity).to.eq(3)
                    expect(xhr.request.body.printer_name).to.eq('Zebra_dev_128')
                })

                cy.get('[data-context=slide-out-container]').should('not.exist')

                cy.toast(`Étiquette imprimée avec succès.`, 'success')
            })

            it('ask for confirmation if quantity higher than 10', function () {
                visit()

                cy.get('[data-context=slide_in_print-btn]').click()
                cy.wait('@printer')

                // check btn display
                cy.get('[data-context=print-source-input]').multiselect('Zebra_dev_128', 'Zebra_dev_128')
                cy.get('[data-context=print-btn]').should('have.attr', 'disabled', 'disabled')
                cy.get('[data-context=print-btn]').find('[data-context=print-btn]').should('not.exist')
                cy.get('[data-context=print-qty-input]').focus().type('3')
                cy.get('[data-context=print-btn]').should('not.have.attr', 'disabled')
                cy.get('[data-context=print-qty-input]').type('3')
                cy.get('[data-context=print-btn]').should('be.visible')

                // Check confirm modal on quantity higher than 10
                cy.get('[data-context=print-btn]').click()

                cy.get('[data-context=confirm-block]')
                    .should('be.visible')
                    .should('contain', "Confirmer l'impression de 33 étiquettes ?")
                cy.get('[data-context=confirm-block-validate-btn]').should('be.visible')
                // cancel
                cy.get('[data-context=confirm-block-cancel-btn]').should('be.visible').click()

                cy.get('[data-context=confirm-block]').should('not.exist')
                // print
                cy.get('[data-context=print-btn]').click()

                cy.get('[data-context=confirm-block]')
                    .should('be.visible')
                    .should('contain', "Confirmer l'impression de 33 étiquettes ?")

                // check success
                cy.intercept('POST', '**/v1/article/120390/sticker/code-128/print', {
                    statusCode: 200,
                    body: { status: 'success', data: null },
                }).as('print128_success')

                cy.get('[data-context=confirm-block-validate-btn]').click()
                cy.wait('@print128_success').then((xhr) => {
                    expect(xhr.request.body.quantity).to.eq(33)
                    expect(xhr.request.body.printer_name).to.eq('Zebra_dev_128')
                })

                cy.get('[data-context=slide-out-container]').should('not.exist')

                cy.toast(`Étiquette imprimée avec succès.`, 'success')
            })
        })
    })
    context('with an destock', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([ARTICLE_QR_CODE_VIEW])

            cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                fixture: 'erp/article/get_article_by_id_or_sku_v2__DESTOCK-20191120013.json',
            }).as('get_article_by_id_or_sku_v2')

            cy.intercept('POST', '**/api/erp/v1/printers**', { fixture: 'erp/printer/printers_102x152' }).as('printer')
        })
        describe('Print destock sticker', function () {
            it('can print destock from article page', function () {
                visit()

                // check button
                cy.get('[data-context=slide_in_print-btn]').should('be.visible').tooltip('Impression etiquette').click()

                // check slide out
                cy.wait('@printer')
                cy.get('[data-context=slide-out-container]')
                    .find('[data-context=page-header]')
                    .should('contain', "Impression d'étiquette")
                cy.get('[data-context=type-choice]').should('contain', "Type d'etiquette")
                cy.get('[data-context=type-choice] [data-context=single-value]').should('contain', 'Code 128')
                cy.get('[data-context=type-choice] [data-context=erp-multiselect]').erpMultiselect('', 'Destock')
                cy.get('[data-context=type-choice]').should('not.have.attr', 'disabled')
                cy.get('[data-context=print-source-label]').should('contain', 'Imprimante')
                cy.get('[data-context=print-source-input]')
                    .find('[name=printer_autocomplete]')
                    .should('have.attr', 'placeholder', 'Sélectionner une imprimante')
                    .parent()
                    .siblings()
                    .find('.multiselect__element')
                    .as('source_multiselect_tag')
                    .should('have.length', 2)
                cy.get('@source_multiselect_tag').eq(0).should('contain', 'Zebra_prod1')
                cy.get('@source_multiselect_tag').eq(1).should('contain', 'Zebra_prod2')
                cy.get('[data-context=print-qty-label]').should('contain', 'Quantité')
                cy.get('[data-context=print-qty-input]').should('have.value', '0')
                cy.get('[data-context=print-btn]').should('be.visible').should('have.attr', 'disabled', 'disabled')

                // Check validation form
                cy.get('[data-context=print-source-input]').multiselect('Zebra_prod1', 'Zebra_prod1')
                cy.get('[data-context=print-btn]').should('have.attr', 'disabled', 'disabled')
                cy.get('[data-context=print-qty-input]').focus().type('2')
                cy.get('[data-context=print-btn]').should('not.have.attr', 'disabled')

                // check params sent to print request
                // check request error
                cy.intercept('POST', '**/v1/article/143214/sticker/destock/print', {
                    statusCode: 500,
                    body: {},
                }).as('destock_error')

                cy.get('[data-context=print-btn]').click()
                cy.wait('@destock_error')
                cy.get('[data-context=alert-danger]').should(
                    'contain',
                    "Une erreur est survenue lors de l'impression, veuillez réessayer.",
                )

                // check success
                cy.intercept('POST', '**/v1/article/143214/sticker/destock/print', {
                    statusCode: 200,
                    body: { status: 'success', data: null },
                }).as('destock_success')
                cy.get('[data-context=print-btn]').click()
                cy.wait('@destock_success').then((xhr) => {
                    expect(xhr.request.body.quantity).to.eq(2)
                    expect(xhr.request.body.printer_name).to.eq('Zebra_prod1')
                })

                cy.get('[data-context=slide-out-container]').should('not.exist')

                cy.toast(`Étiquette imprimée avec succès.`, 'success')
            })
        })
    })
})
