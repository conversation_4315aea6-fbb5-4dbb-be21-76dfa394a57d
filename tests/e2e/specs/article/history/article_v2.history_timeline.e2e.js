describe('Article v2 - Historique - Timeline', function () {
    beforeEach(function () {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v2/article/KEFQ350NR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__KEFQ350NR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('POST', '**/api/erp/v1/system-events', {
            fixture: 'erp/article/events/article_all_events.json',
        }).as('cpost_article_all_history_by_id')
    })

    const visitPage = () => {
        cy.visit('/articles/KEFQ350NR/history')
        cy.toggleMenu()

        // Initial calls on the page
        cy.wait('@get_article_by_id_or_sku_v2')
    }

    describe('View events & comments related to the article', function () {
        it('should not display anything if there are nothing to show', function () {
            cy.intercept('POST', '**/api/erp/v1/system-events', {
                statusCode: 404,
                body: {},
            }).as('cpost_article_empty_history_by_id')

            visitPage()

            cy.wait('@cpost_article_empty_history_by_id')

            cy.get('[data-context=article-history]').should('contain', "Il n'y pas encore d'évènement pour cet article")
        })

        it('should display the combined events', function () {
            visitPage()

            cy.wait('@cpost_article_all_history_by_id').then((xhr) => {
                expect(xhr.request.body.where).to.deep.eq({
                    type: {
                        _like: 'article.%',
                    },
                    main_id: {
                        _eq: 117735,
                    },
                })
            })

            cy.get('[data-context=article-history]').should(
                'not.contain',
                "Il n'y pas encore d'évènements pour cette article",
            )

            // Check all events combined
            cy.get('[data-context=article-history] > ul > li[data-context]').should('have.length', 7)

            // Check only event items
            cy.get('[data-context=erp-timeline-event-item]').should('have.length', 3)

            // Check only comment items
            cy.get('[data-context=article-timeline-comment]').should('have.length', 4)
        })

        it('should filter the events by comments type', function () {
            visitPage()

            cy.wait('@cpost_article_all_history_by_id')

            cy.get('[data-context=filter-selection]').select('comment')

            cy.wait('@cpost_article_all_history_by_id').then((xhr) => {
                expect(xhr.request.body.where).to.deep.eq({
                    type: {
                        _in: ['article.update.comment', 'article.create.comment', 'article.comment.legacy'],
                    },
                    main_id: {
                        _eq: 117735,
                    },
                })
            })
        })

        it('should display the update events', function () {
            visitPage()

            cy.wait('@cpost_article_all_history_by_id')

            cy.intercept('POST', '**/api/erp/v1/system-events', {
                fixture: 'erp/article/events/article_update_events.json',
            }).as('cpost_article_update_history_by_id')

            cy.get('[data-context=filter-selection]').select('update')

            cy.wait('@cpost_article_update_history_by_id').then((xhr) => {
                expect(xhr.request.body.where).to.deep.eq({
                    _or: [
                        { type: { _like: 'article.update.%' } },
                        { type: { _like: 'article.create.%' } },
                        { type: { _like: 'article.delete.%' } },
                    ],
                    main_id: {
                        _eq: 117735,
                    },
                    type: {
                        _nin: [
                            'article.update.prices',
                            'article.create.weighted_cost_adjustment',
                            'article.update.weighted_cost_adjustment',
                            'article.create.article_planned_price',
                            'article.update.article_planned_price',
                            'article.delete.article_planned_price',
                        ],
                    },
                })
            })

            cy.get('[data-context=article-history] > ul > li[data-context]').should('have.length', 8)
            cy.get('[data-context=article-timeline-update]').should('have.length', 3)

            cy.get('[data-context=article-timeline-update]').eq(0).as('event')
            cy.get('@event')
                .find('[data-context=content]')
                .should('be.visible')
                .should('contain', 'a modifié un ajustement')
            cy.get('@event').find('[data-context=show-more]').should('be.visible').click()
            cy.get('@event')
                .find('[data-context=erp-table] tbody tr')
                .checkRows([
                    ['Chrono', '', 'UYIOP'],
                    ['Quantité', '12', '24'],
                    ['Commentaire', 'tout va bien', 'rien ne va plus'],
                    ['Dates (fourchette)', 'du 10/05/2023 au 20/05/2023', 'du 11/05/2023 au 20/05/2023'],
                ])

            cy.get('[data-context=article-timeline-update]').eq(1).as('event')

            cy.get('@event')
                .find('[data-context=content]')
                .should('be.visible')
                .should('contain', 'a modifié le statut')
            cy.get('@event').find('[data-context=show-more]').should('be.visible').click()
            cy.get('@event').find('[data-context=erp-table]').as('erp-table')

            cy.get('@erp-table').should('be.visible')

            cy.get('@erp-table').find('[data-context=cell-key]').eq(0).should('contain', 'Statut')
            cy.get('@erp-table').find('[data-context=cell-old]').eq(0).should('contain', 'todo')
            cy.get('@erp-table').find('[data-context=cell-new]').eq(0).should('contain', 'a voir')

            cy.get('[data-context=article-timeline-create-weighted-cost-adjustment]').should('have.length', 1)
            cy.get('[data-context=article-timeline-create-weighted-cost-adjustment]').eq(0).as('event')
            cy.get('@event')
                .find('[data-context=content]')
                .should('be.visible')
                .should('contain', 'a créé une dévaluation comptable avec un nouveau prix de 135,26 €')

            cy.get('[data-context=article-timeline-comment]')
                .eq(0)
                .within(() => {
                    cy.get('[data-context=content]').should('be.visible').should('contain', "J'aime bien cet article")
                    cy.get('[data-context=badge]').should('contain', 'Alerte')
                })
        })

        it('should display the prices events', function () {
            cy.intercept('POST', '**/api/erp/v1/sales-channel', {
                fixture: 'erp/sales-channel/cpost_sales_channels_light',
            }).as('cpost_sales_channels')

            visitPage()

            cy.wait('@cpost_article_all_history_by_id')

            cy.intercept('POST', '**/api/erp/v1/system-events', {
                fixture: 'erp/article/events/article_price_events.json',
            }).as('cpost_article_price_history_by_id')

            cy.get('[data-context=filter-selection]').select('price')

            cy.wait('@cpost_sales_channels')

            const expected_where = {
                type: {
                    _in: [
                        'article.update.prices',
                        'article.create.weighted_cost_adjustment',
                        'article.update.weighted_cost_adjustment',
                        'article.create.article_planned_price',
                        'article.update.article_planned_price',
                        'article.delete.article_planned_price',
                    ],
                },
                main_id: {
                    _eq: 117735,
                },
            }
            cy.wait('@cpost_article_price_history_by_id').then((xhr) => {
                expect(xhr.request.body.where).to.deep.eq(expected_where)
            })

            cy.get('[data-context=article-history] > ul > li[data-context]').should('have.length', 4)

            cy.get('[data-context=article-timeline-update]').should('have.length', 2)
            cy.get('[data-context=article-timeline-create]').should('have.length', 1)
            cy.get('[data-context=article-timeline-delete]').should('have.length', 1)

            cy.get('[data-context=article-tab-history] [data-context=reload]').click()
            cy.wait('@cpost_article_price_history_by_id').then((xhr) => {
                expect(xhr.request.body.where).to.deep.eq(expected_where)
            })
        })

        it('should display update list events', function () {
            cy.fixture('erp/article/events/article_update_events.json').then((payload) => {
                payload.data.system_events[0].type = 'article.update.eans'
                payload.data.system_events[0].payload.data = {
                    eans: { added: { 1: '123456' }, deleted: {} },
                    atoms: { added: { 1: 'gold', 2: 'copper' }, deleted: { 1: 'silver' } },
                }

                cy.intercept('POST', '**/api/erp/v1/system-events', {
                    body: payload,
                }).as('cpost_article_history_by_id')
            })

            visitPage()

            cy.wait('@cpost_article_history_by_id')

            cy.get('[data-context=article-timeline-update-list]').eq(0).as('event')

            cy.get('@event').find('[data-context=content]').should('be.visible').should('contain', 'a modifié les EANs')
            cy.get('@event').find('[data-context=show-more]').should('be.visible').click()
            cy.get('@event').find('[data-context=erp-table]').as('erp-table')

            cy.get('@erp-table').should('be.visible')

            cy.get('@erp-table').find('[data-context=cell-key]').eq(0).should('contain', 'eans')
            cy.get('@erp-table').find('[data-context=cell-added]').eq(0).should('contain', '123456')
            cy.get('@erp-table')
                .find('[data-context=cell-deleted]')
                .eq(0)
                .should((cell) => expect(cell.text().trim()).equal(''))

            cy.get('@erp-table').find('[data-context=cell-key]').eq(1).should('contain', 'atoms')
            cy.get('@erp-table').find('[data-context=cell-added]').eq(1).find('li').as('added-li')
            cy.get('@added-li').eq(0).should('contain', 'gold')
            cy.get('@added-li').eq(1).should('contain', 'copper')
            cy.get('@erp-table').find('[data-context=cell-deleted]').eq(1).should('contain', 'silver')
        })

        it('should display article sales channel events', function () {
            cy.intercept('POST', '**/erp/v1/system-events', {
                fixture: 'erp/article/events/article_sales_channel_events',
            }).as('cpost_article_history_by_id')

            cy.intercept('POST', '**/erp/v1/sales-channel', {
                fixture: 'erp/sales-channel/cpost_sales_channels.json',
            }).as('cpost_sales_channels')

            visitPage()

            cy.wait(['@cpost_article_history_by_id', '@cpost_sales_channels'])

            cy.get('[data-context=article-timeline-event-item]')
                .eq(0)
                .should('be.visible')
                .should('contain', 'a ajouté le canal de vente amazon.fr')
            cy.get('[data-context=article-timeline-event-item]')
                .eq(1)
                .should('be.visible')
                .should('contain', 'a supprimé le canal de vente cultura')
        })
    })
})
