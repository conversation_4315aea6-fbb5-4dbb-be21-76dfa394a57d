import { ARTICLE_BUYERS_WRITE } from '../../../../src/apps/erp/permissions.js'

describe('Create supplier order from article page', function () {
    const PAGE = '/articles/SONOSONENR'

    const visit = () => {
        cy.visit(PAGE, {
            // It is used but not tested (widow<PERSON><PERSON> is not used anywhere else and the called url is not checked)
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })

        cy.wait('@get_article_by_id_or_sku_v2')
        cy.toggleMenu()
        cy.get('[data-context=tabs] [data-context=item]:contains(Achats)').click()
        cy.get('[data-context=article-tab-buyers]').should('be.visible')
        cy.closeAllToasts()
    }

    beforeEach(() => {
        cy.authenticate()

        cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
            fixture: 'erp/article/get_article_by_id_or_sku_v2__SONOSONENR',
        }).as('get_article_by_id_or_sku_v2')

        cy.intercept('GET', '**/api/erp/v1/brand/585/suppliers', { fixture: 'erp/supplier/suppliers' }).as('suppliers')
        cy.intercept('POST', '**/api/erp/v1/suppliers', { fixture: 'erp/supplier/suppliers_sonos' }).as('suppliers')
        cy.intercept('POST', '**/api/erp/v1/article-alerts', {}).as('alerts')
    })

    describe('Create supplier order', function () {
        it('Button should be disabled if not enough permission', function () {
            cy.mockErpUser()
            visit()

            cy.get('[data-context=open-supplier-order-form-btn]').should('be.visible').should('be.disabled')
        })

        it('Button is not displayed for packages', function () {
            cy.mockErpUser()
            cy.fixture('erp/article/get_article_by_id_or_sku_v2__SONOSONENR').then((payload) => {
                payload.data.is_package = true
                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })
            visit()

            cy.get('[data-context=open-supplier-order-form-btn]').should('not.exist')
        })

        it('Button is not displayed for destocks', function () {
            cy.mockErpUser()
            cy.fixture('erp/article/get_article_by_id_or_sku_v2__SONOSONENR').then((payload) => {
                payload.data.is_destock = true
                cy.intercept('GET', '**/api/erp/v2/article/SONOSONENR', {
                    statusCode: 200,
                    body: payload,
                }).as('get_article_by_id_or_sku_v2')
            })
            visit()

            cy.get('[data-context=open-supplier-order-form-btn]').should('not.exist')
        })

        it('can create supplier order from article page', function () {
            cy.mockErpUser([ARTICLE_BUYERS_WRITE])
            visit()

            // check button
            cy.get('[data-context=open-supplier-order-form-btn]').should('be.visible').click()
            cy.wait('@suppliers')

            cy.get('[data-context=slide-out-container]')
                .find('[data-context=page-header]')
                .should('contain', 'Commande fournisseur')
            cy.get('[data-context=brand] label').should('contain', 'Marque')
            cy.get('[data-context=brand-input]').should('have.value', 'SONOS')
            cy.get('[data-context=model] label').should('contain', 'Modèle')
            cy.get('[data-context=model-input]').should('have.value', 'ONE Noir')
            cy.get('[data-context=suppliers] label').should('contain', 'Fournisseurs')
            cy.get('[data-context=erp-multiselect] div').should('contain', 'Sonos BV Europe')
            cy.get('[data-context=quantity] label').should('contain', 'Quantité')
            cy.get('[data-context=quantity-input]').should('have.value', '1')
            cy.get('[data-context=price] label').should('contain', "Prix d'achat")
            cy.get('[data-context=price-input]').should('have.value', '142.13')

            // Check validation form
            cy.get('[data-context=suppliers] [data-context=erp-input-helper][data-invalid]').should(
                'contain',
                "La marque n'est pas disponible chez ce fournisseur.",
            )
            cy.get('[data-context=suppliers] [data-context=erp-multiselect]').as('supplier_input')
            cy.get('@supplier_input').click()
            cy.get('@supplier_input').find('[data-context=suggestion]').should('have.length', 10)
            cy.get('@supplier_input').find('[data-context=suggestion]').eq(4).click()

            cy.get('[data-context=quantity-input]').clear()
            cy.get('[data-context=order-btn]').click()
            cy.get('[data-context=quantity] [data-context=erp-input-helper][data-invalid]').should(
                'contain',
                'Ce champ est obligatoire.',
            )
            cy.get('[data-context=quantity-input]').type('0')
            cy.get('[data-context=order-btn]').click()
            cy.get('[data-context=quantity] [data-context=erp-input-helper][data-invalid]').should(
                'contain',
                'La quantité doit être positive.',
            )
            cy.get('[data-context=quantity-input]').clear()
            cy.get('[data-context=quantity-input]').type('3000000')
            cy.get('[data-context=order-btn]').click()
            cy.get('[data-context=quantity] [data-context=erp-input-helper][data-invalid]').should(
                'contain',
                'La quantité ne peut excéder 10 000.',
            )
            cy.get('[data-context=quantity-input]').clear()
            cy.get('[data-context=quantity-input]').type('3.5')
            cy.get('[data-context=order-btn]').click()
            cy.get('[data-context=quantity] [data-context=erp-input-helper][data-invalid]').should(
                'contain',
                'La quantité doit être un entier.',
            )
            cy.get('[data-context=quantity-input]').focus()
            cy.get('[data-context=quantity-input]').clear()
            cy.get('[data-context=quantity-input]').focus()
            cy.get('[data-context=quantity-input]').type('3')

            cy.get('[data-context=price-input]').clear()
            cy.get('[data-context=order-btn]').click()
            cy.get('[data-context=price] [data-context=erp-input-helper][data-invalid]').should(
                'contain',
                'Ce champ est obligatoire.',
            )
            cy.get('[data-context=price-input]').type('aaa')
            cy.get('[data-context=order-btn]').click()
            cy.get('[data-context=price] [data-context=erp-input-helper][data-invalid]').should(
                'contain',
                'Le prix doit être un nombre.',
            )
            cy.get('[data-context=price-input]').focus().clear().type('3000000')
            cy.get('[data-context=order-btn]').click()
            cy.get('[data-context=price] [data-context=erp-input-helper][data-invalid]').should(
                'contain',
                'Le prix maximum est de 1 000 000€.',
            )
            cy.get('[data-context=price-input]').clear().type('3000.326')
            cy.get('[data-context=order-btn]').click()

            // check params sent to supplier order request
            // check request error
            cy.intercept('POST', '**/api/erp/v1/supplier-order', {
                statusCode: 200,
                body: {
                    success: 'success',
                    data: {
                        supplier_order_id: 123,
                    },
                },
            }).as('supplier_order')
            cy.intercept('PUT', '**/api/erp/v1/supplier-order/123/product', {
                fixture: 'erp/supplier-order-products/post_supplier_order_product',
            }).as('supplier_order_product')

            cy.get('[data-context=order-btn]').click()
            cy.wait('@supplier_order')
            cy.wait('@supplier_order_product').then((xhr) => {
                const body = xhr.request.body
                expect(body).to.deep.eq({ product_id: 120390, quantity: '3', price: 3000.33 })
            })
            cy.get('[data-context=slide-out-container]').should('not.exist')

            cy.toast(`La commande a été mise à jour`, 'success')
        })
    })
})
