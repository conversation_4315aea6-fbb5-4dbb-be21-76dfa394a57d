import { TRANSACTIONAL_EMAIL_UPDATE } from '../../../../src/apps/erp/permissions.js'

describe('Administration - transactional mail Manager', () => {
    const PAGE = '/administration/system/transactional-email'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Displays with permission ', () => {
        it('Page is displayed correctly', () => {
            cy.mockErpUser([TRANSACTIONAL_EMAIL_UPDATE])
            cy.intercept('POST', 'https://erp.graphql/v1/graphql', {
                fixture: 'graphql/administration/transactional_mail/fetch_parameters.json',
            }).as('fetch_parameters_email')

            cy.visit(PAGE)
            cy.toggleMenu()
            cy.wait('@fetch_parameters_email')

            cy.get('[data-context=page-header]').should('contain', 'Administration')
            cy.get('[data-context=page-header]').should('contain', 'Emails transactionnels')

            cy.get('[data-context=erp-form-block]').should('have.length', 3).as('form-block')

            cy.get('@form-block').eq(0).find('label').should('contain', 'Mail de confirmation de commande')

            cy.get('@form-block').eq(0).find('input').should('have.value', '123')

            cy.get('@form-block')
                .eq(0)
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Id de template Mailjet')

            cy.get('@form-block')
                .eq(1)
                .find('label')
                .should('contain', "Mail de confirmation d'expédition - transporteur")

            cy.get('@form-block').eq(1).find('input').should('have.value', '456')

            cy.get('@form-block')
                .eq(1)
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Id de template Mailjet')

            cy.get('@form-block').eq(2).find('label').should('contain', "Mail de confirmation d'expédition - emport")

            cy.get('@form-block').eq(2).find('input').should('have.value', '789')

            cy.get('@form-block')
                .eq(2)
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Id de template Mailjet')

            cy.get('[data-context=erp-button]').should('contain', 'Sauvegarder').should('not.be.disabled')
        })

        it('Displayed with error fetch data', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.toast('Une erreur est survenue lors du chargement, veuillez réessayer.', 'danger')

            cy.get('[data-context=erp-form-block]').should('have.length', 0)

            cy.get('[data-context=erp-button]').should('contain', 'Sauvegarder').should('be.disabled')
        })

        it('Edit and save the form', () => {
            cy.mockErpUser([TRANSACTIONAL_EMAIL_UPDATE])

            cy.intercept('POST', 'https://erp.graphql/v1/graphql', {
                fixture: 'graphql/administration/transactional_mail/fetch_parameter.json',
            }).as('fetch_parameter')
            cy.visit(PAGE)
            cy.toggleMenu()
            cy.wait('@fetch_parameter')

            cy.get('[data-context=erp-form-block]').as('form-block')

            cy.get('@form-block').eq(0).find('input').as('input').clear()

            cy.get('[data-context=erp-button]').click()

            cy.get('@form-block')
                .eq(0)
                .find('[data-context=erp-input-helper]')
                .should('have.length', 3)
                .as('erp-input-helper')

            cy.get('@erp-input-helper').eq(0).should('contain', 'Id de template Mailjet')
            cy.get('@erp-input-helper').eq(1).should('contain', 'La valeur doit être un nombre positif')
            cy.get('@erp-input-helper').eq(2).should('contain', 'La valeur ne peux pas être vide')

            cy.get('[data-context=erp-button]').should('contain', 'Sauvegarder').as('submit')

            cy.get('@input').type('44 BZH')

            cy.get('@form-block')
                .eq(0)
                .find('[data-context=erp-input-helper]')
                .should('have.length', 2)
                .as('erp-input-helper')

            cy.get('@erp-input-helper').eq(0).should('contain', 'Id de template Mailjet')
            cy.get('@erp-input-helper').eq(1).should('contain', 'La valeur doit être un nombre positif')

            cy.get('@input').clear().type('-2')

            cy.get('@form-block')
                .eq(0)
                .find('[data-context=erp-input-helper]')
                .should('have.length', 2)
                .as('erp-input-helper')

            cy.get('@erp-input-helper').eq(0).should('contain', 'Id de template Mailjet')
            cy.get('@erp-input-helper').eq(1).should('contain', 'La valeur doit être un nombre positif')

            cy.get('@input').clear().type('44')

            cy.get('@form-block')
                .eq(0)
                .find('[data-context=erp-input-helper]')
                .should('have.length', 1)
                .as('erp-input-helper')

            cy.get('@erp-input-helper').eq(0).should('contain', 'Id de template Mailjet')

            cy.get('@submit').should('not.be.disabled').click()

            cy.wait('@fetch_parameter').then((xhr) => {
                expect(xhr.request.body.operationName).to.eq('updateParameterByPk')
                expect(xhr.request.body.variables).to.deep.eq({
                    name: 'email.mailjet_template_id.confirm_customer_order',
                    value: '44',
                })
            })

            cy.toast('Les templates de mails ont bien été modifiés', 'success')
        })
    })

    describe('Displays without permission', () => {
        it('cannot make change on the form if no permission for it', () => {
            cy.intercept('POST', 'https://erp.graphql/v1/graphql', {
                fixture: 'graphql/administration/transactional_mail/fetch_parameters.json',
            }).as('fetch_parameters')
            cy.visit(PAGE)
            cy.toggleMenu()
            cy.wait('@fetch_parameters')

            cy.get('[data-context=erp-button]').should('contain', 'Sauvegarder').should('be.disabled')
        })
    })
})
