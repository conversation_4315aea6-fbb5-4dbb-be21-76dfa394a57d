import { SYSTEM_ADMINISTRATION_READ, SYSTEM_ADMINISTRATION_UPDATE } from '../../../../src/apps/erp/permissions.js'

describe('System parameters for abandoned cart', () => {
    const PAGE = '/administration/system/abandoned-cart'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Attempt to access page with an unauthorized user', () => {
        it('redirect to 403 page', () => {
            cy.visit(PAGE)
            cy.checkErrorCode(403)
        })
    })

    describe('The user only have read permission', () => {
        beforeEach(() => {
            cy.mockErpUser([SYSTEM_ADMINISTRATION_READ])
        })

        it('display the proper informational text', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=page-header]').should('contain', 'Administration')
            cy.get('[data-context=page-header]').should('contain', 'Relance paniers abandonnés')

            cy.get('[data-context=alert-content]')
                .eq(0)
                .should(
                    'contain',
                    'La relance des clients identifiés pour leurs paniers abandonnés se fait automatiquement par email tous les jours.',
                )

            cy.get('[data-context=alert-content]')
                .eq(0)
                .should(
                    'contain',
                    'Un seul email sera envoyé par client, lui proposant de terminer sa commande ou de nous contacter si il a besoin de conseils.',
                )

            cy.get('[data-context=section-title]').eq(0).should('contain', 'Règles')

            cy.get('[data-context=info-text]')
                .eq(0)
                .should('contain', "Pour qu'un client soit relancé automatiquement, il ne doit pas :")

            cy.get('[data-context=info-text]').eq(0).should('contain', 'Déjà avoir été relancé par email.')

            cy.get('[data-context=info-text]')
                .eq(0)
                .should(
                    'contain',
                    'Avoir passé de commande après la date de création de son panier (délai réglable ci-dessous).',
                )

            cy.get('[data-context=info-text]')
                .eq(0)
                .should('contain', 'Avoir reçu un devis après la date de son panier.')

            cy.get('[data-context=section-title]').eq(1).should('contain', 'Paramètres')
        })

        it('display the form with disabled inputs when data were not retrieved properly', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=erp-form-block]').eq(0).find('input').as('checked_input')

            cy.get('@checked_input').should('have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('be.empty')

            cy.get('[data-context=erp-form-block]').eq(1).find('input')

            cy.get('@checked_input').should('have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('be.empty')

            cy.get('[data-context=erp-form-block]').eq(2).find('input').as('checked_input')

            cy.get('@checked_input').should('have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('be.empty')

            cy.get('[data-context=erp-form-block]').eq(3).find('input').as('checked_input')

            cy.get('@checked_input').should('have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('be.empty')

            cy.get('[data-context=erp-form-block]').eq(4).find('input').as('checked_input')

            cy.get('@checked_input').should('have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('be.empty')

            cy.get('[data-context=form-submit-btn]').should('have.attr', 'disabled')
        })

        it('display the form in read only mode', () => {
            cy.intercept('GET', '**/api/erp/v1/system-administration/cms-parameters/abandoned_cart', {
                body: {
                    status: 'success',
                    data: [
                        {
                            name: 'abandoned_cart.created_at_threshold',
                            value: '2021-03-06 11:49:28.924329+00',
                            description: 'date a partir de laquelle un panier est éligible a la relance',
                        },
                        {
                            name: 'abandoned_cart.days_since_last_customer_order',
                            value: '50',
                            description: 'Temps écoulé, en jour, depuis la dernière commande avant relance',
                        },
                        {
                            name: 'abandoned_cart.hours_since_last_basket_update',
                            value: '32',
                            description:
                                'Temps écoulé, en heure, depuis la dernière mise à jour du panier avant relance',
                        },
                        {
                            name: 'abandoned_cart.total_price_max_amount_threshold',
                            value: '1005',
                            description: 'montant maximum du panier pour relance',
                        },
                        {
                            name: 'abandoned_cart.total_price_min_amount_threshold',
                            value: '5',
                            description: 'montant minimum du panier pour relance',
                        },
                        {
                            name: 'abandoned_cart.mailjet_template_id',
                            value: '1234567',
                            description: 'Id de template mailjet',
                        },
                    ],
                },
            }).as('api_cms_parameters_abandoned_cart_response')

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@api_cms_parameters_abandoned_cart_response')

            cy.get('[data-context=erp-form-block]').eq(0).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('have.value', '32')

            cy.get('[data-context=erp-form-block]').eq(1).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('have.value', '50')

            cy.get('[data-context=erp-form-block]').eq(2).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('have.value', '5')

            cy.get('[data-context=erp-form-block]').eq(3).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('have.value', '1005')

            cy.get('[data-context=erp-form-block]').eq(4).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('have.attr', 'readonly')
            cy.get('@checked_input').should('have.value', '1234567')

            cy.get('[data-context=form-submit-btn]').should('have.attr', 'disabled')
        })
    })

    describe('Administrate system parameters when the user have write permission', () => {
        beforeEach(() => {
            cy.mockErpUser([SYSTEM_ADMINISTRATION_READ, SYSTEM_ADMINISTRATION_UPDATE])

            cy.intercept('GET', '**/api/erp/v1/system-administration/cms-parameters/abandoned_cart', {
                body: {
                    status: 'success',
                    data: [
                        {
                            name: 'abandoned_cart.created_at_threshold',
                            value: '2021-03-06 11:49:28.924329+00',
                            description: 'date a partir de laquelle un panier est éligible a la relance',
                        },
                        {
                            name: 'abandoned_cart.days_since_last_customer_order',
                            value: '50',
                            description: 'Temps écoulé, en jour, depuis la dernière commande avant relance',
                        },
                        {
                            name: 'abandoned_cart.hours_since_last_basket_update',
                            value: '32',
                            description:
                                'Temps écoulé, en heure, depuis la dernière mise à jour du panier avant relance',
                        },
                        {
                            name: 'abandoned_cart.total_price_max_amount_threshold',
                            value: '1005',
                            description: 'montant maximum du panier pour relance',
                        },
                        {
                            name: 'abandoned_cart.total_price_min_amount_threshold',
                            value: '5',
                            description: 'montant minimum du panier pour relance',
                        },
                        {
                            name: 'abandoned_cart.mailjet_template_id',
                            value: '1234567',
                            description: 'Id de template mailjet',
                        },
                    ],
                },
            }).as('api_cms_parameters_abandoned_cart_response')

            cy.intercept('POST', '**/api/erp/v1/system-administration/cms-parameters', {
                body: {
                    status: 'success',
                    data: {
                        success: true,
                        updated: [
                            { name: 'abandoned_cart.days_since_last_customer_order', value: '50' },
                            { name: 'abandoned_cart.hours_since_last_basket_update', value: '32' },
                            { name: 'abandoned_cart.total_price_max_amount_threshold', value: '1005' },
                            { name: 'abandoned_cart.total_price_min_amount_threshold', value: '5' },
                        ],
                    },
                },
            }).as('api_post_cms_parameters_response')
        })

        it('display an editable form', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@api_cms_parameters_abandoned_cart_response')

            cy.get('[data-context=erp-form-block]').eq(0).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('not.have.attr', 'readonly')

            cy.get('[data-context=erp-form-block]').eq(1).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('not.have.attr', 'readonly')

            cy.get('[data-context=erp-form-block]').eq(2).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('not.have.attr', 'readonly')

            cy.get('[data-context=erp-form-block]').eq(3).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('not.have.attr', 'readonly')

            cy.get('[data-context=erp-form-block]').eq(4).find('input').as('checked_input')

            cy.get('@checked_input').should('not.have.attr', 'disabled')
            cy.get('@checked_input').should('not.have.attr', 'readonly')

            cy.get('[data-context=form-submit-btn]').should('not.have.attr', 'disabled')
        })

        it('validate inputs', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@api_cms_parameters_abandoned_cart_response')

            cy.get('[data-context=erp-form-block]').eq(0).find('input').clear()

            cy.get('[data-context=erp-form-block]').eq(1).find('input').clear()

            cy.get('[data-context=erp-form-block]').eq(2).find('input').clear()

            cy.get('[data-context=erp-form-block]').eq(3).find('input').clear()

            cy.get('[data-context=erp-form-block]').eq(4).find('input').clear()

            cy.get('[data-context=form-submit-btn]').click()

            cy.get('[data-context=error-message]').should(
                'contain',
                'Certaines erreurs empêchent la mise à jour des informations, veuillez les corriger.',
            )

            cy.get('[data-context=erp-form-block]').eq(0).as('checked_form_block')

            cy.get('@checked_form_block')
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Ce champ est obligatoire.')

            cy.get('[data-context=erp-form-block]').eq(1).as('checked_form_block')

            cy.get('@checked_form_block')
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Ce champ est obligatoire.')

            cy.get('[data-context=erp-form-block]').eq(2).as('checked_form_block')

            cy.get('@checked_form_block')
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Ce champ est obligatoire.')

            cy.get('[data-context=erp-form-block]').eq(3).as('checked_form_block')

            cy.get('@checked_form_block')
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Ce champ est obligatoire.')

            cy.get('[data-context=erp-form-block]').eq(4).as('checked_form_block')

            cy.get('@checked_form_block')
                .find('[data-context=erp-input-helper]')
                .should('contain', 'Ce champ est obligatoire.')
        })

        it('submit data for update successfully', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@api_cms_parameters_abandoned_cart_response')

            cy.get('[data-context=form-submit-btn]').click()
            cy.wait('@api_post_cms_parameters_response').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    parameters: [
                        { name: 'abandoned_cart.hours_since_last_basket_update', value: '32' },
                        { name: 'abandoned_cart.days_since_last_customer_order', value: '50' },
                        { name: 'abandoned_cart.total_price_min_amount_threshold', value: '5' },
                        { name: 'abandoned_cart.total_price_max_amount_threshold', value: '1005' },
                        { name: 'abandoned_cart.mailjet_template_id', value: '1234567' },
                    ],
                })
            })
        })
    })
})
