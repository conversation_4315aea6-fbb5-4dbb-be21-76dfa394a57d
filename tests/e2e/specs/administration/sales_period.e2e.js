import { SALES_PERIOD_WRITE } from '../../../../src/apps/erp/permissions'

describe('Administration - Sales period Manager', function () {
    const PAGE = '/administration/system/sales-period-manager'

    beforeEach(() => {
        cy.mockErpUser()

        cy.authenticate()
        cy.intercept('POST', '**/api/erp/v1/sales', {
            fixture: 'erp/sales/cpost_sales_limit_5.json',
        })
    })

    it('should display the last 5 sales period and the form ot create a new one should be read only', () => {
        cy.visit(PAGE)

        cy.get('[data-context=title]').should('contain', 'Gestion des périodes de soldes')

        cy.get('[data-context=sales-period-form]').should('be.visible').as('sales_period_panel')

        cy.get('@sales_period_panel').find('[data-context=erp-date-range-picker]').should('be.visible')

        cy.get('@sales_period_panel').find('[data-context=submit-btn]').should('be.visible').should('be.disabled')

        cy.get('[data-context=sales-periods] [data-context=erp-table]').as('sales_period_table').should('be.visible')

        cy.get('@sales_period_table').find('thead tr th').as('header')

        cy.get('@header').should('have.length', 3)

        cy.get('@header').eq(0).should('contain', '#')

        cy.get('@sales_period_table').find('[data-context=table-row]').should('have.length', 5).eq(0).as('first_row')

        cy.get('@first_row').find('[data-context=cell-start-at]').should('contain', '15/02/2022 à 00:00')

        cy.get('@first_row').find('[data-context=cell-end-at]').should('contain', '16/02/2022 à 23:59')
    })

    it('should be able to create a new sales period', () => {
        // freeze clock
        cy.mockDate(new Date(2022, 4, 1, 11, 40, 0, 0))
        cy.mockErpUser([SALES_PERIOD_WRITE])

        cy.visit(PAGE)

        cy.get('[data-context=title]').should('contain', 'Gestion des périodes de soldes')

        cy.get('[data-context=sales-period-form]').should('be.visible').as('sales_period_panel')

        cy.intercept('POST', '**/api/erp/v1/sales-period', {
            statusCode: 200,
            body: {
                status: 'success',
                data: { result: 1 },
            },
        }).as('post_sales_period')

        cy.get('@sales_period_panel').find('[data-context=erp-date-range-picker]').erpDateRangePicker(3, 10)
        cy.get('@sales_period_panel').find('[data-context=submit-btn]').click({ force: true })

        cy.wait('@post_sales_period').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({
                start_at: '2022-05-03 08:00:00',
                end_at: '2022-05-10 23:59:59',
            })
        })
    })
})
