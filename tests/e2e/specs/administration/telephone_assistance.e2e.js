import { TELEPHONE_ASSISTANCE_UPDATE } from '../../../../src/apps/erp/permissions.js'

describe('System parameters for telephone assistance', () => {
    const PAGE = '/administration/system/telephone-assistance'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Displays with permission ', () => {
        beforeEach(() => {
            cy.mockErpUser([TELEPHONE_ASSISTANCE_UPDATE])
            cy.intercept('POST', 'https://erp.graphql/v1/graphql', {
                fixture: 'graphql/cms/system/fetch_cms_parameters.json',
            }).as('fetch_cms_parameters')
        })
        it('can fill and save the form', () => {
            cy.visit(PAGE)
            cy.toggleMenu()
            cy.wait('@fetch_cms_parameters')

            cy.get('[data-context=title]').should('contain', 'Horaires accueil téléphonique')
            cy.get('[data-context=info-text]').should(
                'contain',
                "Pour information, il n'y a pas besoin de modifier les horaires pour les jours fériés, ceux-ci sont gérés automatiquement.",
            )
            cy.get('[data-context=section-title]').eq(0).should('contain', 'Gestion des horaires')

            // form
            cy.get('[data-context=days]').should('be.visible').should('have.length', 7).as('days')
            cy.get('@days').eq(0).as('first_day').find('label').should('contain', 'Lundi')
            cy.get('@first_day').find('[data-context=slot]').should('have.length', 2).eq(0).as('first_slot')

            cy.get('@first_slot').find('[data-context=start-hour]').should('have.value', '9')
            cy.get('@first_slot').find('[data-context=start-minute]').should('have.value', '00')
            cy.get('@first_slot').find('[data-context=end-hour]').should('have.value', '13')
            cy.get('@first_slot').find('[data-context=end-minute]').should('have.value', '00')
            cy.get('@first_slot').find('[data-context=remove]').should('be.visible')
            cy.get('@first_slot').find('[data-context=add]').should('not.exist')

            cy.get('[data-context=form-submit-btn]').should('contain', 'Sauvegarder')
        })

        it('can remove or add a new slot', () => {
            cy.visit(PAGE)
            cy.toggleMenu()
            cy.wait('@fetch_cms_parameters')

            cy.get('[data-context=days]')
                .eq(0)
                .find('[data-context=slot]')
                .should('have.length', 2)
                .eq(0)
                .as('first_slot')
            cy.get('@first_slot').find('[data-context=add]').should('not.exist')
            cy.get('@first_slot').find('[data-context=remove]').should('be.visible').click()

            // remove the first slot
            cy.get('[data-context=days]')
                .eq(0)
                .find('[data-context=slot]')
                .should('have.length', 1)
                .eq(0)
                .as('first_slot')

            cy.get('@first_slot').find('[data-context=add]').should('be.visible')
            cy.get('@first_slot').find('[data-context=remove]').should('be.visible').click()

            cy.get('[data-context=days]').eq(0).find('[data-context=slot]').should('have.length', 0)

            // Creating a new empty slot
            cy.get('[data-context=days]').eq(0).find('[data-context=add]').click()

            cy.get('[data-context=days]')
                .eq(0)
                .find('[data-context=slot]')
                .should('have.length', 1)
                .eq(0)
                .as('new_slot')
            cy.get('@new_slot').find('[data-context=start-hour]').should('have.value', '')
            cy.get('@new_slot').find('[data-context=start-minute]').should('have.value', '')
            cy.get('@new_slot').find('[data-context=end-hour]').should('have.value', '')
            cy.get('@new_slot').find('[data-context=end-minute]').should('have.value', '')
            cy.get('@new_slot').find('[data-context=remove]').should('be.visible')
            cy.get('@new_slot').find('[data-context=add]').should('not.exist')

            // fill this new slot
            cy.get('@new_slot').find('[data-context=start-hour]').type('19')
            cy.get('@new_slot').find('[data-context=start-minute]').type('21')
            cy.get('@new_slot').find('[data-context=end-hour]').type('19')
            cy.get('@new_slot').find('[data-context=end-minute]').type('21')
            cy.get('@new_slot').find('[data-context=remove]').should('be.visible')
            cy.get('@new_slot').find('[data-context=add]').should('be.visible')
        })

        it('can edit informational texts', () => {
            cy.visit(PAGE)
            cy.toggleMenu()
            cy.wait('@fetch_cms_parameters')

            // check infos
            cy.get('[data-context=hours-short-label]').should('contain', 'Texte information bandeau haut')
            cy.get('[data-context=hours-short]').as('hours-short')
            cy.get('@hours-short').should('have.value', 'Du lundi au samedi, 9h-13h et 14h-18h')

            cy.get('[data-context=hours-label]').should('contain', 'Texte information Service Client')
            cy.get('[data-context=hours]').as('hours')
            cy.get('@hours').should('have.value', 'Du lundi au samedi\nde 9h à 13h et de 14h à 18h')

            // can save
            cy.get('[data-context=form-submit-btn]').as('submit-btn')
            cy.get('@submit-btn').should('contain', 'Sauvegarder').should('not.be.disabled')

            cy.get('@hours-short').clear()
            cy.get('@hours').clear()
            cy.get('@hours').type('Du lundi au vendredi de 9h à 13h et de 14h à 18h')

            // can't save
            cy.get('@submit-btn').click()

            cy.get('[data-context=error-hours-short][data-invalid]').should('contain', 'Ce champ est obligatoire.')
            cy.get('[data-context=error-hours][data-invalid]').should(
                'contain',
                'Ce champ est obligatoire et doit avoir un saut de ligne.',
            )

            cy.get('@hours-short').type('Du lundi au vendredi, 9h-13h et 14h-18h')

            cy.get('@hours').clear()
            cy.get('@hours').type('Du lundi au vendredi\nde 9h à 13h et de 14h à 18h')

            // can save
            cy.get('@submit-btn').click()
        })
    })

    describe('Displays without permission', () => {
        it('cannot make change on the form if no permission for it', () => {
            cy.intercept('POST', 'https://erp.graphql/v1/graphql', {
                fixture: 'graphql/cms/system/fetch_cms_parameters.json',
            }).as('fetch_cms_parameters')
            cy.visit(PAGE)
            cy.toggleMenu()
            cy.wait('@fetch_cms_parameters')

            cy.get('[data-context=form-submit-btn]').should('contain', 'Sauvegarder').should('be.disabled')
        })
    })
})
