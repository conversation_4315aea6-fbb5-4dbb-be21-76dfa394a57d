import { ARTICLE_GENERAL_INFORMATION_WRITE } from '../../../../src/apps/erp/permissions.js'

describe('System parameters for marketplace', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/sales-channel', {
            fixture: 'erp/sales-channel/cpost_sales_channels',
        }).as('cpost_sales_channels')
    })

    const visitPage = () => {
        cy.visit('/administration/system/marketplace-configuration')

        cy.wait('@cpost_sales_channels').then((xhr) => {
            expect(xhr.request.body.where.sales_channel_id._neq).to.eq(1)
        })
    }

    it('Displays without permission', () => {
        cy.mockErpUser()
        visitPage()

        cy.get('[data-context=page-header]').should('contain', 'Configuration des marketplaces ')

        cy.get('[data-context=erp-table] ').as('table').should('be.visible')
        cy.get('@table').find('tbody tr').as('rows')
        cy.get('@rows').should('have.length', 11)

        let row_id = 0
        let cell_id = 0
        // row 1
        cy.get('@rows').eq(row_id++).find('td').as('row')
        cy.get('@row').eq(cell_id++).should('contain', 'easylounge.com')
        cy.get('@row').eq(cell_id++).find('[data-context=erp-input]').should('be.empty').should('be.disabled')
        cy.get('@row').eq(cell_id++).find('[data-context=erp-input]').should('be.empty').should('be.disabled')
        cy.get('@row')
            .eq(cell_id++)
            .find('[data-context=erp-input-number]')
            .should('have.value', '0')
            .should('be.disabled')
        cy.get('@row').eq(cell_id++).find('[data-context=erp-button]').should('be.disabled')

        cell_id = 0
        // row 2
        cy.get('@rows').eq(row_id++).find('td').as('row')
        cy.get('@row').eq(cell_id++).should('contain', 'amazon.de')
        cy.get('@row').eq(cell_id++).find('[data-context=erp-input]').should('have.value', '11').should('be.disabled')
        cy.get('@row').eq(cell_id++).find('[data-context=erp-input]').should('have.value', '11').should('be.disabled')
        cy.get('@row')
            .eq(cell_id++)
            .find('[data-context=erp-input-number]')
            .should('have.value', '11')
            .should('be.disabled')
        cy.get('@row').eq(cell_id++).find('[data-context=erp-button]').should('be.disabled')
    })

    it('Displays with permission', () => {
        cy.mockErpUser([ARTICLE_GENERAL_INFORMATION_WRITE])
        visitPage()

        cy.get('[data-context=erp-table] ').find('tbody tr').as('rows')

        let row_id = 2
        let cell_id = 0
        // row 1
        cy.get('@rows').eq(row_id).find('td').as('row')
        cy.get('@row').eq(cell_id++).should('contain', 'amazon.es')
        cy.get('@row')
            .eq(cell_id++)
            .find('[data-context=erp-input]')
            .should('have.value', '4')
            .should('be.not.disabled')
        cy.get('@row')
            .eq(cell_id++)
            .find('[data-context=erp-input]')
            .should('have.value', '12')
            .should('be.not.disabled')
        cy.get('@row')
            .eq(cell_id++)
            .find('[data-context=erp-input-number]')
            .should('have.value', '0')
            .should('be.not.disabled')
        cy.get('@row').eq(cell_id++).find('[data-context=erp-button]').should('be.not.disabled')
    })

    it('Update marketplace parameters', () => {
        cy.intercept('PUT', '**/erp/v1/sales-channel/3', {
            statusCode: 200,
        }).as('put_sales_channel')

        cy.mockErpUser([ARTICLE_GENERAL_INFORMATION_WRITE])
        visitPage()

        cy.get('[data-context=erp-table] ').find('tbody tr').as('rows')

        let row_id = 3
        let cell_id = 0
        // row 1
        cy.get('@rows').eq(row_id).find('td').as('row')
        cy.get('@row').eq(cell_id++).should('contain', 'amazon.fr')
        cy.get('@row').eq(cell_id++).find('[data-context=erp-input]').clear().type(11)
        cy.get('@row').eq(cell_id++).find('[data-context=erp-input]').clear().type(22)
        cy.get('@row').eq(cell_id++).find('[data-context=erp-input-number]').clear().type(33)
        cy.get('@row').eq(cell_id++).find('[data-context=erp-button]').click()

        cy.wait('@put_sales_channel').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({
                sales_channel_id: 3,
                average_commission_rate: 11,
                minimum_margin_rate: 22,
                minimum_available_quantity: 33,
            })
        })

        cy.toast(`les modifications ont été enregistrées avec succès`, 'success')
        cy.wait('@cpost_sales_channels')
    })
})
