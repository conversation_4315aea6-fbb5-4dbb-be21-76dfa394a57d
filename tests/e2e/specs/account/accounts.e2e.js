import { aliasQuery, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../../utils/graphql-test-utils'

describe('Accounts', () => {
    const PAGE = '/accounts'
    const FETCH_QUERY = 'fetchAccounts'
    const FETCH_SITES = 'fetchSites'

    beforeEach(() => {
        cy.authenticate()
        cy.intercept(
            {
                method: 'POST',
                url: GRAPHQL_ENDPOINT,
            },
            (req) => {
                if (hasOperationName(req, FETCH_QUERY)) {
                    aliasQuery(req, FETCH_QUERY)

                    req.reply({
                        fixture: `graphql/${FETCH_QUERY}`,
                    })
                }
                if (hasOperationName(req, FETCH_SITES)) {
                    aliasQuery(req, FETCH_SITES)

                    req.reply({
                        fixture: `graphql/${FETCH_SITES}`,
                    })
                }
            },
        )
    })

    function visit() {
        cy.mockErpUser()
        cy.visit(PAGE)
        cy.toggleMenu()
        cy.closeAllToasts()
        cy.get('[data-context=erp-table]').as('table').should('be.visible')
    }

    describe('Dashboard', () => {
        it('should show a table with an overview of the data', () => {
            visit()

            cy.wait([getQueryAlias(FETCH_QUERY)])
            // Check table headers

            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 7)
            ;['Compte', `Nom d'utilisateur`, 'Poste', 'Statut', 'Actions', 'Site / Magasin', 'Téléphone'].forEach(
                (text, idx) => {
                    cy.get('@header').eq(idx).should('contain', text)
                },
            )

            // Check one row

            cy.get('@table').selectCell(3, 0)
            cy.get('@cell').should('contain', 'Nicolas Dechir')
            cy.get('@cell').should('contain', '<EMAIL>')

            cy.get('@table').selectCell(3, 1)
            cy.get('@cell').should('contain', 'nicolas.dechir')

            cy.get('@table').selectCell(3, 2)
            cy.get('@cell').should('contain', 'Téléconseiller vendeur')

            cy.get('@table').selectCell(3, 3)
            cy.get('@cell').should('contain', 'Actif')

            cy.get('@table').selectCell(3, 4)
            cy.get('@cell')
                .find('a')
                .should('have.length', 1)
                .should('attr', 'href')
                .and('match', /.*\/accounts\/.*\/permissions\/*/)

            cy.get('@table').selectCell(3, 4)
            cy.get('@cell').find('button').should('have.length', 1)
            // tests on the button are done in another file

            cy.get('@table').selectCell(3, 5)
            cy.get('@cell').should('contain', 'Champigny')

            cy.get('@table').selectCell(3, 6)
            cy.get('@cell').should('contain', 'Poste : 1679')
        })

        it('should show some filters above the table & filter results accordingly', () => {
            visit()

            // Check default payload
            cy.get('button:contains(Rechercher)').click()

            cy.location().should((loc) => {
                expect(loc.search).to.not.contain('?pager')
            })

            cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
                expect(xhr.request.body.variables.where).to.deep.eq({
                    _and: [
                        {
                            is_active: {
                                _eq: true,
                            },
                        },
                    ],
                })
            })

            // Change filters
            cy.get('label:contains(Utilisateur)').click()
            cy.get('label:contains(Utilisateur)').type('  toto  ')
            cy.get('label:contains(Téléphone)').click()
            cy.get('label:contains(Téléphone)').type('  2043  ')
            cy.get('label:contains(Statut)').click()
            cy.get('[data-context="suggestion"]').eq(2).click()

            cy.get('button:contains(Rechercher)').click()

            cy.location().should((loc) => {
                expect(loc.search).to.contain('?pager')
            })

            cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
                expect(xhr.request.body.variables.where).to.deep.eq({
                    _and: [
                        {
                            is_active: {
                                _eq: false,
                            },
                        },
                        {
                            _or: [
                                {
                                    information: {
                                        name: {
                                            _ilike: `%toto%`,
                                        },
                                    },
                                },
                                {
                                    username: {
                                        _ilike: `%toto%`,
                                    },
                                },
                            ],
                        },
                        {
                            information: {
                                phone_for_search: {
                                    _ilike: `%2043%`,
                                },
                            },
                        },
                    ],
                })
            })
        })
        it('should filter the results with the "site/magasin" filter', () => {
            visit()

            // Check default payload
            cy.get('button:contains(Rechercher)').click()

            cy.location().should((loc) => {
                expect(loc.search).to.not.contain('?pager')
            })

            cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
                expect(xhr.request.body.variables.where).to.deep.eq({
                    _and: [
                        {
                            is_active: {
                                _eq: true,
                            },
                        },
                    ],
                })
            })

            cy.wait(getQueryAlias(FETCH_SITES))

            // Change filters
            cy.get('label:contains(Site / Magasin)').click()
            cy.get('[data-context="suggestion"]').eq(1).click()

            cy.get('button:contains(Rechercher)').click()

            cy.location().should((loc) => {
                expect(loc.search).to.contain('?pager')
            })

            cy.wait(getQueryAlias(FETCH_QUERY)).then((xhr) => {
                expect(xhr.request.body.variables.where).to.deep.eq({
                    _and: [
                        {
                            is_active: {
                                _eq: true,
                            },
                        },
                        {
                            site_id: {
                                _eq: '70d5fc37-a199-4d37-9fc3-d8aeef638c26',
                            },
                        },
                    ],
                })
            })
        })
    })
})
