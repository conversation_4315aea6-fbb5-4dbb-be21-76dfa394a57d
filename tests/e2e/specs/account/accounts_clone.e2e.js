import { aliasQuery, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../../utils/graphql-test-utils'
import { HAL } from '../../../../src/shared/permissions'

const { USER_ACCOUNT_ADMINISTRATE } = HAL
describe('Accounts', () => {
    const PAGE = '/accounts'
    const FETCH_QUERY = 'fetchAccounts'
    const VALIDATION_QUERY = 'validateAccount'
    const UPSERT_MUTATION = 'upsertAccount'

    beforeEach(() => {
        cy.authenticate()

        cy.intercept(
            {
                method: 'POST',
                url: GRAPHQL_ENDPOINT,
            },
            (req) => {
                if (hasOperationName(req, FETCH_QUERY)) {
                    aliasQuery(req, FETCH_QUERY)

                    req.reply({
                        fixture: `graphql/${FETCH_QUERY}`,
                    })
                }
            },
        )
    })

    function visit(permissions = []) {
        cy.mockErpUser(permissions)
        cy.visit(PAGE)
        cy.toggleMenu()

        cy.wait([getQueryAlias(FETCH_QUERY)])
        cy.get('[data-context=erp-table]').as('table').should('be.visible')

        cy.get('@table').selectCell(3, 4).find('button').should('have.length', 1).eq(0).as('button')
    }

    describe('Without administration permission', () => {
        it('should show the clone button but not allow to click it', () => {
            visit()

            cy.get('@button')
                .should('be.disabled')
                .parent()
                .tooltip(`Vous n'avez pas la permission de cloner un utilisateur`)
        })
    })

    describe('With administration permission', () => {
        it('Validate new username and email', () => {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, VALIDATION_QUERY)) {
                    aliasQuery(req, VALIDATION_QUERY)

                    req.reply({
                        data: {
                            username: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 1,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                            email: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 1,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                        },
                    })
                }
            })

            visit([USER_ACCOUNT_ADMINISTRATE])

            cy.get('@button').should('not.be.disabled').parent().tooltip(`Cloner l'utilisateur`)
            cy.get('@button').click()

            cy.get('button[type=submit]:contains(Créer)').click()

            // First local validation
            cy.get(`label:contains(Email)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('contain', 'Ce champ est obligatoire.')

            cy.get(`label:contains(Identifiant de connexion)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('contain', 'Ce champ est obligatoire.')

            // Second local validation
            cy.get(`label:contains(Email)`).siblings('input').type('foobar')

            cy.get(`label:contains(Email)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('contain', 'Adresse email invalide.')

            cy.get(`label:contains(Identifiant de connexion)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('not.exist')

            cy.get(`label:contains(Identifiant de connexion)`)
                .siblings('[data-context=erp-input]')
                .should('have.value', 'foobar')
            cy.get(`label:contains(Prénom)`).siblings('[data-context=erp-input]').should('have.value', 'Foobar')
            cy.get(`label:contains(Nom)`).siblings('[data-context=erp-input]').should('be.empty')

            // Third local validation
            cy.get(`label:contains(Email)`).siblings('input').clear()
            cy.get(`label:contains(Email)`).siblings('input').type('foo.bar')

            cy.get(`label:contains(Email)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('contain', 'Adresse email invalide.')

            cy.get(`label:contains(Identifiant de connexion)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('not.exist')

            cy.get(`label:contains(Identifiant de connexion)`)
                .siblings('[data-context=erp-input]')
                .should('have.value', 'foo.bar')
            cy.get(`label:contains(Prénom)`).siblings('[data-context=erp-input]').should('have.value', 'Foo')
            cy.get(`label:contains(Nom)`).siblings('[data-context=erp-input]').should('have.value', 'Bar')

            // Server validation
            cy.get(`label:contains(Email)`).siblings('input').clear()
            cy.get(`label:contains(Email)`).siblings('input').type('<EMAIL>')
            cy.get(`label:contains(Email)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('not.exist')
            cy.get(`label:contains(Identifiant de connexion)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('not.exist')

            cy.get('button[type=submit]:contains(Créer)').click()

            cy.wait(getQueryAlias(VALIDATION_QUERY)).then((xhr) => {
                expect(xhr.request.body.variables).to.deep.eq({
                    username: { username: { _eq: 'foo.bar' } },
                    email: { email: { _eq: '<EMAIL>' } },
                })
            })

            cy.get(`label:contains(Email)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('contain', `Cet email est déjà attaché à un autre compte utilisateur`)

            cy.get(`label:contains(Identifiant de connexion)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('contain', `Ce nom d'utilisateur est déjà attaché à un autre compte utilisateur`)

            // Passes validation
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, VALIDATION_QUERY)) {
                    aliasQuery(req, VALIDATION_QUERY)

                    req.reply({
                        data: {
                            username: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 0,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                            email: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 0,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                        },
                    })
                }
            })

            cy.get(`label:contains(Email)`).siblings('input').clear()
            cy.get(`label:contains(Email)`).siblings('input').type('<EMAIL>')

            cy.get('button[type=submit]:contains(Créer)').click()
            cy.wait(getQueryAlias(VALIDATION_QUERY))
            cy.get(`label:contains(Email)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('not.exist')

            cy.get(`label:contains(Identifiant de connexion)`)
                .siblings('[data-context=erp-input-helper][data-invalid]')
                .should('not.exist')
        })

        it('should create a new user and redirect to its profile', () => {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, UPSERT_MUTATION)) {
                    aliasQuery(req, UPSERT_MUTATION)

                    req.reply({
                        data: {
                            insert_account_account_information_one: {
                                account_id: '123e4567-e89b-12d3-a456-************',
                                preferences: [],
                                account: {
                                    email: 'not.sure', // irrelevant
                                    username: '<EMAIL>', // irrelevant
                                    is_active: true,
                                    __typename: 'account_account',
                                },
                                __typename: 'account_account_information',
                            },
                        },
                    })
                }

                if (hasOperationName(req, VALIDATION_QUERY)) {
                    aliasQuery(req, VALIDATION_QUERY)

                    req.reply({
                        data: {
                            username: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 0,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                            email: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 0,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                        },
                    })
                }
            })
            visit([USER_ACCOUNT_ADMINISTRATE])

            cy.get('@button').should('not.be.disabled').parent().tooltip(`Cloner l'utilisateur`)
            cy.get('@button').click()

            cy.get(`label:contains(Email)`).siblings('input').type('<EMAIL>')
            cy.get('button[type=submit]:contains(Créer)').click()

            cy.wait(getQueryAlias(VALIDATION_QUERY))
            cy.wait(getQueryAlias(UPSERT_MUTATION)).then((xhr) => {
                expect(xhr.request.body.variables).to.deep.eq({
                    data: {
                        title: 'Mr',
                        firstname: 'First',
                        lastname: 'Second',
                        preferences: {
                            cloned_from: 'c3ee8c51-c4b5-4e7f-93c6-6027b81e819f',
                        },
                        account: {
                            data: {
                                username: 'first.second',
                                email: '<EMAIL>',
                            },
                        },
                    },
                    update_columns: ['title', 'firstname', 'lastname', 'preferences'],
                })
            })

            // redirect
            cy.url().should('not.eq', Cypress.config().baseUrl + PAGE)
            cy.url().should('eq', Cypress.config().baseUrl + PAGE + '/123e4567-e89b-12d3-a456-************/profile')
        })
    })
})
