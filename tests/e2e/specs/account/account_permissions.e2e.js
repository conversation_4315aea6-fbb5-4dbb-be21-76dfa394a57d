import { aliasQuery, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../../utils/graphql-test-utils'
import { HAL } from '../../../../src/shared/permissions'

const { USER_ACCOUNT_ADMINISTRATE } = HAL

describe('User account - permissions', () => {
    const PAGE = '/accounts/{account_id}/permissions/'
    const FETCH_QUERY = 'fetchAccount'
    const FETCH_PERMISSIONS_QUERY = 'fetchOwnerPermissions'
    const FETCH_ACCOUNTS_QUERY = 'fetchAccounts'
    const USER_1 = '123e4567-e89b-12d3-a456-************'

    function visit(account_id, permissions = []) {
        cy.mockErpUser(permissions)
        cy.visit(PAGE.replace('{account_id}', account_id))
        cy.toggleMenu()

        cy.wait([getQueryAlias(FETCH_QUERY)])
    }

    function mockFetchQuery(account_id) {
        cy.fixture(`graphql/${FETCH_QUERY}`).then((payload) => {
            payload.data.account_account[0].account_id = account_id

            cy.intercept(
                {
                    method: 'POST',
                    url: GRAPHQL_ENDPOINT,
                },
                (req) => {
                    if (hasOperationName(req, FETCH_QUERY)) {
                        aliasQuery(req, FETCH_QUERY)

                        req.reply({
                            body: payload,
                        })
                    }

                    if (hasOperationName(req, FETCH_PERMISSIONS_QUERY)) {
                        aliasQuery(req, FETCH_PERMISSIONS_QUERY)

                        req.reply({
                            data: {
                                permission_owner: [],
                            },
                        })
                    }
                },
            )
        })
    }

    beforeEach(() => {
        cy.authenticate()
    })

    describe('User can view its permission', () => {
        it('should view its permission', () => {
            mockFetchQuery(USER_1)
            visit(USER_1)

            cy.wait(getQueryAlias(FETCH_PERMISSIONS_QUERY)).then((xhr) => {
                expect(xhr.request.body.variables).to.deep.eq({
                    limit: 10,
                    where: {
                        owner_id: {
                            _eq: USER_1,
                        },
                    },
                    order_permissions: [
                        {
                            permission_id: 'asc',
                        },
                    ],
                })
            })

            // No additional tests as the component is thoroughly tested in shared/components/misc/ErpPermissionsEditor.cy.js
        })
    })

    describe('Without permission', () => {
        it('should not be able to copy the user permissions', () => {
            mockFetchQuery(USER_1)
            visit(USER_1)

            cy.get('[data-context=btn-open-copy-permissions]').should('be.disabled')
            cy.get('[data-context=btn-open-copy-permissions]')
                .parent()
                .tooltip(`Vous n'avez pas la permission de copier les permissions`)
        })
    })

    describe('With administration permission', () => {
        it('should be able to copy the user permissions', () => {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_ACCOUNTS_QUERY)) {
                    aliasQuery(req, FETCH_ACCOUNTS_QUERY)

                    req.reply({
                        fixture: `graphql/${FETCH_ACCOUNTS_QUERY}`,
                    })
                }
            })

            mockFetchQuery(USER_1)
            visit(USER_1, [USER_ACCOUNT_ADMINISTRATE])

            cy.get('[data-context=btn-open-copy-permissions]').should('not.be.disabled').click()
            cy.get('[data-context=slide-out-container]').should('contain', `Sélectionner l’utilisateur cible`)
            cy.get('[data-context=slide-out-container]').should(
                'contain',
                `Les permissions et roles existants seront supprimés.`,
            )

            cy.get('[data-context=slide-out-container] button[type=submit]:contains(Valider)').should('be.disabled')

            cy.get('[data-context=erp-multiselect]').click()
            cy.get('[data-context=erp-multiselect] [data-context=dropdown]').should('be.visible')
            cy.get('[data-context=erp-multiselect] [data-context=input]').type('foo')

            // Check what is sent to the server for the autocomplete
            cy.wait(getQueryAlias(FETCH_ACCOUNTS_QUERY)).then((xhr) => {
                expect(xhr.request.body.variables).to.deep.eq({
                    limit: 10,
                    order_by: { username: 'asc' },
                    where: {
                        _and: [
                            {
                                _or: [
                                    {
                                        information: {
                                            name: {
                                                _iregex: 'foo',
                                            },
                                        },
                                    },
                                    {
                                        username: {
                                            _iregex: 'foo',
                                        },
                                    },
                                    {
                                        email: {
                                            _iregex: 'foo',
                                        },
                                    },
                                ],
                            },
                            {
                                account_id: {
                                    _nin: ['123e4567-e89b-12d3-a456-************'],
                                },
                            },
                        ],
                    },
                })
            })

            // Select first result
            cy.get('[data-context="suggestion"]').eq(0).click()

            // We should see the selected user
            cy.get('[data-context=erp-multiselect]').should('contain', 'Nicolas Ange')

            cy.intercept(
                'PUT',
                '**/api/hal/v1/account/123e4567-e89b-12d3-a456-************/copy-permissions-and-roles-to/e5beb222-278c-4a06-b6d1-30cb72113c24\n',
                {
                    statusCode: 200,
                    body: true,
                },
            ).as('copy')

            cy.get('[data-context=slide-out-container] button[type=submit]:contains(Valider)')
                .should('not.be.disabled')
                .click()

            // No payload in the PUT call
            cy.wait('@copy')

            cy.get('[data-context=slide-out-container]').should('not.exist')
        })
    })
})
