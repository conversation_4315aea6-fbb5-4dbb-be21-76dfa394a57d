import { aliasQuery, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../../utils/graphql-test-utils'
import { HAL } from '../../../../src/shared/permissions'

const { USER_ACCOUNT_ADMINISTRATE } = HAL

describe('User account - permissions', () => {
    const PAGE = '/accounts/{account_id}/applications'
    const FETCH_QUERY = 'fetchAccount'
    const USER_1 = '123e4567-e89b-12d3-a456-************'

    function visit(account_id, permissions = []) {
        cy.mockErpUser(permissions)
        cy.visit(PAGE.replace('{account_id}', account_id))
        cy.toggleMenu()

        cy.wait([getQueryAlias(FETCH_QUERY)])
    }

    function mockFetchQuery(account_id) {
        cy.fixture(`graphql/${FETCH_QUERY}`).then((payload) => {
            payload.data.account_account[0].account_id = account_id

            cy.intercept(
                {
                    method: 'POST',
                    url: GRAPHQL_ENDPOINT,
                },
                (req) => {
                    if (hasOperationName(req, FETCH_QUERY)) {
                        aliasQuery(req, FETCH_QUERY)

                        req.reply({
                            body: payload,
                        })
                    }
                },
            )
        })

        cy.intercept('GET', `**/api/hal/v1/applications/${account_id}`, {
            statusCode: 200,
            body: {
                status: 'success',
                data: {
                    applications: [
                        {
                            name: 'app1',
                            base_url: 'https://app1',
                            preferences: null,
                            priority: 4,
                            account_id: 'de374459-6668-4a4c-a2ea-a441ddf5fcc3',
                            created_at: { date: '2019-12-11 15:56:14.828000', timezone_type: 1, timezone: '+01:00' },
                            profile: 'ROLE_USER',
                        },
                        {
                            name: 'app2',
                            base_url: 'http://app2',
                            preferences: { has_menu: false, has_permission: false },
                            priority: 99,
                            account_id: null,
                            created_at: null,
                            profile: null,
                        },
                    ],
                },
            },
        }).as('get_applications')
    }

    beforeEach(() => {
        cy.authenticate()
    })

    describe('Without permission', () => {
        it('should not be able to copy the user permissions', () => {
            mockFetchQuery(USER_1)
            visit(USER_1)

            cy.get('[data-context=erp-toggle]').should('have.length', 2).should('have.attr', 'data-disabled')
        })
    })

    describe('With administration permission', () => {
        it('should be able to activate an applications', () => {
            mockFetchQuery(USER_1)
            visit(USER_1, [USER_ACCOUNT_ADMINISTRATE])

            cy.intercept('POST', `**/api/hal/v1/application/${USER_1}/authorize/app2`, {
                statusCode: 200,
                body: true,
            }).as('activate')

            cy.get('[data-context=erp-toggle]').should('have.length', 2)
            // Checked element are still not clickable
            cy.get('[data-context=erp-toggle]').eq(0).should('have.attr', 'data-disabled')

            // The other can be activated
            cy.get('[data-context=erp-toggle]').eq(1).should('not.have.attr', 'data-disabled')
            cy.get('[data-context=erp-toggle]').eq(1).find('button').click()

            // should be called
            cy.wait(['@activate', '@get_applications'])

            // We don't really care about the result as we know how the toggle behave from the tests above
            // And we really don't want to test our mocked data
        })
    })
})
