import { aliasQuery, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../../utils/graphql-test-utils'
import { HAL } from '../../../../src/shared/permissions'

const { USER_ACCOUNT_ADMINISTRATE } = HAL

describe('User account - change password', () => {
    const PAGE = '/accounts/{account_id}/password-change/'
    const FETCH_QUERY = 'fetchAccount'

    const USER_1 = '123e4567-e89b-12d3-a456-************'
    const USER_2 = '123e4567-e89b-12d3-a456-************'

    function visit(account_id, permissions = []) {
        cy.mockErpUser(permissions)
        cy.visit(PAGE.replace('{account_id}', account_id))
        cy.toggleMenu()

        cy.wait([getQueryAlias(FETCH_QUERY)])
    }

    function mockFetchQuery(account_id) {
        cy.fixture(`graphql/${FETCH_QUERY}`).then((payload) => {
            payload.data.account_account[0].account_id = account_id

            cy.intercept(
                {
                    method: 'POST',
                    url: GRAPHQL_ENDPOINT,
                },
                (req) => {
                    if (hasOperationName(req, FETCH_QUERY)) {
                        aliasQuery(req, FETCH_QUERY)

                        req.reply({
                            body: payload,
                        })
                    }
                },
            )
        })
    }

    beforeEach(() => {
        cy.authenticate()
    })

    const form_fields = [
        { label: 'label:contains(Mot de passe actuel)' },
        { label: 'label:contains(Nouveau mot de passe)' },
        { label: 'label:contains(Répéter le nouveau mot de passe)' },
    ]

    describe('User with no specific permission in its own account', () => {
        it('should show an editable form', () => {
            mockFetchQuery(USER_1)
            visit(USER_1)

            form_fields.forEach((field) => {
                cy.get(field.label).siblings('input').should('not.have.attr', 'disabled')
            })
        })
    })

    describe('User with no specific permission when viewing another account', () => {
        it('should show a readonly form', () => {
            mockFetchQuery(USER_2)
            visit(USER_2)

            form_fields.forEach((field) => {
                cy.get(field.label).siblings('input').should('have.attr', 'disabled')
            })
        })
    })

    describe('Check form behaviour', () => {
        it('should have some validation rules', () => {
            mockFetchQuery(USER_1)
            visit(USER_1, [USER_ACCOUNT_ADMINISTRATE])

            form_fields.forEach((field) => {
                cy.get(field.label).eq(0).siblings('input').clear()
            })

            // Apply validation rules
            cy.get('button:contains(Modifier le mot de passe)').click()

            cy.get('[data-context=erp-input-helper][data-invalid]').should('have.length', 4)

            form_fields.forEach((field) => {
                cy.get(field.label)
                    .siblings('[data-context=erp-input-helper]')
                    .should('contain', 'Ce champ est obligatoire.')
            })

            form_fields.forEach((field) => {
                cy.get(field.label).siblings('input').type('toto')
            })

            cy.get('[data-context=erp-input-helper][data-invalid]').should('have.length', 2)

            cy.get('label:contains(Nouveau mot de passe)')
                .siblings('[data-context=erp-input-helper]')
                .should('contain', 'Le nouveau mot de passe doit avoir minimum 6 caractères.')

            cy.get('label:contains(Mot de passe actuel)').siblings('input').type('toto@z0z0')
            cy.get('label:contains(Nouveau mot de passe)').siblings('input').type('toto@z0z0')
            cy.get('label:contains(Répéter le nouveau mot de passe)').siblings('input').type('toto@z0z02')

            cy.get('[data-context=erp-input-helper][data-invalid]').should('have.length', 2)

            cy.get('label:contains(Nouveau mot de passe)')
                .siblings('[data-context=erp-input-helper]')
                .should('contain', "Le nouveau mot de passe doit être différent de l'ancien.")

            cy.get('label:contains(Répéter le nouveau mot de passe)')
                .siblings('[data-context=erp-input-helper]')
                .should('contain', 'La valeur saisie est différente du nouveau mot de passe.')

            cy.get('label:contains(Nouveau mot de passe)').siblings('input').type('2')

            cy.get('[data-context=erp-input-helper][data-invalid]').should('not.exist')
        })

        it('should send the correct params to the server on save', () => {
            mockFetchQuery(USER_1)
            visit(USER_1, [USER_ACCOUNT_ADMINISTRATE])

            cy.get('label:contains(Mot de passe actuel)').siblings('input').type('toto')
            cy.get('label:contains(Nouveau mot de passe)').siblings('input').type('PASSWORD123')
            cy.get('label:contains(Répéter le nouveau mot de passe)').siblings('input').type('PASSWORD123')

            cy.intercept('PUT', '**/api/hal/v1/**/change_password', { statusCode: 201, body: {} }).as('put_request')
            cy.get('button:contains(Modifier le mot de passe)').click()

            // check what's sent to server
            cy.wait('@put_request').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    old_password: 'toto',
                    new_password: 'PASSWORD123',
                })
            })
        })
    })
})
