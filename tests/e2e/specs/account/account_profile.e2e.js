import { alias<PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../../utils/graphql-test-utils'
import { HAL } from '../../../../src/shared/permissions'
import { COMMON_CLASSES } from '../../../../src/shared/composable/useFormInput'

const { USER_ACCOUNT_ADMINISTRATE } = HAL

describe('User account - profile', () => {
    const PAGE = '/accounts/{account_id}/profile'
    const FETCH_QUERY = 'fetchAccount'
    const FETCH_SITES_QUERY = 'fetchSites'
    const UPSERT_MUTATION = 'upsertAccount'
    const VALIDATION_QUERY = 'validateAccount'
    const USER_1 = '123e4567-e89b-12d3-a456-************'
    const USER_2 = '123e4567-e89b-12d3-a456-************'

    function visit(account_id, permissions = []) {
        cy.mockErpUser(permissions)

        cy.intercept('GET', '**/api/erp/v1/account/seller-commission-infos', {
            fixture: 'erp/account/seller_commission_infos',
        }).as('seller_commission_infos_request')

        cy.intercept('POST', '**/api/erp/v1/accounts', {
            fixture: 'erp/account/accounts_1390',
        }).as('erp_account_request')

        cy.visit(PAGE.replace('{account_id}', account_id))
        cy.toggleMenu()

        cy.wait([
            getQueryAlias(FETCH_SITES_QUERY),
            getQueryAlias(FETCH_QUERY),
            '@seller_commission_infos_request',
            '@erp_account_request',
        ])
    }

    function mockFetchQuery(account_id) {
        cy.fixture(`graphql/${FETCH_QUERY}`).then((payload) => {
            payload.data.account_account[0].account_id = account_id

            cy.intercept(
                {
                    method: 'POST',
                    url: GRAPHQL_ENDPOINT,
                },
                (req) => {
                    if (hasOperationName(req, FETCH_SITES_QUERY)) {
                        aliasQuery(req, FETCH_SITES_QUERY)

                        req.reply({
                            fixture: `graphql/${FETCH_SITES_QUERY}`,
                        })
                    }

                    if (hasOperationName(req, FETCH_QUERY)) {
                        aliasQuery(req, FETCH_QUERY)

                        req.reply({
                            body: payload,
                        })
                    }
                },
            )
        })
    }

    beforeEach(() => {
        cy.authenticate()
    })

    const normal_form_fields = [
        { label: 'label:contains(Civilité)', type: 'switch' },
        { label: 'label:contains(Nom)' },
        { label: 'label:contains(Prénom)' },
        { label: 'label:contains(Téléphone)' },
        { label: 'label:contains(Mobile)' },
        { label: 'label:contains(Intitulé du poste)' },
        { label: 'label:contains(Signature)', for: 'signature' },
    ]

    describe('User with no specific permission when editing its own account', () => {
        it('should show an editable form but not allow to edit business rule', () => {
            mockFetchQuery(USER_1)
            visit(USER_1)

            // Users cannot set themselves as active/inactive
            // The admins can but should not do so, because they're not stupid
            cy.get('label:contains(Statut)')
                .siblings('[data-context=edit-profile-status]')
                .find('button')
                .should('have.attr', 'disabled')

            normal_form_fields.forEach((field) => {
                if (field?.type === 'switch') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-civility]')
                        .find('button')
                        .should('not.have.attr', 'disabled')

                    return
                } else if (field?.for === 'signature') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-signature]')
                        .should('not.have.attr', 'disabled')

                    return
                }

                cy.get(field.label).siblings('input').should('not.have.attr', 'disabled')
            })

            cy.get('label:contains(Fond de caisse)')
                .closest('[data-context=erp-toggle]')
                .should('have.attr', 'data-disabled')

            // Those field requires erp account to be loaded
            ;['Site / Magasin', 'Role', 'Niveau du role'].forEach((label) => {
                cy.get(`label:contains(${label})`)
                    .siblings('[data-context=erp-multiselect]')
                    .should('have.class', COMMON_CLASSES.disabled)
            })

            cy.get('label:contains(Sous-type de devis)')
                .closest('[data-context=erp-toggle]')
                .should('have.attr', 'data-disabled')
        })
    })

    describe('User with no specific permission when viewing another account', () => {
        it('should show a readonly form', () => {
            mockFetchQuery(USER_2)
            visit(USER_2)

            normal_form_fields.forEach((field) => {
                if (field?.type === 'switch') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-civility]')
                        .find('button')
                        .should('have.attr', 'disabled')

                    return
                } else if (field?.for === 'signature') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-signature]')
                        .should('have.attr', 'disabled')

                    return
                }

                cy.get(field.label).siblings('input').should('have.attr', 'disabled')
            })

            cy.get('label:contains(Fond de caisse)')
                .closest('[data-context=erp-toggle]')
                .should('have.attr', 'data-disabled', 'true')

            // Those field requires erp account to be loaded
            ;['Site / Magasin', 'Role', 'Niveau du role'].forEach((label) => {
                cy.get(`label:contains(${label})`)
                    .siblings('[data-context=erp-multiselect]')
                    .should('have.class', COMMON_CLASSES.disabled)
            })

            cy.get('label:contains(Sous-type de devis)')
                .closest('[data-context=erp-toggle]')
                .should('have.attr', 'data-disabled')
        })
    })

    describe('User with administration permission when editing its own account', () => {
        it('should show a form and allow to edit business rule', () => {
            mockFetchQuery(USER_1)
            visit(USER_1, [USER_ACCOUNT_ADMINISTRATE])

            normal_form_fields.forEach((field) => {
                if (field?.type === 'switch') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-civility]')
                        .find('button')
                        .should('not.have.attr', 'disabled')

                    return
                } else if (field?.for === 'signature') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-signature]')
                        .should('not.have.attr', 'disabled')

                    return
                }

                cy.get(field.label).siblings('input').should('not.have.attr', 'disabled')
            })

            cy.get('label:contains(Fond de caisse)')
                .closest('[data-context=erp-toggle]')
                .should('not.have.attr', 'data-disabled')

            // Those field requires erp account to be loaded
            ;['Site / Magasin', 'Role', 'Niveau du role'].forEach((label) => {
                cy.get(`label:contains(${label})`)
                    .siblings('[data-context=erp-multiselect]')
                    .should('not.have.class', COMMON_CLASSES.disabled)
            })

            cy.get('label:contains(Sous-type de devis)')
                .closest('[data-context=erp-toggle]')
                .should('not.have.attr', 'data-disabled')
        })
    })

    describe('User with administration permission when viewing another account', () => {
        it('should show a form and allow to edit business rule', () => {
            mockFetchQuery(USER_2)
            visit(USER_2, [USER_ACCOUNT_ADMINISTRATE])

            normal_form_fields.forEach((field) => {
                if (field?.type === 'switch') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-civility]')
                        .find('button')
                        .should('not.have.attr', 'disabled')

                    return
                } else if (field?.for === 'signature') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-signature]')
                        .should('not.have.attr', 'disabled')

                    return
                }

                cy.get(field.label).siblings('input').should('not.have.attr', 'disabled')
            })

            cy.get('label:contains(Fond de caisse)')
                .closest('[data-context=erp-toggle]')
                .should('not.have.attr', 'data-disabled')

            // Those field requires erp account to be loaded
            ;['Site / Magasin', 'Role', 'Niveau du role'].forEach((label) => {
                cy.get(`label:contains(${label})`)
                    .siblings('[data-context=erp-multiselect]')
                    .should('not.have.class', COMMON_CLASSES.disabled)
            })

            cy.get('label:contains(Sous-type de devis)')
                .closest('[data-context=erp-toggle]')
                .should('not.have.attr', 'data-disabled')
        })
    })

    describe('Check form behaviour', () => {
        it('should send the correct params to the server on save', () => {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, UPSERT_MUTATION)) {
                    aliasQuery(req, UPSERT_MUTATION)

                    req.reply({
                        data: {
                            insert_account_account_information_one: {
                                account_id: USER_1,
                                preferences: {
                                    erp: { force_selection_quote_subtype: true },
                                },
                                account: {
                                    email: 'toto',
                                    username: 'toto',
                                    is_active: true,
                                    __typename: 'account_account',
                                },
                                __typename: 'account_account_information',
                            },
                        },
                    })
                }
                if (hasOperationName(req, VALIDATION_QUERY)) {
                    aliasQuery(req, VALIDATION_QUERY)

                    req.reply({
                        data: {
                            username: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 1,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                            email: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 1,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                        },
                    })
                }
            })

            mockFetchQuery(USER_1)

            cy.intercept('PUT', '**/api/erp/v1/account/**/profile', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: { user_id: 1390 },
                },
            }).as('erp_account_profile_request')

            visit(USER_1, [USER_ACCOUNT_ADMINISTRATE])

            cy.get('label:contains(Statut)')
                .siblings('[data-context=edit-profile-status]')
                .find('button[data-status=idle]')
                .click()

            normal_form_fields.forEach((field, idx) => {
                if (field?.type === 'switch') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-civility]')
                        .find('button[data-status=idle]')
                        .click()

                    return
                } else if (field?.for === 'signature') {
                    cy.get(field.label)
                        .siblings('[data-context=edit-profile-signature]')
                        .clear()
                        .type('yeah well, this is my signature')

                    return
                }

                cy.get(field.label).eq(0).siblings('input').clear()

                cy.get(field.label)
                    .eq(0)
                    .siblings('input')
                    .type('valeur ' + idx)
            })

            cy.get('label:contains(Fond de caisse)').closest('[data-context=erp-toggle]').find('button').click()
            cy.get('label:contains(Sous-type de devis)').closest('[data-context=erp-toggle]').find('button').click()

            // Passes validation
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, VALIDATION_QUERY)) {
                    aliasQuery(req, VALIDATION_QUERY)

                    req.reply({
                        data: {
                            username: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 0,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                            email: {
                                __typename: 'account_account_aggregate',
                                aggregate: {
                                    count: 0,
                                    __typename: 'account_account_aggregate_fields',
                                },
                            },
                        },
                    })
                }
            })

            // Those field requires erp account to be loaded
            ;['Site / Magasin', 'Role', 'Niveau du role'].forEach((label) => {
                cy.get(`label:contains(${label})`).siblings('[data-context=erp-multiselect]').click()
                cy.get(`label:contains(${label})`)
                    .siblings('[data-context=erp-multiselect]')
                    .find('[data-context=suggestion]')
                    .eq(0)
                    .click()
            })

            cy.get('button:contains(Sauvegarder)').click()

            // check what's sent to graphql
            cy.wait(getQueryAlias(UPSERT_MUTATION)).then((xhr) => {
                expect(xhr.request.body.variables).to.deep.eq({
                    data: {
                        title: 'Ms',
                        firstname: 'valeur 2',
                        lastname: 'valeur 1',
                        phones: { cellphone: 'valeur 4', internal_phone: 'valeur 3' },
                        job: 'valeur 5',
                        preferences: {
                            erp: { force_selection_quote_subtype: true },
                        },
                        account: {
                            data: {
                                account_id: '123e4567-e89b-12d3-a456-************',
                                username: 'foo.bar',
                                email: '<EMAIL>',
                                is_active: false,
                                is_sales_consultant: true,
                                legacy_account_id: 1390,
                                site_id: '70d5fc37-a199-4d37-9fc3-d8aeef638c26',
                            },
                            on_conflict: {
                                constraint: 'account_pk',
                                update_columns: [
                                    'username',
                                    'email',
                                    'is_active',
                                    'is_sales_consultant',
                                    'site_id',
                                    'legacy_account_id',
                                ],
                            },
                        },
                    },
                    update_columns: ['title', 'firstname', 'lastname', 'phones', 'job', 'preferences'],
                })
            })

            cy.wait('@erp_account_profile_request').then((xhr) => {
                expect(xhr.request.body).to.deep.contains({
                    user_id: 1390,
                    warehouse_id: 11,
                    seller_commission_role: 'BUSINESS_DEPARTMENT_SELLER',
                    seller_commission_role_level: '1',
                    first_name: 'valeur 2',
                    last_name: 'valeur 1',
                    signature: 'yeah well, this is my signature',
                })
            })
        })
    })
})
