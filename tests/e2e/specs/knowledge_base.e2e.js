import { aliasQ<PERSON>y, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from '../utils/graphql-test-utils'

const pages = [
    {
        title: 'legacy page',
        slug: '/legacy/tache/index',
        is_legacy: true,
    },
    {
        title: 'regular page',
        slug: '/admin/delivery-note',
        is_legacy: false,
    },
]
const FETCH_ENTRIES = 'fetchEntries'
const FETCH_CONTENT = 'fetchActiveContent'
beforeEach(() => {
    cy.authenticate()
    cy.mockErpUser()
})
describe('Knowledge base', function () {
    context('Entry can be opened and closed', function () {
        pages.forEach((page) => {
            it(`should be able to open and close the entry on a ${page.title}`, function () {
                cy.visit(page.slug)
                cy.closeAllToasts()

                cy.get('[data-context=entry-btn]').should('be.visible').click()
                cy.get('[data-context=entry-container]').should('be.visible').click()
                if (page.is_legacy) {
                    cy.get('[data-context=resize-side-panel]').should('not.exist')
                } else {
                    cy.get('[data-context=resize-side-panel]').should('be.visible')
                }
                cy.closeAllToasts()
                cy.get('[data-context=entry-btn]').should('be.visible').click()
                cy.get('[data-context=entry-container]').should('not.exist')
            })
        })
    })
    context('Can display and use a list of entries', function () {
        beforeEach(() => {
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, FETCH_ENTRIES)) {
                    aliasQuery(req, FETCH_ENTRIES)

                    req.reply({
                        fixture: `graphql/entry/${FETCH_ENTRIES}`,
                    })
                }
                if (hasOperationName(req, FETCH_CONTENT)) {
                    aliasQuery(req, FETCH_CONTENT)

                    req.reply({
                        fixture: `graphql/entry/${FETCH_CONTENT}`,
                    })
                }
            })
        })
        it('display all entries', () => {
            cy.visit('/')
            cy.get('[data-context=entry-btn]').should('be.visible').click()
            cy.wait(getQueryAlias(FETCH_ENTRIES))
            cy.get('[data-context=entry-item]').should('have.length', 8)
            cy.get('[data-context=create-wiki]').should('exist')
        })
        it('display an entry', () => {
            cy.visit('/')
            cy.get('[data-context=entry-btn]').should('be.visible').click()
            cy.wait(getQueryAlias(FETCH_ENTRIES))
            cy.get('[data-context=entry-item]').eq(0).click()

            cy.get('[data-context=page-header]').should('have.text', ' 1988 : Tetris - Sega  KB-3 ')
        })
        it('Open knowledge base list if query string parameter is present in URL', () => {
            cy.visit('/legacy/tache/index', {
                qs: { kb: 'list' },
            })
            cy.get('[data-context=create-wiki]').should('exist').should('be.visible')
        })
        it('Open knowledge base content if query string parameters are present in URL', () => {
            cy.visit('/legacy/tache/index', {
                qs: { kb: 3 },
            })
            cy.get('[data-context=page-header]').should('have.text', ' 1988 : Tetris - Sega  KB-3 ')
        })
        it('Knowledge base dont remove query parameter', () => {
            cy.visit('/accounts?pager[filters]=%7B%22name%22%3A%22nico%22%7D&=list')
            cy.url().should('include', '/accounts?pager[filters]=%7B%22name%22%3A%22nico%22%7D&=list')
            cy.get('[data-context=entry-btn]').should('be.visible').click({ force: true })
            cy.url().should('include', '/accounts?pager[filters]=%7B%22name%22%3A%22nico%22%7D')
            cy.visit('//accounts?pager[filters]=%7B%22name%22%3A%22nico%22%7D&kb=3')
            cy.url().should('include', '/accounts?pager[filters]=%7B%22name%22%3A%22nico%22%7D&kb=3')
            cy.get('[data-context=entry-btn]').should('be.visible').click({ force: true })
            cy.url().should('include', '/accounts?pager[filters]=%7B%22name%22%3A%22nico%22%7D')
        })
    })
})
