import { getMagicSearchArticles, getMagicSearchResponse } from './helper'

describe('Magic search on articles', () => {
    const PAGE = '/'

    const visitPage = () => {
        cy.visit(PAGE)
        cy.get('[data-context=magic-search-btn]').click()
        cy.get('[data-context=search-results]').should('be.visible')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Can display articles results', () => {
        it('displays all needed informations successfully', () => {
            cy.intercept('GET', '**/api/erp/v1/magic-search**', {
                statusCode: 200,
                body: getMagicSearchResponse(getMagicSearchArticles(16), { from: 5, size: 15 }),
            }).as('articles')

            visitPage()
            cy.get('[data-context=search-terms]').type('test')

            // check api call
            cy.wait('@articles').then((xhr) => {
                const url = new URL(xhr.request.url)

                expect(url.searchParams.get('size')).to.eq('15')
                expect(url.searchParams.get('context')).to.eq('articles')
                expect(url.searchParams.get('from')).to.eq('0')
                expect(url.searchParams.get('search_terms')).to.eq('test')
            })

            // and response
            cy.get('[data-context=context-label]').should('contain', 'Articles')
            cy.get('[data-context=article-result-item]').should('be.visible').should('have.length', 5)
            cy.get('[data-context=article-result-item]')
                .eq(0)
                .as('first_product')
                .find('[data-context=image]')
                .should(($img) => {
                    expect($img)
                        .attr('src')
                        .to.match(
                            /.*\/images\/article\/eltax\/ELTAVOYBT15MKII\/voyager-bt-15-mkii_63e224d770714_300_square.jpg/,
                        )
                })
            cy.get('@first_product')
                .find('[data-context=sku]')
                .should('have.attr', 'href', '/articles/ELTAXITEM3513/')
                .should('contain', 'ELTAXITEM3513')
            cy.get('@first_product').find('[data-context=name]').should('contain', 'Eltax Boomer ELTAX ITEM 3513')
            cy.get('@first_product').find('[data-context=info]').should('contain', '40,00 €')

            cy.get('@first_product').find('[data-context=status-delay-badge] svg.fa-shopping-cart').should('exist')

            cy.get('@first_product').find('[data-context=group_brand]').should('contain', 'AVI')

            // View more
            cy.get('[data-context=more-filters]')
                .should('be.visible')
                .should('have.attr', 'href', '/legacy/produit/produitsShow')

            cy.intercept('GET', '**/api/erp/v1/magic-search**', {
                statusCode: 200,
                body: getMagicSearchResponse(getMagicSearchArticles()),
            }).as('articles_more')

            // displays pagination
            cy.get('[data-context=article-result-item]').should('have.length', 5)
            cy.get('[data-context=results-total]').should('contain', '5/16 résultats')
            cy.get('[data-context=see-next]').should('contain', 'Voir les 11 suivants').click()
            cy.get('[data-context=see-next]').should('contain', 'Voir les 6 suivants').click()
            cy.get('[data-context=see-next]').should('contain', 'Voir les 1 suivants').click()

            cy.wait('@articles_more').then((xhr) => {
                const url = new URL(xhr.request.url)

                expect(url.searchParams.get('size')).to.eq('15')
                expect(url.searchParams.get('context')).to.eq('articles')
                expect(url.searchParams.get('from')).to.eq('5')
                expect(url.searchParams.get('search_terms')).to.eq('test')
            })

            // check results
            cy.get('[data-context=article-result-item]').should('have.length', 20)
            cy.get('[data-context=results-total]').should('contain', '20/16 résultats')
            cy.get('[data-context=see-next]').should('not.exist')
        })
    })

    describe('Behaviour when nothing has been found', () => {
        it('shows a feedback message', () => {
            cy.intercept('GET', '**/api/erp/v1/magic-search**', {
                statusCode: 200,
                body: getMagicSearchResponse(),
            }).as('articles')

            visitPage()

            cy.get('[data-context=search-terms]').type('test')
            cy.get('[data-context=article-result-item]').should('not.exist')
            cy.get('[data-context=no-results-message]').should('be.visible').should('contain', 'Aucun résultat')
        })
    })
})
