describe('Magic search input on top bar on every pages', () => {
    const PAGE = '/'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('Can search from everywhere', () => {
        it('open a search results page', () => {
            cy.visit(PAGE)

            cy.get('[data-context=magic-search-btn]').should('be.visible').click()
            cy.get('[data-context=search-results]').should('be.visible')
            cy.get('input[data-context=search-terms]')
                .should('be.visible')
                .should('have.attr', 'placeholder', 'Tapez votre recherche ici…')
                .should('have.value', '')
                .type('gg')
                .should('have.value', 'gg')

            // close
            cy.get('[data-context=btn-close]').should('be.visible').click()
            cy.get('[data-context=search-results]').should('not.exist')
            // check closing resets search term
            cy.get('[data-context=magic-search-btn]').click()
            cy.get('[data-context=search-terms]').should('have.value', '')
        })
    })
})
