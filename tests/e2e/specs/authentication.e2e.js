describe('Handle authentication with tokens and HAL Oauth', function () {
    it('Redirect to HAL login page when tokens not set', function () {
        cy.clearSessionStorage()
        // Without tokens, user is redirected to HAL login page
        cy.visit('/', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })
        cy.get('@windowOpen').should(
            'be.calledWithMatch',
            /\/_hal\/oauth\/v2\/auth\?client_id=oauth_client_id&redirect_uri=.*&response_type=code/,
            '_self',
        )
    })

    it('Handle redirection from HAL to set tokens', function () {
        cy.mockErpUser()
        cy.intercept('/_hal/oauth/v2/token**code=CODE_FROM_HAL**', {
            statusCode: 200,
            fixture: '_hal/oauth/tokens.json',
        }).as('getTokens')

        // In normal conditions, HAL is an outside server
        // which redirect to the "redirect_uri" after login success, adding a "code" parameter
        // So, mimic the redirection
        cy.visit('/?code=CODE_FROM_HAL')

        // Check tokens are retrieved
        cy.wait('@getTokens')

        // Test tokens are set
        cy.getAuthenticationStore().its('access_token').should('equal', 'ACCESS_TOKEN_FROM_HAL')
        cy.getAuthenticationStore().its('refresh_token').should('equal', 'REFRESH_TOKEN_FROM_HAL')
    })

    it('Add Authorization headers on ERP_PROXY api calls', function () {
        cy.authenticate()
        cy.mockErpUser()

        // main page ask for connected user data through an ajax call.
        // we use this to test that the Authorization header is set
        cy.intercept('**/api/hal/v1/me', (req) => {
            expect(req.headers.authorization).to.eq('Bearer ACCESS_TOKEN')
        }).as('getUserData')

        cy.visit('/')
        cy.wait('@getUserData')
    })

    it('Handle 401 errors by refreshing the tokens', function () {
        ;(function prepare() {
            cy.clearSessionStorage()
            cy.authenticate()
            cy.intercept(
                '/_hal/oauth/v2/token' +
                    '?client_id=oauth_client_id' +
                    '&client_secret=oauth_client_secret' +
                    '&refresh_token=REFRESH_TOKEN' +
                    '&grant_type=refresh_token',
                { fixture: '_hal/oauth/refresh_tokens.json' },
            ).as('refreshTokens')
        })()

        // main page ask for connected user data through an ajax call.
        // we mock it with a tokens expired response
        cy.intercept('**/api/hal/v1/me', (req) => {
            req.reply({
                // alter the mocked url so that after refresh it will be fine
                statusCode: req.headers['authorization'] === 'Bearer ACCESS_TOKEN' ? 401 : 200,
                body: {},
            })
        }).as('getUserData')

        cy.visit('/')

        // data should be retrieved
        cy.wait('@getUserData')

        cy.clearSessionStorage()

        // test that refresh token is call
        // test that we automatically retry to retrieve data after refresh
        cy.wait(['@refreshTokens', '@getUserData'])

        // Test tokens are refreshed
        cy.getAuthenticationStore().its('access_token').should('equal', 'ACCESS_TOKEN_REFRESHED')
        cy.getAuthenticationStore().its('refresh_token').should('equal', 'REFRESH_TOKEN_REFRESHED')
    })

    it('Handle 401 errors by using localStore tokens if different', function () {
        ;(function prepare() {
            cy.authenticate()
            cy.clearSessionStorage()

            // main page ask for connected user data through an ajax call.
            // we mock it with a tokens expired response
            cy.intercept('**/api/hal/v1/me', { statusCode: 200, body: {} }).as('getUserData')
        })()

        cy.visit('/')

        cy.getAuthenticationStore().its('access_token').should('equal', 'ACCESS_TOKEN')
        cy.getAuthenticationStore().its('refresh_token').should('equal', 'REFRESH_TOKEN')

        // data should be retrieved
        cy.wait('@getUserData')

        // Now tokens expire and another tab update the token

        // next call will fail
        cy.clearSessionStorage()
        cy.intercept('**/api/hal/v1/me', { statusCode: 401, body: {} }).as('getUserData2')
        // mimic an update of the tokens from another tab
        cy.window().its('localStorage').invoke('setItem', 'session-access-token', 'ACCESS_TOKEN_STORED')
        cy.window().its('localStorage').invoke('setItem', 'session-refresh-token', 'REFRESH_TOKEN_STORED')

        // refresh the page
        cy.visit('/')

        // Test tokens are refreshed to use the ones from localStorage
        cy.getAuthenticationStore().its('access_token').should('equal', 'ACCESS_TOKEN_STORED')
        cy.getAuthenticationStore().its('refresh_token').should('equal', 'REFRESH_TOKEN_STORED')
    })

    it('Handle 400 errors on refresh token queries', function () {
        ;(function prepare() {
            cy.authenticate()
            cy.clearSessionStorage()
            cy.intercept('/_hal/oauth/v2/token**refresh_token=REFRESH_TOKEN**', {
                statusCode: 400,
                body: {},
            }).as('refreshTokensFailure')

            // main page ask for connected user data through an ajax call.
            // we mock it with a tokens expired response
            cy.intercept('**/api/hal/v1/me**', { statusCode: 401, body: {} }).as('getUserData')
        })()

        cy.visit('/', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })
        cy.wait('@getUserData')
        cy.wait('@refreshTokensFailure')

        // should try to open HAL Login page
        cy.get('@windowOpen').should(
            'be.calledWithMatch',
            /\/_hal\/oauth\/v2\/auth\?client_id=oauth_client_id&redirect_uri=.*&response_type=code/,
            '_self',
        )

        tokensShouldBeCleared()
    })

    it('Do nothing for other 400 errors', function () {
        ;(function prepare() {
            cy.authenticate()
            cy.clearSessionStorage()
            cy.intercept('**/api/hal/v1/me', { statusCode: 400, body: {} }).as('getUserData')
        })()

        cy.visit('/', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })
        cy.wait('@getUserData')

        // should not try to open HAL Login page
        cy.get('@windowOpen').should('not.be.called')

        tokensShouldNotBeCleared()
    })

    it('Have a way to logout from HAL', function () {
        cy.authenticate()
        cy.visit('/', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })
        cy.getAuthenticationStore().invoke('logout')

        // should try to open HAL Login page
        cy.get('@windowOpen').should('be.calledWith', '/_hal/oauth/v2/auth/logout', '_self')

        tokensShouldBeCleared()
    })

    it('should reload the /me and /permissions from session storage instead of calling the api', () => {
        cy.authenticate()
        cy.mockErpUser()
        cy.clearSessionStorage()
        cy.visit('/')
        cy.wait('@api_me')
        cy.wait('@api_permissions')

        cy.reload()
        cy.wait(2000)
        cy.get('@api_me.all').then((interceptions) => {
            expect(interceptions).to.have.length(1)
        })
        cy.get('@api_permissions.all').then((interceptions) => {
            expect(interceptions).to.have.length(1)
        })
        cy.get('[data-context=user-dropdown-menu-link]').should('contain', 'Anakin Skywalker')
    })
})

const tokensShouldBeCleared = () => {
    cy.getAuthenticationStore().then((store) => {
        expect(store.access_token).to.be.null
        expect(store.refresh_token).to.be.null
    })
    cy.window().its('localStorage').invoke('getItem', 'session-access-token').should('eq', null)
    cy.window().its('localStorage').invoke('getItem', 'session-refresh-token').should('eq', null)
}
const tokensShouldNotBeCleared = () => {
    cy.getAuthenticationStore().then((store) => {
        expect(store.access_token).to.not.be.null
        expect(store.refresh_token).to.not.be.null
    })
    cy.window().its('localStorage').invoke('getItem', 'session-access-token').should('not.equal', null)
    cy.window().its('localStorage').invoke('getItem', 'session-refresh-token').should('not.equal', null)
}
