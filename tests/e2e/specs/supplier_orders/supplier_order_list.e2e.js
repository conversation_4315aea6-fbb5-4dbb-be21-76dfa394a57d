const PAGE = '/supplier-order/list'
const MOCKED_DATE = new Date(2023, 4, 9, 10, 44, 21, 0)

describe('View by article pivot', () => {
    beforeEach(() => {
        localStorage.removeItem('supplier_order.filters')
        cy.mockDate(MOCKED_DATE)

        cy.authenticate()
        cy.mockErpUser()
        cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
            fixture: 'erp/supplier-order-products/supplier_order_products_2.json',
        }).as('fetch-supplier-order-articles')

        cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
            fixture: 'erp/supplier-orders/supplier_order_123456.json',
        }).as('fetch-supplier_orders')

        cy.intercept('POST', '**/api/erp/v1/suppliers', {
            fixture: 'erp/supplier/suppliers.json',
        }).as('fetch-suppliers')

        cy.intercept('POST', '**/api/erp/v1/accounts', {
            fixture: 'erp/account/accounts.json',
        }).as('fetch-accounts')

        cy.visit(PAGE)
    })

    it('Filters are well displayed', () => {
        cy.get('[data-context=dashboard-filters]').should('be.visible')

        cy.get('[data-context=user]')
            .should('contain', 'Utilisateur')
            .find('[data-context=erp-account-autocomplete]')
            .should('be.visible')

        cy.get('[data-context=suppliers]').should('contain', 'Fournisseurs')

        cy.get('[data-context=status]')
            .should('contain', 'Statut')
            .find('[data-context=tag]')
            .as('status-tag')
            .should('have.length', 2)
        cy.get('@status-tag').eq(0).should('contain', 'EN COURS')
        cy.get('@status-tag').eq(1).should('contain', 'EN PREPARATION')

        cy.get('[data-context=product]').should('contain', 'Produit')

        cy.get('[data-context=last-update]').should('contain', 'Dernière mise à jour')

        cy.get('[data-context=supplier-order]').should('contain', 'Commande')

        cy.get('[data-context=created-at]').should('contain', 'Date de création entre')

        cy.get('[data-context=sent-at]').should('contain', "Date d'envoi entre")

        cy.get('[data-context=dispute]').should('contain', 'Litige uniquement')

        cy.get('[data-context=to-recall]').should('contain', 'À rappeler uniquement')

        cy.get('[data-context=not-delivered]').should('contain', 'Non livrés uniquement')

        cy.get('[data-context=erp-switch-selector-button]:contains(Vue produit)').should(
            'have.attr',
            'data-status',
            'active',
        )
        cy.get('[data-context=display-type] [data-status=idle]:contains(Vue commande)').should(
            'not.have.attr',
            'data-status',
            'active',
        )
        cy.get('[data-context=erp-switch-selector-button]:contains(Vue commande)').click()

        cy.get('[data-context=erp-switch-selector-button]:contains(Vue produit)').should(
            'not.have.attr',
            'data-status',
            'active',
        )
        cy.get('[data-context=erp-switch-selector-button]:contains(Vue commande)').should(
            'have.attr',
            'data-status',
            'active',
        )
    })

    it('Display the table headers and rows', () => {
        cy.wait('@fetch-supplier-order-articles')
        cy.get('[data-context=erp-table').as('table')

        cy.get('@table').find('thead th').as('headers')
        cy.get('@table').find('tr:first-of-type td').as('cells')
        ;[
            ['Commande', 'fournisseur'],
            'Fournisseur',
            ['Date', 'création'],
            ['Date', 'envoi'],
            'Produit',
            'Date màj',
            ['Quantité', 'commandée'],
            ['Quantité', 'livrée'],
            ['Quantité', 'en attente'],
            'Livraison prévue',
        ].forEach((header, index) => {
            if (Array.isArray(header)) {
                header.forEach((h) => cy.get('@headers').eq(index).should('contain', h))

                return
            }

            cy.get('@headers').eq(index).should('contain', header)
        })
        ;[
            [
                { text: '104761', element: '[data-context=content]' },
                { text: 'en cours', element: '[data-context=scoped-badge]' },
            ],
            [{ text: 'Also France' }],
            [{ text: '24/02/2023' }],
            [{ text: '24/02/2023' }],
            [
                {
                    text: 'NETGSXK30',
                    element: 'a',
                    attr: [{ name: 'href', value: '/articles/NETGSXK30' }],
                },
            ],
            [
                { text: '29/03/2023' },
                {
                    element: '[data-context=status-indicator]',
                    attr: [{ name: 'class', value: 'bg-red-500' }],
                },
            ],
            [{ text: '44' }],
            [{ text: '34' }],
            [
                { text: ' 0  /  0 ' },
                {
                    element: '[data-context=backorder-status-indicator]',
                    attr: [{ name: 'class', value: 'bg-green-500' }],
                },
            ],
            [{ text: '07/04/2023' }, { text: '+1 livraison', element: '[data-context=badge]' }],
        ].forEach((cell_elements, index) => {
            cell_elements.forEach((cell) => {
                if (undefined === cell.element) {
                    cy.get('@cells').eq(index).should('contain', cell.text)

                    return
                }

                cy.get('@cells').eq(index).find(cell.element).as('cell')

                if (undefined !== cell.text) {
                    cy.get('@cell').should('contain', cell.text)
                }

                if (undefined !== cell.attr) {
                    cell.attr.forEach((attr) => cy.get('@cell').attribute(attr.name).should('contain', attr.value))
                }
            })
        })

        cy.get('[data-context=column-product]').click()

        cy.wait('@fetch-supplier-order-articles').then((xhr) => {
            expect(xhr.request.body.order_direction).eq('asc')
        })
    })

    it('should apply filters successfully', () => {
        cy.wait('@fetch-supplier-order-articles').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                _and: [
                    {
                        supplier_id: {
                            _in: [555, 634],
                        },
                    },
                    {
                        status: {
                            _in: ['EN COURS', 'EN PREPARATION'],
                        },
                    },
                ],
            })
        })

        // Filter by supplier order id
        cy.get('[data-context=supplier-order]').type(123)

        // Remove selected user
        // TODO this serves nothing, its not in the request
        cy.get('[data-context=user]').find('span').should('contain', 'Zinédine Zidane')
        cy.get('[data-context=user]').find('span').should('contain', 'Zinédine Zidane')

        // Change supplier filter
        cy.get('[data-context=suppliers]').find('[data-context=multiple]').click()
        cy.get('[data-context=suggestion]:contains(Absolument)').click()
        cy.get('[data-context=suggestion]:contains(Acadia)').click()
        cy.get('[data-context=suggestion]:contains(A2L)').click()

        // Change status
        cy.get('[data-context=status]').find('[data-context=multiple]').click()
        cy.get('[data-context=suggestion]:contains(EN COURS)').click()

        // Filter by created at
        cy.get('[data-context=created-at]').erpDateRangePicker('4', '6')

        // Filter by sent at
        cy.get('[data-context=sent-at]').erpDateRangePicker('2', '11')

        // Filter by dispute
        cy.get('[data-context=dispute]').find('button').click()

        // Filter by to-recall
        cy.get('[data-context=to-recall]').find('button').click()

        // Filter by product
        cy.get('[data-context=product]').type('SKU')

        // Filter by last update
        cy.get('[data-context=last-update]').click()
        cy.get('[data-context=suggestion]:contains(-7 jours)').click()

        // Filter by next delivery
        cy.get('[data-context=next-delivery]').click()
        cy.get('[data-context=suggestion]:contains(invalide)').click()

        // Filter by not-delivered
        cy.get('[data-context=not-delivered]').find('button').click()

        // Filter than do not exist in default context
        cy.get('[data-context=not-billed]').should('not.exist')

        cy.get('[data-context=filters-submit-btn]').click()

        cy.wait('@fetch-supplier-order-articles').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                _and: [
                    {
                        supplier_id: {
                            _in: [258],
                        },
                    },
                    {
                        status: {
                            _in: ['EN PREPARATION'],
                        },
                    },
                    {
                        _or: [
                            {
                                sku: {
                                    _eq: 'SKU',
                                },
                            },
                            {
                                article_id: {
                                    _eq: 'SKU',
                                },
                            },
                        ],
                    },
                    {
                        updated_at: {
                            _gt: '2023-05-02',
                        },
                    },
                    {
                        expected_delivery_date: {
                            _lt: '2023-05-08',
                        },
                    },
                    {
                        supplier_order_id: {
                            _eq: '123',
                        },
                    },
                    {
                        created_at: {
                            _gte: '2023-05-04',
                        },
                    },
                    {
                        created_at: {
                            _lte: '2023-05-06',
                        },
                    },
                    {
                        sent_at: {
                            _gte: '2023-05-02',
                        },
                    },
                    {
                        sent_at: {
                            _lte: '2023-05-11',
                        },
                    },
                    {
                        dispute: {
                            _eq: true,
                        },
                    },
                    {
                        delivered: {
                            _eq: false,
                        },
                    },
                    {
                        supplier_recall: {
                            _eq: true,
                        },
                    },
                ],
            })
        })

        // Reload page with no parameters in url
        cy.visit(PAGE, {})

        // Check filters are restored from local storage
        cy.wait('@fetch-supplier-order-articles').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                _and: [
                    {
                        supplier_id: {
                            _in: [258],
                        },
                    },
                    {
                        status: {
                            _in: ['EN PREPARATION'],
                        },
                    },
                ],
            })
        })
    })
})

describe('View by supplier order pivot', () => {
    beforeEach(() => {
        cy.mockDate(MOCKED_DATE)

        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
            fixture: 'erp/supplier-order-products/supplier_order_products_2.json',
        }).as('fetch-supplier-order-articles')

        cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
            fixture: 'erp/supplier-orders/supplier_order_123456.json',
        }).as('fetch-supplier-orders')

        cy.intercept('POST', '**/api/erp/v1/suppliers', {
            fixture: 'erp/supplier/suppliers.json',
        }).as('fetch-suppliers')

        cy.intercept('POST', '**/api/erp/v1/accounts', {
            fixture: 'erp/account/accounts.json',
        }).as('fetch-accounts')

        cy.visit(PAGE)
        cy.wait('@fetch-supplier-order-articles')

        cy.get('[data-context=erp-switch-selector-button]:contains(Vue commande)').click()
    })

    it('Display the table headers and rows', () => {
        cy.wait('@fetch-supplier-orders')

        cy.get('[data-context=next-delivery]').should('not.exist')

        cy.get('[data-context=erp-table').as('table')

        cy.get('@table').find('thead th').as('headers')
        cy.get('@table').find('tr:first-of-type td').as('cells')
        ;[
            ['Commande', 'fournisseur'],
            'Fournisseur',
            ['Date', 'création'],
            ['Date', 'envoi'],
            'Produits',
            'Date màj',
            ['Quantité', 'commandée'],
            ['Quantité', 'livrée'],
            ['Quantité', 'en attente'],
        ].forEach((header, index) => {
            if (Array.isArray(header)) {
                header.forEach((h) => cy.get('@headers').eq(index).should('contain', h))

                return
            }

            cy.get('@headers').eq(index).should('contain', header)
        })
        ;[
            [
                {
                    text: '105963',
                    element: 'a',
                    attr: [
                        { name: 'href', value: '/legacy/commandeFournisseur/edition?id_commande_fournisseur=105963' },
                    ],
                },
            ],
            [{ text: 'Sound United' }],
            [{ text: '15/05/2023' }],
            [{ text: '15/05/2023' }],
            [
                {
                    text: 'MARMCR612SG',
                    nth: 0,
                    element: 'a',
                    attr: [{ name: 'href', value: '/articles/MARMCR612SG' }],
                },
                {
                    text: 'MARPM7000NR',
                    nth: 1,
                    element: 'a',
                    attr: [{ name: 'href', value: '/articles/MARPM7000NR' }],
                },
                {
                    text: 'DENDRA800HNR',
                    nth: 8,
                    element: 'a',
                    attr: [{ name: 'href', value: '/articles/DENDRA800HNR' }],
                },
            ],
            [
                {
                    nth: 0,
                    element: 'div',
                    text: '23/05/2023',
                },
                {
                    nth: 2,
                    element: 'div',
                    text: '23/04/2023',
                },
                {
                    nth: 2,
                    element: '[data-context=status-indicator]',
                    attr: [{ name: 'class', value: 'bg-orange-500' }],
                },
                {
                    nth: 3,
                    element: '[data-context=status-indicator]',
                    attr: [{ name: 'class', value: 'bg-yellow-500' }],
                },
            ],
            [
                { text: '20', element: 'span', nth: 0 },
                { text: '10', element: 'span', nth: 1 },
            ],
            [
                { text: '0', nth: 0 },
                { text: '3', nth: 1 },
            ],
            [
                { text: '3  /  0', element: '[data-context=backorder-quantities]', nth: 0 },
                { text: ' 1  /  5 ', element: '[data-context=backorder-quantities]', nth: 1 },
                {
                    element: '[data-context=backorder-status-indicator]',
                    nth: 1,
                    attr: [{ name: 'class', value: 'bg-red-500' }],
                },
                {
                    element: '[data-context=backorder-status-indicator]',
                    nth: 2,
                    attr: [{ name: 'class', value: 'bg-green-500' }],
                },
            ],
        ].forEach((cell_elements, index) => {
            cell_elements.forEach((cell) => {
                cy.get('@cells').eq(index).as('cell')

                if (undefined !== cell.element) {
                    cy.get('@cell')
                        .find(cell.element)
                        .eq(cell.nth ?? 0)
                        .as('element')
                } else {
                    cy.get('@cell').as('element')
                }

                if (undefined !== cell.text) {
                    cy.get('@element').should('contain', cell.text)
                }

                if (undefined !== cell.attr) {
                    cell.attr.forEach((attr) => cy.get('@element').attribute(attr.name).should('contain', attr.value))
                }
            })
        })
    })

    it('Search by not billed', () => {
        cy.wait('@fetch-supplier-orders').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                _and: [
                    {
                        supplier_id: {
                            _in: [555, 634],
                        },
                    },
                    {
                        status: {
                            _in: ['EN COURS', 'EN PREPARATION'],
                        },
                    },
                ],
            })
        })

        cy.get('[data-context=not-billed]').find('button').click()
        cy.get('[data-context=filters-submit-btn]').click()

        cy.wait('@fetch-supplier-orders').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                _and: [
                    {
                        supplier_id: {
                            _in: [555, 634],
                        },
                    },
                    {
                        status: {
                            _in: ['EN COURS', 'EN PREPARATION'],
                        },
                    },
                    {
                        billed: {
                            _eq: false,
                        },
                    },
                ],
            })
        })

        // no reload - it's already done in article pivot
        // plus the page does not reload in supplier orders pivot context
    })
})

describe('From supplier page', () => {
    beforeEach(() => {
        localStorage.removeItem('supplier_order.filters')
        cy.mockDate(new Date(2023, 4, 9, 10, 44, 21, 0))

        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
            fixture: 'erp/supplier-orders/supplier_order_654321.json',
        }).as('fetch-supplier-orders')

        cy.intercept('POST', '**/api/erp/v1/suppliers', {
            fixture: 'erp/supplier/suppliers.json',
        }).as('fetch-suppliers')

        cy.visit('/legacy/fournisseur/edit?id=654321')
        cy.wait('@fetch-suppliers')
    })
    it('open list with view by supplier and filter by supplier', () => {
        const emitDataSupplierMessage = () => {
            cy.getWrapper().then((wrapper) => {
                wrapper.$emit('receiveMessage', {
                    action: 'data-supplier',
                    payload: {
                        supplier_id: 654321,
                        name: 'Absolument Pas',
                    },
                })
            })
        }
        cy.visit('/legacy/fournisseur/edit?id=654321')
        cy.get('[data-context=commands-list]').should('exist')
        emitDataSupplierMessage()

        cy.get('[data-context=commands-list]').click()
        cy.get('[data-context=page-title]').should('contain', 'Liste des commandes fournisseur')

        cy.get('[data-context=suppliers]')
            .should('contain', 'Fournisseurs')
            .find('[data-context=suppliers-autocomplete]')
            .should('contain', 'Absolument Pas')

        cy.get('[data-context="not-delivered"]').find('button').should('be.visible').should('have.attr', 'aria-checked')

        cy.wait('@fetch-supplier-orders')
        cy.get('[data-context=erp-table]').should('exist').as('table')

        cy.get('@table').selectCell(0, 0).find('[data-context="scoped-badge"]').should('contain', 'en preparation')
        cy.get('@table').selectCell(0, 1).should('contain', 'Absolument Pas')
        cy.get('@table').selectCell(0, 4).should('contain', 'MARNR1200NR')

        cy.get('@table').selectCell(1, 0).should('contain', 'en cours')
        cy.get('@table').selectCell(1, 1).should('contain', 'Absolument Pas')
        cy.get('@table').selectCell(1, 4).should('contain', 'MARMCR612SG').and('contain', 'MARPM7000NR')
    })
})

describe('Check that expected date parameters are ignored in order view', () => {
    beforeEach(() => {
        cy.mockDate(MOCKED_DATE)

        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
            fixture: 'erp/supplier-order-products/supplier_order_products_2.json',
        }).as('fetch-supplier-order-articles')

        cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
            fixture: 'erp/supplier-orders/supplier_order_123456.json',
        }).as('fetch-supplier-orders')

        cy.intercept('POST', '**/api/erp/v1/suppliers', {
            fixture: 'erp/supplier/suppliers.json',
        }).as('fetch-suppliers')

        cy.intercept('POST', '**/api/erp/v1/accounts', {
            fixture: 'erp/account/accounts.json',
        }).as('fetch-accounts')

        cy.visit(PAGE, {})
        cy.wait('@fetch-supplier-order-articles')
    })

    it('Discard expected delivery filter', () => {
        cy.get('[data-context=next-delivery]')
            .find('[data-context=erp-multiselect]')
            .erpMultiselect('', 'dépassée depuis plus de 2 jours')

        cy.get('[data-context=filters-submit-btn]').click()
        cy.wait('@fetch-supplier-order-articles').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                _and: [
                    {
                        supplier_id: {
                            _in: [555, 634],
                        },
                    },
                    {
                        status: {
                            _in: ['EN COURS', 'EN PREPARATION'],
                        },
                    },
                    {
                        expected_delivery_date: {
                            _lt: '2023-05-06',
                        },
                    },
                    {
                        expected_delivery_date: {
                            _neq: '2011-01-01',
                        },
                    },
                ],
            })
        })

        cy.get('[data-context=erp-switch-selector-button]:contains(Vue commande)').click()
        cy.wait('@fetch-supplier-orders').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                _and: [
                    {
                        supplier_id: {
                            _in: [555, 634],
                        },
                    },
                    {
                        status: {
                            _in: ['EN COURS', 'EN PREPARATION'],
                        },
                    },
                ],
            })
        })
    })

    it('Ignores incompatible order by parameters', () => {
        // trigger sort by sku
        cy.get('[data-context=column-product] [data-icon=sort]').eq(0).click()
        cy.wait('@fetch-supplier-order-articles').then((xhr) => {
            expect(xhr.request.body.order_by).to.eq('sku')
        })
        cy.get('[data-context=erp-switch-selector-button]:contains(Vue commande)').click()
        cy.wait('@fetch-supplier-orders').then((xhr) => {
            expect(xhr.request.body).to.deep.eq({
                limit: 20,
                page: 1,
                where: {
                    _and: [
                        {
                            supplier_id: {
                                _in: [555, 634],
                            },
                        },
                        {
                            status: {
                                _in: ['EN COURS', 'EN PREPARATION'],
                            },
                        },
                    ],
                },
            })
        })
    })
})
