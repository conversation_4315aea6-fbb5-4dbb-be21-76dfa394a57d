import { SUPPLIER_ORDER_UPDATE } from '../../../../src/apps/erp/permissions.js'

describe('Edition of expected deliveries', () => {
    describe('Editing expected quantities', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([SUPPLIER_ORDER_UPDATE])

            cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
                fixture: 'erp/supplier-orders/supplier_order_123456.json',
            }).as('supplier_orders')
            cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
                fixture: 'erp/supplier-order-products/supplier_order_products.json',
            }).as('supplier_order_articles')

            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456')
            cy.toggleMenu()

            cy.wait('@supplier_orders')
        })

        it('cannot update a quantity if the delivery has been completed', () => {
            cy.get('[data-context=create-button]').click()

            cy.get('[data-context=table-row]').eq(0).as('article')
            cy.get('@article')
                .find('[data-context=delivery]')
                .eq(0)
                .find('[data-context=update-qty]')
                .should('not.exist')
        })

        it('can update the quantity of a partial delivery', () => {
            cy.get('[data-context=create-button]').click()

            cy.get('[data-context=table-row]')
                .eq(0)
                .find('[data-context=delivery]')
                .eq(1)
                .as('partial_delivery')
                .find('[data-context=update-qty]')
                .click()
            cy.get('@partial_delivery').find('[data-context=badge]').should('contain', '23/08/2021')
            cy.get('@partial_delivery').find('[data-context=leading]').should('contain', '3 / ')
            cy.get('@partial_delivery').find('input').should('have.value', '25')
        })

        it('resets remaining values when editing', () => {
            cy.initReloadTest()
            cy.get('[data-context=create-button]').click()

            cy.get('[data-context=table-row]')
                .eq(0)
                .as('article')
                .find('[data-context=delivery]')
                .eq(2)
                .as('delivery')
                .find('[data-context=update-qty]')
                .click()
            cy.get('@delivery').find('[data-context=leading]').should('contain', '0 / ')
            cy.get('@delivery').find('input').should('have.value', '25')

            // check request error
            cy.intercept('PUT', '**/api/erp/v1/expected-delivery/855', {
                statusCode: 500,
                body: { error: 'Blablabla' },
            }).as('edit_error')

            cy.get('@delivery').find('[data-context=submit-qty]').click()
            cy.wait('@edit_error')
            cy.wait('@supplier_order_articles')
            cy.get('[data-context=error-message]').should('contain', 'Une erreur est survenue lors de la mise à jour.')

            // check success and params sent to API
            cy.get('[data-context=skeleton]').should('not.exist')
            cy.get('@article')
                .find('[data-context=delivery]')
                .eq(2)
                .as('partial_delivery')
                .find('[data-context=update-qty]')
                .click()

            cy.intercept('PUT', '**/api/erp/v1/expected-delivery/855', {
                statusCode: 200,
                body: {},
            }).as('edit_success')

            cy.get('@delivery').find('[data-context=submit-qty]').click()
            cy.wait('@edit_success').then((xhr) => {
                expect(xhr.request.body.quantity).to.eq(25)
            })

            cy.get('@delivery').find('[data-context=leading]').should('contain', '0 /')
            cy.get('@delivery').find('[data-context=expected-qty]').should('have.value', '25')

            // remaining quantity
            cy.get('@article').find('[data-context=new-quantity]').should('have.value', '0')

            // should reload page after popin closure
            cy.checkHasNotBeenReloaded()
            cy.get('[data-context=slide-out-container] header a.close').click()
            cy.checkHasBeenReloaded()
        })

        it('cannot edit an invalid quantity', () => {
            cy.get('[data-context=create-button]').click()

            cy.get('[data-context=table-row]').eq(0).as('article').find('[data-context=delivery]').eq(2).as('delivery')

            cy.get('@delivery').find('[data-context=update-qty]').click()

            cy.get('@delivery').find('[data-context=edit-qty]').should('have.value', '25').type('500')
            cy.get('@delivery').find('[data-context=submit-qty]').click()

            cy.get('@article')
                .find('[data-context=invalid-message]')
                .should('contain', 'La quantité saisie est incorrecte')
        })
    })

    describe('Removing an expected delivery', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([SUPPLIER_ORDER_UPDATE])

            cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
                fixture: 'erp/supplier-orders/supplier_order_123456.json',
            }).as('supplier_orders')
            cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
                fixture: 'erp/supplier-order-products/supplier_order_products.json',
            }).as('supplier_order_articles')

            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456')
            cy.toggleMenu()

            cy.wait('@supplier_orders')
        })

        it('cannot remove a delivery that has already been delivered', () => {
            cy.get('[data-context=create-button]').click()

            cy.get('[data-context=table-row]').eq(0).as('article')
            cy.get('@article').find('[data-context=delivery]').eq(0).find('[data-context=remove]').should('not.exist')
        })

        it('cannot remove a delivery if already partially delivered', () => {
            cy.get('[data-context=create-button]').click()

            cy.get('[data-context=table-row]').eq(0).as('article')
            cy.get('@article').find('[data-context=delivery]').eq(1).find('[data-context=remove]').should('not.exist')
        })

        it('can remove a delivery not delivered yet', () => {
            cy.get('[data-context=create-button]').click()

            cy.get('[data-context=table-row]').eq(0).as('article')
            cy.get('@article').find('[data-context=delivery]').eq(2).find('[data-context=remove]').should('exist')
        })

        it('resets remaining values when editing', () => {
            cy.initReloadTest()
            cy.get('[data-context=create-button]').click()

            // 5 deliveries
            cy.get('[data-context=table-row]')
                .eq(0)
                .as('article')
                .find('[data-context=delivery]')
                .should('have.length', 5)

            // remove one
            // check request error
            cy.intercept('DELETE', '**/api/erp/v1/expected-delivery/855', {
                statusCode: 500,
                body: { error: 'Blablabla' },
            }).as('delete_error')

            cy.get('[data-context=table-row]')
                .eq(0)
                .find('[data-context=delivery]')
                .eq(2)
                .as('delivery')
                .find('[data-context=remove]')
                .click()
            cy.wait('@delete_error')
            cy.wait('@supplier_order_articles')
            cy.get('[data-context=error-message]').should('contain', 'Une erreur est survenue lors de la suppression.')

            // check success and params sent to API
            cy.intercept('DELETE', '**/api/erp/v1/expected-delivery/855', {
                statusCode: 200,
                body: {},
            }).as('delete_success')

            cy.get('[data-context=table-row]')
                .eq(0)
                .find('[data-context=delivery]')
                .eq(2)
                .as('delivery')
                .find('[data-context=remove]')
                .click()

            cy.wait('@delete_success')
            cy.get('table td > [data-context=skeleton]').should('not.exist')

            // 4 deliveries
            cy.get('@article').find('[data-context=delivery]').should('have.length', 4)

            // remaining quantity
            cy.get('@article').find('[data-context=new-quantity]').should('have.value', '25')

            // should reload page after popin closure
            cy.checkHasNotBeenReloaded()
            cy.get('[data-context=slide-out-container] header a.close').click()
            cy.checkHasBeenReloaded()
        })
    })

    describe('Removing expected deliveries by date', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([SUPPLIER_ORDER_UPDATE])

            cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
                fixture: 'erp/supplier-orders/supplier_order_123456.json',
            }).as('supplier_orders')
            cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
                fixture: 'erp/supplier-order-products/supplier_order_products.json',
            }).as('supplier_order_articles')

            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456')
            cy.toggleMenu()

            cy.wait('@supplier_orders')
            cy.get('[data-context=create-button]').click()
            cy.wait('@supplier_order_articles')
            cy.get('[data-context=slide-out-container] [data-context=supplier-order]').should('be.visible').as('form')
        })

        it('gives an ordered list of dates (unique) found in the expected deliveries', () => {
            // not displayed by default
            cy.get('[data-context=dates-deletion]').should('not.exist')

            // has button to open the panel
            cy.get('@form')
                .find('[data-context=open-dates-deletion-btn]')
                .should('be.visible')
                .should('contain', 'Supprimer une date')
                .click()

            // display the dates
            cy.get('[data-context=dates-deletion]').should('be.visible').as('panel')

            // warning phrase
            cy.get('@panel')
                .find('[data-context=title]')
                .should('contain', 'Attention, la date sera supprimée sur l’ensemble des produits')

            // list, ordered
            cy.get('@panel').find('[data-context=date-to-delete]').should('have.length', 7).as('list')
            cy.get('@list').eq(0).should('contain', '01/01/2011')
            cy.get('@list').eq(1).should('contain', '23/07/2021')
            cy.get('@list').eq(2).should('contain', '23/08/2021')
            cy.get('@list').eq(3).should('contain', '01/09/2021')
            cy.get('@list').eq(4).should('contain', '23/10/2088')
            cy.get('@list').eq(5).should('contain', '01/11/2088')
            cy.get('@list').eq(6).should('contain', '23/10/2090')

            // a date should have a delete button next to it
            cy.get('@list').eq(0).find('[data-context=remove]').should('be.visible')
        })

        it('can delete multiple expected deliveries at once', () => {
            cy.intercept('DELETE', '**/api/erp/v1/expected-delivery/4566', { statuCodes: 200, body: {} }).as(
                'api_delete_expected_delivery_4566',
            )
            cy.intercept('DELETE', '**/api/erp/v1/expected-delivery/14785', { statuCodes: 200, body: {} }).as(
                'api_delete_expected_delivery_1478',
            )
            cy.initReloadTest()
            cy.get('@form').find('[data-context=open-dates-deletion-btn]').click()

            // pre-check
            // note: date used in 2 expected deliveries
            const date = '23/10/2088'
            cy.get(`[data-context=erp-table] [data-context=delivery]:contains(${date})`).should('have.length', 2)
            cy.get('[data-context=new-quantity]').as('new_quantities')
            cy.get('@new_quantities').eq(0).should('have.value', 20)
            cy.get('@new_quantities').eq(1).should('have.value', 0)

            // trigger deletions
            cy.get(`[data-context=date-to-delete]:contains(${date}) [data-context=remove]`).click()
            cy.wait(['@api_delete_expected_delivery_4566', '@api_delete_expected_delivery_1478'])

            // date should be deleted from the list and from the expected deliveries
            cy.get(`[data-context=date-to-delete]:contains(${date})`).should('have.length', 0)
            cy.get(`[data-context=erp-table] [data-context=delivery]:contains(${date})`).should('have.length', 0)

            // quantity fields are properly recomputed
            cy.get('@new_quantities').eq(0).should('have.value', 30)
            cy.get('@new_quantities').eq(1).should('have.value', 25)

            // should reload page after slide-in closes
            cy.checkHasNotBeenReloaded()
            cy.get('[data-context=slide-out-container] header a.close').click()
            cy.checkHasBeenReloaded()
        })

        it('properly handle cases where a delete call may fail', () => {
            cy.intercept('DELETE', '**/api/erp/v1/expected-delivery/4566', { statuCodes: 200, body: {} }).as(
                'api_delete_expected_delivery_4566',
            )
            cy.intercept('DELETE', '**/api/erp/v1/expected-delivery/14785', {
                statusCode: 500,
                body: { error: 'Blablabla' },
            }).as('api_delete_expected_delivery_1478')
            cy.initReloadTest()
            cy.get('@form').find('[data-context=open-dates-deletion-btn]').click()

            // delete a date used in 2 expected deliveries
            const date = '23/10/2088'
            cy.get(`[data-context=erp-table] [data-context=delivery]:contains(${date})`).should('have.length', 2)
            cy.get(`[data-context=date-to-delete]:contains(${date}) [data-context=remove]`).click()
            cy.wait('@api_delete_expected_delivery_4566')
            cy.wait('@api_delete_expected_delivery_1478')

            // the date should remain in the list
            cy.get(`[data-context=date-to-delete]:contains(${date})`).should('have.length', 1)
            // 1 date should have been deleted from the expected deliveries
            cy.get(`[data-context=erp-table] [data-context=delivery]:contains(${date})`).should('have.length', 1)
            // an error message indicates that a deletion failed
            cy.get('[data-context=error-message]').should('contain', 'La suppression a échoué pour 1 ligne(s)')

            // should reload page after popin closure
            cy.checkHasNotBeenReloaded()
            cy.get('[data-context=slide-out-container] header a.close').click()
            cy.checkHasBeenReloaded()
        })
    })
})
