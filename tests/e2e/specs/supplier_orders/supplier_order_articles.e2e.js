import { SUPPLIER_ORDER_UPDATE } from '../../../../src/apps/erp/permissions'

describe('Display supplier order articles', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    describe('With updating permission', () => {
        beforeEach(() => {
            cy.mockErpUser([SUPPLIER_ORDER_UPDATE])
            cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
                fixture: 'erp/supplier-order-products/supplier_order_products.json',
            }).as('supplier_order_articles')

            cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
                fixture: 'erp/supplier-orders/supplier_order_123456.json',
            }).as('supplier_orders')

            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456')
            cy.wait('@supplier_orders')
        })

        it('lists all supplier order articles', () => {
            cy.get('[data-context=page-header]').should('contain', 'Commande fournisseur 123456')
            cy.get('[data-context=supplier-order]').should('not.exist')
            cy.get('[data-context=create-button]').should('contain', 'Livraisons prévues').click()
            cy.wait('@supplier_order_articles').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    where: { _and: [{ supplier_order_id: { _eq: 123456 } }, { is_delivered: { _eq: false } }] },
                    limit: 999,
                    included_dependencies: ['expected_deliveries'],
                })
            })

            cy.get('[data-context=slide-out-container] [data-context=supplier-order]')
                .should('be.visible')
                .find('[data-context=page-header]')
                .should('contain', 'Edition produits')

            // Check table header
            cy.get('[data-context=slide-out-container] [data-context=erp-table]').as('table')
            cy.get('@table').find('thead tr th').as('header')
            cy.get('@header').should('have.length', 4)
            cy.get('@header').eq(1).should('contain', 'Qtés')
            cy.get('@header').eq(2).should('contain', 'Produit')
            cy.get('@header').eq(3).should('contain', 'Livraisons prévues')

            // content first article
            cy.get('[data-context=slide-out-container] [data-context=table-row]')
                .should('have.length', 5)
                .eq(0)
                .as('article')
            cy.get('@article').find('[data-context=quantity]').should('contain', '8/50')
            cy.get('@article').find('[data-context=erp-article-item]').as('article_item')
            cy.get('@article_item').find('[data-context=image]').should('have.attr', 'src')
            cy.get('@article_item')
                .find('[data-context=sku]')
                .should('contain', ' FIIOBTR5NR ')
                .should('have.attr', 'href', '/articles/FIIOBTR5NR/')
            cy.get('@article_item').find('[data-context=name]').should('contain', 'Récepteur bluetooth Fiio BTR5 Noir')

            // display different deliveries
            // with unknown date (01-01-2011)
            cy.get('@article')
                .find('[data-context=delivery]')
                .should('have.length', 5)
                .eq(4)
                .as('expected_deliveries')
                .find('[data-context=badge]')
                .should('contain', '01/01/2011')
            cy.get('@expected_deliveries').find('[data-context=leading]').should('contain', '0 /')
            cy.get('@expected_deliveries').find('[data-context=expected-qty]').should('have.value', '5')

            // past and complete
            cy.get('@article')
                .find('[data-context=delivery]')
                .eq(0)
                .as('delivered_delivery')
                .find('[data-context=badge]')
                .should('contain', '23/07/2021')
            cy.get('@delivered_delivery').find('[data-context=leading]').should('contain', '5 /')
            cy.get('@delivered_delivery').find('[data-context=expected-qty]').should('have.value', '5')

            // past and partial
            cy.get('@article')
                .find('[data-context=delivery]')
                .eq(1)
                .as('partial_delivery')
                .find('[data-context=badge]')
                .should('contain', '23/08/2021')
            cy.get('@partial_delivery').find('[data-context=leading]').should('contain', '3 /')
            cy.get('@partial_delivery').find('[data-context=expected-qty]').should('have.value', '5')

            // past and not delivered at all
            cy.get('@article')
                .find('[data-context=delivery]')
                .eq(2)
                .as('past_delivery_not_delivered')
                .find('[data-context=badge]')
                .should('contain', '01/09/2021')
            cy.get('@past_delivery_not_delivered').find('[data-context=leading]').should('contain', '0 /')
            cy.get('@past_delivery_not_delivered').find('[data-context=expected-qty]').should('have.value', '5')

            // future
            cy.get('@article')
                .find('[data-context=delivery]')
                .eq(3)
                .as('future_delivery')
                .find('[data-context=badge]')
                .should('contain', '23/10/2088')
            cy.get('@future_delivery').find('[data-context=leading]').should('contain', '0 /')
            cy.get('@future_delivery').find('[data-context=expected-qty]').should('have.value', '10')

            // article without any expected delivery is still displayed
            cy.get('[data-context=slide-out-container] [data-context=table-row]').eq(2).as('article_without_delivery')
            cy.get('@article_without_delivery').find('[data-context=quantity]').should('contain', '0/50')
            cy.get('@article_without_delivery').find('[data-context=delivery]').should('have.length', 0)
        })

        it('display quantities already displayed on future expected date', function () {
            cy.get('[data-context=create-button]').click()
            cy.wait('@supplier_order_articles')

            // article with a future delivery already delivered
            cy.get('[data-context=slide-out-container] [data-context=table-row]')
                .eq(3)
                .as('article_with_future_delivery')
            cy.get('@article_with_future_delivery').find('[data-context=quantity]').should('contain', '17/50')

            cy.get('@article_with_future_delivery')
                .find('[data-context=delivery]')
                .should('have.length', 3)
                .as('expected_deliveries')
                .eq(2)
                .as('unknown_delivery')
                .find('[data-context=badge]')
                .should('contain', '01/01/2011')
            cy.get('@unknown_delivery').find('[data-context=leading]').should('contain', '0 /')
            cy.get('@unknown_delivery').find('[data-context=expected-qty]').should('have.value', '5')

            cy.get('@expected_deliveries')
                .eq(0)
                .as('past_delivered_delivery')
                .find('[data-context=badge]')
                .should('contain', '23/07/2021')
            cy.get('@past_delivered_delivery').find('[data-context=leading]').should('contain', '5 /')
            cy.get('@past_delivered_delivery').find('[data-context=expected-qty]').should('have.value', '5')

            cy.get('@expected_deliveries')
                .eq(1)
                .as('future_delivery')
                .find('[data-context=badge]')
                .should('contain', '23/10/2090')
            cy.get('@future_delivery').find('[data-context=leading]').should('contain', '10 /')
            cy.get('@future_delivery').find('[data-context=expected-qty]').should('have.value', '10')

            // article with a future delivery already partially delivered
            cy.get('[data-context=slide-out-container] [data-context=table-row]')
                .eq(4)
                .as('article_with_future_partial_delivery')
            cy.get('@article_with_future_partial_delivery').find('[data-context=quantity]').should('contain', '8/50')

            cy.get('@article_with_future_partial_delivery')
                .find('[data-context=delivery]')
                .should('have.length', 3)
                .as('expected_deliveries')
                .eq(2)
                .as('unknown_delivery')
                .find('[data-context=badge]')
                .should('contain', '01/01/2011')
            cy.get('@unknown_delivery').find('[data-context=leading]').should('contain', '0 /')
            cy.get('@unknown_delivery').find('[data-context=expected-qty]').should('have.value', '5')

            cy.get('@expected_deliveries')
                .eq(0)
                .as('past_delivered_delivery')
                .find('[data-context=badge]')
                .should('contain', '23/07/2021')
            cy.get('@past_delivered_delivery').find('[data-context=leading]').should('contain', '5 /')
            cy.get('@past_delivered_delivery').find('[data-context=expected-qty]').should('have.value', '5')

            cy.get('@expected_deliveries')
                .eq(1)
                .as('partial_future_delivery')
                .find('[data-context=badge]')
                .should('contain', '23/10/2090')
            cy.get('@partial_future_delivery').find('[data-context=leading]').should('contain', '3 /')
            cy.get('@partial_future_delivery').find('[data-context=expected-qty]').should('have.value', '10')
        })

        it('open the expected deliveries on a product when receiving a demand from the iframe', function () {
            const emitOpenExpectedDeliveriesMessage = () => {
                cy.getWrapper().then((wrapper) => {
                    wrapper.$emit('receiveMessage', {
                        action: 'open-expected-deliveries',
                        payload: {
                            id_commande_fournisseur: 123456,
                            id_produit: 54321,
                        },
                    })
                })
            }
            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456')

            emitOpenExpectedDeliveriesMessage()
            cy.wait('@supplier_order_articles').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    where: {
                        _and: [
                            { supplier_order_id: { _eq: 123456 } },
                            { article_id: { _eq: 54321 } },
                            { is_delivered: { _eq: false } },
                        ],
                    },
                    limit: 999,
                    included_dependencies: ['expected_deliveries'],
                })
            })

            cy.get('[data-context=slide-out-container] [data-context=supplier-order]').should('be.visible')
        })
    })

    describe('Edition forbidden', () => {
        it('cannot create a new expected delivery without permission', () => {
            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456')
            cy.get('[data-context=create-button]').should('not.exist')
        })

        it('cannot create a new expected delivery if supplier order status not in progress', () => {
            cy.mockErpUser([SUPPLIER_ORDER_UPDATE])

            cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
                fixture: 'erp/supplier-orders/supplier_order_5434.json',
            }).as('supplier_orders')

            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=5434')
            cy.wait('@supplier_orders').then((xhr) => {
                expect(xhr.request.body.where.supplier_order_id._eq).to.eq(5434)
            })

            cy.get('[data-context=create-button]').should('not.exist')
        })
    })
})
