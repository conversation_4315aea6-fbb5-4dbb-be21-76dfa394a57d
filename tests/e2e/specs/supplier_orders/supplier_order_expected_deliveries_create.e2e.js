import { SUPPLIER_ORDER_UPDATE } from '../../../../src/apps/erp/permissions.js'

describe('Creation of new expected deliveries', () => {
    describe('With updating permission', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([SUPPLIER_ORDER_UPDATE])

            // freeze clock - Today is 06/09/2021
            cy.mockDate(new Date(2021, 8, 6, 11, 40, 0, 0))

            cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
                fixture: 'erp/supplier-orders/supplier_order_123456.json',
            }).as('supplier_orders')
            cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
                fixture: 'erp/supplier-order-products/supplier_order_products.json',
            }).as('supplier_order_articles')

            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456')
            cy.wait('@supplier_orders')
        })

        // User can select any date from now OR 01/01/2011
        it('can select a new date', () => {
            cy.get('[data-context=create-button]').click()
            cy.wait('@supplier_order_articles')
            cy.get('[data-context=supplier-order]').should('be.visible')

            // date selectors
            cy.get('[data-context=date-picker] input')
                .should('have.attr', 'placeholder', 'Sélectionner nouvelle date de livraison')
                .click()
            // opens on today's month with only today and future enabled (Remember: it's 06/09/2021)
            cy.get('[data-context=date-picker] [data-context=header]').should('contain', 'septembre')
            cy.get('[data-context=date-picker] [data-context=header]').should('contain.text', '2021')
            cy.get('[data-context=date-picker]')
                .find(`div[data-context=day]:contains(5)`)
                .first()
                .should('have.css', 'cursor', 'not-allowed')
            cy.get('[data-context=date-picker]')
                .find(`div[data-context=day]:contains(6)`)
                .first()
                .should('not.have.css', 'cursor', 'not-allowed')

            // direct selection of 01/01/2011 with a click
            cy.get('[data-context=unknown-date]').should('contain', '01/01/2011').click()
            // opens on january 2011 with all except for the 1st disabled
            cy.get('[data-context=date-picker] input').should('have.value', '01 janv. 2011').click()
            cy.get('[data-context=date-picker] [data-context=header]').should('contain', 'janvier')
            cy.get('[data-context=date-picker] [data-context=header]').should('contain.text', '2011')
            cy.get('[data-context=date-picker]')
                .find(`div[data-context=day]:contains(2)`)
                .first()
                .should('have.css', 'cursor', 'not-allowed')
            cy.get('[data-context=date-picker]')
                .find(`div[data-context=day]:contains(1)`)
                .first()
                .should('have.css', 'cursor', 'not-allowed')

            // remove unknown date selection
            cy.get('[data-context=unknown-date]').click()
            cy.get('[data-context=date-picker] input').should('be.empty').click()
            // opens on today's month with only today and future enabled (Remember: it's 06/09/2021)
            cy.get('[data-context=date-picker] [data-context=header]').should('contain', 'septembre')
            cy.get('[data-context=date-picker] [data-context=header]').should('contain.text', '2021')
        })

        it('can select articles and quantities as long as a date has been selected', () => {
            cy.get('[data-context=create-button]').click()

            // Without selected date, we cant select articles
            cy.get('[data-context=toggle]').should('have.length', 5)

            cy.get('[data-context=toggle]').eq(2).should('have.attr', 'data-disabled')
            cy.get('[data-context=toggle]').eq(2).find('button').should('not.have.attr', 'aria-checked', 'true')

            cy.get('[data-context=new-quantity]')
                .should('have.length', 5)
                .eq(2)
                .should('have.attr', 'disabled', 'disabled')

            // With selected date, we can select articles
            cy.get('[data-context=unknown-date]').click()

            cy.get('[data-context=toggle]').eq(2).should('not.have.attr', 'data-disabled')
            cy.get('[data-context=toggle]').eq(2).find('button').should('not.have.attr', 'aria-checked', 'true')

            // if i remove the date, we cannot select articles
            cy.get('[data-context=unknown-date]').click()
            cy.get('[data-context=toggle]').eq(2).should('have.attr', 'data-disabled')
            cy.get('[data-context=toggle]').eq(2).find('button').should('not.have.attr', 'aria-checked', 'true')

            // date selected again
            cy.get('[data-context=unknown-date]').click()

            // when I select an article, I have to select a quantity
            // default value is the remaining quantity
            cy.get('[data-context=table-row]').eq(2).as('article')
            cy.get('@article').find('[data-context=toggle]').click()
            cy.get('@article').find('[data-context=toggle]').find('button').should('have.attr', 'aria-checked', 'true')
            cy.get('@article')
                .find('[data-context=new-quantity]')
                .should('have.value', '50')
                .should('not.have.attr', 'disabled', 'disabled')
            cy.get('@article').find('[data-context=new-quantity]').clear()
            cy.get('@article').find('[data-context=new-quantity]').type('10')

            // when removing article from selection, the quantity input is disabled and reset too
            cy.get('@article').find('[data-context=toggle]').click()
            cy.get('@article').find('[data-context=new-quantity]').should('have.attr', 'disabled', 'disabled')

            // article selected back and quantity is reset to remaining quantity
            cy.get('@article').find('[data-context=toggle]').click()
            cy.get('@article').find('[data-context=new-quantity]').should('have.value', '50')
        })

        it('has action buttons', () => {
            cy.get('[data-context=create-button]').click()

            // close side panel
            cy.get('div.sticky [data-context=close-button]').should('contain', 'Fermer').click()
            // reopen
            cy.get('[data-context=create-button]').click()

            // add new expected deliveries button
            cy.get('div.sticky [data-context=add-button]')
                .should('contain', 'Ajouter')
                .should('have.attr', 'disabled', 'disabled')
            // select date and at least 1 article with a quantity
            cy.get('[data-context=unknown-date]').click()
            cy.get('[data-context=table-row]').eq(2).as('article')
            cy.get('@article').find('[data-context=toggle]').click()
            // add button is now enabled
            cy.get('[data-context=add-button]').should('not.have.attr', 'disabled', 'disabled')

            // disable add button when no article selected
            cy.get('@article').find('[data-context=toggle]').click()
            cy.get('[data-context=add-button]').should('have.attr', 'disabled', 'disabled')

            // disable add button when quantity set at 0
            cy.get('@article').find('[data-context=toggle]').click()
            cy.get('@article').find('[data-context=new-quantity]').clear()
            cy.get('@article').find('[data-context=new-quantity]').type('0')
            cy.get('[data-context=add-button]').should('have.attr', 'disabled', 'disabled')

            // disable add button when no date
            cy.get('@article').find('[data-context=new-quantity]').clear()
            cy.get('@article').find('[data-context=new-quantity]').type('10')
            cy.get('[data-context=unknown-date]').click()
            cy.get('[data-context=add-button]').should('have.attr', 'disabled', 'disabled')
        })

        it('can create a new expected delivery', () => {
            cy.initReloadTest()
            cy.get('[data-context=create-button]').click()

            // select date and at least 1 article with a quantity
            cy.get('[data-context=unknown-date]').click()
            cy.get('[data-context=table-row]').eq(2).find('[data-context=toggle]').click()

            // check request error
            cy.intercept('POST', '**/api/erp/v1/supplier-order/123456/expected-deliveries', {
                statusCode: 500,
                body: { error: 'Blablabla' },
            }).as('create_error')

            cy.get('[data-context=add-button]').click()
            cy.wait('@create_error')
            cy.get('[data-context=error-message]').should(
                'contain',
                'Une erreur est survenue lors du chargement, veuillez réessayer.',
            )

            // check success and params sent to API
            cy.intercept('POST', '**/api/erp/v1/supplier-order/123456/expected-deliveries', {
                statusCode: 200,
                body: {},
            }).as('create_success')

            cy.get('[data-context=add-button]').click()
            cy.wait('@create_success').then((xhr) => {
                expect(xhr.request.body.date).to.eq('2011-01-01')
                expect(xhr.request.body.expected_deliveries).to.deep.eq([
                    { supplier_order_product_id: 415131, quantity: 50 },
                ])
            })
            cy.get('[data-context=error-message]').should('not.exist')

            // data are reloaded
            cy.wait('@supplier_order_articles').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    where: { _and: [{ supplier_order_id: { _eq: 123456 } }, { is_delivered: { _eq: false } }] },
                    included_dependencies: ['expected_deliveries'],
                    limit: 999,
                })
            })

            cy.toast(`Création effectuée`, 'success')

            // should reload page after popin closure
            cy.checkHasNotBeenReloaded()
            cy.get('[data-context=slide-out-container] header a.close').click()
            cy.checkHasBeenReloaded()
        })

        it('cannot create with invalid quantity', () => {
            cy.get('[data-context=create-button]').click()

            // select date and at least 1 article with a quantity
            cy.get('[data-context=unknown-date]').click()
            cy.get('[data-context=table-row]').eq(2).as('article').find('[data-context=toggle]').click()

            // type invalid quantity = more than expected
            cy.get('@article').find('[data-context=new-quantity]').clear()
            cy.get('@article').find('[data-context=new-quantity]').type('999')
            cy.get('@article').find('[data-context=new-quantity]').should('have.class', 'border-red-500')
            cy.get('[data-context=invalid-creation]').should(
                'contain',
                'Une ou plusieurs quantités saisies sont incorrectes et empêchent la validation.',
            )
            cy.get('[data-context=add-button]').should('contain', 'Ajouter').should('have.attr', 'disabled', 'disabled')

            // type invalid quantity = negative
            cy.get('@article').find('[data-context=new-quantity]').clear()
            cy.get('@article').find('[data-context=new-quantity]').type('-10')
            cy.get('@article').find('[data-context=new-quantity]').should('have.class', 'border-red-500')
            cy.get('[data-context=invalid-creation]').should(
                'contain',
                'Une ou plusieurs quantités saisies sont incorrectes et empêchent la validation.',
            )
            cy.get('[data-context=add-button]').should('contain', 'Ajouter').should('have.attr', 'disabled', 'disabled')

            // type valid quantity
            cy.get('@article').find('[data-context=new-quantity]').clear()
            cy.get('@article').find('[data-context=new-quantity]').type('10')
            cy.get('@article').find('[data-context=new-quantity]').should('not.have.class', 'border-red-500')
            cy.get('[data-context=invalid-creation]').should('not.exist')
            cy.get('[data-context=add-button]')
                .should('contain', 'Ajouter')
                .should('not.have.attr', 'disabled', 'disabled')
        })

        it('cannot create if date already saved', () => {
            cy.get('[data-context=create-button]').click()

            // select a date that already exist
            cy.get('[data-context=unknown-date]').click()
            cy.get('[data-context=table-row]').eq(0).find('[data-context=toggle]').should('have.attr', 'data-disabled')

            // change date for a new one
            cy.get('[data-context=unknown-date]').click()
            // Remember : today is 6/09/2021 so it's September 2021 that opens by default
            cy.get('[data-context=date-picker]').click()
            cy.get('[data-context=date-picker] [data-context=day]:contains(30):nth(1)').click()
            cy.get('[data-context=table-row]')
                .eq(0)
                .find('[data-context=toggle]')
                .should('not.have.attr', 'data-disabled')
        })

        it('can toggle all eligible toggles at once', () => {
            // open the slider
            cy.get('[data-context=create-button]').click()
            cy.wait('@supplier_order_articles')
            cy.get('[data-context=slide-out-container] [data-context=supplier-order]').should('be.visible').as('slider')

            // "toggle all" button is in the table's header's first column, disabled by default (no date selected)
            cy.get('@slider')
                .find('[data-context=erp-table] thead tr:nth-of-type(1) [data-context=toggle-all]')
                .as('toggle-all-btn')
                .should('be.visible')
                .should('have.attr', 'data-disabled')

            // click on it do nothing
            cy.get('@toggle-all-btn').click()
            cy.get('@toggle-all-btn').should('have.attr', 'data-disabled')

            // enabled when setting a date
            cy.get('@slider').find('[data-context=date-picker]').click()
            cy.get('@slider').find('[data-context=date-picker] [data-context=day]:contains(13)').click()
            cy.get('@toggle-all-btn').should('not.have.attr', 'data-disabled')
            cy.get('@toggle-all-btn').find('button').should('not.have.attr', 'aria-checked', 'true')

            // click should activate the first and third lines
            // the second line is disabled and should remain inactive
            cy.get('@slider').find('[data-context=toggle]').as('toggles')
            cy.get('@toggles').should('not.have.attr', 'data-disabled')
            cy.get('@toggles').eq(0).should('not.have.attr', 'data-disabled')
            cy.get('@toggles').eq(0).find('button').should('not.have.attr', 'aria-checked', 'true')
            cy.get('@toggles').eq(1).should('have.attr', 'data-disabled')
            cy.get('@toggles').eq(1).find('button').should('not.have.attr', 'aria-checked', 'true')
            cy.get('@toggles').eq(2).should('not.have.attr', 'data-disabled')
            cy.get('@toggles').eq(2).find('button').should('not.have.attr', 'aria-checked', 'true')

            cy.get('@toggle-all-btn').click()
            cy.get('@toggle-all-btn').find('button').should('have.attr', 'aria-checked', 'true')
            cy.get('@toggles').eq(0).should('not.have.attr', 'data-disabled')
            cy.get('@toggles').eq(0).find('button').should('have.attr', 'aria-checked', 'true')
            cy.get('@toggles').eq(1).should('have.attr', 'data-disabled')
            cy.get('@toggles').eq(1).find('button').should('not.have.attr', 'aria-checked', 'true')
            cy.get('@toggles').eq(2).should('not.have.attr', 'data-disabled')
            cy.get('@toggles').eq(2).find('button').should('have.attr', 'aria-checked', 'true')

            // another click toggle off
            cy.get('@toggle-all-btn').click()
            cy.get('@toggle-all-btn').find('button').should('not.have.attr', 'aria-checked', 'true')
            cy.get('@toggles').eq(0).should('not.have.attr', 'data-disabled')
            cy.get('@toggles').eq(0).find('button').should('not.have.attr', 'aria-checked', 'true')
            cy.get('@toggles').eq(1).should('have.attr', 'data-disabled')
            cy.get('@toggles').eq(1).find('button').should('not.have.attr', 'aria-checked', 'true')
            cy.get('@toggles').eq(2).should('not.have.attr', 'data-disabled')
            cy.get('@toggles').eq(2).find('button').should('not.have.attr', 'aria-checked', 'true')

            // select the unknown date and activate the toggle
            // => the first line should remain disabled and inactive (date already used on this line)
            cy.get('[data-context=unknown-date]').click()
            cy.get('@toggle-all-btn').click()
            cy.get('@toggle-all-btn').find('button').should('have.attr', 'aria-checked', 'true')
            cy.get('@toggles').eq(0).should('have.attr', 'data-disabled')
            cy.get('@toggles').eq(0).find('button').should('not.have.attr', 'aria-checked', 'true')

            // unselecting the unknown date should toggle off the "toggle all" button and disable it
            cy.get('[data-context=unknown-date]').click()
            cy.get('@toggle-all-btn').should('have.attr', 'data-disabled')
        })
    })

    describe('Editing one article', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([SUPPLIER_ORDER_UPDATE])

            // freeze clock - Today is 06/09/2021
            cy.mockDate(new Date(2021, 8, 6, 11, 40, 0, 0))

            cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
                fixture: 'erp/supplier-orders/supplier_order_123456.json',
            }).as('supplier_orders')
            cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
                fixture: 'erp/supplier-order-products/supplier_order_product.json',
            }).as('supplier_order_article')

            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456')
            cy.wait('@supplier_orders')
        })

        it('preselect article if only one displayed', () => {
            cy.get('[data-context=create-button]').click()

            cy.get('[data-context=toggle]').should('have.length', 1)
            cy.get('[data-context=toggle]').eq(0).should('have.attr', 'data-disabled')
            cy.get('[data-context=toggle]').find('button').should('have.attr', 'aria-checked', 'true')

            // stays disabled even if date selected
            cy.get('[data-context=unknown-date]').click()
            cy.get('[data-context=toggle]').eq(0).should('have.attr', 'data-disabled')
            cy.get('[data-context=toggle]').find('button').should('have.attr', 'aria-checked', 'true')

            // article stays disabled and selected when selected back a date
            cy.get('[data-context=unknown-date]').click()
            cy.get('[data-context=toggle]').eq(0).should('have.attr', 'data-disabled')
            cy.get('[data-context=toggle]').find('button').should('have.attr', 'aria-checked', 'true')
        })
    })

    describe('No quantity left to create', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([SUPPLIER_ORDER_UPDATE])

            // freeze clock - Today is 06/09/2021
            cy.mockDate(new Date(2021, 8, 6, 11, 40, 0, 0))

            cy.intercept('POST', '**/api/erp/v1/supplier-orders', {
                fixture: 'erp/supplier-orders/supplier_order_123456.json',
            }).as('supplier_orders')
            cy.intercept('POST', '**/api/erp/v1/supplier-order-products', {
                fixture: 'erp/supplier-order-products/supplier_order_products_without_quantity.json',
            }).as('supplier_order_products')

            cy.visit('/legacy/commandeFournisseur/edition?id_commande_fournisseur=123456')
            cy.wait('@supplier_orders')
        })

        it('cannot select any date if there is no quantity left for creation', () => {
            cy.get('[data-context=create-button]').click()

            // no quantities left on those 2 articles
            cy.get('[data-context=table-row]').eq(0).find('[data-context=new-quantity]').should('have.value', '0')
            cy.get('[data-context=table-row]').eq(1).find('[data-context=new-quantity]').should('have.value', '0')

            // we cant select a date
            cy.get('[data-context=date-picker] input').should('have.attr', 'disabled', 'disabled')
            cy.get('[data-context=unknown-date]').should('have.attr', 'disabled', 'disabled')
        })
    })
})
