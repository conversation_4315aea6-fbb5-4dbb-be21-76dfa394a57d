describe('Product return notes page', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.visit('/legacy/v1/retours/tableau-bord.php', {
            onBeforeLoad(win) {
                cy.stub(win, 'open').as('windowOpen')
            },
        })
    })

    it('redirect success', () => {
        cy.intercept('GET', '**/api/erp/v1/product-return-note/123/sticker-url', {
            statusCode: 200,
            body: { status: 'success', data: { url: 'https://google.com' } },
        }).as('getUrl')

        cy.emitEvent('generate-return-sticker-mondialrelay', { product_return_note_id: 123 })
        cy.wait('@getUrl')

        cy.get('@windowOpen').should('be.calledWith', 'https://google.com', '_blank')
    })

    it('redirect fail', () => {
        cy.intercept('GET', '**/api/erp/v1/product-return-note/456/sticker-url', {
            statusCode: 404,
            body: false,
        }).as('getUrl')
        cy.emitEvent('generate-return-sticker-mondialrelay', { product_return_note_id: 456 })
        cy.wait('@getUrl')

        cy.toast(`Une erreur est survenue, veuillez réessayer.`, 'danger')
    })
})
