describe('After sale service file page', () => {
    const FIXTURE_FILE = 'erp/after_sale_service/after_sale_service_file.json'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/after-sale-services', {
            fixture: FIXTURE_FILE,
        }).as('after_sale_services')
    })

    it('display after sale service file SVD page header', () => {
        cy.fixture(FIXTURE_FILE).then((payload) => {
            payload.data.after_sale_services[0].origin = 'son-video.com'
            cy.intercept('POST', '**/api/erp/v1/after-sale-services', {
                statusCode: 200,
                status: 'success',
                body: payload,
            }).as('after_sale_services')
        })

        cy.visit('/legacy/sav/articleEdit?id=21100')

        cy.wait('@after_sale_services').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                after_sale_service_id: {
                    _eq: 21100,
                },
            })
        })

        cy.get('[data-context=page-header]')
            .as('header')
            .should('be.visible')
            .should('contain', 'Dossier SAV')
            .should('contain', '21100')

        cy.get('@header').find('[data-context=origin]').should('contain', 'son-video.com')

        cy.get('@header').find('[data-context=open-ezl-invoice]').should('not.exist')
    })

    it('display after sale service file EZL page header', () => {
        cy.visit('/legacy/sav/articleEdit?id=21100')

        cy.wait('@after_sale_services')

        cy.get('[data-context=page-header]')
            .as('header')
            .should('be.visible')
            .should('contain', 'Dossier SAV')
            .should('contain', '21100')

        cy.get('@header').find('[data-context=origin]').should('contain', 'ecranlounge.com')

        cy.get('@header').find('[data-context=open-ezl-invoice]').should('contain', 'Factures EZL').click()

        cy.get('[data-context=slide-out-container] [data-context=invoice-ezl]').should('be.visible')
    })

    it('can load data when using the "return number" in url', () => {
        cy.visit('/legacy/sav/articleEdit?no_retour=123456')

        cy.wait('@after_sale_services').then((xhr) => {
            expect(xhr.request.body.where).to.deep.eq({
                product_return_note_id: {
                    _eq: 123456,
                },
            })
        })
    })

    it('display after sale service file with api error', () => {
        cy.intercept('POST', '**/api/erp/v1/after-sale-services', {
            statusCode: 404,
            body: {},
        }).as('after_sale_services')

        cy.visit('/legacy/sav/articleEdit?id=21100')

        cy.wait('@after_sale_services')

        cy.toast(`Une erreur est survenue, veuillez réessayer.`, 'danger')

        cy.get('[data-context=page-header]')
            .as('header')
            .should('be.visible')
            .should('contain', 'Dossier SAV')
            .should('contain', '21100')

        cy.get('@header').find('[data-context=origin]').should('not.exist')

        cy.get('@header').find('[data-context=open-ezl-invoice]').should('not.exist')
    })

    describe('Component InvoiceEzl', () => {
        beforeEach(() => {
            cy.intercept('GET', '**/api/erp/v1/easy-lounge/customer-order/2072936/invoices', {
                fixture: 'erp/after_sale_service/invoice_ezl.json',
            }).as('fetch_easylounge_invoice')

            cy.intercept('POST', '**/api/erp/v1/customer-orders', {
                fixture: 'erp/after_sale_service/customer_order.json',
            }).as('customer_order')

            cy.visit('/legacy/sav/articleEdit?id=21100')

            cy.wait('@after_sale_services')
        })

        it('Display invoice EZL', () => {
            cy.get('[data-context=open-ezl-invoice]').click()
            cy.wait(['@customer_order', '@fetch_easylounge_invoice'])

            cy.get('[data-context=slide-out-container] [data-context=invoice-ezl]')
                .as('invoice-ezl')
                .should('be.visible')

            cy.get('@invoice-ezl')
                .find('[data-context=page-header]')
                .should('contain', 'Facture(s) de la commande EasyLounge 2072936')

            cy.get('@invoice-ezl').find('[data-context=erp-table]').as('table')

            const table_header_tests = [
                { should: 'contain', value: 'Référence' },
                { should: 'contain', value: 'date' },
            ]

            cy.get('@table').find('thead tr').checkRow(table_header_tests, { cell_selector: 'th' })

            const table_tests = [
                [
                    [
                        { should: 'contain', value: 'facture' },
                        { should: 'contain', value: '123456' },
                    ],
                    [{ should: 'contain', value: '19/10/2022 09:41:55' }],
                    [
                        {
                            context: 'pdf-ezl',
                            should: 'have.attr',
                            method: 'href',
                            value: 'https://preprod-aws.easylounge.com/pdf/Factures/facture.pdf',
                        },
                        {
                            context: 'capcaisse-link',
                            should: 'have.attr',
                            method: 'href',
                            value: 'https://stagingcapcaisse.easylounge.com/StatCompta/VoirFacture/123456',
                        },
                    ],
                ],
                [
                    [
                        { should: 'contain', value: 'avoir' },
                        { should: 'contain', value: '456789' },
                    ],
                    [{ should: 'contain', value: '19/10/2022 00:00:00' }],
                    [
                        {
                            context: 'pdf-ezl',
                            should: 'have.attr',
                            method: 'href',
                            value: 'https://preprod-aws.easylounge.com/pdf/Factures/avoir.pdf',
                        },
                        {
                            context: 'capcaisse-link',
                            should: 'have.attr',
                            method: 'href',
                            value: 'https://stagingcapcaisse.easylounge.com/StatCompta/VoirFacture/456789',
                        },
                    ],
                ],
            ]

            cy.get('@table').find('tbody tr').as('rows').checkRows(table_tests)
        })

        it('Display invoice EZL but invoice not found', () => {
            cy.intercept('GET', '**/api/erp/v1/easy-lounge/customer-order/2072936/invoices', {
                statusCode: 404,
                body: {},
            }).as('fetch_easylounge_invoice')

            cy.get('[data-context=open-ezl-invoice]').click()
            cy.wait(['@customer_order', '@fetch_easylounge_invoice'])

            cy.get('[data-context=slide-out-container] [data-context=invoice-ezl]')
                .should('be.visible')
                .should('contain', 'Pas de facture trouvée pour cette commande chez EasyLounge')
        })
    })
})
