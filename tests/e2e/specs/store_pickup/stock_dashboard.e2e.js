describe('Store Pickup - Stock Dashboard', function () {
    const PAGE = '/full-dashboard/store-pickup/21/stock-dashboard'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    it('displays expected content', function () {
        cy.intercept('GET', '**/api/erp/v1/warehouse/21/started-store-pickups', {
            fixture: 'erp/warehouse/started-store-pickups/all_possibilities.json',
        }).as('api_started_pickups')

        // freeze clock
        cy.clock(new Date(2021, 3, 11, 11, 40, 0, 0))

        cy.visit(PAGE)
        cy.wait('@api_started_pickups')
        // it uses blank layout
        cy.get('[data-context=base-blank]').should('be.visible')

        // page header
        cy.get('[data-context=page-header]')
            .should('have.class', 'sticky')
            .should('contain', 'Emport Commandes')
            .should('contain', 'BLs à picker dans l’ordre')

        // only some statuses are displayed in the pickup list
        cy.get('[data-context=store-pickup-for-stock]')
            .should('have.length', 4)
            .as('pickups')
            .should('contain', '4536912')
            .should('contain', '4536913')
            .should('contain', '4536915')
            .should('contain', '4536921')

        // content expected in a pickup store for stock
        cy.get('@pickups').eq(0).as('pickup')
        cy.get('@pickup').find('[data-context=qr-code]').should('be.visible')
        cy.get('@pickup')
            .should('contain', '4536912')
            .should('contain', 'Commande 3023603')
            .should('contain', 'Gérard Manvussa')
            .should('contain', 'il y a 10 minutes')

        // picking in progress is displayed when it is the case
        // CREATED
        cy.get('@pickups').eq(0).should('not.contain', 'Picking en cours')
        // PICKING_ABORTED
        cy.get('@pickups').eq(1).should('not.contain', 'Picking en cours')
        // PICKING_STARTED
        cy.get('@pickups').eq(2).should('contain', 'Picking en cours').should('contain', 'par Nathan TABOUILLOT')
        // PICKING_STARTED again, but display is inverted
        cy.get('@pickups').eq(3).should('contain', 'Picking en cours').should('contain', 'par Nathan TABOUILLOT')

        // every 20s, api_started_pickups should be recalled and timer refreshed

        const ticking_time = 60 * 1000 // = 1 minute
        cy.tick(ticking_time)
        cy.wait('@api_started_pickups')
        cy.wait('@api_started_pickups')
        cy.wait('@api_started_pickups')
        cy.get('@pickup').should('contain', 'il y a 11 minutes')

        cy.tick(ticking_time)
        cy.wait('@api_started_pickups')
        cy.wait('@api_started_pickups')
        cy.wait('@api_started_pickups')
        cy.get('@pickup').should('contain', 'il y a 12 minutes')
    })
})
