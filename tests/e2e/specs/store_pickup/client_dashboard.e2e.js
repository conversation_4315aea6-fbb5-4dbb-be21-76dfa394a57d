describe('Store Pickup - Client Dashboard', function () {
    const PAGE = '/full-dashboard/store-pickup/21/client-dashboard'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    it('displays expected content', function () {
        cy.intercept('GET', '**/api/erp/v1/warehouse/21/started-store-pickups', {
            fixture: 'erp/warehouse/started-store-pickups/all_possibilities.json',
        })
        cy.visit(PAGE)

        // it uses blank layout
        cy.get('[data-context=base-blank]').should('be.visible')

        // page header
        cy.get('[data-context=page-header]').should('contain', 'Retrait de marchandises')

        // only some statuses are displayed in the pickup list
        cy.get('[data-context=store-pickup-for-client]')
            .should('have.length', 10)
            .as('pickups')
            .should('not.contain', '3023608')
            .should('not.contain', '3023611')

        // customer name is properly formatted
        cy.get('@pickups').eq(1).should('contain', 'Le Sanglier De Cornouailles A.')

        // sort: « À retirer » first, by customer's last name
        cy.get('@pickups')
            .eq(0)
            .should('contain', 'Elric E.')
            .should('contain', '3023602')
            .should('contain', 'À retirer')
        cy.get('@pickups')
            .eq(1)
            .should('contain', '3023601')
            .should('contain', 'Le Sanglier De Cornouailles A.')
            .should('contain', 'À retirer')
        cy.get('@pickups')
            .eq(2)
            .should('contain', '3023605')
            .should('contain', 'Manvussa G.')
            .should('contain', 'À retirer')
        cy.get('@pickups').eq(3).should('contain', '3023607').should('contain', 'À retirer')
        cy.get('@pickups').eq(4).should('contain', '3023609').should('contain', 'À retirer')
        cy.get('@pickups').eq(5).should('contain', '3023610').should('contain', 'À retirer')

        // sort: « En préparation » second, by customer's last name
        cy.get('@pickups')
            .eq(6)
            .should('contain', '3023612')
            .should('contain', 'De La Vega D.')
            .should('contain', 'En préparation')
        cy.get('@pickups')
            .eq(7)
            .should('contain', '3023606')
            .should('contain', 'Manvussa G.')
            .should('contain', 'En préparation')

        // sort: « En attente » last, by pickup started date
        cy.get('@pickups').eq(8).should('contain', '3023603').should('contain', 'En attente')
        cy.get('@pickups').eq(9).should('contain', '3023604').should('contain', 'En attente')
    })
})
