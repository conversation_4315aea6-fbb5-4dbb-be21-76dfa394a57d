import {
    aliasMutation,
    aliasQ<PERSON>y,
    getMutation<PERSON>lias,
    getQueryAlias,
    GRAPHQL_ENDPOINT,
    hasOperationName,
} from '../../utils/graphql-test-utils'
import { STORE_OPENING_HOURS_UPDATE } from '../../../../src/apps/erp/permissions'

const FETCH_ACCOUNTS_QUERY = 'fetchAccounts'

describe('Company directory - Retail stores', function () {
    const PAGE = '/company-directory/retail-stores'

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/accounts', {
            fixture: 'erp/company-directory/retail-stores-current-account.json',
        }).as('retail_store_account_request')

        cy.intercept('POST', '**/api/erp/v2/wms/warehouses', {
            fixture: 'erp/company-directory/retail-stores.json',
        }).as('retail_store_request')

        cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
            if (hasOperationName(req, FETCH_ACCOUNTS_QUERY)) {
                aliasQuery(req, FETCH_ACCOUNTS_QUERY)
                req.reply({
                    fixture: `graphql/${FETCH_ACCOUNTS_QUERY}`,
                })
            }
        })
    })

    describe('Main list', () => {
        it('Shows retail stores', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_account_request')
            cy.wait('@retail_store_request')

            // Check page title
            cy.get('[data-context=page-header]').should('contain', 'Magasins')

            // Check list
            cy.get('[data-context="warehouse-item"]').should('have.length', 18)
        })

        it('Verify that filtering works as expected', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_account_request')
            cy.wait('@retail_store_request')

            cy.get('[data-context=warehouse-details]').should('not.exist')

            cy.get('[data-context="attribute-filter"]').type('Li{enter}')

            cy.get('[data-context="warehouse-item"]').as('warehouse-item')

            cy.get('@warehouse-item').should('have.length', 2)
            cy.get('@warehouse-item').eq(0).should('contain', 'Lille')
            cy.get('@warehouse-item').eq(1).should('contain', 'Montpellier')

            cy.get('[data-context="attribute-filter"]').clear().type('Pa{enter}')

            cy.get('[data-context="warehouse-item"]').as('warehouse-item')

            cy.get('@warehouse-item').should('have.length', 2)
            cy.get('@warehouse-item').eq(0).should('contain', 'Paris 8')
            cy.get('@warehouse-item').eq(1).should('contain', 'Paris 7')
        })
    })

    describe('Store details - Manage assignees', () => {
        it('Show assigned sale advisors for selected store', function () {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_account_request')
            cy.wait('@retail_store_request')

            cy.get('[data-context=warehouse-details]').should('not.exist')

            cy.get('[data-context="warehouse-item"]').as('warehouse-item')

            // Check list
            cy.get('@warehouse-item')
                .eq(4)
                .should('contain', 'Lyon')
                .should('contain', '1 place Louis Chazette')
                .should('contain', '2 conseill.er.ère(s)')
                .click()

            cy.wait(getQueryAlias(FETCH_ACCOUNTS_QUERY))

            cy.get('[data-context=warehouse-details]').should('be.visible')

            cy.get('[data-context=warehouse-details]')
                .should('be.visible')
                .should('contain', 'Lyon')
                .should('contain', '1 place Louis Chazette')
                .should('contain', '69001')

            cy.get('[data-context=comment]')
                .should('be.visible')
                .should('have.value', 'Bon bin voilà un premier commentaire')
            cy.get('[data-context="save-comment"]').should('be.visible')

            cy.get('[data-context=user-block-item]').eq(0).should('contain', 'Michel RADIX')

            // Check list
            cy.get('@warehouse-item').eq(9).should('contain', 'Antibes').click()

            cy.get('[data-context=warehouse-details]').should('be.visible').should('contain', 'Antibes')

            cy.get('[data-context=user-block-item]')
                .eq(0)
                .should('contain', 'Sébastien DENIS')
                .should('contain', '<EMAIL>')
                .should('contain', 'Mobile : 0678901234')
                .should('contain', 'Poste : 1653')

            cy.get('[data-context=user-block-item]').eq(2).should('contain', 'Johanne FRABOULET')

            cy.get('[data-context=comment]').type('On rajoute un commentaire')
            cy.get('[data-context="save-comment"]').click()

            cy.get('[data-context=comment]').should('be.visible').should('have.value', 'On rajoute un commentaire')
        })
    })

    describe('Store details - Manage schedules', () => {
        it('Show schedules for selected store', () => {
            cy.intercept('POST', '**/api/erp/v1/accounts', {
                fixture: 'erp/company-directory/retail-stores-sale-advisor.json',
            }).as('retail_store_request_assigned_sale_advisor_account')

            // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
            // alias the queries and mutations for our tests in a beforeEach
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'fetchCmsShipmentMethods')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCmsShipmentMethods')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/fetch_cms_shipment_methods_37.json',
                    })

                    expect(req.body.variables.where.shipment_method_id._eq).to.eq(37)
                }

                if (hasOperationName(req, 'fetchCmsWarehousesOpenTime')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCmsWarehousesOpenTime')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/fetch_cms_warehouses_5.json',
                    })

                    expect(req.body.variables.where.warehouse_id._eq).to.eq(5)
                }
            })

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_request_assigned_sale_advisor_account')
            cy.wait('@retail_store_request')

            cy.get('[data-context=tabs] [data-context=item]').eq(1).click()
            cy.wait(getQueryAlias('fetchCmsShipmentMethods'))
            cy.wait(getQueryAlias('fetchCmsWarehousesOpenTime'))

            cy.get('[data-context=tabs] [data-context=skeleton]').should('not.exist')

            cy.get('[data-context=days]').should('be.visible').should('have.length', 7).as('days')

            cy.get('@days').eq(1).as('first_opened_day').find('label').should('contain', 'Mardi')
            cy.get('@first_opened_day').find('[data-context=slot]').should('have.length', 2).eq(0).as('first_slot')

            cy.get('@first_slot').find('[data-context=start-hour]').should('have.value', '10')
            cy.get('@first_slot').find('[data-context=start-minute]').should('have.value', '00')
            cy.get('@first_slot').find('[data-context=end-hour]').should('have.value', '13')
            cy.get('@first_slot').find('[data-context=end-minute]').should('have.value', '30')
            cy.get('@first_slot').find('[data-context=remove]').should('be.visible')
            cy.get('@first_slot').find('[data-context=add]').should('not.exist')

            cy.get('[data-context=schedule-text-input]').should('have.value', 'Du mardi au samedi de 10h à 19h.')
        })

        it('Show disabled schedules on not retail stores', () => {
            cy.intercept('POST', '**/api/erp/v1/accounts', {
                fixture: 'erp/company-directory/retail-stores-sale-advisor.json',
            }).as('retail_store_request_assigned_sale_advisor_account')

            // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
            // alias the queries and mutations for our tests in a beforeEach
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'fetchCmsShipmentMethods')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCmsShipmentMethods')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/fetch_cms_shipment_methods_37.json',
                    })

                    expect(req.body.variables.where.shipment_method_id._eq).to.eq(37)
                }
            })

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_request_assigned_sale_advisor_account')
            cy.wait('@retail_store_request')

            cy.get('[data-context=tabs] [data-context=item]').eq(1).click()
            cy.wait(getQueryAlias('fetchCmsShipmentMethods'))

            cy.get('[data-context=warehouse-name]:contains(Salon)').click()

            cy.get('[data-context=tabs] [data-context=skeleton]').should('not.exist')

            cy.get('[data-context=erp-tabs] [data-context=tab]')
                .find('[data-context=schedule-content-wrapper]')
                .should('have.class', 'cursor-not-allowed')
                .find('> div')
                .should('have.class', 'opacity-50')
        })

        it('Saving should not be allowed without the right permission', () => {
            cy.intercept('POST', '**/api/erp/v1/accounts', {
                fixture: 'erp/company-directory/retail-stores-sale-advisor.json',
            }).as('retail_store_request_assigned_sale_advisor_account')

            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'fetchCmsShipmentMethods')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCmsShipmentMethods')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/fetch_cms_shipment_methods_37.json',
                    })

                    expect(req.body.variables.where.shipment_method_id._eq).to.eq(37)
                }

                if (hasOperationName(req, 'fetchCmsWarehousesOpenTime')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCmsWarehousesOpenTime')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/fetch_cms_warehouses_5.json',
                    })

                    expect(req.body.variables.where.warehouse_id._eq).to.eq(5)
                }
            })

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_request_assigned_sale_advisor_account')
            cy.wait('@retail_store_request')

            cy.get('[data-context=tabs] [data-context=item]').eq(1).click()
            cy.wait(getQueryAlias('fetchCmsShipmentMethods'))

            cy.get('[data-context=tabs] [data-context=skeleton]').should('not.exist')

            cy.get('[data-context=days]').should('be.visible').should('have.length', 7)
            cy.get('[data-context=schedule-submit-button]').should('be.disabled')

            cy.get('[data-context=tabs] [data-context=item]').eq(2).click()

            cy.get('[data-context=skeleton]').should('not.exist')

            cy.get('[data-context=delivery-open-day-form]').should('be.visible').should('exist')
            cy.get('[data-context=delivery-open-day-submit-button]').should('be.disabled')
        })

        it('Check schedules text is required', () => {
            cy.mockErpUser([STORE_OPENING_HOURS_UPDATE])
            cy.intercept('POST', '**/api/erp/v1/accounts', {
                fixture: 'erp/company-directory/retail-stores-sale-advisor.json',
            }).as('retail_store_request_assigned_sale_advisor_account')

            // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
            // alias the queries and mutations for our tests in a beforeEach
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'fetchCmsShipmentMethods')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCmsShipmentMethods')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/fetch_cms_shipment_methods_37.json',
                    })

                    expect(req.body.variables.where.shipment_method_id._eq).to.eq(37)
                }

                if (hasOperationName(req, 'fetchCmsWarehousesOpenTime')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCmsWarehousesOpenTime')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/fetch_cms_warehouses_5.json',
                    })

                    expect(req.body.variables.where.warehouse_id._eq).to.eq(5)
                }
            })

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_request_assigned_sale_advisor_account')
            cy.wait('@retail_store_request')

            cy.get('[data-context=tabs] [data-context=item]').eq(1).click()
            cy.wait(getQueryAlias('fetchCmsShipmentMethods'))
            cy.wait(getQueryAlias('fetchCmsWarehousesOpenTime'))

            cy.get('[data-context=tabs] [data-context=skeleton]').should('not.exist')

            cy.get('[data-context=schedule-text-input]')
                .should('have.value', 'Du mardi au samedi de 10h à 19h.')
                .clear()

            cy.get('[data-context=error-schedule-text]').should('contain', 'Ce champ est obligatoire')
            cy.get('[data-context=schedule-submit-button]').click()

            cy.toast(
                'Une ou plusieurs erreurs empêchent la mise à jour des informations, veuillez les corriger',
                'danger',
            )
        })

        it('Saves schedules correctly', () => {
            cy.mockErpUser([STORE_OPENING_HOURS_UPDATE])
            cy.intercept('POST', '**/api/erp/v1/accounts', {
                fixture: 'erp/company-directory/retail-stores-sale-advisor.json',
            }).as('retail_store_request_assigned_sale_advisor_account')

            // https://docs.cypress.io/guides/testing-strategies/working-with-graphql
            // alias the queries and mutations for our tests in a beforeEach
            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'fetchCmsShipmentMethods')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCmsShipmentMethods')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/fetch_cms_shipment_methods_37.json',
                    })

                    expect(req.body.variables.where.shipment_method_id._eq).to.eq(37)
                }

                if (hasOperationName(req, 'fetchCmsWarehousesOpenTime')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasQuery(req, 'fetchCmsWarehousesOpenTime')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/fetch_cms_warehouses_5.json',
                    })

                    expect(req.body.variables.where.warehouse_id._eq).to.eq(5)
                }

                if (hasOperationName(req, 'updateCmsShipmentMethodByPk')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasMutation(req, 'updateCmsShipmentMethodByPk')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/update_cms_shipment_method_by_pk_37.json',
                    })

                    expect(req.body.variables).to.deep.eq({
                        id: 37,
                        description_i18n: '"fr" => "Du mardi au samedi de 10h à 19h."',
                    })
                }

                if (hasOperationName(req, 'updateCmsWarehouseByPk')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasMutation(req, 'updateCmsWarehouseByPk')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/update_cms_warehouse_by_pk_5.json',
                    })
                    console.log(req.body.variables)
                    expect(req.body.variables).to.deep.eq({
                        id: 5,
                        data: {
                            city: 'Nantes',
                            name: 'Nantes',
                            address: '9 place de la Bourse',
                            open_hours: {
                                fri: [
                                    ['10:00', '13:30'],
                                    ['14:30', '19:00'],
                                ],
                                mon: [
                                    ['10:00', '13:30'],
                                    ['14:30', '19:00'],
                                ],
                                sat: [
                                    ['10:00', '13:30'],
                                    ['14:30', '19:30'],
                                ],
                                sun: [],
                                thu: [
                                    ['10:00', '13:30'],
                                    ['14:30', '19:00'],
                                ],
                                tue: [],
                                wed: [
                                    ['10:00', '13:30'],
                                    ['14:30', '19:00'],
                                ],
                            },
                            postal_code: '44000',
                        },
                    })
                }
            })

            cy.intercept('PUT', '/api/erp/v1/wms/warehouse/5/business_hours', {
                statusCode: 200,
                body: {
                    data: [
                        {
                            fri: [
                                ['10:00', '13:30'],
                                ['14:30', '19:00'],
                            ],
                            mon: [
                                ['10:00', '13:30'],
                                ['14:30', '19:00'],
                            ],
                            sat: [
                                ['10:00', '13:30'],
                                ['14:30', '19:30'],
                            ],
                            sun: [],
                            thu: [
                                ['10:00', '13:30'],
                                ['14:30', '19:00'],
                            ],
                            tue: [],
                            wed: [
                                ['10:00', '13:30'],
                                ['14:30', '19:00'],
                            ],
                        },
                    ],
                },
            }).as('put_warehouse_business_hours')

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_request_assigned_sale_advisor_account')
            cy.wait('@retail_store_request')

            cy.get('[data-context=tabs] [data-context=item]').eq(1).click()
            cy.wait(getQueryAlias('fetchCmsWarehousesOpenTime'))
            cy.wait(getQueryAlias('fetchCmsShipmentMethods'))

            cy.get('[data-context=tabs] [data-context=skeleton]').should('not.exist')

            // test first slot
            cy.get('[data-context=days]').eq(1).find('[data-context=slot]').should('have.length', 2).eq(0)

            // test second slot
            cy.get('[data-context=days]')
                .eq(1)
                .find('[data-context=slot]')
                .eq(1)
                .find('[data-context=remove]')
                .should('be.visible')
                .click()

            // click "remove" in first slot
            cy.get('[data-context=days]')
                .eq(1)
                .find('[data-context=slot]')
                .eq(0)
                .find('[data-context=remove]')
                .should('be.visible')
                .click()

            cy.get('[data-context=schedule-submit-button]').click()

            cy.wait([getMutationAlias('updateCmsShipmentMethodByPk')])
            cy.wait('@put_warehouse_business_hours').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    fri: [
                        ['10:00', '13:30'],
                        ['14:30', '19:00'],
                    ],
                    mon: [],
                    sat: [
                        ['10:00', '13:30'],
                        ['14:30', '19:00'],
                    ],
                    sun: [],
                    thu: [
                        ['10:00', '13:30'],
                        ['14:30', '19:00'],
                    ],
                    tue: [],
                    wed: [
                        ['10:00', '13:30'],
                        ['14:30', '19:00'],
                    ],
                })
            })
            cy.wait([getMutationAlias('updateCmsWarehouseByPk')])

            cy.toast('Les horaires ont bien été mis a jour', 'success')

            cy.get('[data-context=tabs] [data-context=skeleton]').should('not.exist')
        })
    })

    describe('Store details - Manage delivery open day', () => {
        it('Show and save open day for selected store without previously data stored', () => {
            cy.mockErpUser([STORE_OPENING_HOURS_UPDATE])
            cy.intercept('POST', '**/api/erp/v1/accounts', {
                fixture: 'erp/company-directory/retail-stores-sale-advisor.json',
            }).as('retail_store_request_assigned_sale_advisor_account')

            cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                if (hasOperationName(req, 'updateCmsWarehouseDeliveryOpenDayByPk')) {
                    // Declare the alias from the initial intercept in the beforeEach
                    aliasMutation(req, 'updateCmsWarehouseDeliveryOpenDayByPk')

                    // Set req.fixture for the response
                    req.reply({
                        fixture: 'graphql/cms/update_cms_warehouse_delivery_open_day_by_pk_standard.json',
                    })

                    expect(req.body.variables).to.deep.eq({
                        id: 5,
                        deliveryOpenDay: {
                            fri: false,
                            mon: true,
                            sat: true,
                            sun: false,
                            thu: false,
                            tue: false,
                            wed: false,
                        },
                    })
                }
            })

            cy.intercept('PUT', '/api/erp/v1/wms/warehouse/5/delivery_days', {
                statusCode: 200,
                body: {
                    data: [
                        {
                            fri: false,
                            mon: true,
                            sat: true,
                            sun: false,
                            thu: false,
                            tue: false,
                            wed: false,
                        },
                    ],
                },
            }).as('put_warehouse_delivery_days')

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_request_assigned_sale_advisor_account')
            cy.wait('@retail_store_request')

            cy.get('[data-context=tabs] [data-context=item]').eq(2).click()

            cy.get('[data-context=skeleton]').should('not.exist')

            cy.get('[data-context=delivery-open-day-form]').should('be.visible').should('exist')

            cy.get('[data-context=erp-toggle]').should('have.length', 7).as('delivery-toggles')
            cy.get('@delivery-toggles')
                .eq(0)
                .as('monday')
                .find('label')
                .should('contain', 'Lundi')
                .should('not.have.attr', 'aria-checked')
            cy.get('@monday').find('button').click().should('have.attr', 'aria-checked')
            cy.get('@delivery-toggles').eq(1).find('button').click()
            cy.get('@delivery-toggles').eq(2).find('button').click()
            cy.get('@delivery-toggles').eq(3).find('button').click()
            cy.get('@delivery-toggles').eq(4).find('button').click()

            cy.get('[data-context=delivery-open-day-submit-button]').click()

            cy.wait('@put_warehouse_delivery_days').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    fri: false,
                    mon: true,
                    sat: true,
                    sun: false,
                    thu: false,
                    tue: false,
                    wed: false,
                })
            })
            cy.wait([getMutationAlias('updateCmsWarehouseDeliveryOpenDayByPk')])

            cy.toast('Les jours de livraison ont bien été mis a jour', 'success')
        })

        it('Show open day stored for selected store', () => {
            cy.mockErpUser([STORE_OPENING_HOURS_UPDATE])
            cy.intercept('POST', '**/api/erp/v1/accounts', {
                fixture: 'erp/company-directory/retail-stores-sale-advisor.json',
            }).as('retail_store_request_assigned_sale_advisor_account')

            cy.visit(PAGE)
            cy.toggleMenu()

            cy.wait('@retail_store_request_assigned_sale_advisor_account')
            cy.wait('@retail_store_request')

            cy.get('[data-context=tabs] [data-context=item]').eq(2).click()

            cy.get('[data-context=erp-toggle]').as('delivery-toggles')
            cy.get('@delivery-toggles').eq(0).find('button').should('not.have.attr', 'aria-checked')
            cy.get('@delivery-toggles').eq(1).find('button').should('have.attr', 'aria-checked')
            cy.get('@delivery-toggles').eq(2).find('button').should('have.attr', 'aria-checked')
            cy.get('@delivery-toggles').eq(3).find('button').should('have.attr', 'aria-checked')
            cy.get('@delivery-toggles').eq(4).find('button').should('have.attr', 'aria-checked')
            cy.get('@delivery-toggles').eq(5).find('button').should('have.attr', 'aria-checked')
            cy.get('@delivery-toggles').eq(6).find('button').should('not.have.attr', 'aria-checked')
        })
    })
})
