import { customerOrderErpV2Mock } from '../../../utils/erp-server-utils'

const PAGE = '/legacy/v1/commandes/edition_commande.php?id_commande=123456'
const PAYLOAD = {
    customer_order_payment_id: 3210123,
    customer_order_id: 123456,
    customer_order_created_at: '2021-11-10 11:11:01',
    customer_order_total_amount: 563.9,
    customer_id: 1736460,
    customer_email: 'teddyva<PERSON><EMAIL>',
    unique_id: 5,
    payment_id: 17,
    type: 'paiement',
    warehouse_id: null,
    warehouse_name: null,
    created_at: '2021-11-10 16:55:04',
    created_by: 'hichem.aissaoui',
    created_amount: 299,
    created_proof: null,
    created_origin: 'son-video.com',
    accepted_at: '2021-11-10 16:55:06',
    accepted_by: 'hichem.aissaoui',
    accepted_amount: 299,
    accepted_proof: null,
    cancelled_at: null,
    cancelled_by: null,
    cancelled_amount: 0,
    remit_asked_at: null,
    remit_asked_by: null,
    remitted_at: null,
    remitted_by: null,
    remitted_amount: 0,
    remitted_proof: null,
    remit_note: null,
    remit_rate: 1,
    unpaid_overdue_at: null,
    unpaid_overdue_by: null,
    unpaid_overdue_amount: 0,
    auto_status: null,
    auto_status_detail: null,
    auto_warranty: null,
    auto_warranty_detail: null,
    country_ip: null,
    country_country_code: null,
    payment_mean: 'PANT',
    payment_description: 'Avoir (Paiement ant\u00e9rieur)',
    payment_created_remotely: 'N',
    use_remit_proof: 'N',
    remit_proof_source: 'manuel',
    remit_proof_type: 'No cheque',
    remit_proof_validation_regex: '',
    use_remit_note: 'N',
    remit_note_validation_regex: '',
    computed_customer_firstname: 'Teddy',
    computed_customer_lastname: 'VAUTIER',
    computed_created_by: 'Hichem A\u00cfSSAOUI',
    computed_accepted_by: 'Hichem A\u00cfSSAOUI',
    computed_remitted_by: '',
    computed_cancelled_by: '',
    computed_transaction_status: 'ACCEPTED',
    can_be_accepted: false,
    can_be_cancelled: true,
    can_be_remitted: false,
    can_cancel_remit: false,
    should_be_remitted: true,
}

describe('Perform actions on customer order payment transaction', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/api/erp/v1/customer-order/123456/for-edition-page', {
            statusCode: 200,
            body: {
                data: customerOrderErpV2Mock(123456),
            },
        }).as('customer-order-legacy-123456')
    })

    describe('Transaction remittance', () => {
        it('does not have permission to remit the a payment', () => {
            cy.visit(PAGE)
            cy.emitEvent('content-fully-loaded')
            cy.wait('@customer-order-legacy-123456')

            cy.intercept('POST', '**/api/erp/v2/customer-order-payments', {
                statusCode: 200,
                body: {
                    data: {
                        payments: [PAYLOAD],
                    },
                },
            }).as('accounting')

            cy.emitEvent('open-remit-dialog', { customer_order_payment_id: 3210123 })
            cy.wait('@accounting')

            cy.get('[data-context=amount]').should('contain', '299,00 €')
            cy.get('[data-context=mean]').should('contain', 'PANT')
            cy.get('[data-context=description]').should('contain', 'Avoir (Paiement antérieur)')
            cy.get('[data-context=accepted-text]').should(
                'contain',
                'Accepté le 10/11/2021 à 16:55 par Hichem AÏSSAOUI',
            )
            cy.get('[data-context=remit-btn]').should('not.exist')
            cy.get('[data-context=warning-msg]').should(
                'contain',
                "Vous n'avez pas la permission de remiser ce paiement.",
            )
        })

        it('can remit a payment successfully', () => {
            cy.visit(PAGE)
            cy.emitEvent('content-fully-loaded')
            cy.wait('@customer-order-legacy-123456')

            const payload_with_permission = Object.assign({}, PAYLOAD)
            payload_with_permission.can_be_remitted = true

            cy.intercept('POST', '**/api/erp/v2/customer-order-payments', {
                statusCode: 200,
                body: {
                    data: {
                        payments: [payload_with_permission],
                    },
                },
            }).as('accounting')

            cy.emitEvent('open-remit-dialog', { customer_order_payment_id: 3210123 })
            cy.wait('@accounting')

            cy.get('[data-context=amount]').should('contain', '299,00 €')
            cy.get('[data-context=mean]').should('contain', 'PANT')
            cy.get('[data-context=description]').should('contain', 'Avoir (Paiement antérieur)')
            cy.get('[data-context=accepted-text]').should(
                'contain',
                'Accepté le 10/11/2021 à 16:55 par Hichem AÏSSAOUI',
            )
            cy.get('[data-context=warning-msg]').should('not.exist')

            cy.intercept('PUT', '**/api/erp/v1/accounting/payment/remit', {
                statusCode: 200,
                body: null,
            }).as('accounting-payment-remit')

            cy.get('[data-context=erp-input]').should('not.exist')

            cy.get('[data-context=remit-btn]').should('contain', 'Remiser').click()

            cy.wait('@accounting-payment-remit').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    payments: [
                        {
                            customer_order_payment_id: 3210123,
                            amount_to_remit: 299,
                        },
                    ],
                })
            })
        })

        it('can remit a payment successfully with a remit proof', () => {
            cy.visit(PAGE)
            cy.emitEvent('content-fully-loaded')
            cy.wait('@customer-order-legacy-123456')

            cy.intercept('POST', '**/api/erp/v2/customer-order-payments', {
                fixture: 'erp/accounting/customer-order-payments.json',
            }).as('accounting')

            cy.emitEvent('open-remit-dialog', { customer_order_payment_id: 3210123 })
            cy.wait('@accounting')

            cy.get('[data-context=amount]').should('contain', '10,00 €')
            cy.get('[data-context=mean]').should('contain', 'RTLCTLM')
            cy.get('[data-context=description]').should('contain', 'Paiement en plusieurs fois pour les magasins')

            cy.intercept('PUT', '**/api/erp/v1/accounting/payment/remit', {
                statusCode: 200,
                body: null,
            }).as('accounting-payment-remit')

            cy.get('[data-context=erp-form-block]').should('be.visible').find('label').should('not.be.empty')

            cy.get('[data-context=erp-input]').as('erp-input').should('be.visible')

            cy.get('[data-context=remit-btn]').should('contain', 'Remiser').as('btn-remiser').click()

            cy.get('[data-context=erp-input-helper]')
                .as('erp-input-helper')
                .should('be.visible')
                .should('contain', 'No transaction est invalide')

            cy.get('@btn-remiser').click()

            cy.get('@erp-input').should('be.not.disabled')

            cy.get('@erp-input').type('*************')

            cy.get('@erp-input-helper').should('be.visible').should('contain', 'No transaction est invalide')

            cy.get('@erp-input').type('4')

            cy.get('@erp-input-helper').should('not.exist')

            cy.get('@btn-remiser').click()

            cy.wait('@accounting-payment-remit').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    payments: [
                        {
                            customer_order_payment_id: 3210123,
                            amount_to_remit: 10,
                            remit_proof: '**************',
                        },
                    ],
                })
            })
        })

        it('can remit a payment successfully with an other remit proof pattern', () => {
            cy.visit(PAGE)
            cy.emitEvent('content-fully-loaded')
            cy.wait('@customer-order-legacy-123456')

            cy.intercept('POST', '**/api/erp/v2/customer-order-payments', {
                fixture: 'erp/accounting/customer-order-payments-pattern-2.json',
            }).as('accounting')

            cy.emitEvent('open-remit-dialog', { customer_order_payment_id: 3210123 })
            cy.wait('@accounting')

            cy.intercept('PUT', '**/api/erp/v1/accounting/payment/remit', {
                statusCode: 200,
                body: null,
            }).as('accounting-payment-remit')

            cy.get('[data-context=remit-btn]').should('contain', 'Remiser').as('btn-remiser').click()

            cy.get('[data-context=erp-input-helper]')
                .as('erp-input-helper')
                .should('be.visible')
                .should('contain', 'Date transaction est invalide')

            cy.get('[data-context=erp-input]').as('erp-input').type('*************')

            cy.get('@erp-input-helper').should('be.visible').should('contain', 'Date transaction est invalide')

            cy.get('@erp-input').clear()
            cy.get('@erp-input').type('2022-09-07')

            cy.get('@erp-input-helper').should('not.exist')

            cy.get('@btn-remiser').click()

            cy.wait('@accounting-payment-remit').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    payments: [
                        {
                            customer_order_payment_id: 3210123,
                            amount_to_remit: 10,
                            remit_proof: '2022-09-07',
                        },
                    ],
                })
            })
        })
    })
})
