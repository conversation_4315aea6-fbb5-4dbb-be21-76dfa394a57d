import { CUSTOMER_ORDER_PAYMENT_REMIT } from '../../../../../src/apps/erp/permissions'

describe('Customer order payments - Transaction actions', function () {
    const PAGE = '/customer-order/payment'

    const selectRow = (idx) => {
        return cy.get('@transactions').find('tbody tr').eq(idx).as('row')
    }

    const selectCell = (idx) => {
        return cy.get('@row').find('td').eq(idx).as('cell')
    }

    const selectBatchRemitButton = () => {
        return cy.get('[data-context=remit-button]').eq(0).as('batchRemitButton')
    }

    const selectExportButton = () => {
        return cy.get('[data-context=export-button]').eq(0).as('exportButton')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('GET', '**/v1/customer-order/sources', {
            fixture: 'erp/accounting/payment/sources.json',
        }).as('sources')

        cy.intercept('POST', '**/api/erp/v2/wms/warehouses', {
            fixture: 'erp/accounting/payment/retail-stores.json',
        }).as('retail_stores')

        cy.intercept('POST', '**/api/erp/v2/payment_methods', {
            fixture: 'erp/accounting/payment/payment-methods.json',
        }).as('payment_methods')

        cy.intercept('POST', '**/api/erp/v2/customer-order-payments', {
            fixture: 'erp/accounting/payment/transactions-actions.json',
        }).as('transactions_request')
    })

    /**
     * Helper function to automatize actions done on each upcoming tests
     */
    const visitPageAndSeeTransactionsActions = () => {
        cy.visit(PAGE)
        cy.toggleMenu()

        cy.wait(['@sources', '@retail_stores', '@payment_methods', '@transactions_request'])

        cy.get('[data-context=dashboard-filters]').should('be.visible')

        cy.get('[data-context=table-empty-placeholder]').should('not.exist')

        cy.get('[data-context=dashboard-filters]').as('selected')
        cy.get('@selected').get('[data-context=filters-submit-btn]').click()

        cy.wait(['@transactions_request'])

        cy.get('[data-context=transactions]').as('transactions')
        cy.get('@transactions').find('tbody tr').should('have.length', 4)
    }

    const visitPage = () => {
        cy.visit(PAGE)
        cy.toggleMenu()

        cy.wait(['@sources', '@retail_stores', '@payment_methods'])

        cy.get('[data-context=dashboard-filters]').should('be.visible')

        cy.get('[data-context=table-empty-placeholder]').should('not.exist')

        cy.get('[data-context=dashboard-filters]').as('selected')
    }

    it('Remit button should not be visible in first row', function () {
        visitPageAndSeeTransactionsActions()
        selectRow(0)
        selectCell(13).find('[data-context=remit-button]').should('not.exist')
    })

    it('Export should pick up selected filters and trigger a 500-line export', function () {
        // freeze clock - Today is 12/12/2024
        cy.mockDate(new Date(2024, 11, 12))

        visitPage()
        cy.get('[data-context=dashboard-filters]').as('filters')
        cy.get('@filters')
            .get('[data-context=filter-status]')
            .find('[data-context=erp-multiselect]')
            .erpMultiselect('', 'Remisé')

        selectExportButton()
        cy.get('@exportButton').should('contain', 'Exporter').should('be.enabled').click()
        cy.get('[data-context=order-export-slide-out-container]').should('exist')
        cy.get('[data-context=order-export-slide-out-container] [data-context=export-limit]')
            .find('[data-context=erp-multiselect]')
            .erpMultiselect('', '500 paiements')

        cy.intercept('POST', '/api/erp/v1/accounting/payment/export/txt', {
            statusCode: 200,
            body: {},
        }).as('export_transactions')

        cy.get('[data-context=order-export-slide-out-container] [data-context=export-button]').click()

        cy.wait('@export_transactions').then((xhr) => {
            expect(xhr.request.body.where._and[0].remitted_at._between[0]).to.eq('2024-12-12 00:00:00')
            expect(xhr.request.body.where._and[0].remitted_at._between[1]).to.eq('2024-12-12 23:59:59')
            expect(xhr.request.body.where._and[1].status._in[0]).to.eq('REMITTED')
            expect(xhr.request.body.order_by).to.eq('remitted_at ASC, customer_order_id ASC')
            expect(xhr.request.body.limit).to.eq(500)
        })
    })

    describe('Remit a transaction', function () {
        it('remit a simple transaction', function () {
            cy.mockErpUser([CUSTOMER_ORDER_PAYMENT_REMIT])
            cy.intercept('PUT', '/api/erp/v1/accounting/payment/remit', {
                statusCode: 200,
                body: {
                    data: {
                        payments: [
                            {
                                customer_order_payment_id: 3575376,
                                remitted_at: '2024-07-17 16:01:06',
                                computed_transaction_status: 'REMITTED',
                                remit_error: null,
                                can_be_remitted: false,
                                can_cancel_remit: false,
                            },
                        ],
                    },
                },
            }).as('remit_transaction_request')

            visitPageAndSeeTransactionsActions()
            selectRow(2)
            selectCell(13).find('button:contains(Remiser)').click()

            cy.wait('@remit_transaction_request').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    payments: [
                        {
                            customer_order_payment_id: 3575376,
                            amount_to_remit: 250,
                        },
                    ],
                })
            })
        })

        describe('Cancel a remit on a transaction', function () {
            it('checks the cancel button', function () {
                cy.mockErpUser([CUSTOMER_ORDER_PAYMENT_REMIT])
                cy.intercept('PUT', '/api/erp/v1/accounting/payment/cancel-remit/3577208', {
                    statusCode: 200,
                    body: {
                        data: {
                            payments: [
                                {
                                    customer_order_payment_id: 3577208,
                                    remitted_at: null,
                                    computed_transaction_status: 'ACCEPTED',
                                    remit_error: null,
                                    can_be_remitted: false,
                                    can_cancel_remit: false,
                                },
                            ],
                        },
                    },
                }).as('transactions_request')

                visitPageAndSeeTransactionsActions()
                selectRow(1)
                selectCell(13).find('button:contains(Déremiser)').click()
                cy.wait('@transactions_request')
            })
        })
    })

    describe('Remit multiple transactions', function () {
        it('remit two transactions', function () {
            cy.mockErpUser([CUSTOMER_ORDER_PAYMENT_REMIT])
            cy.intercept('PUT', '/api/erp/v1/accounting/payment/remit', {
                statusCode: 200,
                body: {
                    data: {
                        payments: [
                            {
                                customer_order_payment_id: 3576730,
                                remitted_at: '2024-07-17 16:01:06',
                                computed_transaction_status: 'REMITTED',
                                remit_error: null,
                                can_be_remitted: false,
                                can_cancel_remit: false,
                            },
                            {
                                customer_order_payment_id: 3574971,
                                remitted_at: '2024-07-17 16:01:06',
                                computed_transaction_status: 'REMITTED',
                                remit_error: null,
                                can_be_remitted: false,
                                can_cancel_remit: false,
                            },
                        ],
                    },
                },
            }).as('batch_remit_transaction_request')

            visitPageAndSeeTransactionsActions()
            selectRow(0)
            selectCell(0).find('input[type="checkbox"]').check()
            selectRow(3)
            selectCell(0).find('input[type="checkbox"]').check()
            selectBatchRemitButton()
            cy.get('@batchRemitButton').should('be.enabled').click()

            cy.get('[data-context=batch-remit-slide-out-container]').should('exist')
            cy.get('[data-context=batch-remit-confirm-button]').click()

            cy.wait('@batch_remit_transaction_request').then((xhr) => {
                expect(xhr.request.body).to.deep.eq({
                    payments: [
                        {
                            customer_order_payment_id: 3576730,
                        },
                        {
                            customer_order_payment_id: 3574971,
                        },
                    ],
                })
            })
        })
    })
})
