import { CUSTOMER_ORDER_PAYMENT_REMIT } from '../../../../../src/apps/erp/permissions'

describe('Customer order payments - Transactions', function () {
    const PAGE = '/customer-order/payment'

    const selectRow = (idx) => {
        cy.get('[data-context=transactions]').as('transactions')
        return cy.get('@transactions').find('tbody tr').eq(idx).as('row')
    }

    const selectCell = (idx) => {
        return cy.get('@row').find('td').eq(idx).as('cell')
    }

    const selectBatchRemitButton = () => {
        return cy.get('[data-context=remit-button]').eq(0).as('batchRemitButton')
    }

    const selectImportButton = () => {
        return cy.get('[data-context=import-button]').eq(0).as('importButton')
    }

    const selectExportButton = () => {
        return cy.get('[data-context=export-button]').eq(0).as('exportButton')
    }

    const selectMasterCheckbox = () => {
        return cy
            .get('[data-context=transactions]')
            .find('thead tr th')
            .eq(0)
            .find('input[type="checkbox"]')
            .as('masterCheckbox')
    }

    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser([CUSTOMER_ORDER_PAYMENT_REMIT])

        cy.intercept('GET', '**/v1/customer-order/sources', {
            fixture: 'erp/accounting/payment/sources.json',
        }).as('sources')

        cy.intercept('POST', '**/api/erp/v2/wms/warehouses', {
            fixture: 'erp/accounting/payment/retail-stores.json',
        }).as('retail_stores')

        cy.intercept('POST', '**/api/erp/v2/payment_methods', {
            fixture: 'erp/accounting/payment/payment-methods.json',
        }).as('payment_methods')

        cy.intercept('POST', '**/api/erp/v2/customer-order-payments', {
            fixture: 'erp/accounting/payment/transactions.json',
        }).as('transactions_request')
    })

    const visitPageAndSeeTransactions = (qs = '') => {
        cy.visit(PAGE + qs)
        cy.toggleMenu()

        if (qs !== '') {
            cy.wait(['@sources', '@retail_stores', '@payment_methods'])
        } else {
            cy.wait(['@sources', '@retail_stores', '@payment_methods', '@transactions_request'])
        }
    }

    describe('Page setup', function () {
        it('check source group and group filters', function () {
            visitPageAndSeeTransactions()

            cy.get('[data-context=dashboard-filters]').as('filters')

            cy.get('@filters').find('[data-context=filter-group-source]').should('exist')
            cy.get('@filters').find('[data-context=filter-source]').should('exist')
        })

        it('check table columns', function () {
            visitPageAndSeeTransactions()

            // table header
            cy.get('[data-context=transactions]').as('transactions')
            cy.get('@transactions').find('thead tr th').as('transactions_header')

            cy.get('@transactions_header').should('have.length', 14)

            cy.get('@transactions_header').eq(0).find('input[type="checkbox"]')
            let i = 1
            cy.get('@transactions_header').eq(i++).should('contain', 'Commande')
            cy.get('@transactions_header').eq(i++).should('contain', 'Origine commande')
            cy.get('@transactions_header').eq(i++).should('contain', 'Date commande')
            cy.get('@transactions_header').eq(i++).should('contain', 'Création paiement')
            cy.get('@transactions_header').eq(i++).should('contain', 'Acceptation paiement')
            cy.get('@transactions_header').eq(i++).should('contain', 'Remise paiement')
            cy.get('@transactions_header').eq(i++).should('contain', 'Source')
            cy.get('@transactions_header').eq(i++).should('contain', 'Montant commande')
            cy.get('@transactions_header').eq(i++).should('contain', 'Montant paiement')
            cy.get('@transactions_header').eq(i++).should('contain', 'Type')
            cy.get('@transactions_header').eq(i++).should('contain', 'Statut')
            cy.get('@transactions_header').eq(i++).should('contain', 'Magasin')
            cy.get('@transactions_header').eq(i++).should('contain', 'Actions')

            cy.get('[data-context=dashboard-filters]').as('filters')
            //cy.get('@filters').find('[data-context=filters-submit-btn]').click()

            cy.get('[data-context=transactions]').find('tbody tr').should('have.length', 4)
            cy.get('[data-context=table-total-transaction]').should('be.visible').should('contain', '20 342,00')
        })
    })

    describe('Behaviours for required filters', function () {
        it('select required filters for a user attached to a specific store', function () {
            cy.fixture('erp/accounting/payment/retail-stores.json').then((payload) => {
                payload.data.warehouses[9].users.push({
                    user_id: 120,
                    username: 'anakin.skywalker',
                    email: '<EMAIL>',
                    full_name: 'Magasin Nantes - Anakin Skywalkerv',
                    barcode: '13110810f91964e20122',
                    warehouse_id: 5,
                })

                cy.intercept('POST', '**/api/erp/v2/wms/warehouses', {
                    body: payload,
                    statusCode: 200,
                }).as('retail_stores')
            })

            visitPageAndSeeTransactions()

            cy.get('[data-context=dashboard-filters]').as('filters')

            cy.get('@filters')
                .find(
                    '[data-context=filter-source] [data-context=erp-multiselect] [data-context=multiple] [data-context=tag]',
                )
                .eq(0)
                .should('contain', 'Magasin Pro SV')

            cy.get('@filters')
                .find(
                    '[data-context=filter-source] [data-context=erp-multiselect] [data-context=multiple] [data-context=tag]',
                )
                .eq(1)
                .should('contain', 'Magasin SV')

            cy.get('@filters')
                .find(
                    '[data-context=filter-site] [data-context=erp-multiselect] [data-context=multiple] [data-context=tag]',
                )
                .eq(0)
                .should('contain', 'Nantes')

            cy.get('@filters').find('[data-context=filters-submit-btn]').click()

            cy.wait('@transactions_request').then((xhr) => {
                expect(xhr.request.body.where._and[1][0].source_id._in[0]).to.eq('source.shop.sonvideo_pro')
                expect(xhr.request.body.where._and[1][0].source_id._in[1]).to.eq('source.shop.sonvideo')
                expect(xhr.request.body.where._and[1][0].warehouse_id._in[0]).to.eq(5)
                expect(xhr.request.body.order_by).to.eq('created_at ASC, customer_order_id ASC')
            })

            cy.get('[data-context=table-empty-placeholder]').should('not.exist')

            cy.get('[data-context=transactions]').as('transactions')
            cy.get('@transactions').find('tbody tr').should('have.length', 4)
            cy.get('[data-context=table-total-transaction]').should('be.visible').should('contain', '20 342,00')
        })

        it('select required filters for a user not attached to any store', function () {
            visitPageAndSeeTransactions()

            cy.get('[data-context=dashboard-filters]').as('filters')

            cy.get('@filters')
                .find('[data-context=filter-source]')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'Magasin SV')
                .type('{esc}')

            cy.get('@filters')
                .find('[data-context=filter-site]')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'Grenoble')
                .type('{esc}')

            cy.get('@filters').find('[data-context=filters-submit-btn]').click()
            cy.wait('@transactions_request').then((xhr) => {
                expect(xhr.request.body.where._and[0].created_at._between[0]).to.eq(
                    new Date().toISOString().slice(0, 10) + ' 00:00:00',
                )
                expect(xhr.request.body.where._and[0].created_at._between[1]).to.eq(
                    new Date().toISOString().slice(0, 10) + ' 23:59:59',
                )
                expect(xhr.request.body.where._and[1][0].source_id._in[0]).to.eq('source.shop.sonvideo')
                expect(xhr.request.body.where._and[1][0].warehouse_id._in[0]).to.eq(10)
                expect(xhr.request.body.order_by).to.eq('created_at ASC, customer_order_id ASC')
            })

            cy.get('[data-context=table-empty-placeholder]').should('not.exist')

            cy.get('[data-context=transactions]').as('transactions')
            cy.get('@transactions').find('tbody tr').should('have.length', 4)

            cy.get('[data-context=table-total-transaction]').should('be.visible').should('contain', '20 342,00')
        })

        it('makes sure that no request is sent if no filter is selected or search field filled', function () {
            visitPageAndSeeTransactions()

            cy.get('[data-context=dashboard-filters]').as('filters')

            cy.get('@filters')
                .find('[data-context=filter-payment-status-date]')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'Dates spécifiques')

            cy.get('@filters')
                .find('[data-context=filter-payment-status-date]')
                .find('[data-context=start-date]')
                .find('[data-context=clear]')
                .click()

            cy.get('@filters').find('[data-context=filters-submit-btn]').click()

            cy.intercept('POST', '**/api/erp/v2/customer-order-payments', cy.spy().as('busted'))
            cy.get('@busted').should('not.have.been.called')
        })

        it('triggers a search with an order number', function () {
            visitPageAndSeeTransactions()

            cy.get('[data-context=dashboard-filters]').as('filters')

            cy.get('@filters')
                .find('[data-context=filter-payment-status-date]')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'Dates spécifiques')

            cy.get('@filters')
                .find('[data-context=filter-payment-status-date]')
                .find('[data-context=start-date]')
                .find('[data-context=clear]')
                .click()

            cy.get('@filters')
                .find('[data-context=filter-status]')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'Accepté')

            cy.get('@filters').find('[data-context=search-by-order]').type('645')

            cy.get('@filters').find('[data-context=filters-submit-btn]').click()
            cy.wait('@transactions_request').then((xhr) => {
                expect(xhr.request.body.where._and[0].customer_order_id._ilike).to.eq('645%')
                expect(xhr.request.body.where._and[1].status._in[0]).to.eq('ACCEPTED')
                expect(xhr.request.body.order_by).to.eq('accepted_at ASC, customer_order_id ASC')
            })
        })

        it('triggers a search with 2 statuses', function () {
            visitPageAndSeeTransactions()

            cy.get('[data-context=dashboard-filters]').as('filters')

            cy.get('@filters')
                .find('[data-context=filter-payment-status-date]')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'Dates spécifiques')

            cy.get('@filters')
                .find('[data-context=filter-payment-status-date]')
                .find('[data-context=start-date]')
                .find('[data-context=clear]')
                .click()

            cy.get('@filters')
                .find('[data-context=filter-status]')
                .find('[data-context=erp-multiselect]')
                .erpMultiselect('', 'Remisé')
                .erpMultiselect('', 'Accepté')

            cy.get('body').click(50, 50, { force: true })

            cy.get('@filters').get('[data-context=filters-submit-btn]').click()

            cy.wait('@transactions_request').then((xhr) => {
                expect(xhr.request.body.where._and[0].status._in[0]).to.eq('REMITTED')
                expect(xhr.request.body.where._and[0].status._in[1]).to.eq('ACCEPTED')
                expect(xhr.request.body.order_by).to.eq('remitted_at ASC, customer_order_id ASC')
            })
        })
    })

    describe('Transactions - list', function () {
        it('check first transaction', function () {
            visitPageAndSeeTransactions()
            selectRow(0)

            // For first row, we do a couple of additional tests for some cell content
            selectCell(1)
                .find('a')
                .eq(0)
                .should('contain', '2012955')
                .should('have.attr', 'href')
                .and('match', /\/legacy\/v1\/commandes\/edition_commande/)
            selectCell(1)
                .find('a')
                .eq(1)
                .should('contain', 'Alex')
                .should('have.attr', 'href')
                .and('match', /\/customer\/1589915/)
            selectCell(1).find('[data-context=copy-customer_order_id]').should('exist')

            selectCell(2).should('contain', 'A548714-A66-X')
            selectCell(7).should('contain', 'Marketplaces').should('contain', 'Amazon.fr SV')
            selectCell(10).should('contain', 'VIR')
            selectCell(10).should('contain', '-')
            selectCell(11).should('contain', 'Créé')
            selectCell(12).should('contain', 'Nantes')
        })

        it('check transactions with accepted status', function () {
            visitPageAndSeeTransactions()
            selectRow(1)
            selectCell(11).should('contain', 'Accepté')
        })

        it('check transactions with remitted status', function () {
            visitPageAndSeeTransactions()
            selectRow(2)
            selectCell(11).should('contain', 'Remisé')
        })

        it('check transaction with can be remitted', function () {
            visitPageAndSeeTransactions()
            selectRow(3)
            selectCell(11).should('contain', 'Accepté')
            selectCell(13).find('button').should('contain', 'Remiser')
        })

        it('check transaction batch remit button is disabled', function () {
            visitPageAndSeeTransactions()
            selectBatchRemitButton()
            cy.get('@batchRemitButton').should('contain', 'Remiser').should('be.disabled')
        })

        it('check that the export button is present', function () {
            visitPageAndSeeTransactions()
            selectExportButton()
            cy.get('@exportButton').should('contain', 'Exporter').should('be.enabled')
        })

        it('check that the import button is present and enabled', function () {
            visitPageAndSeeTransactions()
            selectImportButton()
            cy.get('@importButton').should('contain', 'Importer').should('be.enabled').click()
        })

        it('perform a select all', function () {
            visitPageAndSeeTransactions()
            selectBatchRemitButton()
            selectMasterCheckbox()
            cy.get('@masterCheckbox').check()
            selectRow(3)
            selectCell(0).find('input[type="checkbox"]').should('be.checked')
            cy.get('@batchRemitButton').should('be.enabled').click()
            cy.get('[data-context=slide-out-container]').should('exist')
        })

        it('Check url is handled correctly', function () {
            // freeze clock - Today is 12/12/2024
            cy.mockDate(new Date(2024, 11, 12))

            const qs =
                '?pager[limit]=25&pager[filters]={"payment_methods"%3A[{"payment_id"%3A2%2C"payment_methods"%3A"CBS"%2C"name"%3A"CBS"}%2C{"payment_id"%3A59%2C"payment_methods"%3A"CBS-O"%2C"name"%3A"CBS-O"}]%2C"period_identifier"%3A"last_week"%2C"statuses"%3A[{"value"%3A"ACCEPTED"%2C"label"%3A"Accepté"}]}'
            visitPageAndSeeTransactions(qs)

            cy.get('[data-context=dashboard-filters]').as('filters')

            cy.get('@filters')
                .find(
                    '[data-context=filter-payment-methods] [data-context=erp-multiselect] [data-context=multiple] [data-context=tag]',
                )
                .eq(0)
                .should('contain', 'CBS')
            cy.get('@filters')
                .get(
                    '[data-context=filter-payment-methods] [data-context=erp-multiselect] [data-context=multiple] [data-context=tag]',
                )
                .eq(1)
                .should('contain', 'CBS-O')
            cy.get('@filters')
                .find(
                    '[data-context=filter-payment-status-date] [data-context=erp-multiselect] [data-context=single-value]',
                )
                .should('contain', 'La semaine dernière')
            cy.get('@filters')
                .find(
                    '[data-context=filter-status] [data-context=erp-multiselect] [data-context=multiple] [data-context=tag]',
                )
                .eq(0)
                .should('contain', 'Accepté')

            cy.wait('@transactions_request').then((xhr) => {
                expect(xhr.request.body.where._and[0].accepted_at._between[0]).to.eq('2024-12-02 00:00:00')
                expect(xhr.request.body.where._and[0].accepted_at._between[1]).to.eq('2024-12-08 23:59:59')
                expect(xhr.request.body.where._and[1].payment_id._in[0]).to.eq(2)
                expect(xhr.request.body.where._and[1].payment_id._in[1]).to.eq(59)
                expect(xhr.request.body.where._and[2].status._in[0]).to.eq('ACCEPTED')
            })
        })
    })
})
