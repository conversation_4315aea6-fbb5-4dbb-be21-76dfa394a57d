describe('Task indicator', function () {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()
    })

    it('display an animation until tasks have not been retrieved', function () {
        cy.intercept('GET', '**/api/erp/v1/tasks', {
            statusCode: 500,
            body: { status: 'error' },
        })
        cy.visit('/')

        cy.get('[data-context=task-indicator]').as('indicator')

        cy.get('@indicator').should('have.attr', 'href', '/legacy/tache/index')
        cy.get('@indicator').find('[data-context=wait]').should('not.exist')
        cy.get('@indicator').hasIcon('tasks', 'fal')
        cy.get('@indicator').find('[data-context=quantity]').should('contain', 0)
    })

    it('has a way to display empty tasks in the top bar', function () {
        cy.intercept('GET', '**/api/erp/v1/tasks', { fixture: 'erp/tasks/empty.json' })
        cy.visit('/')

        cy.get('[data-context=task-indicator]').as('indicator')

        cy.get('@indicator').should('have.attr', 'href', '/legacy/tache/index')
        cy.get('@indicator').hasIcon('tasks', 'fal')
        cy.get('@indicator').find('[data-context=quantity]').should('contain', '0')
    })

    it('has a way to display number of current tasks in the top bar', function () {
        cy.intercept('GET', '**/api/erp/v1/tasks', { fixture: 'erp/tasks/has_tasks.json' })
        cy.visit('/')

        cy.get('[data-context=task-indicator]').as('indicator')

        cy.get('@indicator').should('have.attr', 'href', '/legacy/tache/index')
        cy.get('@indicator').hasIcon('tasks', 'fal')
        cy.get('@indicator').find('[data-context=quantity]').should('contain', '88')
    })

    it('display 99+ when having 100 or more tasks', function () {
        cy.intercept('GET', '**/api/erp/v1/tasks', { fixture: 'erp/tasks/has_many_tasks.json' })
        cy.visit('/')

        cy.get('[data-context=task-indicator]').as('indicator')

        cy.get('@indicator').should('have.attr', 'href', '/legacy/tache/index')
        cy.get('@indicator').hasIcon('tasks', 'fal')
        cy.get('@indicator').find('[data-context=quantity]').should('contain', '99+')
    })

    it('should use a cached result if present and not expired', function () {
        cy.intercept('GET', '**/api/erp/v1/tasks', { fixture: 'erp/tasks/has_tasks.json' }).as('tasks')
        cy.visit('/')
        cy.wait('@tasks')
        cy.get('[data-context=task-indicator] [data-context=quantity]').should('contain', '88')

        // mimic another tab retrieving the task indicator data
        cy.window().its('localStorage').invoke('setItem', 'task_indicator.last_call_time', JSON.stringify(new Date()))
        cy.fixture('erp/tasks/empty.json').then((result) => {
            cy.window().its('localStorage').invoke('setItem', 'task_indicator.last_result', JSON.stringify(result.data))

            // refresh
            cy.visit('/')
            cy.get('[data-context=task-indicator] [data-context=quantity]').should('contain', '0')
        })
    })
})
