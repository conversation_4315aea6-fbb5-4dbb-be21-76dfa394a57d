describe('Main menu', function () {
    describe('without user mocking', () => {
        it('should be empty', function () {
            cy.authenticate()
            cy.clearSessionStorage()
            cy.intercept('**/api/hal/v1/me', { statusCode: 409, body: { data: {} } })
            cy.intercept('**/api/hal/v1/me/permissions', { statusCode: 200, body: { data: { permissions: [] } } })
            cy.intercept('GET', '**/api/hal/v1/me/menu', { fixture: 'hal/me/menu/menus_response.json' })

            // home page
            cy.visit('/')

            // menu data
            cy.get('[data-context=main-menu-list]').find('[data-context=main-menu-item].lvl1').should('have.length', 0)
        })
    })

    describe('with user mocking', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()
        })

        it('should have the expected structure', function () {
            cy.intercept('GET', '**/api/hal/v1/me/menu', { fixture: 'hal/me/menu/menus_response.json' })

            // home page
            cy.visit('/')

            // menu data
            cy.get('[data-context=main-menu-list]').find('[data-context=main-menu-item].lvl1').should('have.length', 4)
            cy.get('[data-context=main-menu-item].lvl1').eq(0).should('contain', 'SAV')
            cy.get('[data-context=main-menu-item].lvl1').eq(1).should('contain', 'Prospects')
            cy.get('[data-context=main-menu-item].lvl1').eq(2).should('contain', 'Commandes')
            cy.get('[data-context=main-menu-item].lvl1').eq(3).should('contain', 'Tâches')

            // submenu data
            cy.get('[data-context=main-menu-list]').contains('SAV').click()
            cy.get('[data-context=main-menu-item].lvl1')
                .eq(0)
                .find('>[data-context=main-menu-item--sub-menu-list]')
                .as('this-submenu-items')
            cy.get('@this-submenu-items').find('[data-context=main-menu-item--sub-menu-title]').should('have.length', 2)
            cy.get('@this-submenu-items')
                .find('[data-context=main-menu-item--sub-menu-title]')
                .eq(0)
                .should('contain', 'Factures')
            cy.get('@this-submenu-items')
                .find('[data-context=main-menu-item--sub-menu-title]')
                .eq(1)
                .should('contain', 'Suivi SAV')

            // menu links
            cy.get('@this-submenu-items').contains('Factures').click()
            cy.get('@this-submenu-items')
                .find('>[data-context=main-menu-item]')
                .eq(0)
                .find('[data-context=main-menu-item--link]')
                .as('this-submenu-page-links')
            cy.get('@this-submenu-page-links').should('have.length', 2)
            cy.get('@this-submenu-page-links')
                .eq(0)
                .should('contain', 'Recherche')
                .should('have.attr', 'href')
                .and('match', /\/legacy\/facture\/search/)
            cy.get('@this-submenu-page-links')
                .eq(1)
                .should('contain', 'Liste Factures')
                .should('have.attr', 'href')
                .and('match', /\/legacy\/v1\/administration\/divers\/pdfFacturesList\.php/)

            // follow links
            cy.get('@this-submenu-page-links').eq(0).click()
            cy.location('pathname').should('eq', '/legacy/facture/search')
            // style on active page
            cy.get('[data-context=main-menu-list]').contains('Recherche').should('have.class', 'active')
            cy.get('[data-context=main-menu-item].lvl1').eq(0).should('have.class', 'open')
            cy.get('[data-context=main-menu-item].lvl2').eq(0).should('have.class', 'open')

            // active leaf (= not present when not active)
            cy.get('[data-context=main-menu-item].lvl1')
                .eq(1)
                .find('[data-context=main-menu-item].lvl2')
                .eq(0)
                .find('[data-context=main-menu-item].lvl3')
                .as('this-submenu-items')
            cy.get('@this-submenu-items').find('[data-context=main-menu-item--active-leaf]').should('have.length', 0)
            // visit a dynamic page
            cy.visit('/legacy/prospect/prospectEdit?id=123654')
            cy.get('@this-submenu-items').find('[data-context=main-menu-item--active-leaf]').should('have.length', 1)
            cy.get('[data-context=main-menu-list]').contains('Edition prospect').should('have.class', 'active')
        })

        it('should reload the menu from session storage instead of calling the api', () => {
            cy.clearSessionStorage()
            cy.visit('/')
            cy.wait('@main_menu')

            cy.reload()
            cy.get('@main_menu.all').then((interceptions) => {
                expect(interceptions).to.have.length(1)
            })
            cy.get('[data-context=main-menu-item]').should('have.length', 19)
        })

        it('can use multiple matches for an entry definition', function () {
            // check when different matches
            cy.visit('/legacy/commande/testrecherche')
            cy.get('[data-context=main-menu-item].lvl1')
                .eq(2)
                .find('[data-context=main-menu-item].lvl2')
                .eq(0)
                .find('[data-context=main-menu-item].lvl3')
                .eq(0)
                .as('this-menu-entry')
            cy.get('@this-menu-entry')
                .find('[data-context=main-menu-item--link]')
                .should('have.class', 'active')
                .and('contain', 'Chercher')
            cy.visit('/legacy/commande/recherche')
            cy.get('@this-menu-entry')
                .find('[data-context=main-menu-item--link]')
                .should('have.class', 'active')
                .and('contain', 'Chercher')
        })

        it('open/close as expected', function () {
            cy.visit('/legacy/commande/recherche')
            cy.get('[data-context=main-menu-item].lvl1')
                .eq(2)
                .find('[data-context=main-menu-item].lvl2')
                .eq(0)
                .find('[data-context=main-menu-item].lvl3')
                .eq(0)
                .as('this-menu-entry')

            // behavior when navigate in menu (close / open)
            cy.get('[data-context=main-menu-list]').contains('SAV').click()
            cy.get('[data-context=main-menu-list]').contains('Factures').click()
            cy.get('[data-context=main-menu-list]').contains('Liste Factures').click()
            cy.get('[data-context=main-menu-list]').contains('Liste Factures').should('have.class', 'active')
            // close and deactivate previous menu
            cy.get('@this-menu-entry').find('[data-context=main-menu-item--link]').should('not.have.class', 'active')
            cy.get('[data-context=main-menu-item].lvl1').eq(2).should('not.have.class', 'open')
        })

        it('hide useless entries', function () {
            cy.visit('/legacy/commande/recherche')
            // no display of all parent menus if children only link to dynamic pages (= hidden until on page)
            cy.get('[data-context=main-menu-list]').contains('Inutile').should('have.length', 0)
            cy.visit('/legacy/test')
            cy.get('[data-context=main-menu-list]').contains('Inutile').should('be.visible')
        })

        it('fold menu tree for alone child', function () {
            cy.visit('/')

            // alone child substitute to its parent
            cy.get('[data-context=main-menu-item].lvl1')
                .eq(2) // Commandes
                .find('[data-context=main-menu-item].lvl2')
                .eq(1) // > À accepter - À accepter
                .as('this-submenu-item')
            cy.get('@this-submenu-item')
                .should('contain', 'À accepter')
                .find('[data-context=main-menu-item].lvl3')
                .should('have.length', 0)

            // never for parent on lvl1
            cy.get('[data-context=main-menu-item].lvl1')
                .eq(3) // Tâches
                .find('[data-context=main-menu-item].lvl2') // (children)
                .should('have.length', 1)

            // parent title is prepended to child title if different
            cy.get('[data-context=main-menu-item].lvl1')
                .eq(2) // Commandes
                .find('[data-context=main-menu-item].lvl2')
                .eq(2) // > CB … - Chercher
                .as('this-submenu-item')
            cy.get('@this-submenu-item')
                .should('contain', 'CB avec rappel client - Chercher')
                .find('[data-context=main-menu-item].lvl3')
                .should('have.length', 0)
        })

        it('can be filtered', function () {
            cy.visit('/')

            // simple filter
            cy.get('#main_menu_filter input').clear()
            cy.get('#main_menu_filter input').type('    tation  ')
            cy.get('[data-context=main-menu-item].lvl1')
                .should('have.length', 1)
                .eq(0)
                .should('contain', 'SAV')
                .find('[data-context=main-menu-item].lvl2')
                .should('have.length', 1)
                .eq(0)
                .should('contain', 'Suivi SAV - Stations')

            // insensitive to case and accents
            cy.get('#main_menu_filter input').clear()
            cy.get('#main_menu_filter input').type('a accépt')
            cy.get('[data-context=main-menu-item].lvl1')
                .should('have.length', 1)
                .eq(0)
                .should('contain', 'Commandes')
                .find('[data-context=main-menu-item].lvl2')
                .should('have.length', 1)
                .eq(0)
                .should('contain', 'À accepter')

            // can search on parent name too
            cy.get('#main_menu_filter input').clear()
            cy.get('#main_menu_filter input').type('command')
            cy.get('[data-context=main-menu-item].lvl1')
                .should('have.length', 1)
                .eq(0)
                .should('contain', 'Commandes')
                .find('[data-context=main-menu-item].lvl2')
                .should('have.length', 3)
                .eq(1)
                .should('contain', 'À accepter')
        })
    })
})
