describe('Release notes page', function () {
    describe('Release notes', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            cy.intercept('POST', 'https://erp.graphql/v1/graphql', {
                fixture: 'graphql/release_note/fetch_communication_release_notes.json',
            }).as('fetch_communication_release_notes')

            cy.visit('/release-notes')
            cy.toggleMenu()
            cy.wait('@fetch_communication_release_notes')
        })

        it('has a direct link on topbar to open the release notes page', () => {
            cy.get('[data-context=whats-up]').should('have.attr', 'href', '/release-notes')
        })

        it('displays release notes', () => {
            cy.get('[data-context=page-header]').should('contain', 'Release notes')
            cy.get('[data-context=release]').should('have.length', 3).eq(1).as('release_note')

            cy.get('@release_note').find('[data-context=title]').should('contain', 'Release-2021-10')
            cy.get('@release_note').find('[data-context=badge]').should('contain', 'release')
            cy.get('@release_note').find('[data-context=description]').should('be.visible')

            cy.get('@release_note').find('[data-context=notes]').should('have.length', 1).eq(0).as('notes')

            cy.get('@notes').find('[data-context=section-title]').should('contain', 'Mises à jour')

            cy.get('@notes').find('[data-context=note]').should('have.length', 6).eq(0).as('note')

            cy.get('@note').find('[data-context=note-prefix]').should('contain', 'WEB')

            cy.get('@note')
                .find('[data-context=note-title]')
                .should('contain', 'Pouvoir payer une commande en attente de paiement')

            cy.get('@notes').find('[data-context=note]').eq(5).as('note')

            cy.get('@note').find('[data-context=note-prefix]').should('contain', 'ERP')

            cy.get('@note')
                .find('[data-context=note-title]')
                .should('contain', 'Plus de filtres dans la page des transactions magasins')

            cy.get('[data-context=top-pagination]').should('be.visible')
            cy.get('[data-context=bottom-pagination]').should('be.visible')
        })

        it('display a release note details in the side panel', () => {
            cy.intercept('POST', 'https://erp.graphql/v1/graphql', {
                fixture: 'graphql/release_note/fetch_communication_release_note_by_pk.json',
            }).as('fetch_communication_release_note_by_pk')

            cy.get('[data-context=release]')
                .should('have.length', 3)
                .eq(2)
                .find('[data-context=notes]')
                .eq(1)
                .find('[data-context=note]')
                .eq(0)
                .find('[data-context=note-title]')
                .eq(0)
                .click()

            cy.wait('@fetch_communication_release_note_by_pk')

            cy.get('[data-context=slide-out-container]')
                .should('be.visible')
                .as('side_panel')
                .find('[data-context=page-header]')
                .should('contain', 'Correction du calcul de la quantité en cours par dépôt')
            cy.get('@side_panel').find('[data-context=badge]').should('contain', 'erp')

            cy.get('@side_panel').find('[data-context=notes]').as('notes').should('have.length', 3)

            cy.get('@notes').eq(0).find('[data-context=section-title]').should('contain', 'Mises à jour')

            cy.get('@notes').eq(1).find('[data-context=section-title]').should('contain', 'Correction(s) de bug(s)')

            cy.get('@notes')
                .eq(2)
                .find('[data-context=section-title]')
                .should('contain', 'Nouvelle(s) fonctionnalité(s)')
        })
    })

    describe('Single release note', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()

            cy.intercept('POST', 'https://erp.graphql/v1/graphql', {
                fixture: 'graphql/release_note/fetch_communication_release_notes_Xv6UZ.json',
            }).as('fetch_communication_release_notes')

            cy.visit('/release-notes/Xv6UZ')
            cy.toggleMenu()
            cy.wait('@fetch_communication_release_notes')
        })

        it('show a single non-paginated release note if the ID is provided in the url', () => {
            cy.get('[data-context=page-header]').should('contain', 'Release notes')
            cy.get('[data-context=release]').should('have.length', 1).eq(0).as('release_note')

            cy.get('@release_note').find('[data-context=title]').should('contain', 'Release-2021-11')
            cy.get('@release_note').find('[data-context=badge]').should('contain', 'release')
            cy.get('@release_note').find('[data-context=description]').should('be.visible')

            cy.get('[data-context=top-pagination]').should('not.exist')
        })
    })
})
