describe('View safety stock dashboard', () => {
    beforeEach(() => {
        cy.authenticate()
        cy.mockErpUser()

        cy.intercept('POST', '**/api/erp/v1/article/safety-stock/recommendations', {
            fixture: 'erp/safety-stock-recommendations/cpost_safety_stock_recommendations.json',
        }).as('fetch-safety-stock-recommendations')

        cy.visit('/safety-stock')

        cy.get('[data-context=erp-page]').should('be.visible')
        cy.wait('@fetch-safety-stock-recommendations')
    })

    it('Filters are well displayed', () => {
        cy.get('[data-context=dashboard-filters]').should('be.visible')

        cy.get('[data-context=product]').should('contain', 'Produit')
        cy.get('[data-context=brand]').should('contain', 'Marque')
        cy.get('[data-context=supplier]').should('contain', 'Fournisseur')
        cy.get('[data-context=subcategory]').should('contain', 'Sous-catégorie')

        cy.contains('ABC - XYZ').should('exist')
    })

    it('Table headers and first row content are visible', () => {
        cy.get('[data-context=erp-table]').as('table')

        cy.get('@table').find('thead th').as('headers')
        const headers = [
            'Produit',
            'Fournisseur',
            'Sous-Catégorie',
            'Stock livrable',
            'Rotation journalière',
            'Rupture de stock',
            'Délai de réapprovisionnement moyen du produit',
            'Stock de sécurité actuel',
            'Stock de sécurité suggéré',
        ]
        headers.forEach((header, index) => {
            cy.get('@headers').eq(index).should('contain', header)
        })

        cy.get('@table').find('tbody tr').first().as('firstRow')

        cy.get('@firstRow').within(() => {
            cy.get('[data-context=copy-sku]').should('exist')
            cy.get('[data-context=erp-link]').should('have.attr', 'href')
        })
    })

    it('Should sort results', () => {
        cy.get('[data-context=erp-table]').find('thead th').as('headers')

        const columns_to_test = [
            { index: 7, field: 'original_safety_stock' },
            { index: 8, field: 'recommended_safety_stock_quantity' },
        ]

        columns_to_test.forEach(({ index, field }) => {
            cy.get('@headers').eq(index).click()
            cy.wait('@fetch-safety-stock-recommendations').its('request.body').should('include', {
                limit: 50,
                order_by: field,
                order_direction: 'asc',
            })

            cy.get('@headers').eq(index).click()
            cy.wait('@fetch-safety-stock-recommendations').its('request.body').should('include', {
                limit: 50,
                order_by: field,
                order_direction: 'desc',
            })
        })
    })
})
