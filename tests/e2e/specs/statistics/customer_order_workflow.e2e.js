import { STATISTICS_GLOBAL_READ } from '../../../../src/apps/erp/permissions.js'
import { NARROW_NO_BREAK_SPACE, NON_BREAKING_SPACE } from '../../utils/text-utils.js'

describe('Customer order statistic dashboard page', () => {
    const PAGE = '/statistics/dashboard'

    describe('Page UI', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([STATISTICS_GLOBAL_READ])

            cy.mockDate(new Date(2023, 8, 1))

            cy.intercept('POST', '**/v1/statistics/customer-order/workflow', (request) => {
                const search = request.body.filters[0].order_status._eq

                let response = {}
                if (search === 'Payée') {
                    request.alias = 'fetch_paid'

                    response = {
                        status: 'success',
                        data: {
                            statistics: [
                                {
                                    current: { margin: 10, number: 10, turnover: 100, customer_order_ids: [10] },
                                    source_group: 'Solo',
                                    source: 'Solo',
                                },
                                {
                                    current: { margin: 20, number: 20, turnover: 200, customer_order_ids: [20] },
                                    source_group: 'Group',
                                    source: 'Group 1',
                                },
                                {
                                    current: { margin: 30, number: 30, turnover: 300, customer_order_ids: [30] },
                                    source_group: 'Group',
                                    source: 'Group 2',
                                },
                                {
                                    current: { margin: 4, number: 4, turnover: 40, customer_order_ids: [40] },
                                    source_group: 'Small Group',
                                    source: 'Small Group 1',
                                },
                                {
                                    current: { margin: 5, number: 5, turnover: 50, customer_order_ids: [5] },
                                    source_group: 'Small Group',
                                    source: 'Small Group 2',
                                },
                            ],
                        },
                    }
                }

                if (search === 'Préparable') {
                    request.alias = 'fetch_preparable'

                    response = {
                        status: 'success',
                        data: {
                            statistics: [
                                {
                                    current: { margin: 123, number: 123, turnover: 1234, customer_order_ids: [123] },
                                    warehouse: null,
                                    carrier: 'Emport Truc',
                                },
                                {
                                    current: { margin: 456, number: 456, turnover: 4567, customer_order_ids: [456] },
                                    warehouse: null,
                                    carrier: 'Uber',
                                },
                                {
                                    current: { margin: 147, number: 258, turnover: 1423, customer_order_ids: [258] },
                                    warehouse: null,
                                    carrier: 'Uber',
                                },
                            ],
                        },
                    }
                }

                if (search === 'En préparation') {
                    request.alias = 'fetch_in_preparation'
                    response = {
                        status: 'success',
                        data: {
                            statistics: [
                                {
                                    current: {
                                        margin: 11,
                                        number: 11,
                                        turnover: 111,
                                        customer_order_ids: [1, 11, 111],
                                    },
                                    warehouse: 'Dépot 1',
                                    carrier: 'Emport',
                                },
                                {
                                    current: {
                                        margin: 11,
                                        number: 11,
                                        turnover: 111,
                                        customer_order_ids: [1, 11, 111],
                                    },
                                    warehouse: 'Dépot 1',
                                    carrier: 'Emport',
                                },
                                {
                                    current: {
                                        margin: 22,
                                        number: 22,
                                        turnover: 222,
                                        customer_order_ids: [2, 22, 222],
                                    },
                                    warehouse: 'Dépot 2',
                                    carrier: 'Livraison',
                                },
                                {
                                    current: {
                                        margin: 33,
                                        number: 33,
                                        turnover: 333,
                                        customer_order_ids: [3, 33, 333],
                                    },
                                    warehouse: 'Dépot 3',
                                    carrier: 'Livraison',
                                },
                            ],
                        },
                    }
                }

                if (search === 'Préparée') {
                    request.alias = 'fetch_prepared'

                    response = {
                        status: 'success',
                        data: {
                            statistics: [
                                {
                                    current: {
                                        margin: 44,
                                        number: 44,
                                        turnover: 444,
                                        customer_order_ids: [4, 44, 444],
                                    },
                                    warehouse: 'Dépot 4',
                                    carrier: 'Emport',
                                },
                                {
                                    current: {
                                        margin: 55,
                                        number: 55,
                                        turnover: 555,
                                        customer_order_ids: [5, 55, 555],
                                    },
                                    warehouse: 'Dépot 5',
                                    carrier: 'Livraison',
                                },
                                {
                                    current: {
                                        margin: 66,
                                        number: 66,
                                        turnover: 666,
                                        customer_order_ids: [6, 66, 666],
                                    },
                                    warehouse: 'Dépot 6',
                                    carrier: 'Livraison',
                                },
                                {
                                    current: { margin: 55, number: 55, turnover: 555, customer_order_ids: [5, 55] },
                                    warehouse: 'Dépot 5',
                                    carrier: 'Livraison',
                                },
                                {
                                    current: {
                                        margin: 66,
                                        number: 66,
                                        turnover: 666,
                                        customer_order_ids: [6, 66, 666],
                                    },
                                    warehouse: 'Dépot 6',
                                    carrier: 'Livraison',
                                },
                            ],
                        },
                    }
                }

                request.reply(response)
            })
        })

        it('should have a first page visible', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            cy.get('[data-context=link-to-order-followup]').should('exist')

            // well, we are already on workflow page
            cy.get('[data-context=statistic-tabs] [data-context=item]:nth(0)').click()

            /* Paid */
            cy.wait('@fetch_paid')

            let table_headers = ['', 'Source', 'CA HT', 'Nbr cmd', '']

            cy.get('[data-context=workflow-paid] th[scope=col]').should('have.length', table_headers.length)
            table_headers.forEach((text, idx) => {
                cy.get('[data-context=workflow-paid] th[scope=col]').eq(idx).should('contain', text)
            })

            cy.get('[data-context=workflow-paid] tbody tr').should('have.length', 4)
            cy.selectTable('[data-context=workflow-paid] table')

            // TOTAL
            cy.selectCell(0, 0).should('contain', 'TOTAL')
            cy.selectCell(0, 1).should('contain', `690${NON_BREAKING_SPACE}€`)
            cy.selectCell(0, 2).should('contain', '69')

            // Group
            cy.selectCell(1, 0).should('not.empty')
            cy.selectCell(1, 1).should('contain', 'Group')
            cy.selectCell(1, 2).should('contain', `500${NON_BREAKING_SPACE}€`)
            cy.selectCell(1, 3).should('contain', '50')

            // unfold Group
            cy.selectCell(1, 0).find('[data-context=erp-expand-trigger]').click()
            cy.get('[data-context=workflow-paid] tbody tr').should('have.length', 6)

            // fold group
            cy.selectCell(1, 0).find('[data-context=erp-expand-trigger]').click()
            cy.get('[data-context=workflow-paid] tbody tr').should('have.length', 4)

            /* preparable */
            cy.wait('@fetch_preparable')
            table_headers = ['Dépot', 'CA HT', 'Nbr cmd', '']

            cy.get('[data-context=workflow-preparable] th[scope=col]').should('have.length', table_headers.length)
            table_headers.forEach((text, idx) => {
                cy.get('[data-context=workflow-preparable]  th[scope=col]').eq(idx).should('contain', text)
            })

            cy.get('[data-context=workflow-preparable] tbody tr').should('have.length', 3)
            cy.selectTable('[data-context=workflow-preparable] table')

            cy.selectCell(0, 0).should('contain', 'TOTAL')
            cy.selectCell(0, 1).should('contain', `7${NARROW_NO_BREAK_SPACE}224${NON_BREAKING_SPACE}€`)
            cy.selectCell(0, 2).should('contain', '837')

            cy.selectCell(1, 0).should('contain', 'Expédition')
            cy.selectCell(1, 1).should('contain', `5${NARROW_NO_BREAK_SPACE}990${NON_BREAKING_SPACE}€`)
            cy.selectCell(1, 2).should('contain', '714')

            cy.selectCell(2, 0).should('contain', 'Emport')
            cy.selectCell(2, 1).should('contain', `1${NARROW_NO_BREAK_SPACE}234${NON_BREAKING_SPACE}€`)
            cy.selectCell(2, 2).should('contain', '123')

            /* in_preparation */
            cy.wait('@fetch_in_preparation')

            cy.get('[data-context=workflow-in-preparation] th[scope=col]').should('have.length', table_headers.length)
            table_headers.forEach((text, idx) => {
                cy.get('[data-context=workflow-in-preparation] th[scope=col]').eq(idx).should('contain', text)
            })

            cy.get('[data-context=workflow-in-preparation] tbody tr').should('have.length', 6)
            cy.selectTable('[data-context=workflow-in-preparation] table')

            // total TOTAL
            cy.selectCell(0, 0).should('contain', 'TOTAL')
            cy.selectCell(0, 1).should('contain', `777${NON_BREAKING_SPACE}€`)
            cy.selectCell(0, 2).should('contain', '77')

            // Expedition
            cy.selectCell(1, 0).should('contain', 'Expédition')
            cy.selectCell(1, 1).should('contain', `555${NON_BREAKING_SPACE}€`)
            cy.selectCell(1, 2).should('contain', '55')

            // dépot 3
            cy.selectCell(2, 0).should('contain', 'Dépot 3')
            cy.selectCell(2, 1).should('contain', `333${NON_BREAKING_SPACE}€`)
            cy.selectCell(2, 2).should('contain', '33')

            // dépot 2
            cy.selectCell(3, 0).should('contain', 'Dépot 2')
            cy.selectCell(3, 1).should('contain', `222${NON_BREAKING_SPACE}€`)
            cy.selectCell(3, 2).should('contain', '22')

            // Emport
            cy.selectCell(4, 0).should('contain', 'Emport')
            cy.selectCell(4, 1).should('contain', `222${NON_BREAKING_SPACE}€`)
            cy.selectCell(4, 2).should('contain', '22')

            // Depot 1
            cy.selectCell(5, 0).should('contain', 'Dépot 1')
            cy.selectCell(5, 1).should('contain', `222${NON_BREAKING_SPACE}€`)
            cy.selectCell(5, 2).should('contain', '22')

            /* prepared */
            cy.wait('@fetch_prepared')

            cy.get('[data-context=workflow-prepared] th[scope=col]').should('have.length', table_headers.length)
            table_headers.forEach((text, idx) => {
                cy.get('[data-context=workflow-prepared] th[scope=col]').eq(idx).should('contain', text)
            })

            cy.get('[data-context=workflow-prepared] tbody tr').should('have.length', 6)
            cy.selectTable('[data-context=workflow-prepared] table')

            // total
            cy.selectCell(0, 0).should('contain', 'TOTAL')
            cy.selectCell(0, 1).should('contain', `2${NARROW_NO_BREAK_SPACE}886${NON_BREAKING_SPACE}€`)
            cy.selectCell(0, 2).should('contain', '286')

            cy.selectCell(1, 0).should('contain', 'Expédition')
            cy.selectCell(1, 1).should('contain', `2${NARROW_NO_BREAK_SPACE}442${NON_BREAKING_SPACE}€`)
            cy.selectCell(1, 2).should('contain', '242')

            cy.selectCell(2, 0).should('contain', 'Dépot 6')
            cy.selectCell(2, 1).should('contain', `1${NARROW_NO_BREAK_SPACE}332${NON_BREAKING_SPACE}€`)
            cy.selectCell(2, 2).should('contain', '132')

            cy.selectCell(3, 0).should('contain', 'Dépot 5')
            cy.selectCell(3, 1).should('contain', `1${NARROW_NO_BREAK_SPACE}110${NON_BREAKING_SPACE}€`)
            cy.selectCell(3, 2).should('contain', '110')

            cy.selectCell(4, 0).should('contain', 'Emport')
            cy.selectCell(4, 1).should('contain', `444${NON_BREAKING_SPACE}€`)
            cy.selectCell(4, 2).should('contain', '44')

            cy.selectCell(5, 0).should('contain', 'Dépot 4')
            cy.selectCell(5, 1).should('contain', `444${NON_BREAKING_SPACE}€`)
            cy.selectCell(5, 2).should('contain', '44')

            // refreshing data
            cy.get('[data-context=workflow-paid] [data-context=erp-hr] [data-context=erp-button]').click()
            cy.wait('@fetch_paid')
            cy.get('[data-context=workflow-preparable] [data-context=erp-hr] [data-context=erp-button]').click()
            cy.wait('@fetch_preparable')
            cy.get('[data-context=workflow-in-preparation] [data-context=erp-hr] [data-context=erp-button]').click()
            cy.wait('@fetch_in_preparation')
            cy.get('[data-context=workflow-prepared] [data-context=erp-hr] [data-context=erp-button]').click()
            cy.wait('@fetch_prepared')

            // refresh all data
            cy.get('[data-context=refresh-all]').click()
            cy.wait('@fetch_paid')
            cy.wait('@fetch_preparable')
            cy.wait('@fetch_in_preparation')
            cy.wait('@fetch_prepared')
        })
    })
})
