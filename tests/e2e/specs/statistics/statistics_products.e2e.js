import { STATISTICS_GLOBAL_READ } from '../../../../src/apps/erp/permissions.js'
import { NARROW_NO_BREAK_SPACE, NON_BREAKING_SPACE } from '../../utils/text-utils.js'

describe('Customer order statistic dashboard page', () => {
    const PAGE = '/statistics/dashboard'

    describe('Page UI', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([STATISTICS_GLOBAL_READ])

            // mock the first statistics dashboard page
            cy.intercept('POST', '**/v1/statistics/customer-order/workflow', {
                status: 'success',
                data: {
                    statistics: [],
                },
            })

            cy.mockDate(new Date(2023, 8, 1))

            cy.intercept('POST', '**/v1/statistics/unavailable-products', {
                statusCode: 200,
                body: {
                    data: {
                        statistics: [
                            {
                                date: '2023-09-01',
                                total_quantity: 100,
                                turnover: 1000,
                                sku_number: 10,
                            },
                            {
                                date: '2023-08-25',
                                total_quantity: 80,
                                turnover: 1200,
                                sku_number: 8,
                            },
                            {
                                date: '2023-08-04',
                                total_quantity: 120,
                                turnover: 800,
                                sku_number: 12,
                            },
                        ],
                    },
                },
            })
        })

        it('should have a first page visible', () => {
            cy.visit(PAGE)
            cy.toggleMenu()

            // Go to product page
            cy.get('[data-context=statistic-tabs] [data-context=item]:nth(2)').click()

            /* unavailable products */
            cy.selectTable('[data-context=unavailable-products] table')
            // turnover
            cy.selectCell(0, 1).should('contain', `1${NARROW_NO_BREAK_SPACE}000${NON_BREAKING_SPACE}€`)
            cy.selectCell(0, 2).should('contain', `-17${NON_BREAKING_SPACE}%`)
            cy.selectCell(0, 3).should('contain', `25${NON_BREAKING_SPACE}%`)
            // Number of sku
            cy.selectCell(1, 1).should('contain', `10`)
            cy.selectCell(1, 2).should('contain', `25${NON_BREAKING_SPACE}%`)
            cy.selectCell(1, 3).should('contain', `-17${NON_BREAKING_SPACE}%`)
            // Quantity
            cy.selectCell(2, 1).should('contain', `100`)
            cy.selectCell(2, 2).should('contain', `25${NON_BREAKING_SPACE}%`)
            cy.selectCell(2, 3).should('contain', `-17${NON_BREAKING_SPACE}%`)
        })
    })
})
