import { STATISTICS_GLOBAL_READ } from '../../../../src/apps/erp/permissions.js'
import { NON_BREAKING_SPACE, NARROW_NO_BREAK_SPACE } from '../../utils/text-utils.js'

describe('Customer order statistic dashboard page', () => {
    const PAGE = '/statistics/dashboard'

    describe('Page UI', () => {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser([STATISTICS_GLOBAL_READ])

            cy.mockDate(new Date(2023, 5, 14)) // 2023/14/06

            // mock the first statistics dashboard page
            cy.intercept('POST', '**/v1/statistics/customer-order/workflow', {
                status: 'success',
                data: {
                    statistics: [],
                },
            })

            // mock the request of overview
            cy.intercept('GET', '**/v1/customer-order/sources', { fixture: 'erp/customer-order/sources' }).as(
                'get_sources',
            )
            cy.intercept('POST', '**/v1/statistics/customer-order/overview', {
                statusCode: 200,
                body: {
                    data: {
                        statistics: [
                            {
                                period: 'J+',
                                from: '2023-07-19',
                                to: '2023-07-20',
                                current: {
                                    turnover: 1150,
                                    number: 11,
                                    margin: 110,
                                },
                                n_1: {
                                    turnover: 1000,
                                    number: 10,
                                    margin: 90,
                                },
                            },
                            {
                                period: 'J-',
                                from: '2023-07-17',
                                to: '2023-07-18',
                                current: {
                                    turnover: 1600,
                                    number: 10,
                                    margin: 150,
                                },
                                n_1: {
                                    turnover: 2100,
                                    number: 11,
                                    margin: 200,
                                },
                            },
                        ],
                    },
                },
            }).as('fetch_customer_order_overview')

            cy.intercept('POST', '**/v1/statistics/invoice/overview', {
                statusCode: 200,
                body: {
                    data: {
                        statistics: [
                            {
                                period: 'J+',
                                from: '2023-07-19',
                                to: '2023-07-20',
                                current: {
                                    turnover: 1234,
                                    number: 12,
                                    margin: 123,
                                },
                                n_1: {
                                    turnover: 0,
                                    number: 0,
                                    margin: 0,
                                },
                            },
                            {
                                period: 'J-',
                                from: '2023-07-17',
                                to: '2023-07-18',
                                current: {
                                    turnover: 4561,
                                    number: 45,
                                    margin: 456,
                                },
                                n_1: {
                                    turnover: 0,
                                    number: 0,
                                    margin: 0,
                                },
                            },
                        ],
                    },
                },
            }).as('fetch_invoice_overview')

            cy.visit(PAGE)
            cy.toggleMenu()
            cy.get('[data-context=statistic-tabs] [data-context=item]:nth(1)').click()
        })

        it('should have a first page visible', () => {
            cy.get('[data-context=filter-with-turnover-type] select').should('have.value', 'commande')

            cy.wait('@get_sources')
            cy.wait('@fetch_customer_order_overview')
            // Check table headers
            const table_headers = [
                '',
                'CA',
                '%',
                'Nbr cmd',
                '%',
                'Marge',
                '%',
                'Tx de marque',
                '%',
                '🛒 moyen TTC',
                '%',
            ]

            cy.get('th[scope=col]').should('have.length', table_headers.length)

            table_headers.forEach((text, idx) => {
                cy.get('th[scope=col]').eq(idx).should('contain', text)
            })

            cy.get('[data-context=table-row]').should('have.length', 2)
            cy.get('[data-context=erp-table] tbody').within(() => {
                // J
                cy.get('tr:eq(0) td:eq(0)').should('contain', 'J+')
                cy.get('tr:eq(0) td:eq(1)').should('contain', `1${NARROW_NO_BREAK_SPACE}150${NON_BREAKING_SPACE}€ `)
                cy.get('tr:eq(0) td:eq(2)').should('contain', `+15${NON_BREAKING_SPACE}%`)
                cy.get('tr:eq(0) td:eq(3)').should('contain', `11`)
                cy.get('tr:eq(0) td:eq(4)').should('contain', `+10${NON_BREAKING_SPACE}%`)
                cy.get('tr:eq(0) td:eq(5)').should('contain', `110${NON_BREAKING_SPACE}€`)
                cy.get('tr:eq(0) td:eq(6)').should('contain', `+22${NON_BREAKING_SPACE}%`)
                cy.get('tr:eq(0) td:eq(7)').should('contain', `9,57${NON_BREAKING_SPACE}%`)
                cy.get('tr:eq(0) td:eq(8)').should('contain', `+0,57`)
                cy.get('tr:eq(0) td:eq(9)').should('contain', `125${NON_BREAKING_SPACE}€`)
                cy.get('tr:eq(0) td:eq(10)').should('contain', `+5${NON_BREAKING_SPACE}%`)

                // J-1
                cy.get('tr:eq(1) td:eq(0)').should('contain', 'J-')
                cy.get('tr:eq(1) td:eq(1)').should('contain', `1${NARROW_NO_BREAK_SPACE}600${NON_BREAKING_SPACE}€ `)
                cy.get('tr:eq(1) td:eq(2)').should('contain', `-24${NON_BREAKING_SPACE}%`)
                cy.get('tr:eq(1) td:eq(3)').should('contain', `10`)
                cy.get('tr:eq(1) td:eq(4)').should('contain', `-9${NON_BREAKING_SPACE}%`)
                cy.get('tr:eq(1) td:eq(5)').should('contain', `150${NON_BREAKING_SPACE}€`)
                cy.get('tr:eq(1) td:eq(6)').should('contain', `-25${NON_BREAKING_SPACE}%`)
                cy.get('tr:eq(1) td:eq(7)').should('contain', `9,38${NON_BREAKING_SPACE}%`)
                cy.get('tr:eq(1) td:eq(8)').should('contain', `-0,15`)
                cy.get('tr:eq(1) td:eq(9)').should('contain', `192${NON_BREAKING_SPACE}€`)
                cy.get('tr:eq(1) td:eq(10)').should('contain', `-16${NON_BREAKING_SPACE}%`)
            })

            cy.get('[data-context=delete-comparison]').should('be.disabled')

            // Select filter
            cy.get('[data-context=filter-with-source] [data-context=erp-multiselect]').click()
            cy.get('[data-context=filter-with-source] [data-context=erp-multiselect] [data-context=suggestion]').should(
                'have.length',
                23,
            )

            cy.get('[data-context=filter-with-group-source] [data-context=erp-multiselect]').click()
            cy.get(
                '[data-context=filter-with-group-source] [data-context=erp-multiselect] [data-context=suggestion]',
            ).should('have.length', 7)
            cy.get('[data-context=filter-with-group-source] [data-context=erp-multiselect] [data-context=suggestion]')
                .eq(0)
                .click()

            cy.get('[data-context=filter-with-source] [data-context=erp-multiselect]').click()
            cy.get('[data-context=filter-with-source] [data-context=erp-multiselect] [data-context=suggestion]')
                .should('have.length', 2)
                .eq(0)
                .click()
            cy.get('[data-context=filter-with-source] [data-context=erp-multiselect]').type('{esc}')

            cy.get('[data-context=refresh-statistics]').click()

            cy.wait('@fetch_customer_order_overview').then((xhr) => {
                expect(xhr.request.body.filters).to.deep.eq({
                    _and: [
                        {
                            _and: [
                                { order_status: { _neq: 'Erreur' } },
                                { order_status: { _neq: 'Annulée' } },
                                { order_status: { _neq: 'Ouverte' } },
                                { order_status: { _neq: 'Supprimée' } },
                            ],
                        },
                        { _or: [{ source_group: { _eq: 'Son-Video.com' } }] },
                        { _or: [{ source: { _eq: 'CallCenter SV' } }] },
                    ],
                })
            })

            cy.get('[data-context=filter-with-turnover-type] select').select('CA facturé')
            cy.get('[data-context=refresh-statistics]').click()

            cy.wait('@fetch_invoice_overview').then((xhr) => {
                expect(xhr.request.body.filters).to.deep.eq({
                    _and: [
                        {
                            _or: [{ order_status: { _eq: 'Facturée' } }, { order_status: { _eq: 'Créditée' } }],
                        },
                        { _or: [{ source_group: { _eq: 'Son-Video.com' } }] },
                        { _or: [{ source: { _eq: 'CallCenter SV' } }] },
                    ],
                })
            })
        })

        it('should manage multiple line', () => {
            // add 2 comparisons
            cy.get('[data-context=add-statistic]').click()
            cy.wait('@fetch_customer_order_overview')

            cy.get('[data-context=add-statistic]').click()
            cy.wait('@fetch_customer_order_overview')

            cy.get('[data-context=erp-table] tbody').within(() => {
                cy.get('tr:eq(0) td:eq(1) > div').should('have.length', 3)
                cy.get('tr:eq(0) td:eq(2) > div').should('have.length', 3)
                cy.get('tr:eq(0) td:eq(3) > div').should('have.length', 3)
                cy.get('tr:eq(0) td:eq(4) > div').should('have.length', 3)
                cy.get('tr:eq(0) td:eq(5) > div').should('have.length', 3)
                cy.get('tr:eq(0) td:eq(6) > div').should('have.length', 3)
                cy.get('tr:eq(0) td:eq(7) > div').should('have.length', 3)
                cy.get('tr:eq(0) td:eq(8) > div').should('have.length', 3)
                cy.get('tr:eq(0) td:eq(9) > div').should('have.length', 3)
                cy.get('tr:eq(0) td:eq(10) > div').should('have.length', 3)

                cy.get('tr:eq(1) td:eq(1) > div').should('have.length', 3)
                cy.get('tr:eq(1) td:eq(2) > div').should('have.length', 3)
                cy.get('tr:eq(1) td:eq(3) > div').should('have.length', 3)
                cy.get('tr:eq(1) td:eq(4) > div').should('have.length', 3)
                cy.get('tr:eq(1) td:eq(5) > div').should('have.length', 3)
                cy.get('tr:eq(1) td:eq(6) > div').should('have.length', 3)
                cy.get('tr:eq(1) td:eq(7) > div').should('have.length', 3)
                cy.get('tr:eq(1) td:eq(8) > div').should('have.length', 3)
                cy.get('tr:eq(1) td:eq(9) > div').should('have.length', 3)
                cy.get('tr:eq(1) td:eq(10) > div').should('have.length', 3)
            })

            cy.get('[data-context=delete-comparison]:eq(1)').should('not.be.disabled')

            cy.get('[data-context=delete-comparison]:eq(2)').click()
            cy.get('[data-context=delete-comparison]').should('have.length', 2)

            cy.get('[data-context=delete-comparison]:eq(1)').click()
            cy.get('[data-context=delete-comparison]').should('have.length', 1)
            cy.get('[data-context=delete-comparison]').should('be.disabled')
        })

        it('should manage multiple periods', () => {
            cy.get('[data-context=edit-periods]').click()
            cy.wait('@fetch_customer_order_overview')

            cy.get('[data-context=slide-out-container]').should('be.visible')

            const container = '[data-context=slide-out-container]'
            cy.get(container)

            const period_row = '[data-context=statistic-period]'
            cy.get(`${container} ${period_row}`).should('have.length', 7)

            // add custom period
            cy.get(`${container} [data-context=statistic-period-add]`).click()
            cy.get(`${container} ${period_row}`).should('have.length', 8)

            // delete period
            cy.get(`${container} ${period_row}:eq(7) [data-context=statistic-period-remove]`).click()
            cy.get(`${container} ${period_row}`).should('have.length', 7)

            const check_period_inputs = (periods) => {
                periods.forEach((period, index_period) => {
                    period.forEach((value, index_value) => {
                        cy.get(
                            `${container} ${period_row}:eq(${index_period}) [data-context=erp-input]:eq(${index_value})`,
                        ).should('have.value', value)
                    })
                })
            }

            check_period_inputs([
                ['J', '14 juin 2023', '14 juin 2023'],
                ['J-1', '13 juin 2023', '13 juin 2023'],
                ['J-2', '12 juin 2023', '12 juin 2023'],
                ['7J', '07 juin 2023', '14 juin 2023'],
                ['1M', '14 mai 2023', '14 juin 2023'],
                ['3M', '14 mars 2023', '14 juin 2023'],
                ['1Y', '14 juin 2022', '14 juin 2023'],
            ])

            cy.get(`${container} select`).select('Dernière semaine')
            check_period_inputs([
                ['J', '14 juin 2023', '14 juin 2023'],
                ['J-1', '13 juin 2023', '13 juin 2023'],
                ['J-2', '12 juin 2023', '12 juin 2023'],
                ['J-3', '11 juin 2023', '11 juin 2023'],
                ['J-4', '10 juin 2023', '10 juin 2023'],
                ['J-5', '09 juin 2023', '09 juin 2023'],
                ['J-6', '08 juin 2023', '08 juin 2023'],
                ['J-7', '07 juin 2023', '07 juin 2023'],
            ])

            cy.get(`${container} select`).select('Mois glissants')
            check_period_inputs([
                ['M-1', '14 mai 2023', '14 juin 2023'],
                ['M-2', '14 avr. 2023', '14 mai 2023'],
                ['M-3', '14 mars 2023', '14 avr. 2023'],
                ['M-4', '14 févr. 2023', '14 mars 2023'],
                ['M-5', '14 janv. 2023', '14 févr. 2023'],
                ['M-6', '14 déc. 2022', '14 janv. 2023'],
                ['M-7', '14 nov. 2022', '14 déc. 2022'],
                ['M-8', '14 oct. 2022', '14 nov. 2022'],
                ['M-9', '14 sept. 2022', '14 oct. 2022'],
                ['M-10', '14 août 2022', '14 sept. 2022'],
                ['M-11', '14 juill. 2022', '14 août 2022'],
                ['M-12', '14 juin 2022', '14 juill. 2022'],
                ['Y', '14 juin 2022', '14 juin 2023'],
            ])

            cy.get(`${container} select`).select('Années')
            check_period_inputs([
                ['Y', '01 janv. 2023', '31 déc. 2023'],
                ['Y-1', '01 janv. 2022', '31 déc. 2022'],
                ['Y-2', '01 janv. 2021', '31 déc. 2021'],
            ])

            cy.get(`${container} select`).select('Mois calendaires')
            check_period_inputs([
                ['M', '01 juin 2023', '30 juin 2023'],
                ['M-1', '01 mai 2023', '31 mai 2023'],
                ['M-2', '01 avr. 2023', '30 avr. 2023'],
                ['M-3', '01 mars 2023', '31 mars 2023'],
                ['M-4', '01 févr. 2023', '28 févr. 2023'],
                ['M-5', '01 janv. 2023', '31 janv. 2023'],
                ['M-6', '01 déc. 2022', '31 déc. 2022'],
                ['M-7', '01 nov. 2022', '30 nov. 2022'],
                ['M-8', '01 oct. 2022', '31 oct. 2022'],
                ['M-9', '01 sept. 2022', '30 sept. 2022'],
                ['M-10', '01 août 2022', '31 août 2022'],
                ['M-11', '01 juill. 2022', '31 juill. 2022'],
                ['Y', '01 juill. 2022', '30 juin 2023'],
            ])

            cy.get(`${container} button[type=submit]`).click()

            cy.wait('@fetch_customer_order_overview').then((xhr) =>
                expect(xhr.request.body.periods).deep.equal({
                    M: ['2023-06-01 00:00:00', '2023-06-30 00:00:00'],
                    'M-1': ['2023-05-01 00:00:00', '2023-05-31 00:00:00'],
                    'M-2': ['2023-04-01 00:00:00', '2023-04-30 00:00:00'],
                    'M-3': ['2023-03-01 00:00:00', '2023-03-31 00:00:00'],
                    'M-4': ['2023-02-01 00:00:00', '2023-02-28 00:00:00'],
                    'M-5': ['2023-01-01 00:00:00', '2023-01-31 00:00:00'],
                    'M-6': ['2022-12-01 00:00:00', '2022-12-31 00:00:00'],
                    'M-7': ['2022-11-01 00:00:00', '2022-11-30 00:00:00'],
                    'M-8': ['2022-10-01 00:00:00', '2022-10-31 00:00:00'],
                    'M-9': ['2022-09-01 00:00:00', '2022-09-30 00:00:00'],
                    'M-10': ['2022-08-01 00:00:00', '2022-08-31 00:00:00'],
                    'M-11': ['2022-07-01 00:00:00', '2022-07-31 00:00:00'],
                    Y: ['2022-07-01 00:00:00', '2023-06-30 00:00:00'],
                }),
            )
        })
    })
})
