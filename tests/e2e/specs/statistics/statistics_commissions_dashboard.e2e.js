import { ALL_COMMISSIONS_READ } from '../../../../src/apps/erp/permissions'

const PAGE_COMMISSIONS = '/statistics/commissions-dashboard'

describe('All commissions dashboard', function () {
    describe('Without permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser()
        })

        it('display error page', () => {
            cy.checkUnauthorizedAccessFor(PAGE_COMMISSIONS)
        })
    })

    describe('With permission', function () {
        beforeEach(() => {
            cy.authenticate()
            cy.mockErpUser(ALL_COMMISSIONS_READ)

            cy.mockDate(new Date(2022, 6, 1, 15, 44, 0, 0))

            cy.intercept('GET', '**/api/erp/v1/seller-commission/all-commissions?*', {
                fixture: 'erp/seller-commission/commissions/all-commissions.json',
            }).as('fetch_commissions')
        })

        it('has expected content for commissions', () => {
            cy.visit(PAGE_COMMISSIONS)
            cy.toggleMenu()
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.get('at')).to.eq('2022-07')
            })

            cy.get('[data-context=page-header]').should('contain', "Tableau des chiffres d'affaire commissionables")
            cy.get('[data-context=month-picker]')
                .should('be.visible')
                .find('.multiselect__element')
                .should('have.length', 13)
                .as('options')
            cy.get('@options').eq(0).should('contain', 'juillet 2022')
            cy.get('@options').eq(12).should('contain', 'juillet 2021')

            cy.get('[data-context=commissions-table]').should('be.visible').as('table')

            //
            // Header line
            //

            // Dynamic datas
            cy.get('[data-context=commissions-table] thead th')
                .should('contain', 'CA HT')
                .should('contain', 'INDICE MARGE')
                .should('contain', 'TX DE MARQUE')

            //
            // Seller line
            //
            let location_index = 0

            // Seller data
            cy.get('@table').selectCell(0, location_index++).should('contain', 'Anna Desneiges')

            // CA
            cy.get('@table').selectCell(0, location_index++).should('contain', '987 987 €')
            cy.get('@table').selectCell(0, location_index++).should('contain', '4 321 €')
            cy.get('@table').selectCell(0, location_index++).should('contain', '38,93 %')

            //
            // Check ordering
            //
            cy.get('@table').selectCell(1, 0).should('contain', 'Kristoff Bjorgman')
            cy.get('@table').selectCell(2, 0).should('contain', 'Olaf Snowman')
        })

        it('reload data on month selection and update search parameter', () => {
            cy.visit(PAGE_COMMISSIONS)
            cy.toggleMenu()
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.get('at')).to.eq('2022-07')
            })
            cy.get('[data-context="month-picker"]').multiselect('', 'avril 2022', true)
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.has('at')).to.eq(true)
                expect(params.get('at')).to.eq('2022-04')
            })

            cy.location().should((loc) => {
                expect(loc.search).to.eq('?at=2022-04')
            })
        })

        it('load data on month selected in the url', () => {
            cy.visit(`${PAGE_COMMISSIONS}?at=2022-03`)
            cy.wait('@fetch_commissions').then((xhr) => {
                const url = new URL(xhr.request.url)
                const params = new URLSearchParams(url.search)
                expect(params.has('at')).to.eq(true)
                expect(params.get('at')).to.eq('2022-03')
            })
            cy.get('[data-context=month-picker] .multiselect__single').should('contain', 'mars 2022')
        })

        it('display an failure error', () => {
            cy.intercept('GET', '**/api/erp/v1/seller-commission/all-commissions?*', {
                statusCode: 500,
                body: {
                    code: 500,
                    message: 'Un message custom de l’API',
                    data: {},
                },
            }).as('fetch_commissions')

            cy.visit(PAGE_COMMISSIONS)

            cy.toast(`Une erreur est survenue`, 'danger')
        })

        it('display an alert when empty data', () => {
            cy.intercept('GET', '**/api/erp/v1/seller-commission/all-commissions?*', {
                statusCode: 200,
                body: {
                    status: 'success',
                    data: {
                        commissions: [],
                    },
                },
            }).as('fetch_commissions')

            cy.visit(PAGE_COMMISSIONS)

            cy.get('[data-context=alert-info]').should('be.visible').should('contain', 'Aucune donnée à afficher.')
        })
    })
})
