{"status": "success", "data": {"page": "1", "limit": "15", "order_by": "fullname", "order_direction": "asc", "filter": {"job": "Dév", "company": "PHC", "site": "Nantes", "is_active": "active", "phones": "2043", "fullname": "<PERSON>"}, "accounts": [{"account_id": "0f2200d5-aead-4663-ba81-7df322d6bacc", "username": "nicolas.marniesse", "email": "<EMAIL>", "is_active": true, "created_at": {"date": "2001-01-01 00:00:00.000000", "timezone_type": 1, "timezone": "+01:00"}, "last_connection": {"date": "2018-02-20 17:44:03.000000", "timezone_type": 1, "timezone": "+01:00"}, "site_id": "79ce3932-9b66-4fe4-87e2-9f6c92bfa841", "lastname": "Marniesse", "firstname": "<PERSON>", "fullname": "<PERSON>", "job": "Développeur", "phones": {"internal_phone": "2043"}, "has_avatar": true, "site": "Nantes", "company": "PHC"}, {"account_id": "e43711b1-0cec-4ecf-b171-f3c69c147af9", "username": "nicolas.pencreach", "email": "<EMAIL>", "is_active": true, "created_at": {"date": "2001-01-01 00:00:00.000000", "timezone_type": 1, "timezone": "+01:00"}, "last_connection": {"date": "2018-04-30 14:23:29.000000", "timezone_type": 1, "timezone": "+02:00"}, "site_id": "79ce3932-9b66-4fe4-87e2-9f6c92bfa841", "lastname": "Pencreach", "firstname": "<PERSON>", "fullname": "<PERSON>", "job": "Développeur", "phones": {"cellphone": "**********", "internal_phone": "2043"}, "has_avatar": true, "site": "Nantes", "company": "PHC"}], "total": 2, "previous_page": null, "next_page": null, "last_page": 1, "from": 1, "to": 2}}