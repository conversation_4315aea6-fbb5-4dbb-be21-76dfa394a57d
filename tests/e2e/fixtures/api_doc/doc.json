{"openapi": "3.0.0", "info": {"title": "ERP Server", "description": "API pour l'ERP de Son-Vidéo.com", "version": "1.0.0"}, "paths": {"/api/v1/carrier/eligible-shipment-methods": {"post": {"tags": ["Carrier"], "summary": "Retrieve a list of eligible shipment methods", "operationId": "post_post_carrier_eligible_shipment_methods", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EligibilityEnvelopeDto"}}}}, "responses": {"200": {"description": "The eligible shipment methods information", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EligibleShipmentMethodDto"}}}}}}}}}, "components": {"schemas": {"EligibilityEnvelopeDto": {"required": ["items", "shipping_address"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/EligibilityEnvelopeItemDto"}}, "shipping_address": {"$ref": "#/components/schemas/EligibilityEnvelopeShippingAddressDto"}}, "type": "object"}, "EligibleShipmentMethodDto": {"properties": {"shipment_method_id": {"type": "integer", "example": 15}, "label": {"type": "string", "example": "Chronopost livraison avant 13H"}, "comment": {"type": "string", "example": "Livraison J1 garantie partout en france.", "nullable": true}, "carrier_name": {"type": "string", "example": "Chronopost"}, "is_retail_store": {"type": "boolean", "example": false}, "cost": {"type": "number", "format": "float", "example": 12.9}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["express"]}}, "type": "object"}, "EligibilityEnvelopeItemDto": {"required": ["sku"], "properties": {"sku": {"type": "string", "example": "BRANDMODEL"}, "quantity": {"type": "integer", "example": 2}, "price_vat_excluded": {"type": "number", "format": "float", "example": 99.99}, "price": {"type": "number", "format": "float", "example": 99.99}, "total_price": {"title": "Should be price * quantity", "type": "number", "format": "float", "example": 199.98}}, "type": "object"}, "EligibilityEnvelopeShippingAddressDto": {"required": ["title", "firstname", "lastname", "city", "postal_code", "address", "country_code"], "properties": {"title": {"type": "string", "enum": ["<PERSON>.", "Mme"], "example": "<PERSON>."}, "firstname": {"type": "string", "example": "<PERSON>"}, "lastname": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "cellphone": {"type": "string", "example": "0134567890"}, "city": {"type": "string", "example": "Nantes"}, "postal_code": {"type": "string", "example": "44100"}, "address": {"type": "string", "example": "38 rue de la ville en bois"}, "country_code": {"title": "ISO 3166-1 alpha-2 codes are two-letter country codes defined in ISO 3166-1", "type": "string", "example": "FR"}}, "type": "object"}}, "securitySchemes": {"Bearer": {"type": "http", "bearerFormat": "JWT", "scheme": "Bearer <token>"}}}, "security": [{"Bearer": []}]}