{"status": "success", "data": {"urgent": [{"order_id": 1923278, "creation_date": "2020-09-15 17:00:07", "customer": "GALERIE ORIGINES - <PERSON><PERSON><PERSON><PERSON> RICHARD", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923297, "creation_date": "2020-09-15 18:06:38", "customer": "GRAVIL Jacques", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923299, "creation_date": "2020-09-15 18:13:21", "customer": "Leto<PERSON> Alex", "carrier": "DHL", "carrier_id": 20, "shipment_method": "DHL Express 24h", "shipment_method_id": 9, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923305, "creation_date": "2020-09-15 18:35:26", "customer": "<PERSON><PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923306, "creation_date": "2020-09-15 18:27:23", "customer": "del<PERSON> steven", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923317, "creation_date": "2020-09-15 19:21:15", "customer": "<PERSON><PERSON><PERSON>", "carrier": "DHL", "carrier_id": 20, "shipment_method": "DHL Express 24h", "shipment_method_id": 9, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923331, "creation_date": "2020-09-15 15:49:30", "customer": "CYBERFACT", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923338, "creation_date": "2020-09-15 20:23:57", "customer": "FASAN Kevin", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923342, "creation_date": "2020-09-15 20:54:13", "customer": "Aurelia LEROUX", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}], "order_types": ["Amazon", "Chronopost", "DHL"], "_request": {"where": {"order_type": {"_eq": "Amazon"}}, "sort_by": "creation_date", "sort_direction": "asc", "page": 2, "limit": 25}, "_pager": {"from": 26, "to": 34, "total": 34, "page": 2, "limit": 25, "last_page": 2}}}