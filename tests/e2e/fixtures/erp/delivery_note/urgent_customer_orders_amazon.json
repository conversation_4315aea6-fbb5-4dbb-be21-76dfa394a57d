{"status": "success", "data": {"urgent": [{"order_id": 1851934, "creation_date": "2020-05-01 12:44:18", "customer": "<PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 0, "order_type": "Amazon"}, {"order_id": 1917381, "creation_date": "2020-08-31 16:00:54", "customer": "Matisse Charrier", "carrier": "Le Havre", "carrier_id": 19, "shipment_method": "Calberson Messagerie", "shipment_method_id": 29, "is_deliverable": 0, "order_type": "Amazon"}, {"order_id": 1917463, "creation_date": "2020-08-29 03:45:17", "customer": "<PERSON>", "carrier": "DHL", "carrier_id": 20, "shipment_method": "DHL Express 24h", "shipment_method_id": 9, "is_deliverable": 0, "order_type": "Amazon"}, {"order_id": 1920876, "creation_date": "2020-09-05 18:18:58", "customer": "<PERSON><PERSON><PERSON>", "carrier": "DHL", "carrier_id": 20, "shipment_method": "DHL Express 24h", "shipment_method_id": 9, "is_deliverable": 0, "order_type": "Amazon"}, {"order_id": 1922870, "creation_date": "2020-09-14 18:21:07", "customer": "FAUVEL Marc", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 0, "order_type": "Amazon"}, {"order_id": 1922991, "creation_date": "2020-09-15 07:26:18", "customer": "olivier david", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1922998, "creation_date": "2020-09-15 07:49:28", "customer": "<PERSON><PERSON><PERSON>", "carrier": "DHL", "carrier_id": 20, "shipment_method": "DHL Express 24h", "shipment_method_id": 9, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923010, "creation_date": "2020-09-15 08:08:05", "customer": "LUTRINGER Jean", "carrier": "DHL", "carrier_id": 20, "shipment_method": "DHL Express 24h", "shipment_method_id": 9, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923044, "creation_date": "2020-09-15 08:48:06", "customer": "SMARS INFORMATIQUE", "carrier": "France Express", "carrier_id": 30, "shipment_method": "Express", "shipment_method_id": 34, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923061, "creation_date": "2020-09-15 09:26:39", "customer": "<PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923062, "creation_date": "2020-09-15 09:29:18", "customer": "<PERSON><PERSON><PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923064, "creation_date": "2020-09-15 00:00:00", "customer": "GERUSSI Bruno", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923089, "creation_date": "2020-09-15 10:54:10", "customer": "<PERSON><PERSON> et Laurent", "carrier": "France Express", "carrier_id": 30, "shipment_method": "Express", "shipment_method_id": 34, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923092, "creation_date": "2020-09-15 11:02:05", "customer": "<PERSON><PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923105, "creation_date": "2020-09-15 11:34:58", "customer": "HALBOUT Antoine", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923125, "creation_date": "2020-09-15 11:56:45", "customer": "<PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923144, "creation_date": "2020-09-15 09:55:10", "customer": "ODONE CATHY", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923177, "creation_date": "2020-09-15 14:03:46", "customer": "<PERSON><PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923198, "creation_date": "2020-09-15 14:40:39", "customer": "<PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923207, "creation_date": "2020-09-15 14:54:02", "customer": "MILLOT", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923233, "creation_date": "2020-09-15 00:00:00", "customer": "Colas des Francs Ghislain", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923234, "creation_date": "2020-09-15 00:00:00", "customer": "<PERSON><PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923237, "creation_date": "2020-09-15 15:46:12", "customer": "<PERSON><PERSON>", "carrier": "DHL", "carrier_id": 20, "shipment_method": "DHL Express 24h", "shipment_method_id": 9, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923259, "creation_date": "2020-09-15 16:18:59", "customer": "<PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}, {"order_id": 1923260, "creation_date": "2020-09-15 16:25:06", "customer": "<PERSON><PERSON>", "carrier": "GLS", "carrier_id": 50, "shipment_method": "FlexDeliveryService", "shipment_method_id": 65, "is_deliverable": 1, "order_type": "Amazon"}], "order_types": ["Amazon", "Chronopost", "DHL"], "_request": {"where": {"order_type": {"_eq": "Amazon"}}, "sort_by": "creation_date", "sort_direction": "asc", "page": 1, "limit": 25}, "_pager": {"from": 1, "to": 25, "total": 34, "page": 1, "limit": 25, "last_page": 2}}}