{"status": "success", "data": {"countries": [{"country_id": 67, "name": "FRANCE", "country_code": "FR", "group": "France", "display_order": 1, "postal_code_regex": "^[0-9]{5}$", "postal_code_info": "5 chiffres"}], "_request": {"fields": null, "where": {"country_id": {"_eq": 67}}, "order_by": "display_order ASC", "order_direction": null, "page": "1", "limit": 5}, "_pager": {"from": 1, "to": 1, "total": 1, "page": 1, "limit": 5, "last_page": 1}}}