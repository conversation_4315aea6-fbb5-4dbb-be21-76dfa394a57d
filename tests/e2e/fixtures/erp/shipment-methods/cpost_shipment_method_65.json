{"status": "success", "data": {"shipment_methods": [{"shipment_method_id": 65, "carrier_id": 50, "code": "FDS", "label": "FlexDeliveryService", "is_active": true, "comment": "Livraisons aux particuliers en France et sur certains pays d'Europe", "type": "messagerie", "is_mono_parcel": true, "carrier": {"carrier_id": 50, "code": "GLS", "name": "GLS", "is_pick_up": false}}], "_request": {"fields": null, "where": {"_and": [{"shipment_method_id": {"_eq": 65}}]}, "order_by": "shipment_method_id ASC", "order_direction": null, "page": "1", "limit": "50", "included_dependencies": ["carrier"]}, "_pager": {"from": 1, "to": 1, "total": 1, "page": 1, "limit": 50, "last_page": 1}}}