{"status": "success", "data": {"inventory": {"inventory_id": "4", "created_at": "2020-01-29 14:33:10", "warehouse_id": "18", "warehouse_name": "<PERSON><PERSON><PERSON>", "active_collect_id": null, "validated_at": null, "validated_by": null, "validated_by_name": "", "closed_at": null, "status": "created", "type": "full", "name": null, "collects": [{"collect_id": "13", "collect_type": "global", "collect_number": "1", "counted_articles": "0", "is_active": null}, {"collect_id": "14", "collect_type": "historic", "collect_number": "2", "counted_articles": "0", "is_active": null}, {"collect_id": "15", "collect_type": "produit", "collect_number": "3", "counted_articles": "0", "is_active": null}, {"collect_id": "16", "collect_type": "produit", "collect_number": "4", "counted_articles": "0", "is_active": null}]}, "statistics": [{"label": "EXPECTED", "total": "0"}, {"label": "TO_COUNT", "total": "0"}, {"label": "COUNTED", "total": "0"}, {"label": "CAN_BE_AUTOMATICALLY_VALIDATED", "total": "0"}, {"label": "MANUALLY_VALIDATED", "total": "0"}, {"label": "NEEDS_REVIEW", "total": "0"}], "validating_statuses": null}}