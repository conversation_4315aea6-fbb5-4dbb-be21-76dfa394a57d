{"status": "success", "data": {"inventories": [{"inventory_id": 4, "created_at": "2020-01-29 14:33:10", "validated_at": null, "validated_by": null, "validated_by_name": "", "closed_at": null, "warehouse_id": 18, "warehouse_name": "<PERSON><PERSON><PERSON>", "status": "created", "active_inventory_collect_id": null, "active_collect_type": null, "active_collect_number": null, "type": "full", "name": null}, {"inventory_id": 3, "created_at": "2020-01-29 14:22:11", "validated_at": null, "validated_by": null, "validated_by_name": "", "closed_at": null, "warehouse_id": 9, "warehouse_name": "Lille", "status": "on-going", "active_inventory_collect_id": 9, "active_collect_type": "global", "active_collect_number": 1, "type": "partial", "name": "Ca compte pour du beurre"}, {"inventory_id": 2, "created_at": "2020-01-28 12:49:57", "validated_at": "2020-01-28 13:46:51", "validated_by": 1224, "validated_by_name": "Stéphane BOULARD", "closed_at": "2020-01-28 13:50:53", "warehouse_id": 10, "warehouse_name": "Grenoble", "status": "closed", "active_inventory_collect_id": 5, "active_collect_type": "global", "active_collect_number": 1, "type": "full", "name": ""}, {"inventory_id": 1, "created_at": "2020-01-28 09:55:10", "validated_at": "2020-01-28 12:33:01", "validated_by": 1224, "validated_by_name": "Stéphane BOULARD", "closed_at": "2020-01-28 12:46:59", "warehouse_id": 14, "warehouse_name": "Avignon", "status": "on-going", "active_inventory_collect_id": 1, "active_collect_type": "global", "active_collect_number": 1, "type": "product", "name": null}], "_request": {"where": null, "order_by": "created_at DESC", "order_direction": null, "page": 1, "limit": 100}, "_pager": {"from": 1, "to": 4, "total": 4, "page": 1, "limit": 100, "last_page": 1}}}