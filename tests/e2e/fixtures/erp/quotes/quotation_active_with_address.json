{"status": "success", "data": {"quotes": [{"locked": false, "quote_id": 1, "type": "quotation", "created_at": "2022-02-10 17:18:38", "created_by": 1297, "created_by_name": "<PERSON>", "modified_at": "2022-03-01 15:37:25", "customer_id": 1085749, "customer_email": "<EMAIL>", "expired_at": null, "valid_until": 15, "message": "Message du devis", "billing_address": {"city": "<PERSON><PERSON>", "phone": "", "address": "38 rue de la ville en bois", "country": {"name": "GUYANE FRANÇAISE", "country_id": 84, "country_code": "GF"}, "civility": "Mme", "lastname": "LOLO", "cellphone": "0678787878", "firstname": "<PERSON>hur", "postal_code": "44000", "company_name": ""}, "shipping_address": {"city": "<PERSON><PERSON>", "phone": "", "address": "38 rue de la ville en bois", "country": {"name": "GUYANE FRANÇAISE", "country_id": 84, "country_code": "GF"}, "civility": "Mme", "lastname": "LOLO", "cellphone": "0678787878", "firstname": "<PERSON>hur", "postal_code": "44000", "company_name": ""}, "shipment_method": null, "status": "sent", "vat_rate": 0.2, "quote_line_aggregates": [], "customer_order_aggregates": [], "remaining_steps_to_convert_to_quotation": [{"error": "shipment_method_empty", "message": "The shipment method can't be empty"}, {"error": "quote_lines_empty", "message": "The quote has no line"}], "remaining_steps_to_convert_to_customer_order": [{"error": "shipment_method_empty", "message": "The shipment method can't be empty"}, {"error": "quote_lines_empty", "message": "The quote has no line"}], "remaining_steps_to_convert_to_offer": [{"error": "quote_lines_empty", "message": "The quote has no line"}]}, {"locked": false, "quote_id": 1, "type": "quotation", "created_at": "2022-02-10 17:18:38", "created_by": 1297, "created_by_name": "<PERSON>", "modified_at": "2022-03-01 15:37:25", "customer_id": 1085749, "customer_email": "<EMAIL>", "expired_at": null, "valid_until": 15, "message": "Message du devis", "billing_address": {"city": "<PERSON><PERSON>", "phone": "", "address": "38 rue de la ville en bois", "country": {"name": "GUYANE FRANÇAISE", "country_id": 84, "country_code": "GF"}, "civility": "Mme", "lastname": "LOLO", "cellphone": "0678787878", "firstname": "<PERSON>hur", "postal_code": "44000", "company_name": ""}, "shipping_address": {"city": "<PERSON><PERSON>", "phone": "", "address": "38 rue de la ville en bois", "country": {"name": "GUYANE FRANÇAISE", "country_id": 84, "country_code": "GF"}, "civility": "Mme", "lastname": "LOLO", "cellphone": "0678787878", "firstname": "<PERSON>hur", "postal_code": "44000", "company_name": ""}, "shipment_method": null, "status": "sent", "vat_rate": 0.2, "quote_line_aggregates": [], "customer_order_aggregates": [], "remaining_steps_to_convert_to_quotation": [{"error": "shipment_method_empty", "message": "The shipment method can't be empty"}, {"error": "quote_lines_empty", "message": "The quote has no line"}], "remaining_steps_to_convert_to_customer_order": [{"error": "shipment_method_empty", "message": "The shipment method can't be empty"}, {"error": "quote_lines_empty", "message": "The quote has no line"}], "remaining_steps_to_convert_to_offer": [{"error": "quote_lines_empty", "message": "The quote has no line"}]}], "_request": {"fields": null, "where": {"_and": [{"quote_id": {"_eq": 1}}]}, "order_by": "quote_id ASC", "order_direction": null, "page": "1", "limit": "50"}, "_pager": {"from": 1, "to": 1, "total": 1, "page": 1, "limit": 50, "last_page": 1}}}