{"status": "success", "data": {"system_events": [{"id_for_type": "95", "created_at": "2022-03-30T16:37:32+02:00", "type": "quote.update", "payload": {"_rel": {"quote": 1}, "data": {"shipping_address": {"new": {"city": "NANTES", "name": "Adresse 1", "phone": "", "address": "1 rue toto", "country": {"name": "FRANCE", "country_id": 67, "country_code": "FR"}, "civility": "Mme", "lastname": "<PERSON><PERSON><PERSON>", "cellphone": "06 06 06 06 06", "firstname": "<PERSON>", "created_at": "2021-03-01 19:27:37", "postal_code": "44000", "company_name": ""}, "old": {"city": "NANTES", "name": "Adresse 1", "phone": "", "address": "1 rue toto", "country": {"name": "FRANCE", "country_id": 67, "country_code": "FR"}, "civility": "<PERSON>.", "lastname": "<PERSON><PERSON><PERSON>", "cellphone": "06 06 06 06 06", "firstname": "<PERSON>", "created_at": "2021-03-01 19:27:37", "postal_code": "44000", "company_name": ""}}}, "meta": {"updated_by": {"user_id": 1, "lastname": "<PERSON><PERSON><PERSON>", "username": "elise.emoi", "firstname": "<PERSON><PERSON><PERSON>"}}}, "emitter": null}]}}