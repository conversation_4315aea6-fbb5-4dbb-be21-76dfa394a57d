{"status": "success", "data": {"quotes": [{"locked": false, "quote_id": 1, "type": "quotation", "created_at": "2022-02-04 12:26:49", "created_by": 1224, "created_by_name": "Billy THE KID", "modified_at": "2022-02-04 12:26:49", "customer_id": 970481, "customer_email": "<EMAIL>", "quote_subtype": "Classique", "expired_at": null, "valid_until": 15, "message": "Suite à votre demande voici notre offre pour les produits suivants:", "billing_address": null, "shipping_address": null, "shipment_method": null, "status": "sent", "vat_rate": 0.2, "quote_line_aggregates": [{"quote_line_id": 17, "display_order": 1, "type": "product", "data": {"delay": null, "stock": 10, "status": "oui", "product": {"sku": "SENHD800S", "vat": 0.2, "type": "article", "image": "/images/dynamic/Casques_et_ecouteurs/articles/Sennheiser/SENHD800S/Sennheiser-HD-800S_P_300_square.jpg", "description": "Casque haute-fid<PERSON>lit<PERSON>nheiser HD-800S", "ecotax_price": 0.07, "sorecop_price": 0, "purchase_price": 796.34, "short_description": "Sennheiser HD-800S", "selling_price_tax_included": 1299, "eligible_warranties": []}, "quantity": 1, "product_id": 103964, "unit_discount_amount": 0, "quote_line_product_id": 17, "selected_warranties": []}}, {"quote_line_id": 18, "display_order": 2, "type": "product", "data": {"delay": 0, "stock": 13, "status": "oui", "product": {"sku": "NAIMUNIATOMHDMI", "vat": 0.2, "type": "article", "image": "/images/dynamic/Amplificateurs/articles/Naim_Audio/NAIMUNIATOMHDMI/Naim-Audio-Uniti-Atom-HDMI_P_300_square.jpg", "description": "Ampli-r<PERSON><PERSON> Naim <PERSON>i Atom HDMI", "ecotax_price": 1, "sorecop_price": 0, "purchase_price": 1472.5, "short_description": "<PERSON><PERSON> Atom HDMI", "selling_price_tax_included": 3299, "eligible_warranties": []}, "quantity": 3, "product_id": 111153, "unit_discount_amount": -1234.56, "quote_line_product_id": 18, "selected_warranties": []}}, {"quote_line_id": 19, "display_order": 3, "type": "product", "data": {"delay": 0, "stock": 15, "status": "last", "product": {"sku": "REGAPLANAR1PLUSCARBNRMT", "vat": 0.2, "type": "article", "image": "/images/article/rega/REGAPLANAR1PLUSCARBNRMT/planar-1-plus-noir-mat_60633e8495b72_300_square.jpg", "description": "Platine vinyle préamplifiée Rega Planar 1 Plus Noir mat livrée avec cellule Carbon MM", "ecotax_price": 0.5, "sorecop_price": 0, "purchase_price": 208.96, "short_description": "Rega Planar 1 Plus Noir mat avec cellule Carbon MM", "selling_price_tax_included": 399, "eligible_warranties": []}, "quantity": 1, "product_id": 155597, "unit_discount_amount": 0, "quote_line_product_id": 19, "selected_warranties": []}}, {"quote_line_id": 20, "display_order": 4, "type": "product", "data": {"delay": null, "stock": 0, "status": "yapu", "product": {"sku": "CHORDMOJONR", "vat": 0.2, "type": "article", "image": "/images/dynamic/Streaming_et_reseau/articles/Chord_Electronics/CHORDMOJONR/Chord-Electronics-Mojo_P_300_square.jpg", "description": "DAC Audio et amplificateur casque portable Chord Mojo", "ecotax_price": 0.1, "sorecop_price": 0, "purchase_price": 288, "short_description": "Chord Mojo <PERSON>", "selling_price_tax_included": 499, "eligible_warranties": []}, "quantity": 1, "product_id": 101624, "unit_discount_amount": 0, "quote_line_product_id": 20, "selected_warranties": []}}], "customer_order_aggregates": [], "remaining_steps_to_convert_to_quotation": [{"error": "shipping_address_empty", "message": "The shipping address can't be empty"}, {"error": "billing_address_empty", "message": "The billing address can't be empty"}, {"error": "shipment_method_empty", "message": "The shipment method can't be empty"}], "remaining_steps_to_convert_to_customer_order": [{"error": "shipping_address_empty", "message": "The shipping address can't be empty"}, {"error": "billing_address_empty", "message": "The billing address can't be empty"}, {"error": "shipment_method_empty", "message": "The shipment method can't be empty"}], "remaining_steps_to_convert_to_offer": [], "total_tax_included": 8627.32}], "_request": {"fields": null, "where": {"_and": [{"quote_id": {"_eq": 1}}]}, "order_by": "quote_id ASC", "order_direction": null, "page": "1", "limit": "50"}, "_pager": {"from": 1, "to": 1, "total": 1, "page": 1, "limit": 50, "last_page": 1}}}