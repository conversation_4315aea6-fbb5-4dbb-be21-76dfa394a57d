{"status": "success", "data": {"quotes": [{"locked": false, "quote_id": 1, "type": "quotation", "created_at": "2022-02-04 12:26:49", "created_by": 1224, "created_by_name": "Billy THE KID", "modified_at": "2022-02-04 12:26:49", "customer_id": 970481, "customer_email": "<EMAIL>", "quote_subtype": "CLASSIQUE", "expired_at": null, "valid_until": 15, "message": "Suite à votre demande voici notre offre pour les produits suivants:", "billing_address": null, "shipping_address": null, "shipment_method": null, "status": "inactive", "vat_rate": 0.2, "quote_line_aggregates": [], "customer_order_aggregates": [], "remaining_steps_to_convert_to_quotation": [{"error": "shipping_address_empty", "message": "The shipping address can't be empty"}, {"error": "billing_address_empty", "message": "The billing address can't be empty"}, {"error": "shipment_method_empty", "message": "The shipment method can't be empty"}, {"error": "quote_lines_empty", "message": "The quote has no line"}], "remaining_steps_to_convert_to_customer_order": [{"error": "shipping_address_empty", "message": "The shipping address can't be empty"}, {"error": "billing_address_empty", "message": "The billing address can't be empty"}, {"error": "shipment_method_empty", "message": "The shipment method can't be empty"}, {"error": "quote_lines_empty", "message": "The quote has no line"}], "remaining_steps_to_convert_to_offer": [{"error": "quote_lines_empty", "message": "The quote has no line"}]}], "_request": {"fields": null, "where": {"_and": [{"quote_id": {"_eq": 1}}]}, "order_by": "quote_id ASC", "order_direction": null, "page": "1", "limit": "50"}, "_pager": {"from": 1, "to": 1, "total": 1, "page": 1, "limit": 50, "last_page": 1}}}