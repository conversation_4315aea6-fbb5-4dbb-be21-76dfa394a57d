{"status": "success", "data": {"quotes": [{"locked": false, "status": "inactive", "remaining_steps_to_convert_to_quotation": [{"error": "shipping_address_empty", "message": "The shipping address can't be empty"}, {"error": "billing_address_empty", "message": "The billing address can't be empty"}, {"error": "shipment_method_empty", "message": "The shipment method can't be empty"}, {"error": "quote_lines_empty", "message": "The quote has no line"}], "remaining_steps_to_convert_to_customer_order": [{"error": "shipping_address_empty", "message": "The shipping address can't be empty"}, {"error": "billing_address_empty", "message": "The billing address can't be empty"}, {"error": "shipment_method_empty", "message": "The shipment method can't be empty"}, {"error": "quote_lines_empty", "message": "The quote has no line"}], "remaining_steps_to_convert_to_offer": [{"error": "quote_lines_empty", "message": "The quote has no line"}], "quote_id": 1, "type": "offer", "created_at": "2022-09-26 15:16:18", "created_by": 1404, "created_by_name": "Arthur <PERSON>", "modified_at": "2022-09-26 15:16:18", "customer_id": 1547234, "customer_email": "<EMAIL>", "customer_name": "<PERSON>", "intra_community_vat": null, "sent_at": null, "expired_at": null, "valid_until": 15, "message": "Suite à votre demande voici notre offre pour les produits suivants:", "billing_address": null, "shipping_address": null, "shipment_method": null, "vat_rate": 0.2, "quote_line_aggregates": [], "customer_order_aggregates": []}], "_request": {"fields": null, "where": {"_and": [{"quote_id": {"_eq": "1002240"}}]}, "order_by": "quote_id ASC", "order_direction": null, "page": "1", "limit": "50"}, "_pager": {"from": 1, "to": 1, "total": 1, "page": 1, "limit": 50, "last_page": 1}}}