{"status": "success", "data": {"customers": {"results": [{"customer_id": "333586", "email_address": "<EMAIL>", "civility": "<PERSON>.", "firstname": "<PERSON><PERSON>", "lastname": "Du<PERSON>nd", "company_name": "", "address": "11 rue du verger", "zip_code": "76700", "country_id": "67", "phone": "0203040506", "mobile_phone": "0607080910", "customer_type": "particulier", "is_blacklisted": false, "accept_marketing_emails": true, "created_at": "2008-03-12 15:19:43", "computed_name": "DUPONT DUPOND", "country_name": "FRANCE", "country_code": "FR", "type": "entreprise"}, {"customer_id": "1661996", "email_address": "<PERSON>.<EMAIL>", "civility": "<PERSON>.", "firstname": "didier", "lastname": "<PERSON>", "company_name": "toto", "address": "", "zip_code": "", "country_id": "67", "phone": "", "mobile_phone": "", "customer_type": "particulier", "is_blacklisted": false, "accept_marketing_emails": true, "created_at": "2020-12-02 15:45:38", "computed_name": "DIDIER DUVAL", "country_name": "FRANCE", "country_code": "FR", "type": "particulier"}], "total": 2}, "_request": {"params": {"search_terms": "du", "context": "customers", "size": 50, "from": 0}}}}