{"status": "success", "data": {"system_events": [{"id_for_type": "2070060", "created_at": "2024-01-31 13:11:51", "type": "customer.anonymize", "payload": {"_rel": {"customer": 12345}, "meta": {"created_by": {"user_id": 1, "lastname": "office", "username": "backoffice", "firstname": "Back"}}}, "emitter": null, "main_id": 1547234}, {"id_for_type": "2070059", "created_at": "2024-01-31 12:59:37", "type": "customer.update", "payload": {"_rel": {"customer": 12345}, "data": {"type": {"new": "particulier", "old": "entreprise"}, "blacklist": {"new": true, "old": false}, "encours_sfac": {"new": 15000, "old": 12000}}, "meta": {"updated_by": {"user_id": 1, "lastname": "office", "username": "backoffice", "firstname": "Back"}}}, "emitter": null, "main_id": 1547234}, {"id_for_type": "2070058", "created_at": "2024-01-31 12:59:32", "type": "customer.update", "payload": {"_rel": {"customer": 12345}, "data": {"type": {"new": "entreprise", "old": "particulier"}}, "meta": {"updated_by": {"user_id": 1, "lastname": "office", "username": "backoffice", "firstname": "Back"}}}, "emitter": null, "main_id": 1547234}], "_request": {"fields": null, "where": {"_and": [{"main_id": {"_eq": 1547234}}, {"type": {"_like": "customer.%"}}]}, "order_by": "created_at DESC", "order_direction": null, "page": "1", "limit": "50"}, "_pager": {"from": 1, "to": 3, "total": 3, "page": 1, "limit": 50, "last_page": 1}}}