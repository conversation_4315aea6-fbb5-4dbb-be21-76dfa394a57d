{"status": "success", "data": {"stock_moves": [{"stock_move_id": 7551202, "warehouse_name": "Grenoble", "quantity": 1, "location_label": "Emplacement par défaut (Grenoble)", "location_code": "10.store", "created_at": "2022-02-16 16:23:37", "user": "admin", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "test filtre depot Grenoble", "buy_price": 50, "selling_price": 150.42, "delivery_note_id": 4913057, "supplier_order_id": 104743, "transfer_id": 171319, "return_note_id": 76696, "move_mission_id": null, "credit_note_id": 27030, "gift_card_id": 76661, "customer_order_id": 4301839, "delivery_note_customer_id": 4920091}, {"stock_move_id": 7551196, "warehouse_name": "Champigny 2", "quantity": -1, "location_label": "Zone de sortie", "location_code": "21.exit", "created_at": "2022-01-24 20:42:35", "user": "admin", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9803607, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-09 19:14:22", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4913057, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9803076, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-09 17:10:28", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4913057", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9803075, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-09 17:10:28", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4913057", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9803074, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-09 17:10:19", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4913057", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9803073, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-09 17:10:19", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4913057", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9794445, "warehouse_name": "Havre", "quantity": 2, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-08 12:39:00", "user": "sophie.scaglia", "user_full_name": "Seigneur <PERSON>", "type": "entree", "comment": "Entrée stock lors de l'entrée stock.", "buy_price": 86.16, "selling_price": null, "delivery_note_id": null, "supplier_order_id": 104743, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9783424, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-06 19:56:25", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4910596, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9782921, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-06 19:10:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4910596", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9782920, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-06 19:10:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4910596", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9782919, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-06 19:10:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4910596", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9782918, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-03-06 19:10:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4910596", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9751173, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-28 19:29:32", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4908166, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9750428, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-28 17:10:05", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4908166", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9750427, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-28 17:10:05", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4908166", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9750426, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-28 17:10:05", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4908166", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9750425, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-28 17:10:05", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4908166", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9627847, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-06 20:56:54", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4898014, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9626305, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-06 19:10:08", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4898014", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9626304, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-06 19:10:08", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4898014", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9626303, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-06 19:10:08", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4898014", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9626302, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-02-06 19:10:08", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4898014", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9539513, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-25 19:42:46", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4890122, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9538441, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-25 17:10:37", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4890122", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9538440, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-25 17:10:37", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4890122", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9538439, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-25 17:10:37", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4890122", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9538438, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-25 17:10:37", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4890122", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9430100, "warehouse_name": "Havre", "quantity": 4, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-11 10:57:13", "user": "sophie.scaglia", "user_full_name": "Seigneur <PERSON>", "type": "entree", "comment": "Entrée stock lors de l'entrée stock.", "buy_price": 86.16, "selling_price": null, "delivery_note_id": null, "supplier_order_id": 103986, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9418729, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 20:00:35", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4878334, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9418467, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 19:42:47", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4878012, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9418004, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 19:10:28", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4878334", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9418003, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 19:10:28", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4878334", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9418002, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 19:10:28", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4878334", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9418001, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 19:10:28", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4878334", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9417988, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 19:10:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4878012", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9417987, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 19:10:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4878012", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9417986, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 19:10:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4878012", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9417985, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-09 19:10:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4878012", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9396726, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-05 19:42:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4874336, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9396364, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-05 19:10:44", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4874336", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9396363, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-05 19:10:44", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4874336", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9396362, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-05 19:10:44", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4874336", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9396361, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2023-01-05 19:10:44", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4874336", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9283193, "warehouse_name": "Havre", "quantity": 3, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-21 14:47:20", "user": "sophie.scaglia", "user_full_name": "Seigneur <PERSON>", "type": "entree", "comment": "Entrée stock lors de l'entrée stock.", "buy_price": 86.16, "selling_price": null, "delivery_note_id": null, "supplier_order_id": 103432, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9245679, "warehouse_name": "Paris 8e", "quantity": -1, "location_label": "Emplacement par défaut (Paris 8e)", "location_code": "02.store", "created_at": "2022-12-16 13:24:24", "user": "mathieu.rivoli", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4863595, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9245678, "warehouse_name": "Paris 8e", "quantity": 1, "location_label": "Emplacement par défaut (Paris 8e)", "location_code": "02.store", "created_at": "2022-12-16 13:24:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4863595", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9245677, "warehouse_name": "Paris 8e", "quantity": -1, "location_label": "Emplacement par défaut (Paris 8e)", "location_code": "02.store", "created_at": "2022-12-16 13:24:17", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4863595", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9235019, "warehouse_name": "Paris 8e", "quantity": 1, "location_label": "Emplacement par défaut (Paris 8e)", "location_code": "02.store", "created_at": "2022-12-15 11:13:30", "user": "mathieu.rivoli", "user_full_name": "Seigneur <PERSON>", "type": "entree", "comment": "Entrée stock lors de l'entrée stock.", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": 171319, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9221453, "warehouse_name": "Havre", "quantity": -2, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 19:01:34", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4860968, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9220912, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 17:11:13", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 86.16, "delivery_note_id": 4860837, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9220911, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 17:11:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4860837", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9220910, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 17:11:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4860837", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9220909, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 17:11:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4860837", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9220908, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 17:11:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4860837", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9220779, "warehouse_name": "Havre", "quantity": 2, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 17:10:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4860968", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9220778, "warehouse_name": "Havre", "quantity": -2, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 17:10:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4860968", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9220777, "warehouse_name": "Havre", "quantity": 2, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 17:10:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4860968", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9220776, "warehouse_name": "Havre", "quantity": -2, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-13 17:10:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4860968", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9122717, "warehouse_name": "Havre", "quantity": 2, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-12-01 13:17:42", "user": "sophie.scaglia", "user_full_name": "Seigneur <PERSON>", "type": "entree", "comment": "Entrée stock lors de l'entrée stock.", "buy_price": 86.16, "selling_price": null, "delivery_note_id": null, "supplier_order_id": 103163, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9084795, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-28 19:58:09", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4848805, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9083116, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-28 17:12:57", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4848805", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9083115, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-28 17:12:57", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4848805", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9083114, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-28 17:12:57", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4848805", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9083113, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-28 17:12:57", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4848805", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9027996, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-22 19:43:43", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4843576, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9025668, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-22 17:10:46", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4843576", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9025667, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-22 17:10:46", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4843576", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9025666, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-22 17:10:46", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4843576", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 9025665, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-11-22 17:10:46", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4843576", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8896880, "warehouse_name": "Bordeaux", "quantity": -1, "location_label": "Emplacement par dé<PERSON>ut (Bordeaux)", "location_code": "12.store", "created_at": "2022-11-02 13:50:07", "user": "david.laurent", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4832957, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8896873, "warehouse_name": "Bordeaux", "quantity": 1, "location_label": "Emplacement par dé<PERSON>ut (Bordeaux)", "location_code": "12.store", "created_at": "2022-11-02 13:49:55", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4832957", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8896872, "warehouse_name": "Bordeaux", "quantity": -1, "location_label": "Emplacement par dé<PERSON>ut (Bordeaux)", "location_code": "12.store", "created_at": "2022-11-02 13:49:55", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4832957", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8872006, "warehouse_name": "Bordeaux", "quantity": 1, "location_label": "Emplacement par dé<PERSON>ut (Bordeaux)", "location_code": "12.store", "created_at": "2022-10-27 11:33:22", "user": "david.laurent", "user_full_name": "Seigneur <PERSON>", "type": "entree", "comment": "Entrée stock lors de l'entrée stock.", "buy_price": 86.16, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": 166619, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8861976, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-25 15:10:05", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 86.16, "delivery_note_id": 4829507, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8861975, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-25 15:10:05", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4829507", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8861974, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-25 15:10:05", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4829507", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8861973, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-25 15:10:05", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4829507", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8861972, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-25 15:10:05", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4829507", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8849383, "warehouse_name": "Havre", "quantity": 4, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-24 09:30:33", "user": "sophie.scaglia", "user_full_name": "Seigneur <PERSON>", "type": "entree", "comment": "Entrée stock lors de l'entrée stock.", "buy_price": 86.16, "selling_price": null, "delivery_note_id": null, "supplier_order_id": 102436, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8807309, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-13 19:15:49", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 229, "delivery_note_id": 4824679, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8806512, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-13 17:10:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4824679", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8806511, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-13 17:10:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4824679", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8806510, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-13 17:10:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4824679", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8806509, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-13 17:10:12", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le bl: 4824679", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8793819, "warehouse_name": "Champigny 2", "quantity": -1, "location_label": "Zone de sortie", "location_code": "21.exit", "created_at": "2022-10-11 19:14:32", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 230.9, "delivery_note_id": 4823650, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788466, "warehouse_name": "Champigny 2", "quantity": 1, "location_label": "Zone de sortie", "location_code": "21.exit", "created_at": "2022-10-11 09:40:35", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4823650", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788465, "warehouse_name": "Champigny 2", "quantity": -1, "location_label": "Prépa RDC1", "location_code": "21.prepa.rdc1", "created_at": "2022-10-11 09:40:35", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4823650", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788300, "warehouse_name": "Champigny 2", "quantity": 1, "location_label": "Prépa RDC1", "location_code": "21.prepa.rdc1", "created_at": "2022-10-11 09:25:05", "user": "valeriu.gaitur", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4823650", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788299, "warehouse_name": "Champigny 2", "quantity": -1, "location_label": "Chariot <PERSON> Gaitur VALERIU", "location_code": "21.user.1457", "created_at": "2022-10-11 09:25:05", "user": "valeriu.gaitur", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4823650", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788298, "warehouse_name": "Champigny 2", "quantity": 1, "location_label": "Chariot <PERSON> Gaitur VALERIU", "location_code": "21.user.1457", "created_at": "2022-10-11 09:24:49", "user": "valeriu.gaitur", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4823650", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788297, "warehouse_name": "Champigny 2", "quantity": -1, "location_label": "TV9", "location_code": "21.tv9", "created_at": "2022-10-11 09:24:49", "user": "valeriu.gaitur", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4823650", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788066, "warehouse_name": "Champigny 2", "quantity": 1, "location_label": "TV9", "location_code": "21.tv9", "created_at": "2022-10-11 09:02:59", "user": "jerome.borel", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788065, "warehouse_name": "Champigny 2", "quantity": -1, "location_label": "<PERSON><PERSON> <PERSON> BOR<PERSON>", "location_code": "21.user.1482", "created_at": "2022-10-11 09:02:59", "user": "jerome.borel", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788062, "warehouse_name": "Champigny 2", "quantity": 1, "location_label": "<PERSON><PERSON> <PERSON> BOR<PERSON>", "location_code": "21.user.1482", "created_at": "2022-10-11 09:02:27", "user": "jerome.borel", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788061, "warehouse_name": "Champigny 2", "quantity": -1, "location_label": "Zone d'entrée", "location_code": "21.04.s", "created_at": "2022-10-11 09:02:27", "user": "jerome.borel", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8788060, "warehouse_name": "Champigny 2", "quantity": 1, "location_label": "Zone d'entrée", "location_code": "21.04.s", "created_at": "2022-10-11 09:02:13", "user": "jerome.borel", "user_full_name": "Seigneur <PERSON>", "type": "entree", "comment": "Entrée stock lors de l'entrée stock.", "buy_price": 86.16, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": 165498, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8786446, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-10 17:10:38", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "sortie", "comment": "Sortie stock lors de la validation du Bon de Livraison.", "buy_price": null, "selling_price": 86.16, "delivery_note_id": 4823172, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8786445, "warehouse_name": "Havre", "quantity": 1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-10 17:10:38", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4823172", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}, {"stock_move_id": 8786444, "warehouse_name": "Havre", "quantity": -1, "location_label": "Emplacement par défaut (Havre)", "location_code": "01.store", "created_at": "2022-10-10 17:10:38", "user": "backoffice", "user_full_name": "Seigneur <PERSON>", "type": "interne", "comment": "Déplacement interne pour le BL: 4823172", "buy_price": null, "selling_price": null, "delivery_note_id": null, "supplier_order_id": null, "transfer_id": null, "return_note_id": null, "move_mission_id": null, "credit_note_id": null, "gift_card_id": null, "customer_order_id": null, "delivery_note_customer_id": null}], "_request": {"where": {"_and": [{"product_id": {"_eq": 84048}}]}, "order_by": "stock_move_id desc", "order_direction": null, "page": 1, "limit": 100}, "_pager": {"from": 1, "to": 100, "total": 200, "page": 1, "limit": 100, "last_page": 2}}}