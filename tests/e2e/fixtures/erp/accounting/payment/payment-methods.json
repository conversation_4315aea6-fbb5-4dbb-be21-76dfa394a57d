{"status": "success", "data": {"payment_methods": [{"payment_id": 8, "payment_methods": "BKDO", "name": "<PERSON>"}, {"payment_id": 9, "payment_methods": "VIR", "name": "Virement"}, {"payment_id": 11, "payment_methods": "CTPE", "name": "CTPE"}, {"payment_id": 12, "payment_methods": "ESP", "name": "Espèces"}, {"payment_id": 15, "payment_methods": "TCMD", "name": "Avoir (Transfert commande)"}, {"payment_id": 17, "payment_methods": "PANT", "name": "Avoir (Pmt antérieur)"}, {"payment_id": 18, "payment_methods": "TIRG", "name": "Tir Groupé"}, {"payment_id": 20, "payment_methods": "CHKF", "name": "Chèque à récept facture"}, {"payment_id": 26, "payment_methods": "AMZN", "name": "Amazon"}, {"payment_id": 35, "payment_methods": "MDTA", "name": "<PERSON><PERSON><PERSON>"}, {"payment_id": 48, "payment_methods": "DCT", "name": "Dé<PERSON>"}, {"payment_id": 49, "payment_methods": "SVDCC", "name": "Carte <PERSON>au SVD"}, {"payment_id": 64, "payment_methods": "CBS-OT", "name": "CBOL-Ogone-Tél"}, {"payment_id": 65, "payment_methods": "AMX-OT", "name": "Amex-Ogone-Tél"}, {"payment_id": 66, "payment_methods": "AUR-OT", "name": "Aurore-Ogone-Tél"}, {"payment_id": 70, "payment_methods": "EMPT", "name": "Emport dépôt"}, {"payment_id": 71, "payment_methods": "PRESTO", "name": "Cetel<PERSON>"}, {"payment_id": 72, "payment_methods": "CBS-EMPT", "name": "CBOL-Ogone-emport"}, {"payment_id": 73, "payment_methods": "FNAC", "name": "Fnac"}, {"payment_id": 74, "payment_methods": "ECLNG", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"payment_id": 76, "payment_methods": "RETANT", "name": "Retour SAV anticipé"}, {"payment_id": 79, "payment_methods": "", "name": ""}, {"payment_id": 80, "payment_methods": "CBS-COT", "name": "CBOL-Ogone-COT"}, {"payment_id": 81, "payment_methods": "COT", "name": "<PERSON><PERSON><PERSON> de co<PERSON>"}, {"payment_id": 83, "payment_methods": "CTPE-AMX", "name": "CTPE Amex"}, {"payment_id": 88, "payment_methods": "CDSCNT", "name": "Cdiscount"}, {"payment_id": 89, "payment_methods": "DARTY", "name": "<PERSON><PERSON>"}, {"payment_id": 90, "payment_methods": "FULLCB3X", "name": "FullCB 3x"}, {"payment_id": 91, "payment_methods": "FULLCB4X", "name": "FullCB 4x"}, {"payment_id": 92, "payment_methods": "CILO", "name": "<PERSON><PERSON>"}, {"payment_id": 93, "payment_methods": "RTLCTLM", "name": "Retail Cetelem"}, {"payment_id": 94, "payment_methods": "ER AMZN", "name": "Étiquette retour Amazon"}, {"payment_id": 100, "payment_methods": "BIMPLI", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"payment_id": 101, "payment_methods": "UPCADHOC", "name": "Up Cadhoc"}, {"payment_id": 102, "payment_methods": "KADEOS", "name": "<PERSON><PERSON><PERSON>"}, {"payment_id": 103, "payment_methods": "BEST", "name": "Best Cadeaux"}], "_request": {"where": {"actif": {"_eq": "Y"}}, "order_by": "payment_id ASC", "order_direction": null, "page": 1, "limit": 100}, "_pager": {"from": 1, "to": 36, "total": 36, "page": 1, "limit": 100, "last_page": 1}}}