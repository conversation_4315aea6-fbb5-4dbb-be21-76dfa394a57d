{"status": "success", "data": {"payments": [{"customer_order_payment_id": 3576730, "customer_order_id": 2011837, "customer_order_origin": "A548714-A66-X", "customer_order_created_at": "2021-01-20 13:08:34", "customer_order_total_amount": 650, "customer_id": 1671479, "customer_email": "<EMAIL>", "unique_id": 6, "payment_id": 11, "type": "paiement", "warehouse_id": null, "created_at": "2021-01-21 12:38:25", "created_by": "anselme.mugisha", "created_amount": 100, "created_proof": null, "created_origin": "son-video.com", "accepted_at": "2021-01-21 12:38:41", "accepted_by": "anselme.mugisha", "accepted_amount": 100, "accepted_proof": 362869, "cancelled_at": null, "cancelled_by": null, "cancelled_amount": 0, "remit_asked_at": null, "remit_asked_by": null, "remitted_at": null, "remitted_by": null, "remitted_amount": 0, "remitted_proof": null, "remit_note": null, "remit_rate": 1, "unpaid_overdue_at": null, "unpaid_overdue_by": null, "unpaid_overdue_amount": 0, "auto_status": null, "auto_status_detail": null, "auto_warranty": null, "auto_warranty_detail": null, "country_ip": null, "country_country_code": null, "payment_mean": "CTPE", "payment_created_remotely": "N", "use_remit_proof": "Y", "remit_proof_source": "manuel", "remit_proof_type": "No remise", "remit_proof_validation_regex": "^(?:TPE-)?\\d{1,64}$", "use_remit_note": "N", "remit_note_validation_regex": "", "computed_customer_firstname": "<PERSON>", "computed_customer_lastname": "FORTUNE", "computed_created_by": "Anselme MUGISHA", "computed_accepted_by": "Anselme MUGISHA", "computed_remitted_by": "", "computed_transaction_status": "ACCEPTED", "source": "Amazon.fr SV", "source_group": "Marketplaces", "can_be_accepted": false, "can_be_cancelled": true, "can_be_remitted": true, "can_cancel_remit": false}, {"customer_order_payment_id": 3577208, "customer_order_id": 2011628, "customer_order_origin": "A548714-A66-X", "customer_order_created_at": "2021-01-20 10:39:06", "customer_order_total_amount": 205, "customer_id": 1254227, "customer_email": "<EMAIL>", "unique_id": 6, "payment_id": 12, "type": "paiement", "warehouse_id": null, "created_at": "2021-01-21 16:34:34", "created_by": "benjamin.pey", "created_amount": 155, "created_proof": null, "created_origin": "son-video.com", "accepted_at": "2021-01-21 16:35:19", "accepted_by": "benjamin.pey", "accepted_amount": 155, "accepted_proof": null, "cancelled_at": null, "cancelled_by": null, "cancelled_amount": 0, "remit_asked_at": null, "remit_asked_by": null, "remitted_at": "2021-01-28 12:08:17", "remitted_by": "stephane.boulard", "remitted_amount": 155, "remitted_proof": null, "remit_note": null, "remit_rate": 1, "unpaid_overdue_at": null, "unpaid_overdue_by": null, "unpaid_overdue_amount": 0, "auto_status": null, "auto_status_detail": null, "auto_warranty": null, "auto_warranty_detail": null, "country_ip": null, "country_country_code": null, "payment_mean": "ESP", "payment_created_remotely": "N", "use_remit_proof": "N", "remit_proof_source": "manuel", "remit_proof_type": "No remise", "remit_proof_validation_regex": "", "use_remit_note": "N", "remit_note_validation_regex": "^\\d{1,64}$", "computed_customer_firstname": "<PERSON>", "computed_customer_lastname": "HOCHON", "computed_created_by": "<PERSON>", "computed_accepted_by": "<PERSON>", "computed_remitted_by": "Stéphane BOULARD", "computed_transaction_status": "REMITTED", "source": "Amazon.fr SV", "source_group": "Marketplaces", "can_be_accepted": false, "can_be_cancelled": false, "can_be_remitted": false, "can_cancel_remit": true}, {"customer_order_payment_id": 3575376, "customer_order_id": 2012221, "customer_order_origin": "A548714-A66-X", "customer_order_created_at": "2021-01-20 17:34:27", "customer_order_total_amount": 290, "customer_id": 1111715, "customer_email": "<EMAIL>", "unique_id": 3, "payment_id": 8, "type": "paiement", "warehouse_id": null, "created_at": "2021-01-20 17:37:33", "created_by": "cassandra.bouzon", "created_amount": 250, "created_proof": null, "created_origin": "son-video.com", "accepted_at": "2021-01-20 17:38:29", "accepted_by": "cassandra.bouzon", "accepted_amount": 250, "accepted_proof": 253361441010, "cancelled_at": null, "cancelled_by": null, "cancelled_amount": 0, "remit_asked_at": null, "remit_asked_by": null, "remitted_at": null, "remitted_by": null, "remitted_amount": 0, "remitted_proof": null, "remit_note": null, "remit_rate": 1, "unpaid_overdue_at": null, "unpaid_overdue_by": null, "unpaid_overdue_amount": 0, "auto_status": null, "auto_status_detail": null, "auto_warranty": null, "auto_warranty_detail": null, "country_ip": null, "country_country_code": null, "payment_mean": "BKDO", "payment_created_remotely": "N", "use_remit_proof": "N", "remit_proof_source": "manuel", "remit_proof_type": "No cheque", "remit_proof_validation_regex": "", "use_remit_note": "N", "remit_note_validation_regex": "", "computed_customer_firstname": "<PERSON><PERSON><PERSON>", "computed_customer_lastname": "WILLER", "computed_created_by": "Cassandra BOUZON", "computed_accepted_by": "Cassandra BOUZON", "computed_remitted_by": "", "computed_transaction_status": "ACCEPTED", "source": "Amazon.fr SV", "source_group": "Marketplaces", "can_be_accepted": false, "can_be_cancelled": true, "can_be_remitted": true, "can_cancel_remit": false}, {"customer_order_payment_id": 3574971, "customer_order_id": 2011956, "customer_order_origin": "A548714-A66-X", "customer_order_created_at": "2021-01-20 14:40:51", "customer_order_total_amount": 2398.7, "customer_id": 289781, "customer_email": "<EMAIL>", "unique_id": 4, "payment_id": 20, "type": "paiement", "warehouse_id": null, "created_at": "2021-01-20 14:51:46", "created_by": "nicolas.dechir", "created_amount": 2398.7, "created_proof": null, "created_origin": "son-video.com", "accepted_at": "2021-01-20 14:51:53", "accepted_by": "nicolas.dechir", "accepted_amount": 2398.7, "accepted_proof": 111111111, "cancelled_at": null, "cancelled_by": null, "cancelled_amount": 0, "remit_asked_at": null, "remit_asked_by": null, "remitted_at": null, "remitted_by": null, "remitted_amount": 0, "remitted_proof": null, "remit_note": 11111, "remit_rate": 1, "unpaid_overdue_at": null, "unpaid_overdue_by": null, "unpaid_overdue_amount": 0, "auto_status": null, "auto_status_detail": null, "auto_warranty": null, "auto_warranty_detail": null, "country_ip": null, "country_country_code": null, "payment_mean": "CHKF", "payment_created_remotely": "N", "use_remit_proof": "Y", "remit_proof_source": "manuel", "remit_proof_type": "No cheque", "remit_proof_validation_regex": "^\\d{7}$", "use_remit_note": "Y", "remit_note_validation_regex": "^\\d{1,64}$", "computed_customer_firstname": "<PERSON><PERSON><PERSON>", "computed_customer_lastname": "CLERE", "computed_created_by": "Nicolas <PERSON>", "computed_accepted_by": "Nicolas <PERSON>", "computed_remitted_by": "", "computed_transaction_status": "ACCEPTED", "source": "Amazon.fr SV", "source_group": "Marketplaces", "can_be_accepted": false, "can_be_cancelled": true, "can_be_remitted": true, "can_cancel_remit": false}], "_request": {"warehouse_id": 8, "created_at": "2021-01-20"}, "_pager": {"from": 1, "to": 18, "total": 18, "page": 1, "limit": 999999, "last_page": 1}}}