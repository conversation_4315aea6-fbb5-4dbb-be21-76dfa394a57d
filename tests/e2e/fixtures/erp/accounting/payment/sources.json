{"status": "success", "data": {"sources": [{"tag_id": "source.sonvideo.callcenter", "source": "CallCenter SV", "source_group": "Son-Video.com"}, {"tag_id": "source.sonvideo.web", "source": "Web SV", "source_group": "Son-Video.com"}, {"tag_id": "source.sonvideo_pro.callcenter", "source": "CallCenter Pro SV", "source_group": "Pro.Son-Video.com"}, {"tag_id": "source.sonvideo_pro.business_department", "source": "Département Pro", "source_group": "Pro.Son-Video.com"}, {"tag_id": "source.sonvideo_pro.web", "source": "Web PRO SV", "source_group": "Pro.Son-Video.com"}, {"tag_id": "source.marketplace.amazon_easylounge", "source": "Amazon EL", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.amazon_sonvideo", "source": "Amazon SV", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.boulanger_sonvideo", "source": "Boulanger SV", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.cdiscount_easylounge", "source": "Cdiscount EL", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.cdiscount_sonvideo", "source": "Cdiscount SV", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.darty_sonvideo", "source": "Darty <PERSON>", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.fnac_easylounge", "source": "Fnac EL", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.fnac_sonvideo", "source": "Fnac SV", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.easylounge", "source": "Marketplace EL", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.sonvideo", "source": "Marketplace SV", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.source.marketplace.rueducommerce_sonvideo", "source": "Rue du commerce SV", "source_group": "Marketplaces"}, {"tag_id": "source.marketplace.ubaldi_easylounge", "source": "Ubaldi EL", "source_group": "Marketplaces"}, {"tag_id": "source.shop.sonvideo_pro", "source": "Magasin Pro SV", "source_group": "Ma<PERSON>ins"}, {"tag_id": "source.shop.sonvideo", "source": "Magasin SV", "source_group": "Ma<PERSON>ins"}, {"tag_id": "source.intragroup.intragroup", "source": "Intragroupe", "source_group": "Intragroupe"}, {"tag_id": "source.easylounge.callcenter", "source": "CallCenter EL", "source_group": "EasyLounge.com"}, {"tag_id": "source.easylounge.web", "source": "Web EL", "source_group": "EasyLounge.com"}, {"tag_id": "source.other.other", "source": "Autres", "source_group": "Autres"}]}}