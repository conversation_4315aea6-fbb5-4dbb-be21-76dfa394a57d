{"status": "success", "data": {"payments": [{"customer_order_payment_id": 3576919, "customer_order_id": 2012955, "customer_order_origin": "A548714-A66-X", "customer_order_created_at": "2021-01-21 14:14:47", "customer_order_total_amount": 20000, "customer_id": 1589915, "customer_email": "<EMAIL>", "unique_id": 3, "payment_id": 9, "type": "paiement", "warehouse_id": null, "warehouse_name": "Nantes", "created_at": "2021-01-21 14:18:14", "created_by": "sebastien.denis", "created_amount": 20000, "created_proof": null, "created_origin": "son-video.com", "accepted_at": null, "accepted_by": null, "accepted_amount": 0, "accepted_proof": null, "cancelled_at": null, "cancelled_by": null, "cancelled_amount": 0, "remit_asked_at": null, "remit_asked_by": null, "remitted_at": null, "remitted_by": null, "remitted_amount": 0, "remitted_proof": null, "remit_note": null, "remit_error": null, "remit_rate": 1, "unpaid_overdue_at": null, "unpaid_overdue_by": null, "unpaid_overdue_amount": 0, "auto_status": null, "auto_status_detail": null, "auto_warranty": null, "auto_warranty_detail": null, "country_ip": null, "country_country_code": null, "payment_mean": "VIR", "payment_created_remotely": "N", "use_remit_proof": "Y", "remit_proof_source": "auto", "remit_proof_type": "Date de virement", "remit_proof_validation_regex": "^\\d{2}-\\d{2}-\\d{2}$", "use_remit_note": "N", "remit_note_validation_regex": "", "computed_customer_firstname": "<PERSON>", "computed_customer_lastname": "LEES-BUCKLEY", "computed_created_by": "<PERSON><PERSON><PERSON><PERSON>", "computed_accepted_by": "", "computed_remitted_by": "", "computed_transaction_status": "CREATED", "source": "Amazon.fr SV", "source_group": "Marketplaces", "can_be_accepted": true, "can_be_cancelled": true, "can_be_remitted": false, "can_cancel_remit": false}, {"customer_order_payment_id": 3575216, "customer_order_id": 2012127, "customer_order_origin": "A548714-A66-Y", "customer_order_created_at": "2021-01-20 16:33:23", "customer_order_total_amount": 329, "customer_id": 1657896, "customer_email": "<EMAIL>", "unique_id": 3, "payment_id": 64, "type": "paiement", "warehouse_id": null, "warehouse_name": "Nantes", "created_at": "2021-01-20 16:35:13", "created_by": "sebastien.denis", "created_amount": 69, "created_proof": "2012127-3", "created_origin": "son-video.com", "accepted_at": "2021-01-20 16:38:29", "accepted_by": "hichem.aissa<PERSON>i", "accepted_amount": 69, "accepted_proof": "133363-5718339036", "cancelled_at": null, "cancelled_by": null, "cancelled_amount": 0, "remit_asked_at": null, "remit_asked_by": null, "remitted_at": null, "remitted_by": null, "remitted_amount": 0, "remitted_proof": null, "remit_note": null, "remit_error": null, "remit_rate": 1, "unpaid_overdue_at": null, "unpaid_overdue_by": null, "unpaid_overdue_amount": 0, "auto_status": "accepte", "auto_status_detail": 5, "auto_warranty": null, "auto_warranty_detail": null, "country_ip": null, "country_country_code": null, "payment_mean": "CBS-OT", "payment_created_remotely": "Y", "use_remit_proof": "Y", "remit_proof_source": "auto", "remit_proof_type": "No remise", "remit_proof_validation_regex": "^[A-Za-z0-9_\\/\\-]{1,64}$", "use_remit_note": "N", "remit_note_validation_regex": "", "computed_customer_firstname": "<PERSON>", "computed_customer_lastname": "MALOD", "computed_created_by": "<PERSON><PERSON><PERSON><PERSON>", "computed_accepted_by": "Hichem AÏSSAOUI", "computed_remitted_by": "", "computed_transaction_status": "ACCEPTED", "source": "Amazon.fr SV", "source_group": "Marketplaces", "can_be_accepted": false, "can_be_cancelled": true, "can_be_remitted": false, "can_cancel_remit": false}, {"customer_order_payment_id": 3574505, "customer_order_id": 2011693, "customer_order_origin": "A548714-A66-Z", "customer_order_created_at": "2021-01-20 11:17:07", "customer_order_total_amount": 23, "customer_id": 440122, "customer_email": "<EMAIL>", "unique_id": 3, "payment_id": 12, "type": "paiement", "warehouse_id": null, "warehouse_name": "Nantes", "created_at": "2021-01-20 11:17:36", "created_by": "johanne.frabou<PERSON>", "created_amount": 23, "created_proof": null, "created_origin": "son-video.com", "accepted_at": "2021-01-20 11:21:11", "accepted_by": "johanne.frabou<PERSON>", "accepted_amount": 23, "accepted_proof": null, "cancelled_at": null, "cancelled_by": null, "cancelled_amount": 0, "remit_asked_at": null, "remit_asked_by": null, "remitted_at": "2021-01-21 09:36:10", "remitted_by": "vanessa.herisson", "remitted_amount": 23, "remitted_proof": null, "remit_note": null, "remit_error": null, "remit_rate": 1, "unpaid_overdue_at": null, "unpaid_overdue_by": null, "unpaid_overdue_amount": 0, "auto_status": null, "auto_status_detail": null, "auto_warranty": null, "auto_warranty_detail": null, "country_ip": null, "country_country_code": null, "payment_mean": "ESP", "payment_created_remotely": "N", "use_remit_proof": "N", "remit_proof_source": "manuel", "remit_proof_type": "No remise", "remit_proof_validation_regex": "", "use_remit_note": "N", "remit_note_validation_regex": "^\\d{1,64}$", "computed_customer_firstname": "s<PERSON>phane", "computed_customer_lastname": "SUAREZ", "computed_created_by": "<PERSON><PERSON>", "computed_accepted_by": "<PERSON><PERSON>", "computed_remitted_by": "<PERSON>", "computed_transaction_status": "REMITTED", "source": "Amazon.fr SV", "source_group": "Marketplaces", "can_be_accepted": false, "can_be_cancelled": false, "can_be_remitted": false, "can_cancel_remit": false}, {"customer_order_payment_id": 3575376, "customer_order_id": 2012221, "customer_order_origin": "A548714-A45", "customer_order_created_at": "2021-01-20 17:34:27", "customer_order_total_amount": 290, "customer_id": 1111715, "customer_email": "<EMAIL>", "unique_id": 4, "payment_id": 8, "type": "paiement", "warehouse_id": null, "warehouse_name": "Nantes", "created_at": "2021-01-20 17:37:33", "created_by": "cassandra.bouzon", "created_amount": 250, "created_proof": null, "created_origin": "son-video.com", "accepted_at": "2021-01-20 17:38:29", "accepted_by": "cassandra.bouzon", "accepted_amount": 250, "accepted_proof": 253361441010, "cancelled_at": null, "cancelled_by": null, "cancelled_amount": 0, "remit_asked_at": null, "remit_asked_by": null, "remitted_at": null, "remitted_by": null, "remitted_amount": 0, "remitted_proof": null, "remit_note": null, "remit_error": null, "remit_rate": 1, "unpaid_overdue_at": null, "unpaid_overdue_by": null, "unpaid_overdue_amount": 0, "auto_status": null, "auto_status_detail": null, "auto_warranty": null, "auto_warranty_detail": null, "country_ip": null, "country_country_code": null, "payment_mean": "BKDO", "payment_created_remotely": "N", "use_remit_proof": "N", "remit_proof_source": "manuel", "remit_proof_type": "No cheque", "remit_proof_validation_regex": "", "use_remit_note": "N", "remit_note_validation_regex": "", "computed_customer_firstname": "<PERSON><PERSON><PERSON>", "computed_customer_lastname": "WILLER", "computed_created_by": "Cassandra BOUZON", "computed_accepted_by": "Cassandra BOUZON", "computed_remitted_by": "", "computed_transaction_status": "ACCEPTED", "source": "Amazon.fr SV", "source_group": "Marketplaces", "can_be_accepted": false, "can_be_cancelled": true, "can_be_remitted": true, "can_cancel_remit": false}], "_request": {"warehouse_id": 9, "created_at": "2021-01-20"}, "_pager": {"from": 1, "to": 4, "total": 4, "page": 1, "limit": 999999, "last_page": 1}}}