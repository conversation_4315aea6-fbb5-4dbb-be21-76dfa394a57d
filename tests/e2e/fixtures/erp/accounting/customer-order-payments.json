{"status": "success", "data": {"payments": [{"customer_order_payment_id": 3210123, "customer_order_id": 123456, "customer_order_origin": "A548714-A66-X", "customer_order_created_at": "2022-08-28 10:19:33", "customer_order_total_amount": 154.9, "customer_id": 1815262, "customer_email": "<EMAIL>", "unique_id": 5, "payment_id": 93, "type": "paiement", "warehouse_id": 5, "warehouse_name": "Nantes", "created_at": "2022-09-06 11:53:59", "created_by": "anthony.guiteny", "created_amount": 10, "created_proof": null, "created_origin": "son-video.com", "accepted_at": "2022-09-06 11:54:10", "accepted_by": "anthony.guiteny", "accepted_amount": 10, "accepted_proof": "12345678901234", "cancelled_at": null, "cancelled_by": null, "cancelled_amount": 0, "remit_asked_at": null, "remit_asked_by": null, "remitted_at": null, "remitted_by": null, "remitted_amount": 0, "remitted_proof": null, "remit_note": null, "remit_rate": 1, "unpaid_overdue_at": null, "unpaid_overdue_by": null, "unpaid_overdue_amount": 0, "auto_status": null, "auto_status_detail": null, "auto_warranty": null, "auto_warranty_detail": null, "country_ip": null, "country_country_code": null, "payment_mean": "RTLCTLM", "payment_description": "Paiement en plusieurs fois pour les magasins", "payment_created_remotely": "N", "use_remit_proof": "Y", "remit_proof_source": "manuel", "remit_proof_type": "No transaction", "remit_proof_validation_regex": "^\\d{14}$", "use_remit_note": "N", "remit_note_validation_regex": "", "computed_customer_firstname": "Augustin", "computed_customer_lastname": "NENERT", "computed_created_by": "<PERSON><PERSON><PERSON> - Responsable ANTHONY GUITENY", "computed_accepted_by": "<PERSON><PERSON><PERSON> - Responsable ANTHONY GUITENY", "computed_remitted_by": "", "computed_cancelled_by": "", "computed_transaction_status": "ACCEPTED", "can_be_accepted": false, "can_be_cancelled": true, "can_be_remitted": true, "can_cancel_remit": false, "should_be_remitted": true}], "_request": {"fields": [], "where": {"customer_order_payment_id": {"_eq": 3210123}}, "order_by": "created_at", "order_direction": "asc", "page": "1", "limit": 1}, "_pager": {"from": 1, "to": 1, "total": 1, "page": 1, "limit": 1, "last_page": 1}}}