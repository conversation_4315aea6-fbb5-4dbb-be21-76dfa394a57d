{"status": "success", "data": {"store_pickups": [{"delivery_note_id": 4536910, "customer_order_id": 3023601, "store_pickup_started_at": "2021-04-11 11:30:00", "last_name": "Le sanglier de cornou<PERSON>les", "first_name": "<PERSON>hur", "workflow_status": "AVAILABILITY_EMAIL_SENT", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}, {"delivery_note_id": 4536911, "customer_order_id": 3023602, "store_pickup_started_at": "2021-04-11 11:30:01", "last_name": "ELRIC", "first_name": "edward", "workflow_status": "CANCELLED", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}, {"delivery_note_id": 4536912, "customer_order_id": 3023603, "store_pickup_started_at": "2021-04-11 11:30:02", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Gérard", "workflow_status": "CREATED", "picked_by": null, "picked_by_name": null}, {"delivery_note_id": 4536913, "customer_order_id": 3023604, "store_pickup_started_at": "2021-04-11 11:36:03", "last_name": "Delon", "first_name": "<PERSON>", "workflow_status": "PICKING_ABORTED", "picked_by": null, "picked_by_name": null}, {"delivery_note_id": 4536914, "customer_order_id": 3023605, "store_pickup_started_at": "2021-04-11 11:36:04", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Gérard", "workflow_status": "PICKING_FINISHED", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}, {"delivery_note_id": 4536915, "customer_order_id": 3023606, "store_pickup_started_at": "2021-04-11 11:38:05", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Gérard", "workflow_status": "PICKING_STARTED", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}, {"delivery_note_id": 4536916, "customer_order_id": 3023607, "store_pickup_started_at": "2021-04-11 11:38:06", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Gérard", "workflow_status": "PREPARATION_ABORTED", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}, {"delivery_note_id": 4536917, "customer_order_id": 3023608, "store_pickup_started_at": "2021-04-11 11:38:07", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Gérard", "workflow_status": "PREPARATION_FINISHED", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}, {"delivery_note_id": 4536918, "customer_order_id": 3023609, "store_pickup_started_at": "2021-04-11 11:38:08", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Gérard", "workflow_status": "PREPARATION_STARTED", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}, {"delivery_note_id": 4536919, "customer_order_id": 3023610, "store_pickup_started_at": "2021-04-11 11:38:09", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Gérard", "workflow_status": "STOCK_ENTRY", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}, {"delivery_note_id": 4536920, "customer_order_id": 3023611, "store_pickup_started_at": "2021-04-11 11:38:10", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Gérard", "workflow_status": "VALIDATED", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}, {"delivery_note_id": 4536921, "customer_order_id": 3023612, "store_pickup_started_at": "2021-04-11 11:39:59", "last_name": "de la vega", "first_name": "diego", "workflow_status": "PICKING_STARTED", "picked_by": "nathan.tabouillot", "picked_by_name": "<PERSON>"}], "_request": [], "_pager": {"from": 1, "to": 12, "total": 12, "page": 1, "limit": 100, "last_page": 1}}}