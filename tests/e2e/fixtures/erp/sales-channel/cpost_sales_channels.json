{"status": "success", "data": {"sales_channels": [{"sales_channel_id": 2, "label": "easylounge.com", "legacy_name": "e<PERSON><PERSON><PERSON><PERSON>", "display_order": 2, "average_commission_rate": null, "minimum_margin_rate": null, "minimum_available_quantity": 0, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "2", "number_products_configured": "10585", "number_eligible_products": "10585"}]}, {"sales_channel_id": 6, "label": "amazon.de", "legacy_name": "amazon.de", "display_order": 3, "average_commission_rate": 11, "minimum_margin_rate": 11, "minimum_available_quantity": 11, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "6", "number_products_configured": "3629", "number_eligible_products": "3602"}]}, {"sales_channel_id": 5, "label": "amazon.es", "legacy_name": "amazon.es", "display_order": 3, "average_commission_rate": 4, "minimum_margin_rate": 12, "minimum_available_quantity": 0, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "5", "number_products_configured": "3616", "number_eligible_products": "3615"}]}, {"sales_channel_id": 3, "label": "amazon.fr", "legacy_name": "amazon", "display_order": 3, "average_commission_rate": 2, "minimum_margin_rate": 12, "minimum_available_quantity": 0, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "3", "number_products_configured": "5743", "number_eligible_products": "5742"}]}, {"sales_channel_id": 4, "label": "amazon.it", "legacy_name": "amazon.it", "display_order": 3, "average_commission_rate": 3, "minimum_margin_rate": 12, "minimum_available_quantity": 1, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "4", "number_products_configured": "3570", "number_eligible_products": "3569"}]}, {"sales_channel_id": 12, "label": "boulanger", "legacy_name": "boulanger", "display_order": 3, "average_commission_rate": 12, "minimum_margin_rate": 6, "minimum_available_quantity": 55, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "12", "number_products_configured": "5464", "number_eligible_products": "5464"}]}, {"sales_channel_id": 8, "label": "cdiscount", "legacy_name": "cdiscount", "display_order": 3, "average_commission_rate": 7, "minimum_margin_rate": 12, "minimum_available_quantity": 1, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "8", "number_products_configured": "4559", "number_eligible_products": "4556"}]}, {"sales_channel_id": 10, "label": "cilo.dk", "legacy_name": "cilo.dk", "display_order": 3, "average_commission_rate": 9, "minimum_margin_rate": 7, "minimum_available_quantity": 2, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "10", "number_products_configured": "9032", "number_eligible_products": "9030"}]}, {"sales_channel_id": 11, "label": "cultura", "legacy_name": "cultura", "display_order": 3, "average_commission_rate": 10, "minimum_margin_rate": 50, "minimum_available_quantity": 50, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "11", "number_products_configured": "5464", "number_eligible_products": "4045"}]}, {"sales_channel_id": 9, "label": "darty", "legacy_name": "darty", "display_order": 3, "average_commission_rate": 8, "minimum_margin_rate": 7, "minimum_available_quantity": 5, "minimum_selling_price": null, "maximum_selling_price": null, "statistics_sales_channel": [{"sales_channel_id": "9", "number_products_configured": "4765", "number_eligible_products": "4765"}]}, {"sales_channel_id": 7, "label": "fnac", "legacy_name": "fnac", "display_order": 3, "average_commission_rate": 6, "minimum_margin_rate": 10, "minimum_available_quantity": 0, "minimum_selling_price": 1, "maximum_selling_price": 20000, "statistics_sales_channel": [{"sales_channel_id": "7", "number_products_configured": "5108", "number_eligible_products": "5096"}]}], "_request": {"fields": null, "where": {"sales_channel_id": {"_neq": 1}}, "order_by": "display_order ASC, label ASC", "order_direction": null, "page": "1", "limit": "50", "included_dependencies": ["statistics_sales_channel"]}, "_pager": {"from": 1, "to": 11, "total": 11, "page": 1, "limit": 50, "last_page": 1}}}