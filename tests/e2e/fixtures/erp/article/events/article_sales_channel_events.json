{"status": "success", "data": {"system_events": [{"id_for_type": "3035626", "created_at": "2024-12-12 12:57:41", "type": "article.update.sales_channel_product", "payload": {"_rel": {"article": 173805, "sales_channel_id": 7}, "data": {"is_active": {"new": false, "old": true}}, "meta": {"updated_by": {"user_id": 1404, "lastname": "<PERSON><PERSON><PERSON>", "username": "arthur.lo<PERSON>", "firstname": "<PERSON>"}}}, "emitter": null, "main_id": 173805}, {"id_for_type": "2367410", "created_at": "2024-05-16 16:02:40", "type": "article.create.sales_channel_product", "payload": {"_rel": {"article": 173805}, "data": {"label": "amazon.fr", "sales_channel_id": "4"}, "meta": {"created_by": {"user_id": 1404, "lastname": "<PERSON><PERSON><PERSON>", "username": "arthur.lo<PERSON>", "firstname": "<PERSON>"}}}, "emitter": null, "main_id": 173805}, {"id_for_type": "2367409", "created_at": "2024-05-16 15:28:45", "type": "article.delete.sales_channel_product", "payload": {"_rel": {"article": 173805}, "data": {"label": "cultura", "sales_channel_id": "12"}, "meta": {"deleted_by": {"user_id": 1404, "lastname": "<PERSON><PERSON><PERSON>", "username": "arthur.lo<PERSON>", "firstname": "<PERSON>"}}}, "emitter": null, "main_id": 173805}], "_request": {"fields": null, "where": {"type": {"_like": "article.%"}, "article_id": {"_eq": 117735}}, "order_by": "created_at", "order_direction": "desc", "page": 1, "limit": 25}, "_pager": {"from": 1, "to": 3, "total": 3, "page": 1, "limit": 25, "last_page": 1}}}