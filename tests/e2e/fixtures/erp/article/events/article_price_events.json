{"status": "success", "data": {"system_events": [{"id_for_type": "1155286", "created_at": "2023-07-04 11:34:27", "type": "article.update.prices", "payload": {"_rel": {"article": 117735}, "data": {"tariff_tax_excluded": {"new": 133.51, "old": 123.51}}, "meta": {"updated_by": {"user_id": 2, "lastname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON>", "firstname": "<PERSON><PERSON><PERSON>"}}}, "emitter": null}, {"id_for_type": "3408686", "created_at": "2025-04-07 19:38:12", "type": "article.delete.article_planned_price", "payload": {"_rel": {"article": 117735}, "data": {"ends_at": "2025-04-07 16:08:11", "starts_at": "2025-04-07 10:00:41", "selling_price": 126, "sales_channel_ids": [1, 2]}, "meta": {"deleted_by": {"user_id": 1539, "lastname": "<PERSON><PERSON>", "username": "joseph.landry", "firstname": "<PERSON>"}}}, "emitter": null, "main_id": 117735}, {"id_for_type": "3408685", "created_at": "2025-04-07 19:37:31", "type": "article.update.article_planned_price", "payload": {"_rel": {"article": 117735, "planned_price_id": 10}, "data": {"ends_at": {"new": "2025-04-17 00:00:00", "old": "2025-04-16 16:00:00"}, "starts_at": {"new": "2025-04-07 00:00:00", "old": "2025-04-08 17:00:00"}, "selling_price": {"new": 13, "old": 135}, "sales_channel_ids": {"new": [6, 9, 10, 8], "old": [1, 2, 6, 9, 7]}, "exit_selling_price": {"new": 346, "old": 349}}, "meta": {"updated_by": {"user_id": 1539, "lastname": "<PERSON><PERSON>", "username": "joseph.landry", "firstname": "<PERSON>"}}}, "emitter": null, "main_id": 117735}, {"id_for_type": "3408684", "created_at": "2025-04-07 19:36:45", "type": "article.create.article_planned_price", "payload": {"_rel": {"article": 117735}, "data": {"ends_at": "2025-04-16 16:00:00", "starts_at": "2025-04-08 17:00:00", "article_id": 117735, "created_by": 1539, "selling_price": 135, "sales_channel_ids": [1, 2, 6, 9, 7], "exit_selling_price": 349}, "meta": {"created_by": {"user_id": 1539, "lastname": "<PERSON><PERSON>", "username": "joseph.landry", "firstname": "<PERSON>"}}}, "emitter": null, "main_id": 117735}], "_request": {"fields": null, "where": {"type": {"_like": "article.%"}, "article_id": {"_eq": 117735}}, "order_by": "created_at", "order_direction": "desc", "page": 1, "limit": 25}, "_pager": {"from": 1, "to": 1, "total": 4, "page": 1, "limit": 25, "last_page": 1}}}