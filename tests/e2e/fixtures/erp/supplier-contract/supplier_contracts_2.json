{"status": "success", "data": {"supplier_contracts": [{"supplier_contract_id": 1, "supplier_id": 987, "brand_id": null, "year": 2025, "discount_description": "co<PERSON><PERSON> test test", "pam": {"blog": 3, "training": 1, "newsletters": 0, "stores_presence": 1, "website_promotion": 0, "sales_inventory_reporting": 1}, "rfa": [], "additional_rewards": [], "unconditional_discount": 0.0}, {"supplier_contract_id": 2, "supplier_id": 987, "brand_id": 825, "year": 2025, "discount_description": "test 2", "pam": {"blog": 0, "comment": "test", "training": 0, "newsletters": 1, "stores_presence": 1, "website_promotion": 3, "sales_inventory_reporting": 0}, "rfa": {"comment": "", "landing": [{"landing": 1, "end_value": 100000, "rate_value": 1, "starting_value": 0}, {"landing": 2, "end_value": 200000, "rate_value": 2, "starting_value": 100001}, {"landing": 3, "end_value": 300000, "rate_value": 2.5, "starting_value": 200001}, {"landing": 4, "end_value": 400000, "rate_value": 3, "starting_value": 300001}, {"landing": 5, "end_value": 500000, "rate_value": 3.5, "starting_value": 400001}]}, "additional_rewards": [], "unconditional_discount": 0.0}, {"supplier_contract_id": 3, "supplier_id": 987, "brand_id": 125, "year": 2025, "discount_description": "test 3", "pam": [], "rfa": {"comment": "", "landing": [{"landing": 1, "end_value": 100000, "rate_value": 2, "starting_value": 0}, {"landing": 2, "end_value": 200000, "rate_value": 2.5, "starting_value": 100001}, {"landing": 3, "end_value": 300000, "rate_value": 3, "starting_value": 200001}, {"landing": 4, "end_value": 400000, "rate_value": 3.5, "starting_value": 300001}, {"landing": 5, "end_value": 500000, "rate_value": 4, "starting_value": 400001}]}, "additional_rewards": [], "unconditional_discount": 0.0}], "_request": {"fields": null, "where": {"_and": [{"supplier_id": {"_eq": 987}}, {"year": {"_eq": 2025}}]}, "order_by": "supplier_id", "order_direction": null, "page": 1, "limit": 50}, "_pager": {"from": 1, "to": 3, "total": 3, "page": 1, "limit": 50, "last_page": 1}}}