{"status": "success", "data": {"warranty_types": [{"type": "NON", "label": "Aucune", "description": "A utiliser pour forcer le fait de ne pas avoir de garantie et ne pas utiliser la règle parente"}, {"type": "SON", "label": "Son", "description": "Garantie pour les produits appartenant à la famille du son"}, {"type": "VIDEO", "label": "Vidéo", "description": "Garantie pour les produits de diffusion vidéo"}], "_request": {"where": null, "order_by": "type ASC", "order_direction": null, "page": 1, "limit": 50}, "_pager": {"from": 1, "to": 3, "total": 3, "page": 1, "limit": 50, "last_page": 1}}}