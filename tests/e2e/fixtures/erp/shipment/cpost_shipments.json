{"status": "success", "data": {"shipments": [{"shipment_id": 1111, "carrier_name": "DHL", "shipment_account": "EDNDHL", "slip_number": 1596, "closed_at": "2023-03-10 13:58:37", "created_at": "2023-03-06 13:58:37", "status": 0, "parcel_quantity": 0, "shipment_delivery_notes": []}, {"shipment_id": 2222, "carrier_name": "France Express", "shipment_account": "FREXPRESS", "slip_number": 3456, "closed_at": "2023-03-10 13:58:37", "created_at": "2023-03-06 14:09:36", "status": 0, "parcel_quantity": 12, "shipment_delivery_notes": [{"delivery_note_id": 4911493, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911554, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911497, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911456, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911480, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911492, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911496, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911495, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911568, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911491, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911564, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911460, "status": 4, "have_all_tracking_numbers": true}]}, {"shipment_id": 3333, "carrier_name": "Novea", "shipment_account": "NOVEA", "slip_number": 1571, "closed_at": "2023-03-10 13:58:37", "created_at": "2023-03-06 09:01:12", "status": 0, "parcel_quantity": 3, "shipment_delivery_notes": [{"delivery_note_id": 4911051, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911044, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911156, "status": 4, "have_all_tracking_numbers": true}]}, {"shipment_id": 4444, "carrier_name": "Chronopost", "shipment_account": "CHRNOPOST", "slip_number": 1507, "closed_at": null, "created_at": "2023-03-06 12:25:09", "status": 1, "parcel_quantity": 3, "shipment_delivery_notes": [{"delivery_note_id": 444441, "status": 0, "have_all_tracking_numbers": true}, {"delivery_note_id": 444442, "status": 1, "have_all_tracking_numbers": true}, {"delivery_note_id": 444443, "status": 4, "have_all_tracking_numbers": false}]}, {"shipment_id": 5555, "carrier_name": "Chronopost", "shipment_account": "CHRNOPOST", "slip_number": 1508, "closed_at": null, "created_at": "2023-03-06 12:25:09", "status": 1, "parcel_quantity": 3, "shipment_delivery_notes": [{"delivery_note_id": 4910993, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4910965, "status": 4, "have_all_tracking_numbers": true}, {"delivery_note_id": 4911329, "status": 4, "have_all_tracking_numbers": true}]}, {"shipment_id": **********, "carrier_name": "France Express", "shipment_account": "FREXPRESS", "slip_number": 7890, "closed_at": "2023-03-10 13:58:37", "created_at": "2023-03-06 14:09:36", "status": 0, "parcel_quantity": 12, "shipment_delivery_notes": [{"delivery_note_id": 123456, "status": 4, "have_all_tracking_numbers": true}]}], "_request": {"fields": null, "where": [], "order_by": "created_at DESC", "order_direction": null, "page": 1, "limit": 25, "included_dependencies": ["shipment_delivery_notes"]}, "_pager": {"from": 1, "to": 25, "total": 15204, "page": 1, "limit": 25, "last_page": 609}}}