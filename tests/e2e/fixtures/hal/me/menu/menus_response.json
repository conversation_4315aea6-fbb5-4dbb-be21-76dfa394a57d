{"success": true, "data": {"menu": [{"title": "SAV", "node_type": "menu", "children": [{"title": "Factures", "node_type": "menu", "children": [{"title": "Recherche", "node_type": "route", "url": "/legacy/facture/search", "matches": ["^/legacy/facture/search$"], "confirmation": false}, {"title": "Liste Factures", "node_type": "route", "url": "/legacy/v1/administration/divers/pdfFacturesList.php", "matches": ["^/legacy/v1/administration/divers/pdfFacturesList.php$"], "confirmation": false}]}, {"title": "Suivi SAV", "node_type": "menu", "children": [{"title": "Stations", "node_type": "route", "url": "/legacy/sav/stationList", "matches": ["^/legacy/sav/stationList$"], "confirmation": false}, {"title": "Dossiers SAV", "node_type": "route", "url": "/legacy/sav/listeArticles", "matches": ["^/legacy/sav/listeArticles$"], "confirmation": false}]}]}, {"title": "Prospects", "node_type": "menu", "children": [{"title": "<PERSON><PERSON><PERSON>", "node_type": "menu", "children": [{"title": "<PERSON><PERSON><PERSON>", "node_type": "route", "url": "/legacy/prospect/prospectSearch", "matches": ["^/legacy/prospect/prospectSearch$"], "confirmation": false}, {"title": "Lister", "node_type": "route", "url": "/legacy/prospect/list", "matches": ["^/legacy/prospect/list$"], "confirmation": false}, {"title": "Edition prospect", "node_type": "route", "matches": ["^/legacy/prospect/prospectEdit[?#]"], "confirmation": false}]}]}, {"title": "Commandes", "node_type": "menu", "children": [{"title": "<PERSON><PERSON><PERSON>", "node_type": "menu", "children": [{"title": "<PERSON><PERSON><PERSON>", "node_type": "route", "url": "/legacy/commande/recherche", "matches": ["^/legacy/commande/recherche", "^/legacy/commande/testrecherche$"], "confirmation": false}, {"title": "Recherche commande", "node_type": "route", "url": "/legacy/v1/commandes/recherche_commande.php", "matches": ["^/legacy/v1/commandes/recherche_commande.php$"], "confirmation": false}, {"title": "Edition commande", "node_type": "route", "matches": ["^/legacy/v1/commandes/edition_commande.php$"], "confirmation": false}]}, {"title": "À accepter", "node_type": "menu", "children": [{"title": "À accepter", "node_type": "route", "url": "/legacy/v1/commandes/recherche_commande.php?type_recherche=À accepter&action=chercher", "matches": ["^/legacy/v1/commandes/recherche_commande\\.php\\?type_recherche\\=À accepter\\&action\\=chercher$"], "confirmation": false}]}, {"title": "CB avec rappel client", "node_type": "menu", "children": [{"title": "<PERSON><PERSON><PERSON>", "node_type": "route", "url": "/legacy/commande/rechercheAvancee/avec/cb_rappel_client", "matches": ["^/legacy/commande/rechercheAvancee/avec/cb_rappel_client$"], "confirmation": false}]}]}, {"title": "Tâches", "node_type": "menu", "children": [{"title": "Types", "node_type": "menu", "children": [{"title": "Types", "node_type": "route", "url": "/legacy/v1/taches/gestion-types.php", "matches": ["^/legacy/v1/taches/gestion-types.php$"], "confirmation": false}]}]}, {"title": "Inutile", "node_type": "menu", "children": [{"title": "Types", "node_type": "menu", "children": [{"title": "Types", "node_type": "route", "matches": ["^/legacy/test$"], "confirmation": false}]}]}]}}