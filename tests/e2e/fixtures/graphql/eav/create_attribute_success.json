{"data": {"insert_eav_attribute": {"affected_rows": 1, "returning": [{"attribute_id": 783, "name": "Test", "definition": {"type": "numeric"}, "meta": {"unit": "minutes", "label": "Mon label public", "prefix": "pref.", "suffix": "W/s"}, "attribute_values": [], "total": {"aggregate": {"count": 0, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}], "__typename": "eav_attribute_mutation_response"}}}