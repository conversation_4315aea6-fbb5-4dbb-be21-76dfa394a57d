{"data": {"eav_search_attributes": [{"attribute_id": 518, "name": "Babyfoots usage", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Usage", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 92905, "display_order": 10, "value": "Intérieur", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92906, "display_order": 20, "value": "Extérieur", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 2, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 210, "name": "Bande de Fréquences", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Bande de Fréquences", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 80443, "display_order": 20, "value": "H.F. (Hautes Fréquences)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80444, "display_order": 10, "value": "U.H.F. (Ultra Hautes Fréquences)", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 2, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 67, "name": "Bande passante freq maximum", "definition": {"type": "numeric"}, "meta": {"unit": "", "label": "Fréquence maxi", "prefix": "", "suffix": "kHz"}, "attribute_values": [{"attribute_value_id": 79289, "display_order": 0, "value": "20", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79291, "display_order": 0, "value": "0", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79346, "display_order": 0, "value": "0.125", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79398, "display_order": 0, "value": "0.550000000000000044", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79436, "display_order": 0, "value": "0.299999999999999989", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79446, "display_order": 0, "value": "1.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79541, "display_order": 0, "value": "18", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79602, "display_order": 0, "value": "64", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79612, "display_order": 0, "value": "130", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79613, "display_order": 0, "value": "100", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79624, "display_order": 0, "value": "40", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79642, "display_order": 0, "value": "22", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79647, "display_order": 0, "value": "43", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79655, "display_order": 0, "value": "50", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79697, "display_order": 0, "value": "19", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79745, "display_order": 0, "value": "45", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79875, "display_order": 0, "value": "16", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79912, "display_order": 0, "value": "2", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79925, "display_order": 0, "value": "0.200000000000000011", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79931, "display_order": 0, "value": "0.25", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79981, "display_order": 0, "value": "0.149999999999999994", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80024, "display_order": 0, "value": "0.23000000000000001", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80097, "display_order": 0, "value": "0.119999999999999996", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80300, "display_order": 0, "value": "25", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80304, "display_order": 0, "value": "30", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80703, "display_order": 0, "value": "15", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80752, "display_order": 0, "value": "35", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80777, "display_order": 0, "value": "90", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80801, "display_order": 0, "value": "70", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82049, "display_order": 0, "value": "150", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82216, "display_order": 0, "value": "93", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82441, "display_order": 0, "value": "58", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82444, "display_order": 0, "value": "52", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82445, "display_order": 0, "value": "120", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82449, "display_order": 0, "value": "60", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82453, "display_order": 0, "value": "19.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82459, "display_order": 0, "value": "18.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82544, "display_order": 0, "value": "33", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82571, "display_order": 0, "value": "28", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82875, "display_order": 0, "value": "0.135000000000000009", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82876, "display_order": 0, "value": "160", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82881, "display_order": 0, "value": "155", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82885, "display_order": 0, "value": "135", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82913, "display_order": 0, "value": "0.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82918, "display_order": 0, "value": "230", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82962, "display_order": 0, "value": "0.349999999999999978", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82993, "display_order": 0, "value": "0.33500000000000002", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82995, "display_order": 0, "value": "335", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83037, "display_order": 0, "value": "21", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83198, "display_order": 0, "value": "12", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83245, "display_order": 0, "value": "1", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83371, "display_order": 0, "value": "17.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83374, "display_order": 0, "value": "300", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83533, "display_order": 0, "value": "0.100000000000000006", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83573, "display_order": 0, "value": "0.104999999999999996", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83577, "display_order": 0, "value": "0.14499999999999999", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83592, "display_order": 0, "value": "14", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83633, "display_order": 0, "value": "0.400000000000000022", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84397, "display_order": 0, "value": "23", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84415, "display_order": 0, "value": "22.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84426, "display_order": 0, "value": "39.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84431, "display_order": 0, "value": "24", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84433, "display_order": 0, "value": "27", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84632, "display_order": 0, "value": "48", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84721, "display_order": 0, "value": "34", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84733, "display_order": 0, "value": "20.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84739, "display_order": 0, "value": "38.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84833, "display_order": 0, "value": "39.7999999999999972", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 87096, "display_order": 0, "value": "10", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 87460, "display_order": 0, "value": "96", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 87672, "display_order": 0, "value": "41", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 89146, "display_order": 0, "value": "42", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 89245, "display_order": 0, "value": "39", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 89363, "display_order": 0, "value": "23.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 90015, "display_order": 0, "value": "23500", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 90023, "display_order": 0, "value": "26", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 90324, "display_order": 0, "value": "80", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 90460, "display_order": 0, "value": "51", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 90462, "display_order": 0, "value": "44", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 91653, "display_order": 0, "value": "38", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 91820, "display_order": 0, "value": "37", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92781, "display_order": 0, "value": "200", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 93028, "display_order": 0, "value": "108", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 95257, "display_order": 0, "value": "13", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 95543, "display_order": 0, "value": "45000", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 96831, "display_order": 0, "value": "0.0599999999999999978", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 97164, "display_order": 0, "value": "450", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 98671, "display_order": 0, "value": "450000", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 98673, "display_order": 0, "value": "32", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 98699, "display_order": 0, "value": "43.7999999999999972", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 98746, "display_order": 0, "value": "85", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 98800, "display_order": 0, "value": "25000", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 98840, "display_order": 0, "value": "18000", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 98843, "display_order": 0, "value": "10000", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100025, "display_order": 0, "value": "50000", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 95, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 66, "name": "Bande passante freq minimum", "definition": {"type": "numeric"}, "meta": {"unit": "", "label": "Fréquence mini", "prefix": "", "suffix": "Hz"}, "attribute_values": [{"attribute_value_id": 79290, "display_order": 0, "value": "20", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79292, "display_order": 0, "value": "0", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79314, "display_order": 0, "value": "82", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79315, "display_order": 0, "value": "60", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79321, "display_order": 0, "value": "50", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79345, "display_order": 0, "value": "38", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79381, "display_order": 0, "value": "80", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79395, "display_order": 0, "value": "40", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79405, "display_order": 0, "value": "30", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79447, "display_order": 0, "value": "47", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79486, "display_order": 0, "value": "68", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79518, "display_order": 0, "value": "53", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79542, "display_order": 0, "value": "70", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79551, "display_order": 0, "value": "100", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79569, "display_order": 0, "value": "43", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79583, "display_order": 0, "value": "74", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79591, "display_order": 0, "value": "10", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79593, "display_order": 0, "value": "61", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79603, "display_order": 0, "value": "18", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79643, "display_order": 0, "value": "45", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79649, "display_order": 0, "value": "19", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79650, "display_order": 0, "value": "55", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79811, "display_order": 0, "value": "78", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79872, "display_order": 0, "value": "67", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79883, "display_order": 0, "value": "42", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79894, "display_order": 0, "value": "65", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79911, "display_order": 0, "value": "35", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80023, "display_order": 0, "value": "48", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80030, "display_order": 0, "value": "37", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80034, "display_order": 0, "value": "32", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80089, "display_order": 0, "value": "52", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80305, "display_order": 0, "value": "5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80706, "display_order": 0, "value": "22", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80711, "display_order": 0, "value": "120", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80833, "display_order": 0, "value": "25", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80838, "display_order": 0, "value": "31.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 81242, "display_order": 0, "value": "4", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 81437, "display_order": 0, "value": "3", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 81438, "display_order": 0, "value": "16", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 81444, "display_order": 0, "value": "14", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82052, "display_order": 0, "value": "46", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82355, "display_order": 0, "value": "15", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82572, "display_order": 0, "value": "39", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82681, "display_order": 0, "value": "110", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82687, "display_order": 0, "value": "90", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82839, "display_order": 0, "value": "64", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82874, "display_order": 0, "value": "41", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82880, "display_order": 0, "value": "33", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82890, "display_order": 0, "value": "36", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82992, "display_order": 0, "value": "44", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83392, "display_order": 0, "value": "140", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83507, "display_order": 0, "value": "300", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83521, "display_order": 0, "value": "56", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83554, "display_order": 0, "value": "62", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83563, "display_order": 0, "value": "54", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83572, "display_order": 0, "value": "51", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83597, "display_order": 0, "value": "17", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83598, "display_order": 0, "value": "75", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83619, "display_order": 0, "value": "500", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83632, "display_order": 0, "value": "350", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84460, "display_order": 0, "value": "19.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84465, "display_order": 0, "value": "12", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84467, "display_order": 0, "value": "8", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84535, "display_order": 0, "value": "21", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84651, "display_order": 0, "value": "130", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84656, "display_order": 0, "value": "2", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 84675, "display_order": 0, "value": "150", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 85718, "display_order": 0, "value": "28", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 87097, "display_order": 0, "value": "27", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 87671, "display_order": 0, "value": "6", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 87834, "display_order": 0, "value": "7", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 88427, "display_order": 0, "value": "57", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 90022, "display_order": 0, "value": "9", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 90323, "display_order": 0, "value": "87.5", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 93027, "display_order": 0, "value": "88", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 93830, "display_order": 0, "value": "29", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 95256, "display_order": 0, "value": "63", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 95489, "display_order": 0, "value": "85", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 78, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 746, "name": "couleur", "definition": {"type": "color"}, "meta": {"unit": "", "label": "couleur", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 101401, "display_order": 10, "value": "Silver", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Silver.jpg"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101402, "display_order": 20, "value": "Noir", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Noir.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101403, "display_order": 15, "value": "<PERSON>", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Blanc.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101404, "display_order": 40, "value": "Rouge", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Rouge.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101405, "display_order": 50, "value": "Gold", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Gold.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101406, "display_order": 60, "value": "Jaune", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Jaune.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101407, "display_order": 70, "value": "<PERSON>", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Rose.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101408, "display_order": 80, "value": "Bleu", "meta": {"image": "http://www.son-video.com/images/static/Coloris/BleuCobalt.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101409, "display_order": 90, "value": "<PERSON>ert", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Vert_<PERSON>on.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101410, "display_order": 100, "value": "Transparent", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Acrylique.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101411, "display_order": 110, "value": "Orange", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Orange.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101412, "display_order": 120, "value": "Violet", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Violet.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101413, "display_order": 130, "value": "<PERSON><PERSON>", "meta": {"image": "http://www.son-video.com/images/static/Coloris/VioletiPod.jpg"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101414, "display_order": 140, "value": "<PERSON> clair", "meta": {"image": "http://www.son-video.com/images/static/Coloris/AcajouClair.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101415, "display_order": 150, "value": "Bronze", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Bronze.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101416, "display_order": 160, "value": "<PERSON> foncé", "meta": {"image": "http://www.son-video.com/images/static/Coloris/Noyer.gif"}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101417, "display_order": 170, "value": "Granite", "meta": {"image": "http://www.son-video.com/images/static/Coloris/RocheBeige.gif"}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 17, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 260, "name": "Bande passante récepteur min. (Hz)", "definition": {"type": "numeric"}, "meta": {"unit": "", "label": "Bande passante récepteur minimum", "prefix": "", "suffix": "Hz"}, "attribute_values": [{"attribute_value_id": 80726, "display_order": 0, "value": "45", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 1, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 185, "name": "Bande passante Satellites freq max (kHz)", "definition": {"type": "numeric"}, "meta": {"unit": "", "label": "Bande passante Satellites freq maximum ", "prefix": "", "suffix": "kHz"}, "attribute_values": [{"attribute_value_id": 80379, "display_order": 0, "value": "20", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80423, "display_order": 0, "value": "15", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80457, "display_order": 0, "value": "19", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80658, "display_order": 0, "value": "18", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82070, "display_order": 0, "value": "150", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82138, "display_order": 0, "value": "120", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82759, "display_order": 0, "value": "80", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 7, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 184, "name": "Bande passante Satellites freq min (Hz)", "definition": {"type": "numeric"}, "meta": {"unit": "", "label": "Bande passante Satellites freq minimum ", "prefix": "", "suffix": "Hz"}, "attribute_values": [{"attribute_value_id": 80380, "display_order": 0, "value": "65", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80387, "display_order": 0, "value": "60", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80396, "display_order": 0, "value": "50", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80404, "display_order": 0, "value": "55", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80458, "display_order": 0, "value": "85", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80514, "display_order": 0, "value": "100", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80528, "display_order": 0, "value": "130", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80580, "display_order": 0, "value": "120", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 81064, "display_order": 0, "value": "80", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82069, "display_order": 0, "value": "42", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82085, "display_order": 0, "value": "40", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82133, "display_order": 0, "value": "47", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82137, "display_order": 0, "value": "44", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82173, "display_order": 0, "value": "150", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82201, "display_order": 0, "value": "20", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82758, "display_order": 0, "value": "78", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82969, "display_order": 0, "value": "70", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83113, "display_order": 0, "value": "30", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83407, "display_order": 0, "value": "140", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 19, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 188, "name": "Bande passante Sub freq maximum (Hz)", "definition": {"type": "numeric"}, "meta": {"unit": "", "label": "Bande passante Sub freq maximum ", "prefix": "", "suffix": "Hz"}, "attribute_values": [{"attribute_value_id": 80381, "display_order": 0, "value": "150", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80397, "display_order": 0, "value": "120", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80462, "display_order": 0, "value": "130", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80517, "display_order": 0, "value": "100", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82087, "display_order": 0, "value": "150000", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82972, "display_order": 0, "value": "135", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83001, "display_order": 0, "value": "350", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 7, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 187, "name": "Bande passante Sub freq minimum (Hz)", "definition": {"type": "numeric"}, "meta": {"unit": "", "label": "Bande passante Sub freq minimum ", "prefix": "", "suffix": "Hz"}, "attribute_values": [{"attribute_value_id": 80382, "display_order": 0, "value": "50", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80388, "display_order": 0, "value": "40", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80463, "display_order": 0, "value": "37", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80518, "display_order": 0, "value": "43", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80531, "display_order": 0, "value": "47", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80550, "display_order": 0, "value": "45", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 81067, "display_order": 0, "value": "48", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82075, "display_order": 0, "value": "42", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82096, "display_order": 0, "value": "80", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82097, "display_order": 0, "value": "35", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82971, "display_order": 0, "value": "41", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82984, "display_order": 0, "value": "36", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83000, "display_order": 0, "value": "33", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 13, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 527, "name": "Billard accessoire type jeu", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Type de jeu", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 92977, "display_order": 0, "value": "Américain", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92978, "display_order": 0, "value": "Français", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92979, "display_order": 0, "value": "<PERSON> Anglais", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92980, "display_order": 0, "value": "Snooker", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 4, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 525, "name": "Billard convertible table", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Convertible en table", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 92936, "display_order": 0, "value": "O<PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92937, "display_order": 0, "value": "Non", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 2, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 526, "name": "<PERSON><PERSON> couleur tapis", "definition": {"type": "color"}, "meta": {"unit": "", "label": "<PERSON><PERSON><PERSON> tapis", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 92938, "display_order": 0, "value": "Noir", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92939, "display_order": 0, "value": "<PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92940, "display_order": 0, "value": "<PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92941, "display_order": 0, "value": "<PERSON>ert", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92942, "display_order": 0, "value": "Rouge", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92943, "display_order": 0, "value": "<PERSON><PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92944, "display_order": 0, "value": "<PERSON><PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92945, "display_order": 0, "value": "Bleu", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92946, "display_order": 0, "value": "Orange", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 9, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 528, "name": "Billard type accessoire", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Type d'accessoire", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 92981, "display_order": 0, "value": "<PERSON><PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92982, "display_order": 0, "value": "<PERSON><PERSON><PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92983, "display_order": 0, "value": "<PERSON><PERSON><PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92984, "display_order": 0, "value": "Porte queue", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92985, "display_order": 0, "value": "Lustre", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92986, "display_order": 0, "value": "Queue", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92987, "display_order": 0, "value": "Triangle", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92991, "display_order": 0, "value": "Plateau de table", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 8, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 524, "name": "Billard type de jeu", "definition": {"type": "text"}, "meta": {"unit": "", "label": "type de jeu", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 92932, "display_order": 0, "value": "Américain", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92933, "display_order": 0, "value": "Français", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92934, "display_order": 0, "value": "<PERSON> Anglais", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 92935, "display_order": 0, "value": "Snooker", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 4, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 388, "name": "Blindage amagnétique", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Blindage amagnétique", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 86102, "display_order": 0, "value": "O<PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 86103, "display_order": 0, "value": "Non", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 2, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 61, "name": "Boomer", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Boomer", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 79023, "display_order": 0, "value": "4\" (10cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79024, "display_order": 0, "value": "6\" (15cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79025, "display_order": 0, "value": "8\" (20cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79026, "display_order": 0, "value": "10\" (25cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79027, "display_order": 0, "value": "12\" (30 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79028, "display_order": 0, "value": "14\" (35 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79029, "display_order": 0, "value": "15\" (38 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79030, "display_order": 0, "value": "16\" (41cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79031, "display_order": 0, "value": "17\" (43 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79032, "display_order": 0, "value": "18\"(46 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79033, "display_order": 0, "value": "19\" (48 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79034, "display_order": 0, "value": "20\" (51 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79035, "display_order": 0, "value": "22\" (56cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79036, "display_order": 0, "value": "24\" (61 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79037, "display_order": 0, "value": "26\" (66 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79038, "display_order": 0, "value": "28\" (71 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 79039, "display_order": 0, "value": "30\" (76 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80042, "display_order": 0, "value": "5\" (13cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82884, "display_order": 0, "value": "21\" (54 cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83402, "display_order": 0, "value": "3\"1/2", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 83403, "display_order": 0, "value": "3’’1/2 (9 cm)", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 21, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 174, "name": "Boomer Satellites", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Boomer Satellites", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 80312, "display_order": 0, "value": "4\" (10cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80313, "display_order": 0, "value": "5\" (12cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80314, "display_order": 0, "value": "6\" (15cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80315, "display_order": 0, "value": "8\" (20cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80316, "display_order": 0, "value": "10\" (25cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80317, "display_order": 0, "value": "12\" (30cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80318, "display_order": 0, "value": "14\" (35cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80319, "display_order": 0, "value": "15\" (38cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 82093, "display_order": 0, "value": "18\" (46cm)", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 9, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 175, "name": "Boomer <PERSON>", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Boomer <PERSON>", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 80320, "display_order": 0, "value": "6\" (15cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80321, "display_order": 0, "value": "8\" (20cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80322, "display_order": 0, "value": "10\" (25cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80323, "display_order": 0, "value": "12\" (30cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80324, "display_order": 0, "value": "14\" (35cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80325, "display_order": 0, "value": "15\" (38cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80326, "display_order": 0, "value": "16\" (41cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80327, "display_order": 0, "value": "17\" (43cm)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80328, "display_order": 0, "value": "18\" (46cm)", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 9, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 720, "name": "Borne Arcade Boutons", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Type de boutons", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 100227, "display_order": 0, "value": "Joystick 1P", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100228, "display_order": 0, "value": "Joystick 2P", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100229, "display_order": 0, "value": "Boutons 1P", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100230, "display_order": 0, "value": "Boutons 2P", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100231, "display_order": 0, "value": "Boutons Start", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100232, "display_order": 0, "value": "Boutons Contrôle 1", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100233, "display_order": 0, "value": "Boutons Contrôle 2", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100234, "display_order": 0, "value": "T-Molding", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100235, "display_order": 0, "value": "Carrenage", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 9, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 719, "name": "Borne Arcade Collection", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Collection", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 100221, "display_order": 0, "value": "<PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100222, "display_order": 0, "value": "Edition", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100223, "display_order": 0, "value": "<PERSON><PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100224, "display_order": 0, "value": "<PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100225, "display_order": 0, "value": "Standard", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100226, "display_order": 0, "value": "<PERSON><PERSON>", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 6, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 722, "name": "Borne Arcade Fonction supp.", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Fonctionnalités supplémentaires", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 100238, "display_order": 0, "value": "Boutons et Joystick transparents et lumineux", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100239, "display_order": 0, "value": "Monnayeur (20 jetons)", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 2, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 717, "name": "Borne Arcade Gamme", "definition": {"type": "text"}, "meta": {"unit": "", "label": "<PERSON><PERSON><PERSON>", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 100214, "display_order": 0, "value": "Pocket", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100215, "display_order": 0, "value": "Compact", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100216, "display_order": 0, "value": "Mini", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100217, "display_order": 0, "value": "Classic", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100218, "display_order": 0, "value": "Vizion", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100219, "display_order": 0, "value": "Table 80's", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 6, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 581, "name": "Borne Arcade Nombre de joueurs", "definition": {"type": "numeric"}, "meta": {"unit": "", "label": "Nombre de joueurs Max.", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 94518, "display_order": 0, "value": "2", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 98159, "display_order": 0, "value": "680", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100281, "display_order": 0, "value": "1", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 3, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 721, "name": "Borne Arcade Système de jeux", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Système de jeux", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 100236, "display_order": 0, "value": "Arkador (1 300 jeux)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100237, "display_order": 0, "value": "Arcade Box (10 000 jeux)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 100474, "display_order": 0, "value": "<PERSON> <PERSON> (60 jeux)", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 3, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 255, "name": "<PERSON><PERSON>", "definition": {"type": "text"}, "meta": {"unit": "", "label": "<PERSON><PERSON>", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 80636, "display_order": 0, "value": "En S", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80637, "display_order": 0, "value": "<PERSON><PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80638, "display_order": 0, "value": "Réglable en hauteur", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80639, "display_order": 0, "value": "ASTS", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 4, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 739, "name": "Caisson Connectiques", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Connectiques", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 101094, "display_order": 0, "value": "1 x Entrée RCA bas niveau", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101098, "display_order": 0, "value": "2 x Entrée RCA haut niveau", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 2, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 636, "name": "<PERSON><PERSON><PERSON> de basse encastrable", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Encastrable", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 95775, "display_order": 0, "value": "O<PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 95776, "display_order": 0, "value": "Non", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 2, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 173, "name": "Caisson de basses inclus", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Caisson de basses inclus", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 80310, "display_order": 0, "value": "O<PERSON>", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 80311, "display_order": 0, "value": "Non", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 2, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}, {"attribute_id": 742, "name": "<PERSON><PERSON>son Entrées", "definition": {"type": "text"}, "meta": {"unit": "", "label": "Entrées", "prefix": "", "suffix": ""}, "attribute_values": [{"attribute_value_id": 101106, "display_order": 0, "value": "1 x Sub/LFE", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101107, "display_order": 0, "value": "1 x Ligne RCA (Stéréo)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101108, "display_order": 0, "value": "1 x HP stéréo (Borniers enceintes)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101112, "display_order": 0, "value": "2 x Sub/LFE", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101113, "display_order": 0, "value": "2 x Ligne RCA (Stéréo)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101114, "display_order": 0, "value": "2 x HP stéréo (Double borniers enceintes)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101118, "display_order": 0, "value": "1 x Trigger 12v", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101119, "display_order": 0, "value": "2 x Trigger 12v", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101120, "display_order": 0, "value": "1 x XLR (Stéréo)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101121, "display_order": 0, "value": "2 x XLR (Stéréo)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101137, "display_order": 0, "value": "1 x USB", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101143, "display_order": 0, "value": "1 x Infrarouge", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101148, "display_order": 0, "value": "1 x XLR (Mono)", "meta": {}, "__typename": "eav_attribute_value"}, {"attribute_value_id": 101179, "display_order": 0, "value": "1 x Jack 6,35 mm (St<PERSON><PERSON><PERSON>)", "meta": {}, "__typename": "eav_attribute_value"}], "total": {"aggregate": {"count": 14, "__typename": "eav_attribute_value_aggregate_fields"}, "__typename": "eav_attribute_value_aggregate"}, "__typename": "eav_attribute"}], "eav_search_attributes_aggregate": {"aggregate": {"count": 748, "__typename": "eav_attribute_aggregate_fields"}, "__typename": "eav_attribute_aggregate"}}}