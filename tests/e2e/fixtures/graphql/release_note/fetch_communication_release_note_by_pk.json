{"data": {"communication_release_note_by_pk": {"release_note_id": "4lHsO", "type": "update", "status": "published", "released_at": "2021-08-04T11:44:04+02:00", "title": "Correction du calcul de la quantité en cours par dépôt", "description": "<p>La colonne \"Qté cmd à expé en cours\" affiche maintenant bien les quantités en emport et à expédier sur les bons dépôts</p>\n<figure><img src=\"https://s3.amazonaws.com/release-assets/production/team-4120/611ccfadcc778_image.png\" data-image=\"611ccfadf3c42\"></figure>\n<hr>\n<p><mark>ASTUCE</mark><br>Pour différencier les quantités disponibles, des quantités \"expédiables\" lors d'une prise de commande.<sub><br></sub></p>\n<figure><sub><img src=\"https://s3.amazonaws.com/release-assets/production/team-4120/611cd0e9d495c_image.png\" data-image=\"611cd0ea0e098\"></sub></figure>\n<p><sub>Des réflexions de simplifications sont à venir</sub></p>\n<p></p>", "notes": [{"label": "update", "notes": [{"type": "update", "title": "<p>Amélioration des requêtes de calcul de la quantité en attente d'un produit et du passage des produits de last en yapu</p>\n"}], "order": 2, "display_label": "updates"}, {"label": "bugfix", "notes": [{"type": "bugfix", "title": "<p>Correction du calcul de la quantité non expédiée d'un produit</p>\n"}], "order": 3, "display_label": "bugfixes"}], "extracted_notes": {"updates": [{"subject": null, "description": "Amélioration des requêtes de calcul de la quantité en attente d'un produit et du passage des produits de last en yapu", "release_note_id": null}], "bugfixes": [{"subject": null, "description": "Correction du calcul de la quantité non expédiée d'un produit", "release_note_id": null}], "features": [{"subject": null, "description": "Une nouvelle feature", "release_note_id": null}]}, "tags": ["erp"], "__typename": "communication_release_note"}}}