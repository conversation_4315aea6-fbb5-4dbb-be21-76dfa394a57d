{"data": {"communication_release_note": [{"release_note_id": "Xv6UZ", "type": "feature", "status": "published", "released_at": "2021-09-06T10:11:17+02:00", "title": "(En cours) Release-2021-11", "description": "<p><strong>Objectifs principaux :</strong></p>\n<ul><li>Projets<ul><li> Lancement du projet : améliorations de la date de livraison fournisseur prévue</li></ul></li></ul>\n<ul><li>Maintenance évolutives<ul><li>C<PERSON>er une tâche auto sur le retours de mes clients afin de pouvoir prendre contact avec lui pour lui proposer un autre produit</li><li>WEB : suppression du mot cadeau sur les produits à 1€</li><li>Evols compta (droits remises, ordre d’affichage, fix montant clôture de caisse)</li><li>Génération du fichier pour les étiquettes électronique<br></li></ul></li></ul>\n<ul><li>Dates<ul><li>30/08/2021 au 17/09/2021</li></ul></li></ul>", "notes": [{"label": "update", "notes": [{"type": "update", "title": "<p><code>WEB :</code> <a href=\"https://son-video.releasenotes.io/release/ItnXh\">Patch Son-Vidéo.com</a></p>\n"}, {"type": "update", "title": "<p><code>ERP :</code> <a href=\"https://son-video.releasenotes.io/release/q6S97\">Patch ERP Backoffice</a></p>\n"}], "order": 2, "display_label": "updates"}], "extracted_notes": {"updates": [{"subject": "WEB", "description": "Patch Son-Vidéo.com", "release_note_id": null}, {"subject": "ERP", "description": "Patch ERP Backoffice", "release_note_id": null}]}, "tags": ["release"], "__typename": "communication_release_note"}], "communication_release_note_aggregate": {"aggregate": {"count": 1, "__typename": "communication_release_note_aggregate_fields"}, "__typename": "communication_release_note_aggregate"}}}