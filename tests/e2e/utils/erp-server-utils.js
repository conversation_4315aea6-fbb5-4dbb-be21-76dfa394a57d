export const mockEmptyResult = (key) => {
    const result = {
        status: 'success',
        data: {
            _pager: {
                from: 0,
                to: 0,
                total: 0,
                page: 1,
                limit: 5,
                last_page: 1,
            },
        },
    }

    result.data[key] = []

    return { body: result, statusCode: 200 }
}

export function customerOrderErpV2Mock(customer_order_id) {
    return {
        customer_order_id,
        original_customer_order_id: null,
        origin: 'deprecated',
        sales_channel_origin: 'svd.or.any.Marketplace',
        sales_channel_id: null,
        created_at: '',
        modified_at: null,
        status: 'ONGOING',
        computed_status: 'AWAITING',
        customer_id: 970481,
        number_of_visits: null,
        relay_id: null,
        quote_id: null,
        ip_address: null,
        invoicing_mode: 'CUSTOMER_ORDER',
        should_recall_customer: false,
        is_store_pickup: false,
        is_detaxed: false,
        is_intragroup: false,
        has_ongoing_premium_warranty: false,
        is_paid: true,
        has_inconsistent_carrier: false,
        total_price_vat_included: 0,
        total_price_vat_excluded: 0,
        total_accepted_amount: 0,
        total_remitted_amount: 0,
        vat_rate: null,
        ecotax_price: '',
        shipping_price: null,
        pickup_store_id: null,
        warehouse_id: null,
        carrier_id: 2,
        shipment_method_id: null,
        max_delivery_date: null,
        confirm_email_date: null,
        sms_date: null,
        store_label: null,
        billing_address: {
            email: '<EMAIL>',
        },
        effective_shipping_date: null,
        initial_estimated_delivery_date: null,
        current_estimated_shipping_date: null,
        shipping_date_updated_at: '2023-01-01',
        shipping_address: null,
        articles: [],
        payments: [],
        delivery_note_ids: [],
        tags: [],
    }
}

export function customerOrderPaymentMock(payment_method_code = 'CBS-O') {
    return {
        type: 'PAYMENT',
        status: 'AWAITING_REMITTANCE',
        workflow: 'payment_v2',
        unpaid_at: null,
        created_at: '2024-10-18 15:12:21.000000',
        created_by: 'boule.et.bill',
        payment_id: 1,
        accepted_at: null,
        accepted_by: null,
        auto_status: null,
        card_origin: null,
        remit_proof: null,
        remitted_at: null,
        remitted_by: null,
        cancelled_at: null,
        cancelled_by: null,
        operation_id: null,
        remit_amount: 0,
        auto_warranty: null,
        creation_proof: null,
        remit_asked_at: null,
        remit_asked_by: '',
        created_by_name: 'Devteam',
        creation_amount: 0,
        accepted_by_name: null,
        remitted_by_name: '',
        acceptation_proof: null,
        cancelled_by_name: '',
        payment_method_id: 59,
        acceptation_amount: 0,
        auto_status_detail: null,
        cancellation_amount: 0,
        creation_extra_data: [],
        payment_method_code: payment_method_code,
        auto_warranty_detail: null,
        payment_method_description: 'Carte de cr\u00e9dit en ligne s\u00e9curis\u00e9e Ogone 3DS',
    }
}

export function customerOrderContextForPaymentsMock(customer_order_id) {
    return {
        customer_order_id,
        origin: 'son-video.com',
        ip_address: '0.0.0.0',
        total_price: '100.00',
        created_at: '2021-01-02 18:24:52',
        estimated_delivery_date: null,
        customer: {
            customer_id: 970481,
            email: '<EMAIL>',
            name: 'Jean Neymar',
            is_new: null,
            first_order_date: null,
        },
        shipping_address: {
            country_code: 'FR',
            is_in_france: true,
            civility: 'M.',
            firstname: 'Jean',
            lastname: 'Neymar',
            cellphone: '0601020304',
            city: 'Nantes',
            postal_code: '44000',
            address: 'la bas',
            company_name: 'Son-Vidéo.com',
            phone: null,
        },
        billing_address: {
            country_code: 'FR',
            is_in_france: true,
            civility: 'M.',
            firstname: 'Jean',
            lastname: 'Neymar',
            cellphone: '0601020304',
            city: 'Nantes',
            postal_code: '44000',
            address: 'la bas',
            company_name: 'Son-Vidéo.com',
            phone: null,
        },
        shipment_methods: [],
        products: [],
        payments: [],
        remaining_balance: '0.00',
        has_remaining_balance: false,
        operations: [],
    }
}

export function operationMock(customer_order_id, operation_id) {
    return {
        refundable_amount: '0.00',
        operation_id,
        customer_order_id,
        payment_id: '01929fc1-f14d-72b6-b1db-7a04899ce45f',
        code: 'CBS-O',
        label: 'Carte bleue / VISA / Mastercard / E-Carte bleue',
        amount: '0.00',
        type: 'PAYMENT',
        status: 'ACCEPTED',
        related_operations: [],
        merchant_reference: `${customer_order_id}-1`,
        meta: {
            payment_id: `${customer_order_id}_0`,
            internal_label: 'VISA',
        },
        histories: [
            {
                created_at: '2024-10-18 15:12:21.000000',
                description: 'Demande d\u0027URL pour compl\u00e9ter le dossier de financement',
                internal_status: 'AWAITING',
                external_status: 'INITIATE_JOURNEY',
                operation_history_id: 2,
                operation_id: operation_id,
            },
        ],
        available_actions: [],
    }
}
