// GraphQl endpoint, should be the same as in .env.e2e file VITE_APP_GRAPHQL_ENDPOINT
// Uses the .env in component testing (hence the wildcard **)
export const GRAPHQL_ENDPOINT = 'https://**/v1/graphql'

// Utility to match GraphQL mutation based on the operation name
export const hasOperationName = (req, operationName) => {
    return req?.body?.operationName === operationName
}

export const getQueryAlias = (operationName, withPrefix = true) =>
    withPrefix ? `@gql${capitalize(operationName)}Query` : `gql${capitalize(operationName)}Query`
export const getMutationAlias = (operationName, withPrefix = true) =>
    withPrefix ? `@gql${capitalize(operationName)}Mutation` : `gql${capitalize(operationName)}Mutation`

// Alias query if operationName matches
export const aliasQuery = (req, operationName) => {
    if (hasOperationName(req, operationName)) {
        req.alias = getQueryAlias(operationName, false)
    }
}

// Alias mutation if operationName matches
export const aliasMutation = (req, operationName) => {
    if (hasOperationName(req, operationName)) {
        req.alias = getMutationAlias(operationName, false)
    }
}

const capitalize = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1)
}
