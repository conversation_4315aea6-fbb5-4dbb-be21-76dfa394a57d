import { PERMISSION_ADMINISTRATE } from '../../../src/apps/erp/permissions'
import { aliasQuery, get<PERSON><PERSON>y<PERSON><PERSON><PERSON>, GRAPHQL_ENDPOINT, hasOperationName } from './graphql-test-utils'

/**
 * Genetic recipes to test the permissions in context
 * It will mostly test the filtered permission for the target button
 * but not the UI or behaviours as it is already tested in component tests
 *
 * This is mostly to test that the request params really filter the permission that we need to administrate correctly
 *
 * @param button
 * @param visitFunction
 * @param permissions_in_context_where
 * @param permissions_required_to_administrate
 */
export function testPermissionInContext(
    button,
    visitFunction,
    permissions_in_context_where,
    permissions_required_to_administrate = [],
) {
    const FETCH_ACCOUNTS_QUERY = 'fetchAccounts'
    const FETCH_PERMISSION_QUERY = 'fetchOwnerPermissions'

    context('Contextual permissions', () => {
        beforeEach(() => {
            // not imported as the file uses path alias that cypress e2e tests can't use yet :-(
            cy.window().its('localStorage').invoke('removeItem', 'erp.global_config')
        })

        describe('With admin mode deactivated', () => {
            it('should not show the permission in context button', () => {
                void visitFunction()
                cy.get(button).should('not.exist')
            })
        })

        describe('With admin mode activated', () => {
            it.only('should show the permission in context button and send the correct filter params to the server', () => {
                cy.intercept('POST', GRAPHQL_ENDPOINT, (req) => {
                    if (hasOperationName(req, FETCH_ACCOUNTS_QUERY)) {
                        aliasQuery(req, FETCH_ACCOUNTS_QUERY)

                        req.reply({
                            fixture: `graphql/${FETCH_ACCOUNTS_QUERY}`,
                        })
                    }

                    if (hasOperationName(req, FETCH_PERMISSION_QUERY)) {
                        aliasQuery(req, FETCH_PERMISSION_QUERY)

                        req.reply({
                            data: {
                                permission_owner: [],
                            },
                        })
                    }
                })

                void visitFunction()

                cy.addPermissions([PERMISSION_ADMINISTRATE, ...permissions_required_to_administrate])
                cy.get('[data-context=top-bar] [data-context=dropdown-menu] [data-context=trigger]').click()
                cy.get('[data-context=dropdown-menu-item]:contains(Activer mode admin)').click()

                cy.get(button).should('be.visible')
                cy.get(button).should('not.be.disabled')
                cy.get(button).click()

                cy.get('[data-context=slide-out-container]').should('be.visible').as('slide-in')

                cy.get('@slide-in').find('[data-context=erp-multiselect]').click()
                cy.get('@slide-in').find('[data-context=erp-multiselect] [data-context=dropdown]').should('be.visible')

                cy.get('@slide-in').find('[data-context=input]').type('foo')

                // autocomplete
                cy.wait(getQueryAlias(FETCH_ACCOUNTS_QUERY))
                cy.get('@slide-in')
                    .find('[data-context="suggestion"] [data-context=erp-user-item]')
                    .should('have.length', 12)

                // Select a result and check the graphql call for the permission editor
                cy.get('@slide-in').find('[data-context="suggestion"]').eq(0).click()

                cy.log(` === Check request parameters ===`)

                // Only the query parameters changes between the tests using this function
                cy.wait(getQueryAlias(FETCH_PERMISSION_QUERY)).then((xhr) => {
                    expect(xhr.request.body.variables).to.deep.eq({
                        limit: 10,
                        where: {
                            owner_id: {
                                _eq: 'e5beb222-278c-4a06-b6d1-30cb72113c24',
                            },
                        },
                        where_permissions: permissions_in_context_where,
                        order_permissions: [
                            {
                                permission_id: 'asc',
                            },
                        ],
                    })
                })

                cy.log(` === No additional test as the component is generic and unit tested ===`)
            })
        })
    })
}
