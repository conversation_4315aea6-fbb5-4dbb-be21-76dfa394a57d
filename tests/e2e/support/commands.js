// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add("login", (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This is will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })

import '@4tw/cypress-drag-drop'
import 'cypress-file-upload'

Cypress.Commands.add('getAuthenticationStore', () => {
    return cy.window().its('Erp.authenticationStore')
})

/**
 * define cy.authenticate(tokens)
 * Set the tokens in the localStorage.
 *
 * @params {undefined|String|Object} tokens
 *      - Object: {access: 'access_token', refresh: 'refresh_token'}. One or the 2 properties can be omitted.
 *      - String: value of the "access" property to set
 *      - undefined: default to {access: 'ACCESS_TOKEN', refresh: 'REFRESH_TOKEN'}
 */
Cypress.Commands.add('authenticate', (tokens) => {
    // transform string to object
    if (typeof tokens === 'string') {
        tokens = { access: tokens }
    }

    // set defaults
    const { access, refresh } = Object.assign(
        {
            access: 'ACCESS_TOKEN',
            refresh: 'REFRESH_TOKEN',
        },
        tokens,
    )

    cy.window().its('localStorage').invoke('setItem', 'session-access-token', access)
    cy.window().its('localStorage').invoke('setItem', 'session-refresh-token', refresh)

    cy.intercept('GET', '**/ping', { statusCode: 204 })
    cy.intercept('GET', '**/account/**/avatar**', { statusCode: 404 })
    cy.intercept('GET', '**/tasks', { body: { statusCode: 200, success: true, quantity: '0' } })

    // Turn off uncaught exception handling unhandled promise rejections (those used to fail silently in older versions)
    // https://docs.cypress.io/api/events/catalog-of-events.html#To-catch-a-single-uncaught-exception
    cy.on('uncaught:exception', (err, runnable, promise) => {
        // Error thrown by apps while redirecting to the error page
        if (err.message.includes('Cannot read properties of undefined')) {
            // returning false here prevents Cypress from
            // failing the test
            return false
        }
        if (err.message.includes("Cannot read property 'message' of undefined")) {
            // returning false here prevents Cypress from
            // failing the test
            return false
        }
        // when the exception originated from an unhandled promise
        // rejection, the promise is provided as a third argument
        // you can turn off failing the test in this case
        if (promise) {
            // returning false here prevents Cypress from
            // failing the test
            return false
        }
    })
})

/**
 * define cy.clearSessionStorage()
 * Clear the session storage.
 * Needed to avoid loading some requests from the sessionStorage.
 *
 * @params {array} permissions
 */
Cypress.Commands.add('clearSessionStorage', () => {
    cy.window().its('sessionStorage').invoke('clear')
})

/**
 * define cy.mockErpUser()
 * Load a generic default user for the session with:
 * - an account profile and me response
 * - empty permissions and menu, hence avoiding unwanted errors in the console
 *
 * @params {array} permissions
 */
Cypress.Commands.add('mockErpUser', (permissions = []) => {
    // force the api calls
    cy.clearSessionStorage()
    cy.intercept('GET', '**/api/hal/v1/me', { fixture: 'hal/me/me_123e4567-e89b-12d3-a456-************.json' }).as(
        'api_me',
    )
    cy.intercept('GET', '**/api/hal/v1/me/menu', { fixture: 'hal/me/menu/menus_response.json' }).as('main_menu')
    cy.intercept('GET', '**/api/erp/v1/tasks', { fixture: 'erp/tasks/empty.json' })
    cy.fixture('hal/me/permissions/empty_permissions.json').then((response) => {
        // If we have passed some permissions
        if (permissions.length > 0) {
            response.data.permissions = permissions
        }

        // Mock permissions
        cy.intercept('GET', '**/api/hal/v1/me/permissions', response).as('api_permissions')
    })

    cy.intercept('GET', '**/api/hal/v1/account/123e4567-e89b-12d3-a456-************', {
        fixture: 'hal/account/account_123e4567-e89b-12d3-a456-************.json',
    })
})

/**
 * define cy.toggleMenu(opened)
 * Toggle the main menu
 *
 * @param {boolean|undefined} opened    Force the status to this value if present, toggle if not.
 */
Cypress.Commands.add('toggleMenu', () => {
    cy.document().find('[data-context=main-menu]', { timeout: 10000 }).should('be.visible')
    cy.window().its('Erp.menuStore').invoke('toggle')
})

/**
 * define cy.checkErrorCode(statusCode)
 * Check error code message in a page
 */
Cypress.Commands.add('checkErrorCode', (statusCode) => {
    cy.document().find('[data-context=error-code]').should('be.visible').should('contain', statusCode)
})

/**
 * define cy.checkUnauthorizedAccess(path)
 * Check that a page is unauthorized with specified route path
 */
Cypress.Commands.add('checkUnauthorizedAccessFor', (path) => {
    // Reset stub for permissions
    // cy.intercept('GET', '**/api/hal/v1/me/permissions', { fixture: 'account/permissions/permissions.json' })
    cy.visit(path)
    cy.document().within(() => {
        cy.get('[data-context=error-code]').should('contain', '403')
        cy.get('[data-context=error-message-general]').should('contain', 'Oops... Une erreur est survenue')
        cy.get('[data-context=error-message-code]').should(
            'contain',
            "Vous n'avez pas la permission d'accéder à cette page",
        )
        cy.get('[data-context=back-home-link]').should('contain', "Retour sur la page d'accueil")
    })
})

/**
 * define cy.wrap(element).hasIcon(name, domain)
 * Check if the element contains a font-awesome icon "name" from the "domain"
 *
 * @param {string}           name    icon name
 * @param {string|undefined} domain  icon domain, default to 'fal'
 *
 * @example cy.get(…).hasIcon('flag')
 * @example cy.get(…).hasIcon('flag', 'fas')
 */
Cypress.Commands.add('hasIcon', { prevSubject: 'element' }, (subject, name, domain) => {
    domain = domain || 'fal'

    cy.wrap(subject).find(`svg[data-prefix="${domain}"][data-icon="${name}"]`).should('be.visible')

    return cy.wrap(subject)
})

/**
 * define cy.wrap(element).multiselect(value_to_type, value_to_select)
 *
 * Fill the multiselect element, waiting for the animations to complete and avoiding some Cypress errors.
 * First value_to_type is typed in the field. Leave it blank if you just want to open the dropdown list.
 * Then the option containing value_to_select is selected. If undefined, {enter} is pressed, selecting the first option.
 *
 * @param {string|undefined} value_to_type
 * @param {string|undefined} value_to_select
 *
 * @example cy.get(…).multiselect('store')
 * @example cy.get(…).multiselect('d', 'drop_shipment')
 */
Cypress.Commands.add(
    'multiselect',
    { prevSubject: 'element' },
    (subject, value_to_type, value_to_select, use_clock) => {
        cy.wrap(subject).as('multiselect')

        cy.get('@multiselect').click()

        if (use_clock) {
            cy.tick(100)
        }

        cy.get('@multiselect').find('.multiselect__content-wrapper').should('be.visible')

        // Pre-filter using the search field
        if (typeof value_to_type === 'string' && value_to_type.length > 0) {
            // the dropdown can recover the input
            // but keystrokes are still registered, hence the force
            cy.get('@multiselect')
                .find('.multiselect__input')
                .within(() => {
                    cy.root().focus()
                    cy.root().type(value_to_type, { force: true })
                })
        }

        // Then select a specific value from the remaining list of choices (first match)
        // or the first element in the list
        if (typeof value_to_select === 'string' && value_to_select.length > 0) {
            cy.get('@multiselect').find(`.multiselect__element:contains(${value_to_select})`).first().click()
        } else {
            cy.get('@multiselect').find(`.multiselect__element`).first().click()
        }

        cy.wait(50)

        return cy.wrap(subject)
    },
)

/**
 * define cy.wrap(element).erpMultiselect(value_to_type, value_to_select)
 *
 * Fill the erp-multiselect element, waiting for the animations to complete and avoiding some Cypress errors.
 * First value_to_type is typed in the field. Leave it blank if you just want to open the dropdown list.
 * Then the option containing value_to_select is selected. If undefined, {enter} is pressed, selecting the first option.
 *
 * @param {string|undefined} value_to_type
 * @param {string|undefined} value_to_select
 */
Cypress.Commands.add(
    'erpMultiselect',
    { prevSubject: 'element' },
    (subject, value_to_type, value_to_select, use_clock) => {
        cy.wrap(subject).as('erp-multiselect')

        cy.get('@erp-multiselect').click()

        if (use_clock) {
            cy.tick(100)
        }

        cy.get('@erp-multiselect').find('[data-context=dropdown]').should('be.visible')

        // Pre-filter using the search field
        if (typeof value_to_type === 'string' && value_to_type.length > 0) {
            cy.get('@erp-multiselect')
                .find('input')
                .within(() => {
                    cy.root().clear()
                    cy.root().focus()
                    cy.root().type(value_to_type)
                })
        }

        // Then select a specific value from the remaining list of choices (first match)
        // or the first element in the list
        if (typeof value_to_select === 'string' && value_to_select.length > 0) {
            cy.get('@erp-multiselect').find(`[data-context=suggestion]:contains(${value_to_select})`).first().click()
        } else {
            cy.get('@erp-multiselect').find(`[data-context=suggestion]`).first().click()
        }

        return cy.wrap(subject)
    },
)

/**
 * define cy.wrap(element).multiselectCloseTag(tag_to_delete)
 *
 * Delete the selected tag corresponding to the text value
 *
 * @param {string|undefined} tag_to_delete
 *
 * @example cy.get(…).multiselectCloseTag('Champigny 2')
 */
Cypress.Commands.add('multiselectRemoveTag', { prevSubject: 'element' }, (subject, tag_to_delete) => {
    cy.wrap(subject).as('multiselect')

    cy.get('@multiselect')
        .find('.multiselect__tag')
        .contains('span', tag_to_delete)
        .parent()
        .find('.multiselect__tag-icon')
        .click()

    return cy.wrap(subject)
})

/**
 * define cy.wrap(element).datepicker(cell_to_select)
 *
 * Fill the datepicker element, waiting for the animations to complete and avoiding some Cypress errors.
 * Will always select on the current month/year so be careful in your tests.
 *
 * @param {string} cell_to_select   Content of the cell to select
 *
 * @example cy.get(…).datepicker('13')   => will select the first cell which contains '13' in the calendar
 */
Cypress.Commands.add('datepicker', { prevSubject: 'element' }, (subject, cell_to_select) => {
    cy.wrap(subject).as('datepicker')

    cy.get('@datepicker').find('input').click()
    cy.get('@datepicker').find('.vdp-datepicker__calendar:visible').as('calendar')
    cy.get('@calendar').find(`.cell:contains(${cell_to_select})`).first().click()
    cy.get('@datepicker').find('.vdp-datepicker__calendar').should('not.be.visible')

    return cy.wrap(subject)
})

/**
 * define cy.wrap(element).erpDatePicker(cell_to_select)
 *
 * Fill in the erp-date-picker elements, waiting for the animations to complete and avoiding some Cypress errors.
 *
 * @param {string} cell_to_select   Content of the cell to select
 *
 * @example cy.get(…).erpDatePicker('13')   => will select the first cell which contains '13' in the calendar
 */
Cypress.Commands.add('erpDatePicker', { prevSubject: 'element' }, (subject, cell_to_select) => {
    cy.wrap(subject).as('datepicker')

    cy.get('@datepicker').click()
    cy.get('@datepicker').find(`div[data-active][data-context=day]:contains(${cell_to_select})`).first().click()

    cy.get('@datepicker').then((datepicker) => {
        const validate_button = datepicker.find('[data-context=validate]')

        if (validate_button.length > 0) {
            // Hour is displayed, so we have to click to the validate button
            validate_button[0].click()
        }
    })

    return cy.wrap(subject)
})

/**
 * define cy.wrap(element).erpDateRangePicker(cell_to_select)
 *
 * Fill in the erp-date-range-picker elements, waiting for the animations to complete and avoiding some Cypress errors.
 *
 * @param {string} first_cell_to_select   Content of the first cell to select
 * @param {string} second_cell_to_select   Content of the second cell to select
 *
 * @example cy.get(…).erpDateRangePicker('13', '15')   => will select the first cell which contains '13' in the calendar and will select the second cell which will contain 15
 */
Cypress.Commands.add(
    'erpDateRangePicker',
    { prevSubject: 'element' },
    (subject, first_cell_to_select, second_cell_to_select) => {
        cy.wrap(subject).as('datepicker-range')

        cy.get('@datepicker-range').find('[data-context=start-date]').erpDatePicker(first_cell_to_select)
        cy.get('@datepicker-range').find('[data-context=end-date]').erpDatePicker(second_cell_to_select)

        return cy.wrap(subject)
    },
)

/**
 * define cy.wrap(string).normalizeWhitespace()
 *
 * Replace multiple consecutive whitespace characters with a single space.
 *
 * @param {string} subject   A string containing multiple whitespace family characters (space, \n, \l\n…)
 * @return string            "subject" with single spaces
 *
 *
 * @example cy.get(…).invoke('text').normalizeWhitespace()
 */
Cypress.Commands.add('normalizeWhitespace', { prevSubject: 'true' }, (subject) => {
    return subject.replace(/\s+/g, ' ')
})

/**
 * define cy.addPermissions(permissions)
 *
 * Add permissions in the store
 *
 * @param {array|string} permissions   The permissions to add
 *
 * @example cy.addPermissions('INVENTORY_REPORT')
 * @example cy.addPermissions(['INVENTORY_REPORT', 'another_permission'])
 */
Cypress.Commands.add('addPermissions', (permissions) => {
    if (typeof permissions === 'string') {
        permissions = [permissions]
    }

    cy.getAuthenticationStore()
        .its('permissions')
        .then((store_permissions) => {
            cy.getAuthenticationStore().invoke('setPermissions', [...store_permissions, ...permissions])
        })
})

/**
 * define cy.removePermissions(permissions)
 *
 * Remove permissions from the store
 *
 * @param {array|string} permissions   The permissions to remove
 *
 * @example cy.removePermissions('INVENTORY_REPORT')
 * @example cy.removePermissions(['INVENTORY_REPORT'])
 */
Cypress.Commands.add('removePermissions', (permissions) => {
    if (typeof permissions === 'string') {
        permissions = [permissions]
    }

    cy.getAuthenticationStore()
        .its('permissions')
        .then((store_permissions) => {
            cy.getAuthenticationStore().invoke(
                'setPermissions',
                store_permissions.filter((p) => !permissions.includes(p)),
            )
        })
})

/**
 * define cy.getWrapper()
 *
 * Get legacy Wrapper
 */
Cypress.Commands.add('getWrapper', () => {
    cy.window().its('Erp.wrapper')
})

/**
 * Select an erp table row in given table
 */
Cypress.Commands.add('selectTable', (element = '[data-context=erp-table]') => {
    return cy.document().find(element).as('erp-table')
})

/**
 * Select a row in given table
 */
Cypress.Commands.add('selectRow', { prevSubject: 'optional' }, (subject, rowIndex) => {
    if (subject) {
        cy.wrap(subject).as('erp-table')
    }

    cy.get('@erp-table').find('td > [data-context=skeleton]').should('not.exist')

    return cy.get('@erp-table').find('tbody tr').eq(rowIndex).as('row')
})

Cypress.Commands.add('checkElement', { prevSubject: 'true' }, (subject, test) => {
    cy.wrap(subject).as('checkedElement')

    let element_to_test = cy.get('@checkedElement')

    let actual_test = typeof test === 'string' ? { should: 'contain', value: test } : test

    if (actual_test.context) {
        element_to_test = element_to_test.find(`[data-context=${test.context}]`)
    }

    if (actual_test.method && actual_test.value) {
        element_to_test.should(actual_test.should, actual_test.method, actual_test.value)

        return
    }

    element_to_test.should(actual_test.should, actual_test.value)
})

Cypress.Commands.add(
    'checkRow',
    { prevSubject: 'true' },
    (subject, cells_tests, { cell_selector = 'td', selector_method = 'find' } = {}) => {
        cy.wrap(subject)[selector_method](cell_selector).as('cells')

        cells_tests.forEach((cell_tests, index) => {
            const tests = Array.isArray(cell_tests) ? cell_tests : [cell_tests]

            tests.forEach((cell_test) => {
                cy.get('@cells').eq(index).checkElement(cell_test)
            })
        })
    },
)

Cypress.Commands.add(
    'checkRows',
    { prevSubject: 'true' },
    (subject, rows_tests, { cell_selector = 'td', selector_method = 'find' } = {}) => {
        rows_tests.forEach((cells_tests, index) => {
            cy.wrap(subject).eq(index).checkRow(cells_tests, { cell_selector, selector_method })
        })
    },
)

/**
 * Select a cell in given table
 */
Cypress.Commands.add('selectCell', { prevSubject: 'optional' }, (subject, rowOrCellIndex, cellIndex) => {
    if (subject) {
        cy.wrap(subject).as('erp-table')
    }

    if (cy.state('aliases')?.row) {
        cy.get('@row').find('td').eq(rowOrCellIndex).as('cell')

        return
    }

    cy.get('@erp-table').find('tbody tr[data-context=table-row]').eq(rowOrCellIndex).find('td').eq(cellIndex).as('cell')
})

/**
 * We can't stub win.location.reload (error "Cannot redefine property: reload").
 * Instead we use a trick:
 * - add a data to the window object with cy.initReloadTest()
 * - detect if it is not there anymore = page has been reloaded (cy.checkHasBeenReloaded())
 * credit: https://glebbahmutov.com/blog/detect-page-reload/
 */
const custom_prop = '__not_reloaded'
Cypress.Commands.add('initReloadTest', () => {
    cy.window().then((w) => (w[custom_prop] = true))
    cy.window().should('have.prop', custom_prop, true)
})
Cypress.Commands.add('checkHasBeenReloaded', () => {
    cy.window().should('not.have.prop', custom_prop)
})
Cypress.Commands.add('checkHasNotBeenReloaded', () => {
    cy.window().should('have.prop', custom_prop, true)
})

/**
 * Check a page toast value
 */
Cypress.Commands.add('toast', (message, type) => {
    cy.document()
        .find(`[data-context=toast-message][data-type=${type}]:visible`)
        .as('toast')
        .should('be.visible')
        .contains(message)
        // The closest() not in a "should" yield only the matched element
        .closest('[data-context=toast-message]')
        .as('this_toast')
        .find('[data-context=dismiss]')
        .click()

    cy.get('@this_toast').should('not.be.visible')
})

Cypress.Commands.add('closeAllToasts', () => {
    const VISIBLE_CLOSE_TRIGGER = '[data-context=dismiss]:visible'
    cy.log(`[CLOSE ALL TOASTS] Attempt to close dem ol'`)

    // Temporize page load...
    cy.wait(250, { log: false })

    const attemptToCloseAllToast = (attempt = 1) => {
        if (attempt >= 4) {
            cy.log(`[CLOSE ALL TOASTS] bailing out...`)

            return cy.get('[data-context=toast-container]')
        }

        cy.log(`[CLOSE ALL TOASTS] Attempt ${attempt}`)

        cy.get('[data-context=toast-container]').then(($container) => {
            if ($container.find(VISIBLE_CLOSE_TRIGGER).length === 0) {
                cy.wait(250, { log: false })
                return attemptToCloseAllToast(attempt + 1)
            }

            cy.get(VISIBLE_CLOSE_TRIGGER)
                .each(($toast) => {
                    cy.log(`[CLOSE ALL TOASTS] Closing toast...`)

                    cy.wrap($toast).click()
                    cy.wrap($toast).should('not.be.visible')
                })
                .then(() => {
                    return attemptToCloseAllToast(3) // try two time again
                })
        })
    }

    return attemptToCloseAllToast()
})

/**
 * Check that the chained element displays a tooltip on mouseover
 *
 * set message to null to check that no tooltip is displayed at all
 *
 * @param {null|string} message
 *
 * @example cy.get('.target').tooltip('tooltip message')
 * @example cy.get('.target').tooltip(null)
 */
Cypress.Commands.add('tooltip', { prevSubject: 'true' }, (subject, message, use_clock = false) => {
    cy.wrap(subject)
        .trigger('mouseenter')
        .then(($element) => {
            if (use_clock) {
                cy.tick(500)
            }

            if (null === message) {
                cy.document().find('.v-popper__popper--shown').should('not.exist')
                return
            }
            cy.document().find('.v-popper__popper--shown').should('exist')
            cy.document().find('.v-popper__popper--shown').should('be.visible').should('include.text', message)

            cy.wrap($element).trigger('mouseleave')
            if (use_clock) {
                cy.tick(500)
            }

            cy.document().find('.v-popper__inner').should('not.be.visible')
        })

    // allow chaining https://docs.cypress.io/guides/references/error-messages#Cypress-detected-that-you-invoked-one-or-more-cy-commands-in-a-custom-command-but-returned-a-different-value
    return cy.wrap(subject)
})

/**
 * Overrides the native Date function
 * Safe alternative to cy.clock() which does not works well with cy.tooltip() and cy.closeAllToasts()
 *
 * @param Date date
 *
 * @example cy.mockDate(new Date(2024, 1, 20, 11, 44, 1, 0))
 */
Cypress.Commands.add('mockDate', (date) => {
    cy.clock(date.getTime(), ['Date'])
})

/**
 * Check an active tooltip content
 */
Cypress.Commands.add('emitEvent', (action, payload = {}) => {
    cy.getWrapper().then((wrapper) => {
        wrapper.$emit('receiveMessage', {
            action: action,
            payload: payload,
        })
    })
})
