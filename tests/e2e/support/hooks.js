// taken from https://gist.github.com/yagudaev/2ad1ef4a21a2d1cfe0e7d96afc7170bc
// Cypress does not support listening to the fetch method
// Therefore, as a workaround we polyfill `fetch` with traditional XHR which are supported.
// See: https://github.com/cypress-io/cypress/issues/687
//
// Enhanced with GraphQL
enableFetchWorkaround()

// private helpers
function enableFetchWorkaround() {
    let polyfill

    before(() => {
        console.info('Load fetch XHR polyfill')
        cy.readFile('./tests/e2e/support/polyfills/unfetch.umd.js').then((content) => {
            polyfill = content
        })
    })

    // stub the original fetch with
    // - our own response if calling the graphql endpoint
    // - unfetch method, = a polyfill method using xhr, otherwise
    Cypress.on('window:before:load', (win) => {
        // since the application code does not ship with a polyfill
        // load a polyfilled "fetch" from the test, resulting in an "unfetch" method in win object
        win.eval(polyfill)

        const fetchStub = (resource, init) => {
            let { body, method } = init

            if (resource === 'https://_erp_graphql/v1/graphql' && method === 'POST') {
                const graphql_mock = Cypress.env('GRAPHQL_MOCK')
                body = JSON.parse(body)

                if (body.operationName && graphql_mock && graphql_mock.operations.hasOwnProperty(body.operationName)) {
                    try {
                        const { test, data } = graphql_mock.operations[body.operationName]

                        if (typeof test === 'function') {
                            test(body.variables)
                        }

                        return responseStub(data)
                    } catch (err) {
                        return responseStub(err)
                    }
                }

                // TODO[?] for non mocked operations, could be great to implement generated data out of the schema.
                // For now, fallback to the xhr method, which is mockable with cy.route()
            }

            return win.unfetch(resource, init)
        }

        cy.stub(win, 'fetch', fetchStub).as('fetchStub')
    })

    /**
     * Format the result has expected by Apollo.
     *
     * @param result
     * @returns {Promise<unknown>|Promise<string>|Promise<{json(): Promise<*>, text(): Promise<string>, ok: boolean}>}
     */
    function responseStub(result) {
        return Promise.resolve({
            json() {
                return Promise.resolve(result)
            },
            text() {
                return Promise.resolve(JSON.stringify(result))
            },
            ok: result.ok === undefined ? true : result.ok,
        })
    }
}
