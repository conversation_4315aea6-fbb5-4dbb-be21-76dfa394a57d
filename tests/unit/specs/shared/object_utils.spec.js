import { hasOwnDeepProperty } from '@/shared/object_utils'
import { describe, it, expect } from 'vitest'

describe('Unit test "object_utils" shared library', function () {
    describe('hasOwnDeepProperty()', () => {
        it('returns true if path exists', function () {
            const o = {
                deep1: {
                    deep_2: {
                        deep3: null,
                    },
                },
            }

            expect(hasOwnDeepProperty(o, 'deep1')).toEqual(true)
            expect(hasOwnDeepProperty(o, 'deep1.deep_2')).toEqual(true)
            expect(hasOwnDeepProperty(o, 'deep1.deep_2.deep3')).toEqual(true)
        })

        it('returns false if path does not exists or empty', function () {
            const o = {
                deep1: {
                    deep_2: {
                        deep3: null,
                    },
                },
                other: undefined,
                string_value: 'test',
            }

            expect(hasOwnDeepProperty(o, '')).toEqual(false)
            expect(hasOwnDeepProperty(o, 'error1')).toEqual(false)
            expect(hasOwnDeepProperty(o, 'error1.error2')).toEqual(false)
            expect(hasOwnDeepProperty(o, 'deep1.not_exists')).toEqual(false)
            expect(hasOwnDeepProperty(undefined, '')).toEqual(false)
            expect(hasOwnDeepProperty(o, 'other.data')).toEqual(false)
            expect(hasOwnDeepProperty(o, 'string_value.data')).toEqual(false)
        })
    })
})
