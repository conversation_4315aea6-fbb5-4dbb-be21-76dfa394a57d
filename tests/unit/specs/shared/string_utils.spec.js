import {
    formatDateToRFC3339,
    accentFold,
    makeUrlQueryString,
    capitalizeFirstLetter,
    encodeSearchParams,
    formatCurrencyToFloat,
} from '@/shared/string_utils'

import { describe, it, expect } from 'vitest'

describe('Unit test "string_utils" shared library', function () {
    describe('formatDateToRFC3339()', () => {
        it('returns the right date format according to passed Date', function () {
            const date = new Date('December 17, 1995 03:24:00')
            expect(formatDateToRFC3339(date)).toEqual('1995-12-17T03:24:00+01:00')
        })
    })

    // TODO: uncomment if CI supports full icu in its node version
    // describe('formatDate()', () => {
    //     it('returns the right date format according to passed Date', function() {
    //         const date = new Date('December 17, 1995 03:24:00')
    //         expect(formatDate(date)).toEqual('1995-12-17 03:24:00')
    //     })
    // })

    describe('accentFold()', () => {
        it('remove accents', function () {
            expect(accentFold('àáâãäåçèéêëìíîïñòóôõöøùúûüÿæß')).toEqual('aaaaaaceeeeiiiinoooooouuuuyaes')
        })
    })

    describe('makeUrlQueryString()', () => {
        it('create query string out of an object', function () {
            expect(makeUrlQueryString({})).toEqual('')
            expect(makeUrlQueryString({ toto: 'test' })).toEqual('toto=test')
            expect(makeUrlQueryString({ toto: 'test', param_2: 'étonnant' })).toEqual('toto=test&param_2=%C3%A9tonnant')
        })
    })

    describe('capitalizeFirstLetter()', () => {
        it('returns the string with the first letter capitalized', function () {
            expect(capitalizeFirstLetter('')).toEqual('')
            expect(capitalizeFirstLetter('auie')).toEqual('Auie')
            expect(capitalizeFirstLetter(' bÉpo Is CoOl')).toEqual(' bÉpo Is CoOl')
        })
    })

    describe('encodeSearchParams()', () => {
        it('encode the Object to a string representation', function () {
            expect(encodeSearchParams('')).toEqual('IiI=')
            expect(encodeSearchParams({})).toEqual('e30=')
            expect(
                encodeSearchParams({
                    filters: {
                        location: { label: 'Warehouse 13', path: '01.02.03' },
                    },
                }),
            ).toEqual('eyJmaWx0ZXJzIjp7ImxvY2F0aW9uIjp7ImxhYmVsIjoiV2FyZWhvdXNlIDEzIiwicGF0aCI6IjAxLjAyLjAzIn19fQ==')
        })
    })

    describe('formatCurrencyToFloat()', () => {
        it('format currency string to float', function () {
            expect(formatCurrencyToFloat(' 1 5 0 8,  7 6 ')).toStrictEqual(1508.76)
            expect(formatCurrencyToFloat('1 508,76 ')).toStrictEqual(1508.76)
            expect(formatCurrencyToFloat('1508,76€')).toStrictEqual(1508.76)
            expect(formatCurrencyToFloat('1508,76')).toStrictEqual(1508.76)
            expect(formatCurrencyToFloat('1508.76')).toStrictEqual(1508.76)
            expect(formatCurrencyToFloat(' 1 50 8,76 € ')).toStrictEqual(1508.76)
        })
    })
})
