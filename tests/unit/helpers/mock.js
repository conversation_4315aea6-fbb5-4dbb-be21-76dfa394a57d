/**
 * For each possibility, if the mocked function is called with parameter equal to "key", the mock return "value"
 *
 * @param mock          Altered mock
 * @param possibilities Array of {key, value}
 */
const assignResultToParamValue = (mock, possibilities) => {
    mock.mockImplementation((value) => {
        const found = possibilities.find((p) => p.key === value)
        if (found) {
            return found.value
        }
    })
}

export { assignResultToParamValue }
